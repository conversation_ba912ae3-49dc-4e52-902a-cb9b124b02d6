﻿<div id="modalMaNguyenTe" class="modal-drag" style="width:300px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5><span class="modal-drag-title mx-2">Ch<PERSON><PERSON> nguyên tệ</span> <span data-dismiss="modal-drag"><i class="fa fa-times mx-2"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_MaNguyenTe" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalMaNguyenTeElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalMaNguyenTeDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonMaNguyenTe">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalMaNguyenTeDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsmanguyente" id="dsmanguyente_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="manguyente_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalMaNguyenTeItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="manguyente_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
