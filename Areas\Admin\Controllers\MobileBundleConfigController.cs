﻿using ESCS.Attributes;
using ESCS.COMMON.Common;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.COMMON.Response;
using ESCS.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class MobileBundleConfigController : BaseController
    {
        public async Task<IActionResult> Index()
        {
            try
            {
                await GetData();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", ex.Message);
            }
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Index(MobileBundle model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    string data = await Request.UploadFile(
                        StoredProcedure.PHT_MOBILE_BUNDLE_NH,
                        model.GetRequestData(GetUser()),
                        model.file,
                        "/api/esmartclaim/upload-mobile-bundle"
                    );
                    var res = Newtonsoft.Json.JsonConvert.DeserializeObject<BaseResponse<object>>(data);

                    if (res.state_info.status != STATUS_OK)
                        throw new Exception(res.state_info.message_body);

                    return LocalRedirect(Url.Action("Index", "MobileBundleConfig", new { area = "Admin" }));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", ex.Message);
                }
            }

            try
            {
                await GetData();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", ex.Message);
            }

            return View(model: model);
        }

        [NonAction]
        private async Task GetData()
        {
            var user = GetUser();

            var data = await Request.GetResponeNew<IEnumerable<dynamic>>(
                StoredProcedure.PHT_MOBILE_BUNDLE_LKE,
                Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    ma_doi_tac_nsd = user?.ma_doi_tac ?? string.Empty,
                    ma_chi_nhanh_nsd = user?.ma_chi_nhanh ?? string.Empty,
                    nsd = user?.nsd ?? string.Empty,
                    pas = user?.pas ?? string.Empty,
                })
            );
            if (data.state_info.status != STATUS_OK)
                throw new Exception(data.state_info.message_body);
            ViewData["data"] = data.data_info;
        }
    }

    public class MobileBundle
    {
        public string ma_doi_tac_nsd { get; set; }
        public string ma_chi_nhanh_nsd { get; set; }
        public string nsd { get; set; }
        public string pas { get; set; }
        [Required] public string platform { get; set; }
        [Required] public string app_version { get; set; }
        [Required] public long build_number { get; set; }
        [Required] public IFormFile file { get; set; }

        public string GetRequestData(escs_nguoi_dung user)
        {
            ma_doi_tac_nsd = user.ma_doi_tac;
            ma_chi_nhanh_nsd = user.ma_chi_nhanh;
            nsd = user.nsd;
            pas = user.pas;
            return Newtonsoft.Json.JsonConvert.SerializeObject(this);
        }
    }
}