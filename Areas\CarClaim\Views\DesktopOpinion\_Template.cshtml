﻿@*Thông tin số hồ sơ*@
<script type="text/html" id="thongTinSoHoSo_template">
    <% if(ho_so.so_hs!= null && ho_so.so_hs.trim()!= ""){%>
    <h4 class="esmodal-title">
        <PERSON><PERSON> sơ: <a href="#" onclick="copyText(this)"><%- ho_so.so_hs %></a> - <span><%- ho_so.nsd %></span> - <span class="text-color"><%- ho_so.trang_thai %></span> - <a href="#" onclick="xemToanBoThongTinHoSoBoiThuong('<%- ho_so.ma_doi_tac %>','<%- ho_so.ma_chi_nhanh_ql %>','<%- ho_so.ma_chi_nhanh %>','<%- ho_so.so_id %>','<%- ho_so.so_id_hd%>','<%- ho_so.so_id_dt%>')">Xem chi tiế<PERSON> hồ sơ</a>
    </h4>
    <% }else{ %>
    <h4 class="esmodal-title">
        Ch<PERSON><PERSON> c<PERSON> số hồ sơ
    </h4>
    <% } %>
</script>

@*Template lịch sử cho ý kiến*@
<script type="text/html" id="tblLichSuChoYKien_template">
    <% if(data.length > 0) {
    _.forEach(data, function(item, index) {%>
    <tr class="text-center cursor-pointer ls_cho_y_kien" data_ma_doi_tac="<%- item.ma_doi_tac %>" data_so_id_y_kien="<%- item.so_id_yk %>" id="ls_cho_y_kien_<%- item.bt%>" onclick='xemNoiDungChoYKien("<%- item.bt%>")'>
        <td><%- item.stt%></td>
        <td class="font-weight-bold"><%- item.nsd%></td>
        <td>
            <% if(item.trang_thai_cho_yk == 'QUA_HAN'){ %>
            <span style="font-weight:bold;" class="text-danger"><%- item.ngay_cho_yk %></span>
            <% }else if(item.trang_thai_cho_yk == 'TRONG_HAN'){ %>
            <span style="font-weight:bold;" class="text-success"><%- item.ngay_cho_yk %></span>
            <% }else if(item.trang_thai_cho_yk == '' || item.trang_thai_cho_yk  == null){ %>
            <span style="font-weight:bold;" class="text-dark"><%- item.ngay_cho_yk %></span>
            <% } %>
        </td>
        <td>
            <a href="#" title="Xem nội dung cho ý kiến">
                <i class="fas fa-eye"></i>
            </a>
        </td>
    </tr>
    <%})}%>

    <% if(data.length < 7){
    for(var i = 0; i < 7 - data.length;i++ ){
    %>
    <tr>
        <td style="height:36.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblDienBienTonThat_template">
    <table class="table table-bordered">
        <tr>
            <td style="width: 130px; font-weight: bold;">Ngày xảy ra</td>
            <td colspan="5"><%- ngay_xr %></td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold;">Địa điểm xảy ra</td>
            <td colspan="5"><%- dia_diem %></td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold;">Nhóm nguyên nhân</td>
            <td colspan="5"><%- nhom_nguyen_nhan %></td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold; vertical-align: middle; ">Nguyên nhân</td>
            <td colspan="5">
                <%- nguyen_nhan %>
            </td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold; vertical-align: middle; ">Hậu quả</td>
            <td colspan="5">
                <%- hau_qua %>
            </td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold;">Nhóm sự kiện BH</td>
            <td colspan="5"><%- ten_nhom_su_kien %></td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold;">Lái xe</td>
            <td><%- ten_lxe %></td>
            <td style="width: 130px; font-weight: bold;">Điện thoại</td>
            <td><%- dthoai_lxe %></td>
            <td style="width: 130px; font-weight: bold;">Email</td>
            <td><%- email_lxe %></td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold;">Số GPLX</td>
            <td><%- gplx_so %></td>
            <td style="width: 130px; font-weight: bold;">Hạng</td>
            <td><%- gplx_hang %></td>
            <td style="width: 130px; font-weight: bold;">Hiệu lực</td>
            <%
            if(gplx_hieu_luc ===null || gplx_hieu_luc==="" || gplx_het_han===null || gplx_het_han==="")
            {
            %>
            <td></td>
            <%
            }
            else
            {
            %>
            <td><%- gplx_hieu_luc %> - <%- gplx_het_han %></td>
            <%
            }
            %>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold;">Phạm vi bảo hiểm</td>
            <td colspan="5">
                <% if(pham_vi == '1') {%>
                <span class="text-success font-weight-bold"><%- ten_pham_vi %></span>
                <%}else {%>
                <span class="text-danger font-weight-bold"><%- ten_pham_vi %></span>
                <%}%>
            </td>
        </tr>
        <tr>
            <td style="width: 130px; font-weight: bold;">Ghi chú</td>
            <td colspan="5">
                <%- ly_do %>
            </td>
        </tr>
    </table>
</script>

<script type="text/html" id="tblThongTinGiamDinh_template">
    <table class="table table-bordered">
        <tbody>
            <tr>
                <td style="width: 130px; font-weight: bold;">Ngày Giám định</td>
                <td colspan="5"><%- ngay_gd %></td>
            </tr>
            <tr>
                <td style="width: 130px; font-weight: bold;">Địa điểm giám định</td>
                <td colspan="5"><%- dia_diem %></td>
            </tr>
            <tr>
                <td style="width: 130px; font-weight: bold;">Đơn vị GĐ</td>
                <td colspan="5"><%- ten_dvi_gd %></td>
            </tr>
            <tr>
                <td style="width: 130px; font-weight: bold;">Tên GĐV</td>
                <td>
                    <%- ten_gdv %>
                </td>
                <td style="width: 130px; font-weight: bold;">Điện thoại</td>
                <td>
                    <%- dthoai_gdv %>
                </td>
                <td style="width: 130px; font-weight: bold;">Email</td>
                <td>
                    <%- email_gdv %>
                </td>
            </tr>
            <tr>
                <td style="width: 130px; font-weight: bold;">Công ty giám định</td>
                <td colspan="5"><%- ten_cty_giam_dinh %></td>
            </tr>
            <tr>
                <td style="width: 130px; font-weight: bold;">Đối tượng GĐ</td>
                <td colspan="5"><%- doi_tuong_gd_ten %></td>
            </tr>
        </tbody>
    </table>
</script>

<script type="text/html" id="tblThongTinCacBenThamGiaGD_template">
    <%if(arrNguoiGiamDinh.length > 0){
    _.forEach(arrNguoiGiamDinh, function(nguoiGiamDinh) { %>
    <tr>
        <td><%- nguoiGiamDinh.dai_dien %></td>
        <td><%- nguoiGiamDinh.ten %></td>
        <td><%- nguoiGiamDinh.dien_thoai %></td>
        <td><%- nguoiGiamDinh.email %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="4">
            <p class="m-0">Chưa có dữ liệu</p>
        </td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="tblThongTinHoSoGiayToYKienTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-left">
            <a href="#"> <%- item.ten %></a>
        </td>
        <td class="text-center"><%- item.ngay_bs %></td>
        <td style="text-align: center">
            <%- item.loai_ten %>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" disabled="disabled" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% }else{ %>
            <% if(item.hop_le == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" checked="checked" disabled="disabled" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" id="hop_le_<%- item.ma_hs %>" disabled="disabled" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
            <% } %>
        </td>
        <td class="text-center"><%- item.trang_thai %></td>
        <td class="text-center">
            <% if(item.trang_thai == 'C' || item.hop_le == 0 || item.hop_le == null){%>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" checked="checked" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hang_muc_hsgt">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" disabled="disabled" value="" class="custom-control-input input_chon_hang_muc_hsgt">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="6">Chưa có hồ sơ giấy tờ</td>
    </tr>
    <% } %>
</script>

@*Danh sách ảnh*@
<script type="text/html" id="lstImage_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="pt-1 px-1" id="nhom_anh_<%- index %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImg('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <p class="fileNameImage mt-1 mx-0" style="cursor:pointer"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
    <% } %>
</script>

@*  Danh sách tài liệu hồ sơ hình ảnh *@
<script type="text/html" id="dsHinhAnhHangMucTemplate">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <div class="imagesCategory" style="display: table-row" data-nhom="<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p style="margin-bottom:5px;" class="font-weight-bold">
            <a href="#">
                <%- item.nhom %><br />
                <i style="font-size: 10px">(<%- item.ten_doi_tuong %>)</i>
            </a>
        </p>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div style="width:60px; height:60px; margin-right:10px; margin-bottom:5px; cursor:pointer;float:left;">
            <img style="width: 100%; height:100%; border-radius:10px" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay%>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
        </div>
        <% }) %>
    </div>
    <% })} %>

</script>