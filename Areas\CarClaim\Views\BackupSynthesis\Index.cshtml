﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.AspNetCore.Http
@using ESCS.COMMON.Contants
@using Newtonsoft.Json
@using ESCS.MODEL.ESCS.ModelView

@{
    ViewData["Title"] = "Tra cứu dự phòng";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<input type="hidden" id="notify_info" value="@ViewBag.ho_so" />
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col col-1">
                            <div class="form-group">
                                <label for="ngay_d">Ngày tìm kiếm</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_d" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-1">
                            <div class="form-group">
                                <label for="ngay_c">&nbsp;</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_c" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-2">
                            <div class="form-group">
                                <label for="nguon">Điểm dự phòng</label>
                                <select class="select2 form-control custom-select" name="diem" style="width:100%">
                                    <option value="" selected>Chọn điểm dự phòng</option>
                                    <option value="MO_SO_HS">Mở số HS</option>
                                    <option value="KTGD">Kết thúc GĐ</option>
                                    <option value="DUYET_PASC">Duyệt PASC</option>
                                    <option value="DUYET_PABT">Duyệt PABT</option>
                                    <option value="TTOAN">Duyệt thẩm định</option>
                                    <option value="HUY_HS">Hủy hồ sơ</option>
                                    <option value="TU_CHOI_BT">Từ chổi BT</option>
                                    <option value="HUY_TTOAN">Hủy duyệt thẩm định</option>
                                    <option value="CHUYEN_TINH_TOAN">Chuyển tính toán</option>
                                    <option value="DUYET_BLVP">Duyệt bảo lãnh viện phí</option>
                                    <option value="BV_CHUYEN_BL">Bệnh viện chuyển bảo lãnh</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-2">
                            <div class="form-group">
                                <label for="nguon">Tích hợp</label>
                                <select class="select2 form-control custom-select" name="tich_hop" style="width:100%">
                                    <option value="" selected>Chọn tích hợp</option>
                                    <option value="0">Chưa tích hợp</option>
                                    <option value="1">Tích hợp thành công</option>
                                    <option value="2">Tích hợp lỗi do server</option>
                                    <option value="3">Tích hợp lỗi nghiệp vụ</option>
                                    <option value="4">Đang tích hợp lại</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-2">
                            <div class="form-group">
                                <label for="nguon">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" name="nv" style="width:100%">
                                    <option value="" selected>Chọn nghiệp vụ</option>
                                    <option value="XE">Xe ô tô</option>
                                    <option value="XE_MAY">Xe máy</option>
                                    <option value="NG">Con người</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" spellcheck="false" placeholder="Số hồ sơ" class="form-control">
                            </div>
                        </div>
                        <div class="col-2" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" title="Xuất báo cáo" id="btnExport">
                                <i class="fa fa-download"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewPhanTrang" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalDetail" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thông tin chi tiết</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form name="frmDetail">
                    <input type="hidden" name="ma_doi_tac" />
                    <input type="hidden" name="so_id" />
                    <input type="hidden" name="nv" />
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"><i class="fas fa-window-close mr-1"></i>Close</button>
                <button type="button" class="btn btn-primary btn-sm" onclick="xemHSGD?.();"><i class="fas fa-file-export mr-1"></i>Hồ sơ giám định</button>
                <button type="button" class="btn btn-primary btn-sm" onclick="xemHSBT?.();"><i class="fas fa-file-export mr-1"></i>Hồ sơ bồi thường</button>
            </div>
        </div>
    </div>
</div>

<div id="modalCapNhatUocTheoDiem" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width: 70%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cập nhật ước theo điểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmCapNhatUocTheoDiem" name="frmCapNhatUocTheoDiem" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:450px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th style="vertical-align: middle;">STT</th>
                                            <th style="vertical-align: middle;">Ngày dự phòng</th>
                                            <th style="vertical-align: middle;">Điểm dự phòng</th>
                                            <th style="vertical-align: middle;width: 250px">Nghiệp vụ</th>
                                            <th style="vertical-align: middle;width: 120px">Số tiền dự phòng</th>
                                            <th style="vertical-align: middle;width: 120px">Tiền chênh lệch</th>
                                            <th>Trạng thái tích hợp</th>
                                            <th style="vertical-align: middle;">Log</th>
                                            <th>Hành động</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblCapNhatUocTheoDiem"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0" style="display:block;">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnDieuChinhBienDo"><i class="fas fa-window-alt mr-2"></i>Điều chỉnh biên độ</button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right" id="btnThemDiemCapNhatUoc"><i class="fas fa-hdd mr-2"></i>Điều chỉnh tăng giảm dự phòng</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalBienDo" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 25%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Điều chỉnh biên độ ước</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="frmBienDo" name="frmBienDo" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:380px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th style="width: 200px">Điểm</th>
                                            <th style="width: 100px">Biên độ ước (%)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblBienDo"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer d-block">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right" id="btnLuuBienDo"><i class="fas fa-save mr-2"></i>Lưu</button>
            </div>
        </div>
    </div>
</div>

<div id="popoverLogRq" class="popover popover-x popover-default" style="display:none; width:500px; max-width: 500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x">&times;</span>Xem log
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-sm-12" id="divLogRq">
                <div class="form-group">
                    <label>Data Request</label>
                    <textarea class="form-control" autocomplete="off" id="divLogRq_NoiDung" name="log_rq" disabled rows="6"></textarea>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12" id="divLogRes">
                <div class="form-group">
                    <label>Data Response</label>
                    <textarea class="form-control" autocomplete="off" id="divLogRes_NoiDung" name="log_res" disabled rows="6"></textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalChonDiemDP" class="modal-drag" style="width:280px; z-index:9999999;">
    <div class="modal-drag-header px-2 border-bottom">
        <h6><span class="modal-drag-title">Chọn điểm dự phòng</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h6>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_ChonDiemDP" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonDiemDPElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonDiemDPDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonDiemDP">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<div id="modalChonNV" class="modal-drag" style="width:280px; z-index:9999999;">
    <div class="modal-drag-header px-2 border-bottom">
        <h6><span class="modal-drag-title">Chọn loại hình nghiệp vụ</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h6>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_ChonNV" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonNVElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonNVDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonNV">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalThemDiem" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 40%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Thêm điểm dự phòng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form name="frmThemDiem" method="post">
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Ngày dự phòng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" required name="ngay_dp" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" name="lh_nv" required style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Trạng thái tích hợp</label>
                                <select class="select2 form-control custom-select" name="tich_hop" required style="width:100%">
                                    <option value="">Chọn trạng thái tích hợp</option>
                                    <option value="1">Không tích hợp sang kế toán</option>
                                    <option value="0">Có tích hợp sang kế toán</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-8">
                            <div class="form-group">
                                <label class="_required">Chọn điểm tích hợp</label>
                                <select class="select2 form-control custom-select" name="diem" required style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Số tiền chênh lệch</label>
                                <input type="text" name="tien_chenh_lech" id="tien" required autocomplete="off" spellcheck="false" placeholder="Số tiền chênh lệch" class="form-control number_negative">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuThemDiem"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalThemDiemDuPhong" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 40%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Thêm điểm dự phòng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form name="frmThemDiemDuPhong" method="post">
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Ngày dự phòng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" required name="ngay_dp" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" name="lh_nv" required style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Số tiền dự phòng</label>
                                <input type="text" name="tien" id="tien" required autocomplete="off" spellcheck="false" placeholder="Số tiền dự phòng" class="form-control number">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuThemDiemDuPhong"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblBienDo_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr>
            <td>
                <input type="hidden" data-field="diem" name="diem" data-val="<%- item.diem %>" value="<%- item.diem %>"/>
                <%- item.ten_diem %>
            </td>
            <td class="text-right">
                <input type="text" data-field="ty_le" name="ty_le" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(item.ty_le) %>" />
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 3){
    for(var i = 0; i < 3 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblCapNhatUocTheoDiem_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr class="capNhatUoc">
            <td class="text-center">
                <input type="hidden" data-field="nv" name="nv" value="<%- item.nv %>" />
                <input type="hidden" data-field="lh_nv" name="lh_nv" value="<%- item.lh_nv %>" />
                <input type="hidden" data-field="diem" name="diem" value="<%- item.diem %>" />
                <input type="hidden" data-field="tich_hop" name="tich_hop" value="<%- item.tich_hop %>" />
                <input type="hidden" data-field="log_rq" name="log_rq" value="<%- item.log_rq %>" />
                <input type="hidden" data-field="log_res" name="log_res" value="<%- item.log_res %>" />
                <input type="hidden" data-field="so_id" name="so_id" value="<%- item.so_id %>" />
                <input type="hidden" data-field="bt" name="bt" value="<%- item.bt %>" />
                <%- index + 1 %>
            </td>
            <td class="text-center">
                <input type="text" data-field="ngay_dp" class="floating-input datepicker text-center" readonly value="<%- item.ngay_dp %>" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten_diem" data-field="ten_diem" value="<%- item.ten_diem %>" />
                <input type="text" class="floating-input combobox" data-field="ten_diem" data-val="<%- item.diem %>" @* onclick="chonDiemDuPhong(this)" *@ readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten_diem %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten" data-field="ten" value="<%- item.ten %>" />
                <input type="text" class="floating-input combobox" data-field="ten" data-val="<%- item.lh_nv %>" @* onclick="chonNV(this)" *@ readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien" name="tien" class="number floating-input tien_<%- item.bt %>" autocomplete="off" disabled value="<%- ESUtil.formatMoney(item.tien) %>" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien_chenh_lech" name="tien_chenh_lech" class="number floating-input tien_chenh_lech" disabled value="<%- ESUtil.formatMoney(item.tien_chenh_lech) %>" />
            </td>
            <td class="text-center">
                <% if(item.tich_hop == 1){%>
                     <i class="fas fa-check-circle text-success" title="Đã tích hợp"></i>
                <%}else{%>
                    <i class="fas fa-times" style="color:red" title="Chưa tích hợp"></i>
                <%}%>
            </td>
            <td class="text-center">
                <% if(item.log_rq != null && item.log_res !=""){ %>
                    <a href="#" class="cursor-pointer combobox" onclick="showLogRq(this)" data-rq="<%- JSON.stringify(item.log_rq) %>" data-res="<%- JSON.stringify(item.log_res) %>" >
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                    <% }else{ %>
                    <a class="cursor-pointer combobox" onclick="showLogRq(this)" data-val="" data-rq="" data-res="">
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                <% } %>
            </td>
            <td class="text-center">
                <% if(item.tich_hop != 1){%>
                    <a href="#" class="cursor-pointer" onclick="tichHopLaiUoc(<%- item.so_id %>, <%- item.bt %>)">
                        <i class="fas fa-arrow-right" style="color: var(--escs-main-theme-color)" title="Tích hợp lại ước"></i>
                    </a>
                <%}else{%>

                <%}%>
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalChonDiemDPDanhSach_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox diemdp" data-id="diemdp_<%- item.diem %>" data-text="<%- item.diem.toLowerCase() %>-<%- item.ten_diem.toLowerCase() %>">
        <input type="checkbox" id="diem_<%- item.diem %>" value="<%- item.diem %>" class="custom-control-input modalChonDiemDPItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="diem_<%- item.diem %>"><%- item.ten_diem %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="modalChonNV_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox lhnv" data-id="lhnv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="lhnvv_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNVItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="lhnvv_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

@section scripts{
    <script src="~/js/app/carclaim/services/backupsynthesisservice.js" asp-append-version="true"></script>
    <script src="~/js/app/carclaim/backupsynthesis.js" asp-append-version="true"></script>
}