﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RazorEngine.Configuration;
using RazorEngine.Templating;
using System.Threading.Tasks;

namespace ESCS.Areas.CarClaim.Controllers
{
    /// <summary>
    /// Giám định bồi thường xe cơ giới
    /// </summary>
    [Area("CarClaim")]
    [SystemAuthen]
    public class BackupSynthesisController : BaseController
    {
        private TemplateServiceConfiguration config;
        public static IRazorEngineService _service = null;
        private readonly IWebHostEnvironment _env;
        private readonly ILogger _logger;

        public BackupSynthesisController(IWebHostEnvironment env, ILoggerFactory logger)
        {
            _env = env;
            _logger = logger.CreateLogger("BackupSynthesisController");
            config = new TemplateServiceConfiguration();
            config.CachingProvider = new RazorEngine.Templating.DefaultCachingProvider();
            if (_service == null)
                _service = RazorEngineService.Create(config);
        }

        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_DP_TRA_CUU_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_DP_LKE_CT, json);
            return Ok(data);
        }
    }
}