﻿<script type="text/html" id="modalBenhVienDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <%if(item.bl_noitru == 'C' && item.bl_ngoaitru == 'C'){%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %> </label> - <label class="text"> (Bảo lãnh nội trú / Bảo lãnh ngoại trú) </label>
        </div>
        <%}else if( item.bl_noitru == 'C' && item.bl_ngoaitru == 'K'){%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %> - <label class="text"> (Bảo lãnh nội trú) </label> </label>
        </div>
        <%}else if(item.bl_noitru == 'K' && item.bl_ngoaitru == 'C'){%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %> - <label class="text"> (Bảo lãnh ngoại trú) </label> </label>
        </div>
        <%}else{%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %></label>
        </div>
        <%}%>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
