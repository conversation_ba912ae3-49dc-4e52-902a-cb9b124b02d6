﻿<div class="modal fade bs-example-modal-lg" id="modalDiaBanGiamDinh" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmPhanDiaBanGiamDinh" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Phân địa bàn giám định</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body" style="padding-bottom: 0px;">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>Miền</label>
                                <select class="select2 form-control custom-select" name="mien" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="card" style="margin-bottom:unset;">
                                <div class="card-body px-0" style="padding:unset !important;">
                                    <div class="border mb-3 rounded">
                                        <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                                            <h5 class="m-0">Tỉnh/thành phố</h5>
                                        </div>
                                        <div class="pd-10">
                                            <input type="text" autocomplete="off" id="input_tkiem_tinh_thanh" onkeyup="onSearchTinhThanh(this)" placeholder="Tìm kiếm tỉnh thành" class="form-control">
                                        </div>
                                        <div class="pd-10 scrollable" id="ds_tinh_thanh" style="height:260px">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card" style="margin-bottom:unset;">
                                <div class="card-body px-0" style="padding:unset !important;">
                                    <div class="border mb-3 rounded">
                                        <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                                            <h5 class="m-0">Phường/xã</h5>
                                        </div>
                                        <div class="pd-10">
                                            <input type="text" autocomplete="off" name="tim" id="input_tkiem_quan_huyen" onkeyup="onSearchQuanHuyen(this)" placeholder="Tìm kiếm phường xã" class="form-control">
                                        </div>
                                        <div class="pd-10 scrollable" id="ds_quan_huyen" style="height:260px">

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuPhanDiaBan"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div>