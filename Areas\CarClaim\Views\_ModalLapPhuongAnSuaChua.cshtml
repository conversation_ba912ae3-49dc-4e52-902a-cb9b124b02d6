﻿@functions {
    public string newGuid() => Guid.NewGuid().ToString();
}
<style>
    .divPhuongAnDanhGiaItem table p {
        margin-bottom: 0px;
    }

    #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem {
        position: relative;
        cursor: pointer;
    }

        #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem .highlight {
            font-weight: bold;
            color: var(--danger);
        }

        #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem .info-badge {
            position: absolute;
            top: 0%;
            left: 100%;
            transform: translate(-50%,-50%);
        }

        #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem:hover {
            background-color: #e3f6ce;
        }

    #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem.active {
            background-color: #96bf49 !important;
    }

        #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem.active,
        #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem.active td,
        #divLichSuTrinhDuyetViewDanhSach .divLichSuTrinhDuyetViewDanhSachItem.active td > h6 {
            color: var(--white) !important;
        }

    #divLichSuTrinhDuyetViewChiTiet .divLichSuTrinhDuyetViewChiTietItem {
        cursor: pointer;
        user-select: none;
        color: var(--gray);
    }

        #divLichSuTrinhDuyetViewChiTiet .divLichSuTrinhDuyetViewChiTietItem.active {
            color: var(--primary);
            background-color: #007bff08;
            box-shadow: 0 0 1rem #007bff08;
        }

    #tblLichSuTrinhDuyetViewChiTiet tr > td i.trang_thai_C {
        visibility: hidden;
    }

    #tblLichSuTrinhDuyetViewChiTiet tr > th:not(:first-child), #tblLichSuTrinhDuyetChiPhiKhac tr > td:not(:first-child) {
        min-width: 100px;
    }

    #popoverNguyenNhan, #popoverGhiChu {
        z-index: 1065 !important;
    }

    #tblLichSuTrinhDuyetViewChiTiet[data-display_loai_trinh="PHUONG_AN"] [data-col="tien_thue"] {
        display: none;
    }

    #tblLichSuTrinhDuyetViewChiTiet[data-display_loai_trinh="BOI_THUONG"] [data-col="tien_bao_gia"],
    #tblLichSuTrinhDuyetViewChiTiet[data-display_loai_trinh="BOI_THUONG"] [data-col="tien_dx"] {
        display: none;
    }

    #tblLichSuTrinhDuyetViewChiTiet[data-display_doi_tuong="TAI_SAN_KHAC"] [data-col="tien_dx"],
    #tblLichSuTrinhDuyetViewChiTiet[data-display_doi_tuong="TAI_SAN_KHAC"] [data-col="tien_giam_gia"],
    #tblLichSuTrinhDuyetViewChiTiet[data-display_doi_tuong="TAI_SAN_KHAC"] [data-col="tien_ktru_tien_bao_hiem"] {
        display: none;
    }
</style>
<div class="modal fade bs-example-modal-lg" id="modalLapPhuongAnSuaChua" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 100%; margin:0px auto;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0 15px;">
                <h5 class="modal-title">Lập phương án bồi thường</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body py-0" style="height: 89vh;">
                <div class="row content_phuong_an mt-1" style="margin:0; padding:0">
                    <div class="col-3 p-0">
                        <div class="card border mb-0">
                            <div class="card-body p-0">
                                <div class="rounded">
                                    <div class="d-flex justify-content-between align-items-center p-1 card-title-bg">
                                        <h5 class="m-0">Danh sách phương án</h5>
                                    </div>
                                    <div class="table-responsive" style="max-height: 95px;">
                                        <table class="table table-hover" id="tblDsPhuongAn" style="border-bottom:1px solid #e8eef3; font-size:10px;">
                                            <tbody id="tblDsPhuongAnBody">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card border mb-0 mt-1">
                            <div class="card-body p-0">
                                <div class="rounded" style="position: relative; height:67vh" id="modalLapPhuongAnSuaChuaBangPAView">

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-9 pr-0">
                        <div class="row">
                            <div class="col-12 pl-2">
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb" id="navPhuongAnNghiepVu" style="margin:unset;margin-bottom:5px; padding-top:3px; padding-bottom:3px;">
                                    </ol>
                                </nav>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb" id="navPhuongAnNghiepVuTaiSan" style="margin:unset;margin-bottom:3px; padding-top:3px; padding-bottom:3px; font-size: 14px;">
                                    </ol>
                                </nav>
                                <form name="frmTinhToanBoiThuongPA" method="post">
                                    <input type="hidden" name="so_id_pa" value="">
                                    <input type="hidden" name="ma_gara" value="">
                                    <input type="text" class="form-control number d-none" readonly name="thue" value="0">
                                    <div class="row">
                                        <div class="col-2 d-none" id="divInputKhauTruPA">
                                            <div class="form-group">
                                                <label for="khau_tru">Khấu trừ</label>
                                                <div class="input-group">
                                                    <select class="form-control custom-select select2" style="width:100%" name="khau_tru"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-2" id="divInputMucMienThuongPA">
                                            <div class="form-group">
                                                <label class="lblBHMienThuong">Khấu trừ (gồm VAT)/vụ</label>
                                                <input type="text" autocomplete="off" onchange="tinhToanPA(this)" class="form-control number" name="mien_thuong_vutt">
                                            </div>
                                        </div>
                                        <div class="col-1 pl-0">
                                            <div class="form-group">
                                                <label>Số vụ TT</label>
                                                <input type="text" autocomplete="off" onchange="tinhToanPA(this)" class="form-control number" name="so_vu">
                                            </div>
                                        </div>
                                        <div class="col-2 pl-0 d-none" id="divInputTLThueMienThuongPA">
                                            <div class="form-group">
                                                <label>TL thuế khấu trừ theo vụ</label>
                                                <select class="form-control custom-select select2" style="width:100%" name="tl_thue">
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-2 d-none" id="">
                                            <div class="form-group">
                                                <label>Tổng miễn thường(gồm VAT)</label>
                                                <input type="text" autocomplete="off" readonly="readonly" class="form-control number" name="mien_thuong" onchange="tinhToanPA()">
                                            </div>
                                        </div>
                                        <div class="col-1 d-none" style="padding:0">
                                            <button type="button" class="btn btn-primary btn-sm" style="margin-top:21px" onclick="tinhToanPA(this)" id="btnTinhToanPA">
                                                <i class="fas fa-calculator-alt mr-1"></i>Tính toán
                                            </button>
                                        </div>
                                        <div class="col-3 d-none">
                                            <label class="mb-1">Đơn vị nhận hồ sơ, tài liệu</label>
                                            <select class="select2 form-control custom-select" name="dvi_nhan" style="width: 100%; height:36px;">
                                                <option value="">Chọn đơn vị nhận hồ sơ, tài liệu</option>
                                            </select>
                                        </div>
                                        <div class="col-3 d-none">
                                            <label class="mb-1">Đơn vị xuất hóa đơn</label>
                                            <select class="select2 form-control custom-select" name="dvi_xuat" style="width: 100%; height:36px;">
                                                <option value="">Chọn đơn vị xuất hóa đơn</option>
                                            </select>
                                        </div>
                                        <div class="col-2 pl-0" id="divInputTLThueMienThuongPA">
                                            <div class="form-group">
                                                <label>TL thuế miễn thường</label>
                                                <select class="form-control custom-select select2" style="width:100%" name="tl_thue">
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="form-group">
                                                <label></label>
                                                <div class="input-group">
                                                    <p class="form-control bg-chua-dg" id="divInputBoiThuongVienDanhGiaHoSoBoiThuong" style="padding-top: 7px;"></p>
                                                    <div class="input-group-append">
                                                        <label class="input-group-text px-2">
                                                            <a href="#" onclick="xemThongTinBoiThuongVienDanhGiaHoSoBoiThuong(this)" title="Đánh giá của bồi thường viên">
                                                                <i class="fas fa-info-circle mr-2"></i>
                                                                Đánh giá
                                                            </a>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            @*Vật chất xe*@
                            <div id="divPhuongAnVCX" class="col-12 divPhuongAnDanhGiaItem pl-2">
                                <div class="table-responsive" style="max-height: 75vh;">
                                    <table id="tablePhuongAnCT" class="table table-bordered" style="width: 160%">
                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky;top:0;">
                                            <tr class="text-center uppercase">
                                                <th style="width:30px">
                                                   
                                                </th>
                                                <th>
                                                    Hạng mục
                                                </th>
                                                <th style="width:97px">Tiền V.tư</th>
                                                <th style="width:90px">Tiền N.công</th>
                                                <th style="width:90px">Tiền sơn</th>
                                                <th style="width:100px" class="tblPhuongAnCTGiamGia_T">Giảm giá</th>
                                                <th style="width:110px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkKhauHaoPAVCX" checked="">
                                                        <label class="custom-control-label" for="chkKhauHaoPAVCX">% Khấu hao</label>
                                                    </div>
                                                </th>
                                                <th style="width:125px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkGiamTruLoiPAVCX" checked="">
                                                        <label class="custom-control-label" for="chkGiamTruLoiPAVCX">% G.trừ lỗi</label>
                                                    </div>
                                                </th>
                                                <th style="width:125px"><a onclick="nhapGiamTruQTBH()" style="cursor:pointer; text-decoration: underline;">G.trừ theo QTBH</a></th>
                                                <th style="width:110px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkBaoHiemPAVCX" checked="">
                                                        <label class="custom-control-label" for="chkBaoHiemPAVCX">% T.Nhiệm</label>
                                                    </div>
                                                </th>
                                                <th style="width:97px" class="tblPhuongAnCTGiamGia_S"><a onclick="nhapGiamGiaPA()" style="cursor:pointer; text-decoration: underline;">Giảm giá</a></th>
                                                <th style="width:110px"><a onclick="nhapKhauTruPA()" style="cursor:pointer; text-decoration: underline;">Khấu trừ(ĐKBS)</a></th>
                                                <th style="width:110px"><a style="cursor:pointer; text-decoration: underline;">Đối trừ thanh lý</a></th>
                                                <th style="width:110px"><a style="cursor:pointer; text-decoration: underline;">Đối trừ thu đòi</a></th>
                                                <th style="width:80px"><a onclick="nhapThuePA()" style="cursor:pointer; text-decoration: underline;">Thuế</a></th>
                                                <th style="width:155px; vertical-align: middle;">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkNguyenNhanGiamTruPAVCX" checked="">
                                                        <label class="custom-control-label" for="chkNguyenNhanGiamTruPAVCX">Nguyên nhân G.Trừ</label>
                                                    </div>
                                                </th>
                                                <th style="width:70px; vertical-align: middle;">Ghi chú</th>
                                            </tr>
                                        </thead>
                                        <tbody id="tblPhuongAnCT">
                                        </tbody>
                                        <tfoot class="card-title-bg" style="position: sticky; bottom: 0; background-color: #f8f9fa;">
                                            <tr class="text-left card-title-bg">
                                                <td class="font-weight-bold" colspan="2">
                                                    Tổng cộng
                                                </td>
                                                <td class="text-right font-weight-bold tblTinhToanPAVCXTienDuyetGia vtu"></td>
                                                <td class="text-right font-weight-bold tblTinhToanPAVCXTienDuyetGia nhan_cong"></td>
                                                <td class="text-right font-weight-bold tblTinhToanPAVCXTienDuyetGia khac"></td>
                                                <td class="text-right font-weight-bold tblPhuongAnCTGiamGia_T">
                                                    <span class="tblTinhToanPAVCXTienGiamGia_T">0</span>
                                                </td>
                                                <td class="ttpt_khau_hao text-right font-weight-bold tblTinhToanPAVCXTienKhauHao"></td>
                                                <td class="ttpt_giam_tru_loi text-right font-weight-bold tblTinhToanPAVCXTienGiamTruLoi"></td>
                                                <td class="ttpt_giam_tru text-right font-weight-bold tblTinhToanPAVCXTienGiamTru"></td>
                                                <td class="ttpt_bao_hiem text-right font-weight-bold tblTinhToanPAVCXTienBaoHiem"></td>
                                                <td class="ttpt_giam_gia text-right font-weight-bold tblPhuongAnCTGiamGia_S">
                                                    <span class="tblTinhToanPAVCXTienGiamGia">0</span>
                                                </td>
                                                <td class="ttpt_khau_tru text-right font-weight-bold">
                                                    <span class="tblTinhToanPAVCXTienKhauTru">0</span>
                                                </td>
                                                <td class="text-right font-weight-bold tblTinhToanPAVCXTienDTruThanhLy"></td>
                                                <td class="text-right font-weight-bold tblTinhToanPAVCXTienDTruNTBa"></td>
                                                <td class="text-right font-weight-bold d-none"><a href="#" class="tblTinhToanPAVCXThue" onclick="nhapThuePA()">0</a></td>
                                                <td class="text-center"></td>
                                                <td class="text-center"></td>
                                                <td class="text-center"></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            @*TNDS về tài sản, hang hoa*@
                            <div class="col-12 divPhuongAnDanhGiaItem pl-2" id="divPhuongAnTNDSTaiSan">
                                <div class="table-responsive" style="max-height: 75vh;">
                                    <table style="width:110%" id="tableChiTietPhuongAnTNDS_TAI_SAN" data-nhom="TNDS_TAI_SAN" class="table table-bordered fixed-header tableChiTietTonThat">
                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky;top:0;">
                                            <tr class="text-center uppercase">
                                                <th style="width:30px">

                                                </th>
                                                <th>Tên hạng mục chi tiết</th>
                                                <th style="width: 110px">Tiền vật tư</th>
                                                <th style="width: 110px">Tiền nhân công</th>
                                                <th style="width: 110px">Tiền sơn</th>
                                                <th style="width: 110px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkKhauHaoPATNDS_TAISAN" checked="">
                                                        <label class="custom-control-label" for="chkKhauHaoPATNDS_TAISAN">% Khấu hao</label>
                                                    </div>
                                                </th>
                                                <th style="width:125px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkGiamTruLoiPATNDS_TAISAN" checked="">
                                                        <label class="custom-control-label" for="chkGiamTruLoiPATNDS_TAISAN">% G.trừ lỗi</label>
                                                    </div>
                                                </th>
                                                <th style="width:155px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkGiamTruPATNDS_TAISAN" checked="">
                                                        <label class="custom-control-label" for="chkGiamTruPATNDS_TAISAN">% G.trừ theo QTBH</label>
                                                    </div>
                                                </th>
                                                <th style="width:110px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkBaoHiemPATNDS_TAISAN" checked="">
                                                        <label class="custom-control-label" for="chkBaoHiemPATNDS_TAISAN">% T.Nhiệm</label>
                                                    </div>
                                                </th>
                                                <th style="width:90px"><a onclick="nhapThueTaiSanPA()" style="cursor:pointer; text-decoration: underline;">Thuế</a></th>
                                                <th style="width:120px">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" name="chkGroup" id="chkAllNguyenNhanPATNDS_TAISAN" checked="">
                                                        <label class="custom-control-label" for="chkAllNguyenNhanPATNDS_TAISAN">Nguyên nhân</label>
                                                    </div>
                                                </th>
                                                <th style="width: 70px">Ghi chú</th>
                                            </tr>
                                        </thead>
                                        <tbody id="modalChiTietPhuongAnTNDS_TAI_SAN">
                                        </tbody>
                                        <tfoot class="card-title-bg" style="position: sticky; bottom: 0; background-color: #f8f9fa;">
                                            <tr class="text-center card-title-bg">
                                                <td class="font-weight-bold" colspan=2>
                                                    Tổng cộng:
                                                </td>
                                                <td colspan="3" class="font-weight-bold text-center">
                                                    <span id="tblTinhToanPATNDS_TAISANTienDuyetGia">0</span>
                                                </td>
                                                <td class="ttpt_khau_hao text-right font-weight-bold">
                                                    <a href="#" id="tblTinhToanPATNDS_TAISANTienKhauHao">0</a>
                                                </td>
                                                <td class="ttpt_giam_tru text-right font-weight-bold">
                                                    <a href="#" id="tblTinhToanPATNDS_TAISANTienGiamTruLoi">0</a>
                                                </td>
                                                <td class="ttpt_giam_tru text-right font-weight-bold">
                                                    <a href="#" id="tblTinhToanPATNDS_TAISANTienGiamTru">0</a>
                                                </td>
                                                <td class="ttpt_trach_nhiem text-right font-weight-bold">
                                                    <a href="#" id="tblTinhToanPATNDS_TAISANTienTrachNhiem">0</a>
                                                </td>
                                                
                                                <td class="ttpt_thue text-right font-weight-bold d-none">
                                                    <a href="#" id="tblTinhToanPATNDS_TAISANTienThue">0</a>
                                                </td>
                                                <td class="text-center"></td>
                                                <td class="text-center"></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display: block;padding: 0.25rem !important;">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnTinhToanCPKhac">
                    <i class="fas fa-edit mr-2"></i> Tính toán chi phí cẩu/kéo/khác
                </button>
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnXuatExcelPhuongAn">
                    <i class="fas fa-file-excel" mr-2"></i> Xuất excel phương án
                </button>
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnXemDanhSachLanTrinhView">
                    <i class="fas fa-list-ol mr-2"></i> Quá trình phê duyệt
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuPASC">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuPASC_NV">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuPASCTaiSan">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnTrinhPABT">
                    <i class="fas fa-share-square mr-2"></i>  Trình phương án kèm bồi thường
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnTrinhPABaoLanh">
                    <i class="fas fa-share-square mr-2"></i> Trình PASC kèm bảo lãnh
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnTrinhBaoLanh">
                    <i class="fas fa-share-square mr-2"></i> Lưu đánh giá & Trình bảo lãnh
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnTrinhPA">
                    <i class="fas fa-share-square mr-2"></i> Trình PASC
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalGiamGiaPA" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:50%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="">Thông tin chi tiết giảm giá</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color: #54667a0a; padding-top:5px;">
                <form name="frmModalGiamGiaPA" method="post">
                    <div class="row">
                        <div class="col-12" id="frmModalGiamGiaPA_lh_giam_gia">
                            <div class="form-group">
                                <label class="_required">Loại hình giảm giá</label>
                                <select class="form-control custom-select select2" style="width:100%" name="lh_giam_gia">
                                    <option value="BH">Chỉ giảm giá cho nhà bảo hiểm</option>
                                    <option value="BHKH">Giảm giá chung cho nhà bảo hiểm và khách hàng (sau tính toán)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6 d-none">
                            <div class="form-group" id="frmModalGiamGiaPA_lh_tt_giam_gia">
                                <label class="_required">Loại hình tính toán</label>
                                <select class="form-control custom-select select2" style="width:100%" name="lh_tt_giam_gia">
                                    <option value="T">Tiền giảm giá chia đều cho các bên</option>
                                    <option value="S">Tiền giảm giá theo tỷ lệ % trách nhiệm</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive" style="max-height:495px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th rowspan="2" style="vertical-align: middle;">Hạng mục</th>
                                <th colspan="3">Tỷ lệ giảm(%)</th>
                            </tr>
                            <tr class="text-center uppercase">
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_giam_gia_vtu_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_giam_gia_vtu_pa">Vật tư</label>
                                    </div>
                                </th>
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_giam_gia_nhan_cong_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_giam_gia_nhan_cong_pa">Nhân công</label>
                                    </div>
                                </th>
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_giam_gia_khac_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_giam_gia_khac_pa">Sơn</label>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tblHangMucGiamGiaPA">
                        </tbody>
                        <tfoot>
                            <tr class="text-left card-title-bg">
                                <td class="font-weight-bold">Tổng cộng:</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm float-right wd-85" id="btnLuuGiamGiaPA">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnLuuDongGiamGiaPA">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalKhauTruPA" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:45%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="">Thông tin chi tiết khấu trừ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color: #54667a0a; padding-top:5px;">
                <form name="frmKhauTruPA" method="post">
                    <div class="row">
                        <div class="col col-8">
                            <div class="form-group">
                                <label class="_required">Vụ tổn thất</label>
                                <select class="select2 form-control custom-select" name="vu_tt" style="width: 100%"></select>
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required">Số tiền tối thiểu</label>
                                <input type="text" class="form-control number" autocomplete="off" name="tien_ktru_tien_bh" placeholder="Số tiền khấu trừ tối thiểu">
                            </div>
                        </div>
                    </div>
                </form>
                <div class="table-responsive" style="max-height:495px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th>Hạng mục</th>
                                <th style="width:200px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_ktru_tien_bh_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_ktru_tien_bh_pa">TL giảm(%)</label>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tblKhauTruPA">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuKhauTruPA">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnLuuDongKhauTruPA">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThuePA" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:50%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="">Thông tin chi tiết thuế</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color: #54667a0a; padding-top:5px;">
                <div class="table-responsive" style="max-height:495px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th rowspan="2" style="vertical-align: middle;">Hạng mục</th>
                                <th colspan="3">Tỷ lệ thuế(%)</th>
                            </tr>
                            <tr class="text-center uppercase">
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_thue_vtu_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_thue_vtu_pa">Vật tư</label>
                                    </div>
                                </th>
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_thue_nhan_cong_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_thue_nhan_cong_pa">Nhân công</label>
                                    </div>
                                </th>
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_thue_khac_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_thue_khac_pa">Sơn</label>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tblHangMucThuePA">
                        </tbody>
                        <tfoot>
                            <tr class="text-left card-title-bg">
                                <td class="font-weight-bold">Tổng cộng:</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuThuePA">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnLuuDongThuePA">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

@*Chi phí khác*@
<script type="text/html" id="tblTinhToanCPKhacTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="tblTinhToanCPKhacItem">
        <td class="text-center">
            <input type="hidden" class="floating-input number" data-field="stt" value="<%- item.stt %>" />
            <input type="hidden" class="floating-input" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="so_id" value="<%- item.so_id %>" />
            <input type="hidden" class="floating-input" data-field="ma_chi_phi" value="<%- item.ma_chi_phi %>" />
            <input type="hidden" class="floating-input" data-field="ten" value="<%- item.ten %>" />
            <input type="hidden" class="floating-input" data-field="ten_doi_tuong" value="<%- item.ten_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" class="floating-input number" data-field="so_tien" value="<%- item.so_tien %>" />
            <input type="hidden" class="floating-input number" data-field="tien_bao_gia" value="<%- item.tien_bao_gia %>" />
            <%- item.stt %>
        </td>
        <td class="text-center"><%- item.ten_doi_tuong %></td>
        <td><%- item.ten %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_bao_gia) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien) %></td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru_loi" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru_loi) %>" onchange="tinhToanCPKhac(this)" />
        </td>

        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru) %>" onchange="tinhToanCPKhac(this)" />
        </td>

        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_bao_hiem) %>" onchange="tinhToanCPKhac(this)" />
        </td>

        <td class="text-right wd-110-f modalTinhToanCPKhacThue">
            <input type="text" data-field="tl_thue" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.tl_thue) %>" onchange="tinhToanCPKhac(this)" />
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_bh) %></td>
        <td class="text-right modalTinhToanCPKhacThue"><%- ESUtil.formatMoney(item.tien_thue) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tong_tien) %></td>
    </tr>
    <% })} %>

    <% if(data.length < 6){
    for(var i = 0; i < 6 - data.length;i++ ){
    %>
    <tr style="height:35px">
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td class="modalTinhToanCPKhacThue"></td>
        <td></td>
        <td class="modalTinhToanCPKhacThue"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Phương án VCX*@
<script type="text/html" id="tblPhuongAnCT_template">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <% var tong_cong = item.tien_vtu + item.tien_nhan_cong %>
    <tr class="tblPhuongAnCTItem">
        <td class="text-center">
            <% if(item.trang_thai != undefined && item.trang_thai != null && item.trang_thai == 'C'){%>
            <i class="fas fa-plus hm_them_moi" style="color:#96BF49"></i>
            <%}else{%>
            <i class="fas fa-check-circle text-success"></i>
            <%}%>
        </td>
        <td>
            <input type="hidden" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" data-field="bt_bao_gia" value="<%- item.bt_bao_gia %>" />
            <input type="hidden" data-field="trang_thai" value="<%- item.trang_thai %>" />
            <input type="hidden" data-field="tien_dx" name="tien_dx" value="<%- item.tien_dx %>" />
            <input type="hidden" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <input type="hidden" data-field="tl_giam_gia_vtu" value="<%- item.tl_giam_gia_vtu %>" />
            <input type="hidden" data-field="tl_giam_gia_nhan_cong" value="<%- item.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tl_giam_gia_khac" value="<%- item.tl_giam_gia_khac %>" />
            <input type="hidden" data-field="tl_ktru_tien_bh" value="<%- item.tl_ktru_tien_bh %>" />
            <input type="hidden" data-field="tl_thue_vtu" value="<%- item.tl_thue_vtu %>" />
            <input type="hidden" data-field="tl_thue_nhan_cong" value="<%- item.tl_thue_nhan_cong %>" />
            <input type="hidden" data-field="tl_thue_khac" value="<%- item.tl_thue_khac %>" />

            <input type="hidden" data-field="tien_giam_gia_vtu" value="<%- item.tien_giam_gia_vtu %>" />
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" value="<%- item.tien_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tien_giam_gia_khac" value="<%- item.tien_giam_gia_khac %>" />

            <input type="hidden" data-field="lh_giam_gia" value="<%- item.lh_giam_gia %>" />
            <input type="hidden" data-field="lh_tt_giam_gia" value="<%- item.lh_tt_giam_gia %>" />

            <input type="hidden" data-field="hang_muc" name="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" data-field="bt_gara" name="bt_gara" value="<%- item.bt_gara %>" />

            <a class="" data-field="ten_hang_muc" data-val="<%- item.ten_hang_muc %>"><%- item.ten_hang_muc %></a>
            <%if(item.dgrr != undefined && item.dgrr != null && item.dgrr != "" && item.dgrr == "C"){%>
            <a href="#" class="text-warning" onclick="onXemHangMucDGRR('<%- item.hang_muc %>')"><i class="fas fa-exclamation-triangle ml-2"></i><span class="ml-1" style="font-size:10px; font-style:italic;">Có tổn thất cấp đơn</span> </a>
            <%}%>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_vtu_dx" data-val="<%- item.tien_vtu_dx %>"><%- ESUtil.formatMoney(item.tien_vtu_dx) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_nhan_cong_dx" data-val="<%- item.tien_nhan_cong_dx %>"><%- ESUtil.formatMoney(item.tien_nhan_cong_dx) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_khac_dx" data-val="<%- item.tien_khac_dx %>"><%- ESUtil.formatMoney(item.tien_khac_dx) %></a>
        </td>
        <td class="text-right tblPhuongAnCTGiamGia_T">
            <a class="combobox"><%- ESUtil.formatMoney(item.tien_giam_gia) %></a>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_khau_hao" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_khau_hao) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru_loi" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru_loi) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="hidden" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru) %>" />
            <%- ESUtil.formatMoney(item.tien_giam_tru) %>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_bao_hiem) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right wd-110-f tblPhuongAnCTGiamGia_S">
            <a class="combobox" data-field="tien_giam_gia" data-val="<%- item.tien_giam_gia %>"><%- ESUtil.formatMoney(item.tien_giam_gia) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_ktru_tien_bh" data-val="<%- item.tien_ktru_tien_bh %>"><%- ESUtil.formatMoney(item.tien_ktru_tien_bh) %></a>
        </td>
        <td class="text-right">
            <a class="combobox"><%- ESUtil.formatMoney(item.tien_dtru_thanh_ly) %></a>
        </td>
        <td class="text-right">
            <a class="combobox"><%- ESUtil.formatMoney(item.tien_dtru_ntba) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_thue" data-val="<%- item.tien_thue %>"><%- ESUtil.formatMoney(item.tien_thue) %></a>
        </td>
        <td class="text-center">
            <% if(item.nguyen_nhan != null){ %>
            <a href="#" data-field="nguyen_nhan" onclick="showNguyenNhanPA(this)" data-val="<%- item.nguyen_nhan %>" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhanPA(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>

    <% if(data.length < 13){
    for(var i = 0; i < 13 - data.length;i++ ){
    %>
    <tr style="height:35px">
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Phương án hàng hóa trên xe*@
<script type="text/html" id="modalChiTietPhuongAnHANGHOATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>
            <input type="hidden" class="floating-input number" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input number" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" data-field="dia_chi" value="<%- item.dia_chi %>" />
            <input type="hidden" data-field="muc_do" value="<%- item.muc_do %>" />
            <input type="hidden" class="floating-input number" data-field="so_luong" value="<%- item.so_luong %>" />
            <input type="hidden" data-field="dvi_tinh" value="<%- item.dvi_tinh %>" />
            <input type="hidden" class="floating-input number" data-field="tien_tt" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" data-field="thay_the_sc" value="<%- item.thay_the_sc %>" />
            <input type="hidden" data-field="chinh_hang" value="<%- item.chinh_hang %>" />
            <input type="hidden" data-field="thu_hoi" value="<%- item.thu_hoi %>" />
            <input type="hidden" data-field="hang_muc_ct" value="<%- item.hang_muc_ct %>" />
            <input type="hidden" data-field="gia" value="<%- item.gia %>" />
            <input type="hidden" data-field="so_luong_tt" value="<%- item.so_luong_tt %>" />
            <input type="hidden" data-field="tien_khac_dx_pa" value="<%- item.tien_khac_dx_pa %>" />
            <a class="combobox" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td>
            <input type="text" class="floating-input number" name="tien_thoa_thuan" data-field="tien_thoa_thuan" placeholder="Tiền thỏa thuận" required="" value="<%- ESUtil.formatMoney(item.tien_thoa_thuan) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number" onkeyup="tinhTongTienDXuat(this)" name="tien_vtu_dx_pa" data-field="tien_vtu_dx_pa" placeholder="Tiền đề xuất" required="" value="<%- ESUtil.formatMoney(item.tien_vtu_dx_pa) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number" onkeyup="tinhTongTienDXuat(this)" name="tien_nhan_cong_dx_pa" data-field="tien_nhan_cong_dx_pa" placeholder="Tiền đề xuất" required="" value="<%- ESUtil.formatMoney(item.tien_nhan_cong_dx_pa) %>" />
        </td>
        <td>
            <input type="text" class="floating-input number" disabled="disabled" name="tien_dx_pa" data-field="tien_dx_pa" placeholder="Tiền đề xuất" required="" value="<%- ESUtil.formatMoney(item.tien_dx_pa) %>" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_khau_hao" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_khau_hao) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_bao_hiem) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="tl_thue" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.tl_thue) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 13){
    for(var i = 0; i < 13 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Phương án TNDS Tài sản*@
<script type="text/html" id="modalChiTietPhuongAnTNDS_TAI_SANTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
         <td class="text-center">
            <% if(item.trang_thai != undefined && item.trang_thai != null && item.trang_thai == 'C'){%>
            <i class="fas fa-plus" style="color:#007bff"></i>
            <%}else{%>
            <i class="fas fa-check-circle text-success"></i>
            <%}%>
        </td>
        <td>
            <input type="hidden" class="floating-input number" data-field="gia_duyet" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="tien_khau_hao" value="<%- item.tien_khau_hao %>" />
            <input type="hidden" class="floating-input number" data-field="tien_bao_hiem" value="<%- item.tien_bao_hiem %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_tru_loi" value="<%- item.tien_giam_tru_loi %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_tru" value="<%- item.tien_giam_tru %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_gia" value="<%- item.tien_giam_gia %>" />
            <input type="hidden" class="floating-input number" data-field="thue" value="<%- item.thue %>" />
            <input type="hidden" class="floating-input number" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input number" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" class="floating-input" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" class="floating-input number" data-field="so_luong" value="<%- item.so_luong %>" />
            <input type="hidden" class="floating-input number" data-field="tien_tt" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="tl_thue_vtu" value="<%- item.tl_thue_vtu %>" />
            <input type="hidden" class="floating-input number" data-field="tl_thue_nhan_cong" value="<%- item.tl_thue_nhan_cong %>" />
            <input type="hidden" class="floating-input number" data-field="tl_thue_khac" value="<%- item.tl_thue_khac %>" />
            <a class="combobox" data-field="ten_chi_phi" data-val="<%- item.ten_chi_phi %>"><%- item.ten_chi_phi %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_vtu" data-val="<%- item.tien_vtu %>" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_vtu) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_nhan_cong" data-val="<%- item.tien_nhan_cong %>" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_nhan_cong) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_khac" data-val="<%- item.tien_khac %>" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_khac) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_khau_hao" data-val="<%- item.pt_khau_hao %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_khau_hao) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_giam_tru_loi" data-val="<%- item.pt_giam_tru_loi %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru_loi) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_giam_tru" data-val="<%- item.pt_giam_tru %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_bao_hiem" data-val="<%- item.pt_bao_hiem %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_bao_hiem) %>" onchange="tinhToanPA(this)" />
        </td>
        <td class="text-right d-none">
            <a class="combobox" data-field="tien_thue" data-val="<%- item.thue %>"><%- ESUtil.formatMoney(item.thue) %></a>
        </td>
        <td class="text-center">
            <% if(item.nguyen_nhan != null && item.nguyen_nhan != '' && item.nguyen_nhan != undefined){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- item.nguyen_nhan %>" onclick="showNguyenNhanPA(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân giảm"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhanPA(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân giảm"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 13){
    for(var i = 0; i < 13 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Danh sách phương án *@
<script type="text/html" id="tblDsPhuongAnBodyTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr style="cursor: pointer; height:47px;">
        <td class="text-center d-none" style="width: 43px; vertical-align: middle;">
            <% if(item.chon_pa_trinh == 'C'){ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" name="phuong_an_id" id="phuong_an_<%- item.so_id_pa %>" class="custom-control-input dspa" data-val="<%- item.so_id_pa %>" onchange="chonPhuongAn(this)">
                <label class="custom-control-label" style="cursor:pointer;" for="phuong_an_<%- item.so_id_pa %>">&nbsp;</label>
            </div>
            <% }else if(item.chon_pa_trinh == 'D'){ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" name="phuong_an_id" id="phuong_an_<%- item.so_id_pa %>" class="custom-control-input dspa" data-val="<%- item.so_id_pa %>" onchange="chonPhuongAn(this)" checked>
                <label class="custom-control-label" style="cursor:pointer;" for="phuong_an_<%- item.so_id_pa %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td id="pa_<%- item.so_id_pa %>" class="dspa" data-so-id-pa="<%- item.so_id_pa %>" data-so_id_doi_tuong="<%- item.so_id_doi_tuong %>" data-bao-gia="<%- item.bt_bao_gia %>" style="font-weight:bold; text-align:left; vertical-align: middle;" onclick="xemChiTietPhuongAnGara('<%- item.so_id_pa %>')">
            <%- item.ten_pa %>
        </td>
        <td class="text-right" style="width: 30px">
        </td>
    </tr>
    <%})} %>
    <% if(data.length < 2){
    for(var i = 0; i < 2 - data.length;i++ ){
    %>
    <tr>
        <td style="height:47px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách hạng mục giảm giá"*@
<script type="text/html" id="tblHangMucGiamGiaPATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblHangMucGiamGiaItem">
        <td>
            <a class="d-none" data-field="hang_muc" data-val="<%- item.hang_muc %>"></a>
            <a class="d-none" data-field="bt_bao_gia" data-val="<%- item.bt_bao_gia %>"></a>
            <a data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_giam_gia_vtu" onchange="tinhGiamGiaPA('<%- item.hang_muc %>', 'tl_giam_gia_vtu')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_giam_gia_vtu) %>">
            <input type="hidden" data-field="tien_giam_gia_vtu" onchange="tinhGiamGiaPA('<%- item.hang_muc %>', 'tien_giam_gia_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_giam_gia_nhan_cong" onchange="tinhGiamGiaPA('<%- item.hang_muc %>', 'tl_giam_gia_nhan_cong')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_giam_gia_nhan_cong) %>">
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" onchange="tinhGiamGiaPA('<%- item.hang_muc %>', 'tien_giam_gia_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_giam_gia_khac" onchange="tinhGiamGiaPA('<%- item.hang_muc %>', 'tl_giam_gia_khac')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_giam_gia_khac) %>">
            <input type="hidden" data-field="tien_giam_gia_khac" onchange="tinhGiamGiaPA('<%- item.hang_muc %>', 'tien_giam_gia_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_khac) %>">
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách hạng mục khấu trừ"*@
<script type="text/html" id="tblKhauTruPATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblKhauTruItem">
        <td>
            <a class="d-none" data-field="hang_muc" data-val="<%- item.hang_muc %>"></a>
            <a class="d-none" data-field="bt_bao_gia" data-val="<%- item.bt_bao_gia %>"></a>
            <a data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_ktru_tien_bh" onchange="tinhKhauTruPA('<%- item.hang_muc %>', 'tl_ktru_tien_bh')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_ktru_tien_bh) %>">
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 5){
    for(var i = 0; i < 5 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách hạng mục thuế "*@
<script type="text/html" id="tblHangMucThuePATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblHangMucThueItem">
        <td>
            <a class="d-none" data-field="hang_muc" data-val="<%- item.hang_muc %>"></a>
            <a class="d-none" data-field="bt_bao_gia" data-val="<%- item.bt_bao_gia %>"></a>
            <a data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_vtu" onchange="tinhThuePA('<%- item.hang_muc %>', 'tl_thue_vtu')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_vtu) %>">
            <input type="hidden" data-field="tien_thue_vtu" onchange="tinhThuePA('<%- item.hang_muc %>', 'tien_thue_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_vtu) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_nhan_cong" onchange="tinhThuePA('<%- item.hang_muc %>', 'tl_thue_nhan_cong')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_nhan_cong) %>">
            <input type="hidden" data-field="tien_thue_nhan_cong" onchange="tinhThuePA('<%- item.hang_muc %>', 'tien_thue_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_nhan_cong) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_khac" onchange="tinhThuePA('<%- item.hang_muc %>', 'tl_thue_khac')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_khac) %>">
            <input type="hidden" data-field="tien_thue_khac" onchange="tinhThuePA('<%- item.hang_muc %>', 'tien_thue_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_khac) %>">
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Xem bảng tính toán PA*@
<script type="text/html" id="modalLapPhuongAnSuaChuaBangPAViewTemplate">
    <div class="d-flex justify-content-between align-items-center p-1 card-title-bg border-bottom">
        <h5 class="m-0">Thông tin tính toán</h5>
    </div>
    <div class="table-responsive" id="tableLapPhuongAnSuaChuaView" style="height: 55vh;">
        <table class="table table-bordered fixed-header">
            <% if(vcx != null){%>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh VCXXE">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ VẬT CHẤT XE</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN DUYỆT GIÁ (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienDuyetGia text-danger"><%- ESUtil.formatMoney(vcx.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">II</td>
                    <td colspan="3" class="font-weight-bold">KHẤU HAO, GIẢM TRỪ BỒI THƯỜNG, GIẢM GIÁ</td>
                </tr>
                <% if(vcx.lh_tt_giam_gia =="T"){%>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">1</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(vcx.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">2</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(vcx.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">3</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(vcx.tien_giam_gia_khac) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">4</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(vcx.tien_khau_hao) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">5</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(vcx.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">6</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(vcx.tien_bao_hiem) %></td>
                </tr>
                <%} %>
                <% if(vcx.lh_tt_giam_gia =="S"){%>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">1</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(vcx.tien_khau_hao) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">2</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(vcx.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">3</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(vcx.tien_bao_hiem) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">4</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(vcx.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">5</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(vcx.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">6</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(vcx.tien_giam_gia_khac) %></td>
                </tr>
                <%} %>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, GIẢM GIÁ</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauHaoGiamTruGiamGia text-danger"><%- ESUtil.formatMoney(vcx.tong_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">III</td>
                    <td colspan="3" class="font-weight-bold">KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Khấu trừ theo % số tiền bồi thường</td>
                    <td class="text-right tinhToanNVPATienKhauTruPTBoiThuong"><%- ESUtil.formatMoney(vcx.tien_ktru_tien_bh) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right tinhToanNVPAMienThuong"><%- ESUtil.formatMoney(vcx.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauTru text-danger"><%- ESUtil.formatMoney(vcx.tong_khau_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">IV</td>
                    <td colspan="3" class="font-weight-bold">ĐỐI TRỪ BẢO HIỂM</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Đối trừ thanh lý vật tư</td>
                    <td class="text-right"><%- ESUtil.formatMoney(vcx.tien_dtru_thanh_ly) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Đối trừ thu đòi người thứ 3</td>
                    <td class="text-right"><%- ESUtil.formatMoney(vcx.tien_dtru_ntba) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG ĐỐI TRỪ BẢO HIỂM</td>
                    <td class="text-right font-weight-bold text-danger"><%- ESUtil.formatMoney(vcx.tong_doi_tru) %></td>
                </tr>
            </tbody>
            <%}%>
            @*Tài sản là khác*@
            <% _.forEach(tnds_tsan_khac, function(item,index) { %>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh TNDSTAI_SAN">
                <tr>
                    <td colspan="4"><span class="text-danger font-weight-bold"><%- item.ten_doi_tuong %></span></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN DUYỆT GIÁ (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienDuyetGia text-danger"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">II</td>
                    <td colspan="3" class="font-weight-bold">KHẤU HAO, GIẢM TRỪ BỒI THƯỜNG, GIẢM GIÁ</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_gia_khac) %></td>
                </tr>
                <tr>
                    <td class="text-center">4</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_khau_hao) %></td>
                </tr>
                <tr>
                    <td class="text-center">5</td>
                    <td>Giảm trừ lỗi</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_tru_loi) %></td>
                </tr>
                <tr>
                    <td class="text-center">6</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center">7</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, GIẢM GIÁ</td>
                    <td class="text-right font-weight-bold text-danger"><%- ESUtil.formatMoney(item.tong_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">III</td>
                    <td colspan="3" class="font-weight-bold">KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Khấu trừ theo % số tiền bồi thường</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_ktru_tien_bh) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                    <td class="text-right font-weight-bold text-danger"><%- ESUtil.formatMoney(item.tong_khau_tru) %></td>
                </tr>
            </tbody>
            <%})%>
            @*Tài sản là xe*@
            <% _.forEach(tnds_tsan_xe, function(item,index) { %>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh TNDSTAI_SAN">
                <tr>
                    <td colspan="4"><span class="text-danger font-weight-bold"><%- item.ten_doi_tuong %></span></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN DUYỆT GIÁ (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienDuyetGia text-danger"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">II</td>
                    <td colspan="3" class="font-weight-bold">KHẤU HAO, GIẢM TRỪ BỒI THƯỜNG, GIẢM GIÁ</td>
                </tr>
                <% if(item.lh_tt_giam_gia =="T"){%>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">1</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">2</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">3</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(item.tien_giam_gia_khac) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">4</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(item.tien_khau_hao) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">5</td>
                    <td>Giảm trừ lỗi</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTruLoi"><%- ESUtil.formatMoney(item.tien_giam_tru_loi) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">6</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(item.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">7</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(item.tien_bao_hiem) %></td>
                </tr>
                
                <%} %>
                <% if(item.lh_tt_giam_gia =="S"){%>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">1</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(item.tien_khau_hao) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">2</td>
                    <td>Giảm trừ lỗi</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTruLoi"><%- ESUtil.formatMoney(item.tien_giam_tru_loi) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">3</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(item.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">4</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(item.tien_bao_hiem) %></td>
                </tr>
                
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">5</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">6</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">7</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(item.tien_giam_gia_khac) %></td>
                </tr>
                <%} %>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, GIẢM GIÁ</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauHaoGiamTruGiamGia text-danger"><%- ESUtil.formatMoney(item.tong_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">III</td>
                    <td colspan="3" class="font-weight-bold">KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Khấu trừ theo % số tiền bồi thường</td>
                    <td class="text-right tinhToanNVPATienKhauTruPTBoiThuong"><%- ESUtil.formatMoney(item.tien_ktru_tien_bh) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right tinhToanNVPAMienThuong"><%- ESUtil.formatMoney(item.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauTru text-danger"><%- ESUtil.formatMoney(item.tong_khau_tru) %></td>
                </tr>
            </tbody>
            <%})%>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh HHHANG_HOA">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ HÀNG HÓA TRÊN XE</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN TỔN THẤT (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPAHHTienTT text-danger"><%- ESUtil.formatMoney(hhoa.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Số tiền khấu hao</td>
                    <td class="text-right tinhToanNVPAHHTienKhauHao"><%- ESUtil.formatMoney(hhoa.tien_khau_hao) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-right tinhToanNVPAHHTienBaoHiem"><%- ESUtil.formatMoney(hhoa.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Giảm trừ bồi thường</td>
                    <td class="text-right tinhToanNVPAHHTienGiamTru"><%- ESUtil.formatMoney(hhoa.tien_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center">4</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right tinhToanNVPAHHTienMienThuong"><%- ESUtil.formatMoney(hhoa.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, KHẤU TRỪ</td>
                    <td class="text-right font-weight-bold tinhToanNVPAHHTienBoiThuong text-danger"><%- ESUtil.formatMoney(hhoa.tong_giam) %></td>
                </tr>
            </tbody>
            <tbody class="table-bang-gia">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">CHI PHÍ CẨU/KÉO/KHÁC</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">V</td>
                    <td colspan="3" class="font-weight-bold">CHI PHÍ CẨU KÉO (GỒM GIẢM TRỪ NẾU CÓ)</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Chi phí cẩu xe</td>
                    <td class="text-right tinhToanNVPAChiPhiCau"><%- ESUtil.formatMoney(chi_phi.chi_phi_cau) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Chi phí kéo xe</td>
                    <td class="text-right tinhToanNVPAChiPhiKeo"><%- ESUtil.formatMoney(chi_phi.chi_phi_keo) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Chi phí khác</td>
                    <td class="text-right tinhToanNVPAChiPhiKhac"><%- ESUtil.formatMoney(chi_phi.chi_phi_khac) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG CHI PHÍ CẨU KÉO</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongChiPhi text-danger"><%- ESUtil.formatMoney(chi_phi.tong_chi_phi) %></td>
                </tr>
            </tbody>
        </table>
        <table class="table table-bordered fixed-header table-bang-gia" style="height: 40px; position: absolute; bottom: 0px;">
            <tbody>
                <tr class="text-danger">
                    <td colspan="6" class="text-center font-weight-bold">SỐ TIỀN BỒI THƯỜNG (STBT)</td>
                </tr>
                <tr>
                    <td colspan="4" class="font-weight-bold">STBT TRƯỚC THUẾ (I-II-III-IV+V)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienBoiThuongChuaVAT"><%- ESUtil.formatMoney(tong_hop.tong_tien_bt) %></td>
                </tr>
                <tr class="d-none">
                    <td colspan="4" class="font-weight-bold">THUẾ VAT</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongThue"><%- ESUtil.formatMoney(tong_hop.tong_thue) %></td>
                </tr>
                <tr class="d-none">
                    <td colspan="4" class="font-weight-bold">CTYBH CHI TRẢ (BAO GỒM THUẾ)</td>
                    <td class="text-right font-weight-bold text-danger tinhToanNVPATienBoiThuongBaoGomVAT"><%- ESUtil.formatMoney(tong_hop.tong_tien_bt_gom_vat) %></td>
                </tr>
            </tbody>
        </table>
    </div>
</script>

<script type="text/html" id="navPhuongAnNghiepVuTaiSanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.hang_muc %>" data-doi-tuong="<%- item.so_id_doi_tuong %>"><a href="#" onclick="xemChiTietPhuongAnTaiSan('<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')"><%- item.ten %></a></li>
    <%})}%>
</script>

<script type="text/html" id="danh_gia_btv_tab_pa_template">
    <% if(kieu.length > 0){ %>
    <% _.forEach(lhnv, function(item_lhnv, index_lhnv){ %>
    <div class="col-12">
        <hr  class="my-2"/>
        <h6 class="my-2" style="font-weight:bold">
            <%- item_lhnv.ten %>
            <% if(item_lhnv.trang_thai == 'C'){ %>
            - <label style="color:red">Chưa thực hiện đánh giá</label>
            <% }else if(item_lhnv.trang_thai == 'D'){ %>
            - <label style="color:green">Đã thực hiện đánh giá</label>
            <% } %>
        </h6>
    </div>

    <% if(item_lhnv.so_luong == 0){ %>
    <div class="col-12">
        <span>Chưa có dữ liệu cấu hình</span>
    </div>
    <% }else{ %>
    <% _.forEach(kieu, function(item,index) { %>
    <% if(item.kieu == 'CHECKBOX'){ %>
    <% if(item.lhnv == item_lhnv.ma){ %>
    <div class="col-6">
        <a href="#"><%- item.ten_loai %></a>
    </div>
    <% _.forEach(data, function(item1,index1){ %>
    <% if(item1.loai == item.loai && item1.lhnv == item_lhnv.ma){ %>
    <div class="col-3">
        <div class="custom-control custom-checkbox">
            <input type="checkbox" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.loai %>" id="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.ma %>" value="<%- item1.ma %>" class="custom-control-input <%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.loai %> single_checked">
            <label class="custom-control-label" for="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.ma %>" style="cursor:pointer; padding-top:2px"><%- item1.ten %></label>
        </div>
    </div>
    <% } %>
    <% }) %>
    <% } %>
    <% } else if(item.kieu == 'TEXT'){ %>
    <div class="col-6">
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% }else if(item.kieu == 'NUMBER'){ %>
    <div class="col-6">
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <div class="input-group">
                <input type="text" maxlength="15" autocomplete="off" class="form-control number placeholder-left" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } else if(item.kieu == 'TEXTAREA'){ %>
    <% if(item.kieu_loai == 'col12'){ %>
    <div class="col-12">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <a href="#" class="_required"><%- item.ten_loai %></a>
            <textarea class="form-control nhan_xet_btv" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <textarea class="form-control nhan_xet_btv" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% }else if(item.kieu_loai == 'col6'){ %>
    <div class="col-6">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <a href="#" class="_required"><%- item.ten_loai %></a>
            <textarea class="form-control" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <textarea class="form-control" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% } %>
    <% } %>
    <% }) %>
    <% } %>

    <% }) %>
    <% }%>
</script>

<script type="text/html" id="danh_gia_btv_tab_pa_temp_template">
    <% if(kieu.length > 0){ %>
    <% _.forEach(lhnv, function(item_lhnv, index_lhnv){ %>
    <div class="col-12">
        <hr  class="my-2"/>
        <h6 class="my-2" style="font-weight:bold">
            <%- item_lhnv.ten %>
            <% if(item_lhnv.trang_thai == 'C'){ %>
            - <label style="color:red">Chưa thực hiện đánh giá</label>
            <% }else if(item_lhnv.trang_thai == 'D'){ %>
            - <label style="color:green">Đã thực hiện đánh giá</label>
            <% } %>
        </h6>
    </div>

    <% if(item_lhnv.so_luong == 0){ %>
    <div class="col-12">
        <span>Chưa có dữ liệu cấu hình</span>
    </div>
    <% }else{ %>
    <% _.forEach(kieu, function(item,index) { %>
    <% if(item.kieu == 'CHECKBOX'){ %>
    <% if(item.lhnv == item_lhnv.ma){ %>
    <div class="col-6">
        <a href="#"><%- item.ten_loai %></a>
    </div>
    <% _.forEach(data, function(item1,index1){ %>
    <% if(item1.loai == item.loai && item1.lhnv == item_lhnv.ma){ %>
    <div class="col-3">
        <div class="custom-control custom-checkbox">
            <input type="checkbox" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.loai %>" id="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.ma %>" value="<%- item1.ma %>" class="custom-control-input <%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.loai %> single_checked">
            <label class="custom-control-label" for="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.ma %>" style="cursor:pointer; padding-top:2px"><%- item1.ten %></label>
        </div>
    </div>
    <% } %>
    <% }) %>
    <% } %>
    <% } else if(item.kieu == 'TEXT'){ %>
    <div class="col-6">
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% }else if(item.kieu == 'NUMBER'){ %>
    <div class="col-6">
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <div class="input-group">
                <input type="text" maxlength="15" autocomplete="off" class="form-control number placeholder-left" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } else if(item.kieu == 'TEXTAREA'){ %>
    <% if(item.kieu_loai == 'col12'){ %>
    <div class="col-12">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <a href="#" class="_required"><%- item.ten_loai %></a>
            <textarea class="form-control nhan_xet_btv" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <textarea class="form-control nhan_xet_btv" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% }else if(item.kieu_loai == 'col6'){ %>
    <div class="col-6">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <a href="#" class="_required"><%- item.ten_loai %></a>
            <textarea class="form-control" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <textarea class="form-control" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% } %>
    <% } %>
    <% }) %>
    <% } %>

    <% }) %>
    <% }%>
</script>

<div id="modalBoiThuongVienDanhGiaHoSoBoiThuong" class="modal fade" tabindex="-1" data-backdrop="false" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 9999999;width:1000px;top: 0%; left: 18%;">
    <div class="modal-dialog modal-lg" style="width:65%;max-width:unset">
        <div class="modal-content" style="border:3px solid var(--escs-main-theme-color);">
            <div class="modal-header py-1" style="background-color:var(--escs-main-theme-color); cursor:pointer;border:unset;">
                <h5 class="modal-title" style="color:#fff">Đánh giá của bồi thường viên</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="max-height:500px; overflow:auto">
                <form name="frmBoiThuongVienDanhGiaHoSoBoiThuong" id="frmBoiThuongVienDanhGiaHoSoBoiThuong" method="post">
                    <div class="row mb-2">
                        <div class="col-6">
                            <label class="_required font-weight-bold mb-1">Đơn vị nhận hồ sơ, tài liệu</label>
                            <select class="select2 form-control custom-select" name="dvi_nhan" required style="width: 100%; height:36px;">
                                <option value="">Chọn đơn vị</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="_required font-weight-bold mb-1">Xuất hóa đơn cho đơn vị</label>
                            <select class="select2 form-control custom-select" name="dvi_xuat" required style="width: 100%; height:36px;">
                                <option value="">Chọn đơn vị</option>
                            </select>
                        </div>
                    </div>
                    <input type="hidden" name="pt_gt_tham_gia_bh" value="" />
                    <input type="hidden" name="pt_che_tai" value="" />
                    <div class="row mt-2 frmBoiThuongVienDanhGiaHoSoBoiThuongBTV">
                        <div class="col-12">
                            <h6 class="font-weight-bold">Đánh giá, nhận xét cán bộ BTV</h6>
                        </div>
                    </div>
                    <div class="row frmBoiThuongVienDanhGiaHoSoBoiThuongBTV" id="danh_gia_btv_tab_pa"></div>
                    <div class="row frmBoiThuongVienDanhGiaHoSoBoiThuongBTV">
                        <div class="col-12">
                            @{
                                string val = newGuid();

                                <label class="my-1 font-weight-bold _required">Nhận xét và đánh giá lại diễn biến, nguyên nhân tai nạn <span control-counter-label="@val"></span></label>
                                <textarea maxlength="2000" control-counter-input="@val" class="form-control" name="danh_gia" required rows="4" placeholder="Nhận xét đánh giá của bồi thường viên"></textarea>
                            }
                        </div>
                    </div>
                    <div class="row frmBoiThuongVienDanhGiaHoSoBoiThuongBTV">
                        <div class="col-12">
                            @{
                                string val1 = newGuid();

                                <label class="my-1 font-weight-bold _required">Đề xuất của bồi thường viên <span control-counter-label="@val1"></span></label>
                                <textarea maxlength="2000" control-counter-input="@val1" class="form-control" name="de_xuat" required rows="4" placeholder="Đề xuất giải quyết"></textarea>
                            }
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-130" id="btnLuuDongBoiThuongVienDanhGiaHSBT">
                    <i class="fa fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal" id="btnDongBoiThuongVienDanhGiaHSBT">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalBoiThuongVienDanhGiaHoSoBoiThuongTemp" class="modal fade" tabindex="-1" data-backdrop="false" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 9999999;width:1000px;top: 0%; left: 18%;">
    <div class="modal-dialog modal-lg" style="width:65%;max-width:unset">
        <div class="modal-content" style="border:3px solid var(--escs-main-theme-color);">
            <div class="modal-header py-1" style="background-color:var(--escs-main-theme-color); cursor:pointer;border:unset;">
                <h5 class="modal-title" style="color:#fff">Đánh giá của bồi thường viên</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="max-height:500px; overflow:auto">
                <form name="frmBoiThuongVienDanhGiaHoSoBoiThuongTemp" id="frmBoiThuongVienDanhGiaHoSoBoiThuongTemp" method="post">
                    <div class="row mb-2">
                        <div class="col-12">
                            <label class="_required font-weight-bold mb-1">Chọn đơn vị nhận hóa đơn</label>
                            <select class="select2 form-control custom-select" name="dvi_nhan" required style="width: 100%; height:36px;">
                                <option value="">Chọn đơn vị nhận hóa đơn</option>
                            </select>
                        </div>
                    </div>
                    <input type="hidden" name="pt_gt_tham_gia_bh" value="" />
                    <input type="hidden" name="pt_che_tai" value="" />
                    <div class="row mt-2">
                        <div class="col-12">
                            <h6 class="font-weight-bold">Đánh giá, nhận xét cán bộ BTV</h6>
                        </div>
                    </div>
                    <div class="row" id="danh_gia_btv_tab_pa_temp"></div>
                    <div class="row">
                        <div class="col-12">
                            @{
                                string val2 = newGuid();

                                <label class="my-1 font-weight-bold _required">Nhận xét của bồi thường viên <span control-counter-label="@val2"></span></label>
                                <textarea maxlength="2000" control-counter-input="@val2" class="form-control" name="danh_gia" required rows="6" placeholder="Nội dung đánh giá"></textarea>
                            }
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            @{
                                string val3 = newGuid();

                                <label class="my-1 font-weight-bold _required">Đề xuất của bồi thường viên <span control-counter-label="@val3"></span></label>
                                <textarea maxlength="2000" control-counter-input="@val3" class="form-control" name="de_xuat" required rows="6" placeholder="Nội dung đề xuất"></textarea>
                            }
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-130" id="btnLuuDongBoiThuongVienDanhGiaHSBTTemp">
                    <i class="fa fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal" id="btnDongBoiThuongVienDanhGiaHSBTTemp">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThueTaiSanPA" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:50%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="">Thông tin chi tiết thuế</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color: #54667a0a; padding-top:5px;">
                <div class="table-responsive" style="max-height:495px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th rowspan="2" style="vertical-align: middle;">Hạng mục</th>
                                <th colspan="3">Tỷ lệ thuế(%)</th>
                            </tr>
                            <tr class="text-center uppercase">
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_thue_vtu_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_thue_vtu_pa">Vật tư</label>
                                    </div>
                                </th>
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_thue_nhan_cong_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_thue_nhan_cong_pa">Nhân công</label>
                                    </div>
                                </th>
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="check_tl_thue_khac_pa" checked="">
                                        <label class="custom-control-label" for="check_tl_thue_khac_pa">Sơn</label>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tblHangMucThueTaiSanPA">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuThueTaiSanPA">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnLuuDongThueTaiSanPA">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblHangMucThueTaiSanPATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblHangMucThuePAItem">
        <td>
            <a class="d-none" data-field="bt" data-val="<%- item.bt %>"></a>
            <a data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_vtu" onchange="tinhThueTaiSanPA('<%- item.bt %>', 'tl_thue_vtu')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_vtu) %>">
            <input type="hidden" data-field="tien_thue_vtu" onchange="tinhThueTaiSanPA('<%- item.bt %>', 'tien_thue_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_vtu) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_nhan_cong" onchange="tinhThueTaiSanPA('<%- item.bt %>', 'tl_thue_nhan_cong')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_nhan_cong) %>">
            <input type="hidden" data-field="tien_thue_nhan_cong" onchange="tinhThueTaiSanPA('<%- item.bt %>', 'tien_thue_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_nhan_cong) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_khac" onchange="tinhThueTaiSanPA('<%- item.bt %>', 'tl_thue_khac')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_khac) %>">
            <input type="hidden" data-field="tien_thue_khac" onchange="tinhThueTaiSanPA('<%- item.bt %>', 'tien_thue_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_khac) %>">
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>


@* 2024/03/08 *@
<div class="modal fade" id="modalDanhSachLanTrinhDuyet" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title">Chọn lần trình để xem chi tiết</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-0">
                <div class="h-100 table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="sticky-top bg-primary text-white text-center">
                            <tr>
                                <th scope="col">STT</th>
                                <th scope="col">Loại trình</th>
                                <th scope="col">Người trình</th>
                                <th scope="col">Ngày trình</th>
                                <th scope="col" class="d-none">Số tiền</th>
                            </tr>
                        </thead>
                        <tbody id="divDanhSachLanTrinhDuyet" class="text-center"></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer py-1">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="divDanhSachLanTrinhDuyetTemplate">
    <% _.forEach(danh_sach, function(item,index) { %>

        <tr style="cursor:pointer;"
            data-bt="<%- item.bt %>"
            data-lan="<%- item.lan %>"
            data-loai="<%- item.loai %>"
            data-hanh_dong="<%- item.hanh_dong %>"
        >
            <td scope="row"><%- item.sott %></td>
            <td class="text-primary"><%- item.loai_ten %></td>
            <td><%- item.ten_nguoi_trinh %></td>
            <td><%- item.ngay_trinh %></td>
            <td class="text-right font-weight-bold text-danger d-none"><%- ESUtil.formatMoney(item.tien) %></td>
        </tr>

    <% }) %>
    <% for(let i = danh_sach.length; i < 5; i++){ %>

        <tr>
            <td scope="row">&nbsp;</td>
            <td></td>
            <td></td>
            <td></td>
            <td class="d-none"></td>
        </tr>

    <% } %>
</script>

<div class="modal fade" id="modalLichSuTrinhDuyetView" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable" style="max-width:calc( 100% - 2rem );height:100%;">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title">Trình duyệt</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-2">
                <div class="d-flex flex-nowrap h-100" style="gap:.5rem">

                    <div class="card border m-0 flex-grow-0 flex-shrink-0" style="width:350px;">
                        <div class="card-header px-2 py-1 rounded text-center">
                            <h6 class="m-0">Danh sách trình duyệt</h6>
                        </div>
                        <div class="card-body p-2 overflow-auto d-flex flex-column" style="gap:.5rem" id="divLichSuTrinhDuyetViewDanhSach"></div>
                    </div>

                    <div class="card m-0 flex-fill">
                        <div class="card-header px-2 py-1 rounded border">
                            <h6 class="m-0" id="divLichSuTrinhDuyetViewChiTiet">&nbsp;</h6>
                        </div>
                        <div class="card-body px-0 pt-2 pb-0 d-flex flex-column" style="gap:.5rem">
                            <div class="h-100 rounded border overflow-hidden">
                                <div class="table-responsive overflow-auto h-100" id="tblLichSuTrinhDuyetViewChiTiet"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="divLichSuTrinhDuyetViewDanhSachTemplate">
    <% _.forEach(danh_sach_duyet, function(item,index) { %>

        <div class="card m-0 border divLichSuTrinhDuyetViewDanhSachItem" data-bt="<%- item.bt %>">
            <div class="card-body p-2 overflow-hidden">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td class="p-0 text-nowrap" style="width: 100px;">Người duyệt</td>
                        <td class="p-0"><%- item.nsd_duyet %></td>
                    </tr>
                    <tr>
                        <td class="p-0 text-nowrap">Tên người duyệt</td>
                        <td class="p-0"><%- item.nsd_duyet_ten %></td>
                    </tr>
                    <tr>
                        <td class="p-0 text-nowrap">Ngày duyệt</td>
                        <td class="p-0"><%- item.ngay_duyet %></td>
                    </tr>
                    <tr>
                        <td class="p-0 text-nowrap">Số tiền</td>
                        <td class="p-0 highlight"><%- ESUtil.formatMoney(item.tien_duyet) %></td>
                    </tr>
                    <tr>
                        <td class="p-0 text-nowrap">Loại duyệt</td>
                        <td class="p-0"><%- item.phe_duyet %></td>
                    </tr>
                </table>

                <% if(item.da_duyet) { %>
                    <span class="info-badge text-success"><i class="fas fa-check-circle bg-white rounded-circle"></i></span>
                <% } %>
            </div>
        </div>

    <% }) %>
    <% _.forEach(danh_sach_trinh, function(item,index) { %>

        <div class="card m-0 border divLichSuTrinhDuyetViewDanhSachItem" data-bt="<%- item.bt %>">
            <div class="card-body p-2 overflow-hidden">
                <table class="table table-sm table-borderless" data-sidenav-item="full">
                    <tr>
                        <td class="p-0 text-nowrap" style="width: 100px;">Người trình</td>
                        <td class="p-0"><%- item.nsd_trinh %></td>
                    </tr>
                    <tr>
                        <td class="p-0 text-nowrap">Tên người trình</td>
                        <td class="p-0"><%- item.nsd_trinh_ten %></td>
                    </tr>
                    <tr>
                        <td class="p-0 text-nowrap">Ngày trình</td>
                        <td class="p-0"><%- item.ngay_trinh %></td>
                    </tr>
                    <tr>
                        <td class="p-0 text-nowrap">Số tiền</td>
                        <td class="p-0 highlight"><%- ESUtil.formatMoney(item.tien_duyet) %></td>
                    </tr>
                </table>

                <% if(item.da_duyet) { %>
                    <span class="info-badge text-success"><i class="fas fa-check-circle bg-white rounded-circle"></i></span>
                <% } %>
            </div>
        </div>

    <% }) %>
</script>

<script type="text/html" id="tblLichSuTrinhDuyetViewChiTietTemplate">
    <% if(danh_sach.length > 0) { %>

        <table class="table table-bordered table-sm table-hover">
            <thead class="bg-primary text-white text-center sticky-top">
                <tr class="text-nowrap">
                    <th class="align-middle py-1" scope="col" rowspan="2">STT</th>

                    <th class="align-middle py-1" colspan="3">Phương án</th>

                    <th class="align-middle py-1" colspan="3" data-col="tien_bao_gia">Tiền báo giá</th>

                    <th class="align-middle py-1" colspan="3" data-col="tien_dx">Tiền đề xuất</th>

                    <th class="align-middle py-1" colspan="3" data-col="tien_duyet">Tiền duyệt</th>

                    <th class="align-middle py-1" rowspan="2" data-col="tien_giam_gia">Tiền giảm giá</th>

                    <th class="align-middle py-1" rowspan="2">% Khấu hao</th>

                    <th class="align-middle py-1" rowspan="2">% Giảm trừ<br />lỗi</th>

                    <th class="align-middle py-1" rowspan="2">% Giảm trừ<br />khác</th>

                    <th class="align-middle py-1" rowspan="2">% Trách nhiệm</th>

                    <th class="align-middle py-1" rowspan="2" data-col="tien_ktru_tien_bao_hiem">Khấu trừ(ĐKBS)</th>

                    <th class="align-middle py-1" rowspan="2" data-col="tien_thue">Tiền thuế</th>

                    <th class="align-middle py-1" rowspan="2">Nguyên nhân<br />giảm trừ</th>

                    <th class="align-middle py-1" rowspan="2">Ghi chú</th>
                </tr>
                <tr class="text-nowrap">
                    <th class="align-middle py-1" scope="col">Hạng mục</th>
                    <th class="align-middle py-1" scope="col">Mức độ</th>
                    <th class="align-middle py-1" scope="col">Thay thế/sửa chữa</th>

                    <th class="align-middle py-1" scope="col" data-col="tien_bao_gia">Tiền V.tư</th>
                    <th class="align-middle py-1" scope="col" data-col="tien_bao_gia">Tiền N.công</th>
                    <th class="align-middle py-1" scope="col" data-col="tien_bao_gia">Tiền sơn</th>

                    <th class="align-middle py-1" scope="col" data-col="tien_dx">Tiền V.tư</th>
                    <th class="align-middle py-1" scope="col" data-col="tien_dx">Tiền N.công</th>
                    <th class="align-middle py-1" scope="col" data-col="tien_dx">Tiền sơn</th>

                    <th class="align-middle py-1" scope="col" data-col="tien_duyet">Tiền V.tư</th>
                    <th class="align-middle py-1" scope="col" data-col="tien_duyet">Tiền N.công</th>
                    <th class="align-middle py-1" scope="col" data-col="tien_duyet">Tiền sơn</th>
                </tr>
            </thead>
            <tbody>
                <% _.forEach(danh_sach, function(item,index) { %>
                    <tr class="text-nowrap">
                        <td class="align-middle text-center" scope="row"><%- item.stt %></td>

                        <td class="align-middle text-wrap px-2 py-1" style="min-width:165px;">
                            <div class="d-flex flex-nowrap align-items-center" style="gap:.5rem;">
                                <span class="flex-grow-0 flex-shrink-0"><i class="fas fa-fw fa-check text-success trang_thai_<%- item.trang_thai %>"></i></span>
                                <span><%- item.ten %></span>
                            </div>
                        </td>
                        <td class="align-middle text-center"><%- item.muc_do_ten %></td>
                        <td class="align-middle text-center"><%- item.thay_the_sc_ten %></td>

                        <td class="align-middle text-right" data-col="tien_bao_gia"><%- ESUtil.formatMoney(item.tien_vtu) %></td>
                        <td class="align-middle text-right" data-col="tien_bao_gia"><%- ESUtil.formatMoney(item.tien_nhan_cong) %></td>
                        <td class="align-middle text-right" data-col="tien_bao_gia"><%- ESUtil.formatMoney(item.tien_khac) %></td>

                        <td class="align-middle text-right" data-col="tien_dx"><%- ESUtil.formatMoney(item.tien_vtu_dx) %></td>
                        <td class="align-middle text-right" data-col="tien_dx"><%- ESUtil.formatMoney(item.tien_nhan_cong_dx) %></td>
                        <td class="align-middle text-right" data-col="tien_dx"><%- ESUtil.formatMoney(item.tien_khac_dx) %></td>

                        <td class="align-middle text-right" data-col="tien_duyet">
                            <% if (item.tien_vtu_duyet < item.tien_vtu_dx) {%>
                                <span class="flex-grow-0 flex-shrink-0"><i class="fas fa-arrow-down text-danger"></i></span>
                                <span><%- ESUtil.formatMoney(item.tien_vtu_duyet) %></span>
                            <% } else {%>
                                <span><%- ESUtil.formatMoney(item.tien_vtu_duyet) %></span>
                            <% }%>
                         </td>
                        <td class="align-middle text-right" data-col="tien_duyet">
                            <% if (item.tien_nhan_cong_duyet < item.tien_nhan_cong_dx) {%>
                                <span class="flex-grow-0 flex-shrink-0"><i class="fas fa-arrow-down text-danger"></i></span>
                                <span><%- ESUtil.formatMoney(item.tien_nhan_cong_duyet) %></span>
                            <% } else {%>
                                <span><%- ESUtil.formatMoney(item.tien_nhan_cong_duyet) %></span>
                            <% }%>
                         </td>
                        <td class="align-middle text-right" data-col="tien_duyet">
                            <% if (item.tien_khac_duyet < item.tien_khac_dx) {%>
                                <span class="flex-grow-0 flex-shrink-0"><i class="fas fa-arrow-down text-danger"></i></span>
                                <span><%- ESUtil.formatMoney(item.tien_khac_duyet) %></span>
                            <% } else {%>
                                <span><%- ESUtil.formatMoney(item.tien_khac_duyet) %></span>
                            <% }%>
                        <td class="align-middle text-right" data-col="tien_giam_gia"><%- ESUtil.formatMoney(item.tien_giam_gia) %></td>

                        <td class="align-middle text-right"><%- item.pt_khau_hao %></td>

                        <td class="align-middle text-right"><%- item.pt_giam_tru_loi %></td>

                        <td class="align-middle text-right"><%- item.pt_giam_tru %></td>

                        <td class="align-middle text-right"><%- item.pt_bao_hiem %></td>

                        <td class="align-middle text-right" data-col="tien_ktru_tien_bao_hiem"><%- ESUtil.formatMoney(item.tien_ktru_tien_bao_hiem) %></td>

                        <td class="align-middle text-right" data-col="tien_thue"><%- ESUtil.formatMoney(item.tien_thue) %></td>

                        <td class="align-middle text-center">
                            <a class="nguyen_nhan" data-val="<%- item.nguyen_nhan %>" onclick="showNguyenNhan?.(this);">
                                <i class="far fa-file-alt" title="Nguyên nhân giảm trừ"></i>
                            </a>
                        </td>

                        <td class="align-middle text-center">
                            <a class="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChu?.(this);">
                                <i class="far fa-file-alt" title="Ghi chú"></i>
                            </a>
                        </td>
                    </tr>
                <% }) %>
                <% for(let i=0; i<100; i++) { %>
                    <tr data-blank>
                        <td scope="row">&nbsp;</td>
                        <td></td>
                        <td></td>
                        <td></td>

                        <td data-col="tien_bao_gia"></td>
                        <td data-col="tien_bao_gia"></td>
                        <td data-col="tien_bao_gia"></td>
                        
                        <td data-col="tien_dx"></td>
                        <td data-col="tien_dx"></td>
                        <td data-col="tien_dx"></td>
                        
                        <td data-col="tien_duyet"></td>
                        <td data-col="tien_duyet"></td>
                        <td data-col="tien_duyet"></td>

                        <td data-col="tien_giam_gia"></td>

                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>

                        <td data-col="tien_ktru_tien_bao_hiem"></td>
                        <td data-col="tien_thue"></td>

                        <td></td>
                        <td></td>
                    </tr>
                <% } %>
            </tbody>
            <tfoot class="bg-light text-danger" style="position:sticky;bottom:0;z-index:1020;">
                <tr class="text-nowrap">
                    <th class="align-middle" scope="row">&nbsp;</th>
                    <th class="align-middle" colspan="3">Tổng cộng</th>

                    <th class="align-middle text-center" colspan="3" data-col="tien_bao_gia"><%- ESUtil.formatMoney(tong.tong_tien) %></th>

                    <th class="align-middle text-center" colspan="3" data-col="tien_dx"><%- ESUtil.formatMoney(tong.tong_tien_dx) %></th>

                    <th class="align-middle text-center" colspan="3" data-col="tien_duyet"><%- ESUtil.formatMoney(tong.tong_tien_duyet) %></th>

                    <th class="align-middle text-right" data-col="tien_giam_gia"><%- ESUtil.formatMoney(tong.tong_tien_giam_gia) %></th>

                    <th class="align-middle text-right"><%- ESUtil.formatMoney(tong.tong_tien_khau_hao) %></th>

                    <th class="align-middle text-right"><%- ESUtil.formatMoney(tong.tong_tien_giam_tru_loi) %></th>

                    <th class="align-middle text-right"><%- ESUtil.formatMoney(tong.tong_tien_giam_tru) %></th>

                    <th class="align-middle text-right"><%- ESUtil.formatMoney(tong.tong_tien_bao_hiem) %></th>

                    <th class="align-middle text-right" data-col="tien_ktru_tien_bao_hiem"><%- ESUtil.formatMoney(tong.tien_ktru_tien_bao_hiem) %></th>

                    <th class="align-middle text-right" data-col="tien_thue"><%- ESUtil.formatMoney(tong.tong_tien_thue) %></th>

                    <th class="align-middle" colspan="2"></th>
                </tr>
            </tfoot>
        </table>

    <% } else { %>

        <h3 class="text-center m-0 p-3">Không có dữ liệu</h3>

    <% } %>
</script>

<script type="text/html" id="tblLichSuTrinhDuyetViewChiTiet2Template">
    <% if(danh_sach.length > 0) { %>

        <table class="table table-bordered table-sm">
            <thead class="bg-primary text-white text-center sticky-top">
                <tr class="text-nowrap">
                    <th class="align-middle" scope="col">STT</th>
                    <th class="align-middle" scope="col">Đối tượng</th>

                    @* <th class="align-middle" scope="col">Mã</th> *@
                    <th class="align-middle" scope="col" style="min-width:300px;">Thương tật</th>
                    @* <th class="align-middle" scope="col">Mức tối thiểu</th>
                    <th class="align-middle" scope="col">Mức tối đa</th> *@
                    <th class="align-middle" scope="col">TL thương tật</th>
                    <th class="align-middle" scope="col">Mức trách nhiệm</th>
                    <th class="align-middle" scope="col">Ghi chú</th>

                    <th class="align-middle" scope="col">Số tiền tổn thất</th>
                    <th class="align-middle" scope="col">% Giảm trừ lỗi</th>
                    <th class="align-middle" scope="col">% Giảm trừ theo QTBH</th>
                    <th class="align-middle" scope="col">% T.Nhiệm</th>
                    <th class="align-middle" scope="col">Nguyên nhân G.trừ</th>
                    <th class="align-middle" scope="col">Ghi chú</th>
                </tr>
            </thead>
            <tbody>
                <% _.forEach(danh_sach, function(item,index) { %>
                    <% _.forEach(item.danh_sach, function(_item,_index) { %>
                        <% if(_index <= 0) { %>

                            <tr class="text-nowrap">
                                <td class="align-middle text-center" rowspan="<%- item.danh_sach.length %>" scope="row"><%- index+1 %></td>
                                @* <td class="align-middle text-center" rowspan="<%- item.danh_sach.length %>"><%- item.ten %></td> *@
                                <td class="align-middle text-wrap px-2 py-1" style="min-width:150px;" rowspan="<%- item.danh_sach.length %>">
                                    <div class="d-flex flex-nowrap align-items-center" style="gap:.5rem;">
                                        <span class="flex-grow-0 flex-shrink-0"><i class="fas fa-fw fa-check text-success trang_thai_<%- item.trang_thai %>"></i></span>
                                        <span><%- item.ten %></span>
                                    </div>
                                </td>

                                @* <td class="align-middle py-1 text-center"><%- _item.ma_thuong_tat %></td> *@
                                <td class="align-middle py-1 text-left text-wrap"><%- _item.ten %></td>
                                @* <td class="align-middle py-1 text-right"><%- _item.pttt_tu %></td>
                                <td class="align-middle py-1 text-right"><%- _item.pttt_toi %></td> *@
                                <td class="align-middle py-1 text-right"><%- _item.pttt %></td>
                                <td class="align-middle py-1 text-right"><%- ESUtil.formatMoney(_item.tien) %></td>
                                <td class="align-middle py-1 text-center">
                                    <a class="ghi_chu" data-val="<%- _item.ghi_chu %>" onclick="showGhiChu?.(this);">
                                        <i class="far fa-file-alt" title="Ghi chú"></i>
                                    </a>
                                </td>

                                <td class="align-middle text-right" rowspan="<%- item.danh_sach.length %>"><%- ESUtil.formatMoney(item.tien_khac_duyet) %></td>
                                <td class="align-middle text-right" rowspan="<%- item.danh_sach.length %>"><%- item.pt_giam_tru_loi %></td>
                                <td class="align-middle text-right" rowspan="<%- item.danh_sach.length %>"><%- item.pt_giam_tru %></td>
                                <td class="align-middle text-right" rowspan="<%- item.danh_sach.length %>"><%- item.pt_bao_hiem %></td>

                                <td class="align-middle text-center" rowspan="<%- item.danh_sach.length %>">
                                    <a class="nguyen_nhan" data-val="<%- item.nguyen_nhan %>" onclick="showNguyenNhan?.(this);">
                                        <i class="far fa-file-alt" title="Nguyên nhân giảm trừ"></i>
                                    </a>
                                </td>

                                <td class="align-middle text-center" rowspan="<%- item.danh_sach.length %>">
                                    <a class="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChu?.(this);">
                                        <i class="far fa-file-alt" title="Ghi chú"></i>
                                    </a>
                                </td>
                            </tr>

                        <% } else { %>

                            <tr class="text-nowrap">
                                @* <td class="align-middle py-1 text-center"><%- _item.ma_thuong_tat %></td> *@
                                <td class="align-middle py-1 text-left text-wrap"><%- _item.ten %></td>
                                @* <td class="align-middle py-1 text-right"><%- _item.pttt_tu %></td>
                                <td class="align-middle py-1 text-right"><%- _item.pttt_toi %></td> *@
                                <td class="align-middle py-1 text-right"><%- _item.pttt %></td>
                                <td class="align-middle py-1 text-right"><%- ESUtil.formatMoney(_item.tien) %></td>
                                <td class="align-middle py-1 text-center">
                                    <a class="ghi_chu" data-val="<%- _item.ghi_chu %>" onclick="showGhiChu?.(this);">
                                        <i class="far fa-file-alt" title="Ghi chú"></i>
                                    </a>
                                </td>
                            </tr>

                        <% } %>
                    <% }) %>
                <% }) %>
                <% for(let i=0; i<100; i++) { %>
                    <tr data-blank>
                        <td scope="row">&nbsp;</td>
                        <td></td>

                        @* <td></td> *@
                        <td></td>
                        @* <td></td>
                        <td></td> *@
                        <td></td>
                        <td></td>
                        <td></td>

                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                <% } %>
            </tbody>
            <tfoot class="bg-light text-danger" style="position:sticky;bottom:0;z-index:1020;">
                <tr class="text-nowrap">
                    <th class="align-middle" scope="row">&nbsp;</th>
                    <th class="align-middle" colspan="2">Tổng cộng</th>

                    <th class="align-middle text-center" colspan="4"><%- ESUtil.formatMoney(tong.tong_tien_duyet) %></th>

                    <th class="align-middle text-right"><%- ESUtil.formatMoney(tong.tong_tien_giam_tru_loi) %></th>

                    <th class="align-middle text-right"><%- ESUtil.formatMoney(tong.tong_tien_giam_tru) %></th>

                    <th class="align-middle text-right"><%- ESUtil.formatMoney(tong.tong_tien_bao_hiem) %></th>

                    <th class="align-middle" colspan="2"></th>
                </tr>
            </tfoot>
        </table>

    <% } else { %>

        <h3 class="text-center m-0 p-3">Không có dữ liệu</h3>

    <% } %>
</script>


@*bo sung 2024*@
<div id="modalPAGiamTruQTBH" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:45%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="">Thông tin chi tiết giảm trừ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <form name="frmModalPAGiamTruQTBH" method="post" class="modal-body" style="background-color: #54667a0a; padding-top:5px;">
                <input type="hidden" name="ma_doi_tac" />
                <input type="hidden" name="lh_nv" />
                <input type="hidden" name="so_id" />
                <input type="hidden" name="so_id_pa" />
                <input type="hidden" name="so_id_doi_tuong" />
                <div class="row">
                    <div class="col col-4">
                        <div class="form-group">
                            <label class="">Số tiền giảm trừ</label>
                            <input type="text" class="form-control number" autocomplete="off" name="so_tien" placeholder="Số tiền giảm trừ">
                        </div>
                    </div>
                    <div class="col col-8">
                        <div class="form-group">
                            <label class="">Ghi chú giảm trừ</label>
                            <input type="text" class="form-control" autocomplete="off" name="ghi_chu" placeholder="Ghi chú giảm trừ">
                        </div>
                    </div>
                </div>
                <div class="table-responsive" style="max-height:495px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th>Hạng mục</th>
                                <th style="width:200px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="frmModalPAGiamTruQTBH_chkAll_TLPAGiamTruQTBH" checked="">
                                        <label class="custom-control-label" for="frmModalPAGiamTruQTBH_chkAll_TLPAGiamTruQTBH">TL giảm(%)</label>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tblPAGiamTruQTBH">
                        </tbody>
                    </table>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm float-right wd-80" data-modal-action="luu">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right wd-110" data-modal-action="luu_dong">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblPAGiamTruQTBHTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblPAGiamTruQTBHItem">
        <td>
            <input type="hidden" name="hm[][hang_muc]" value="<%- item.hang_muc %>" />
            <input type="hidden" name="hm[][so_id_doi_tuong]" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" name="hm[][bt_bao_gia]" value="<%- item.bt_bao_gia %>" />
            <%- item.hang_muc_ten %>
        </td>
        <td class="text-right">
            <input type="text" name="hm[][pt_giam_tru]" maxlength="3" class="decimal floating-input form-control" value="<%- item.pt_giam_tru??-1 %>" onchange="_cellValueChangePA?.(this);" data-sync="#frmModalPAGiamTruQTBH_chkAll_TLPAGiamTruQTBH">
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 5){
    for(var i = 0; i < 5 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>