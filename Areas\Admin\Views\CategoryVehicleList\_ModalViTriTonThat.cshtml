﻿<div id="modalViTri" class="modal-drag" style="width:350px; z-index:9999999;">
    <div class="modal-drag-header border-bottom">
        <h5><span class="modal-drag-title">Chọn vị trí tổn thất</span> <span data-dismiss="modal-drag"><i class="fa fa-times mr-2"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_ViTri" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonViTriElementSelect">
                
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonViTriDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right mb-2" id="btnChonViTri">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalChonViTriDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox nngt" id="nngt_<%- item.ma %>">
        <input type="checkbox" id="vi_tri_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonViTriItem">
        <label class="custom-control-label" style="cursor:pointer;" for="vi_tri_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
<style>
    #modalViTri {
        left: 319.325px !important;
    }
</style>