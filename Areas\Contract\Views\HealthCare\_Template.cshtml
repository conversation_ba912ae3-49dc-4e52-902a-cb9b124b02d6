﻿<script type="text/html" id="danhDachGCNNguoi_template">
    <% if(gcn.length > 0){
    _.forEach(gcn, function(item,index) { %>
    <tr data-search="<%- item.ten.toLowerCase() %>" style="cursor:pointer" class="item-gcn" onclick="xemChiTietGCNNguoi('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>')" id="item-gcn-<%- item.ma_doi_tac %><%- item.so_id %><%- item.so_id_dt %>">
        <td style="font-weight:bold; padding: 8px 0 8px 0; width: 50%"><%- item.ten_1 %></td>

        <td class ="text-right" style="padding: 8px 0 8px 0"><%- item.hieu_luc %></td>
        <% if(item.count == 0){%>
            <td style="float:right; padding: 9px 0 7px 8px "><input type="checkbox" onclick="event.stopPropagation();" class="gcn" id="gcn_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>"/></td>
        <%}else{%>
            <td style="float:right; padding: 9px 0 7px 8px "><input type="checkbox" disabled onclick="event.stopPropagation();" class="gcn" id="gcn_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>"/></td>
        <%}%>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="2">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="danhSachNV_template">
    <% if(lhnv.length > 0){
    _.forEach(lhnv, function(item,index) { %>
    <tr row-val="<%- item.ma %>">
        <td class="text-center"><%- index+1 %></td>
        <td><%- item.ten %></td>
        <td><input type="text" name="" maxlength="18" class="number floating-input" col-tien-bh="<%- item.ma %>" value="<%- ESUtil.formatMoney(item.tien) %>" /></td>
        <%if(item.mien_thuong=="K"){
        %>
        <td class="text-center"><input type="checkbox" col-mien-thuong="<%- item.ma %>" name="" onclick="onCheckMienThuong(this, '<%- item.ma %>')" /></td>
        <td><input type="text" name="" maxlength="18" col-khau-tru="<%- item.ma %>" disabled="disabled" class="number floating-input" value="<%- ESUtil.formatMoney(item.ktru) %>" /></td>
        <%
        }
        else
        {
        %>
        <td class="text-center"><input type="checkbox" checked="checked" col-mien-thuong="<%- item.ma %>" name="" onclick="onCheckMienThuong(this, '<%- item.ma %>')" /></td>
        <td><input type="text" name="" maxlength="18" col-khau-tru="<%- item.ma %>" class="number floating-input" value="<%- ESUtil.formatMoney(item.ktru) %>" /></td>
        <%
        }
        %>

        <td><input type="text" name="" col-tl-phi="<%- item.ma %>" maxlength="3" class="number floating-input" value="<%- ESUtil.formatMoney(item.tl_phi) %>" /></td>
        <td><input type="text" name="" maxlength="18" col-phi-bh="<%- item.ma %>" class="number floating-input" value="<%- ESUtil.formatMoney(item.phi) %>" /></td>
    </tr>
    <% })}%>
</script>

<script type="text/html" id="danhSachDKBS_template">
    <% if(dkbs.length > 0){
    _.forEach(dkbs, function(item,index) { %>
    <tr row-val="<%- item.ma %>">
        <td class="text-center"><%- index+1 %></td>
        <td class="text-center"><%- item.ma %></td>
        <td><%- item.ten %></td>
        <%if(!item.chon){
        %>
        <td class="text-center"><input type="checkbox" col-dkbs="<%- item.ma %>" /></td>
        <%
        }
        else
        {
        %>
        <td class="text-center"><input type="checkbox" checked="checked" col-dkbs="<%- item.ma %>" /></td>
        <%
        }
        %>
        <td></td>
    </tr>
    <% })} %>
</script>

<script type="text/html" id="tableNhapQLoi_template">
    <% if(dk.length > 0){
    _.forEach(dk, function(item,index) { %>
    <% if(item.lh_nv_ct == null){ %>
    <tr row-val="<%- item.bt %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b style="font-weight: bold; text-transform: uppercase;"><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px; font-weight: bold"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" style="font-weight: bold" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
         <td class="text-right" style="font-weight: bold" col-ngay_lan_kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_kham) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-center" style="font-weight: bold" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" style="font-weight: bold" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
                            @*<td class="text-right" style="font-weight: bold" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" style="font-weight: bold" col-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.phi) %>
        </td>
    </tr>
    <% }else if(item.lh_nv_ct.toString().indexOf('.') != -1){ %>
    <tr row-val="<%- item.bt %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b style="font-style: italic;"><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px; font-style: italic;"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" style="font-style: italic;" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-ngay-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_kham) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-center" style="font-style: italic;" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" style="font-style: italic;" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
                                @*<td class="text-right" style="font-style: italic;" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" style="font-style: italic; " col-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.phi) %>
        </td>
    </tr>
    <% }else{ %>
    <tr row-val="<%- item.bt %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" col-ngay-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
        </td>
        <td class="text-right" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_kham) %>
        </td>
        <td class="text-right" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-center" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
        </td>
        <td class="text-right" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
                                    @*<td class="text-right" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" col-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.phi) %>
        </td>
    </tr>
    <% } %>

    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có điều khoản</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="DongTai_template">
    <% var stt = 1 %>
    <% if(dk.length > 0){ %>
    <% _.forEach(dk, function(item,index) { %>
    <tr class="dong_tai" id="dong_tai_<%- item.so_id%>_<%- item.so_id_dt%>_<%- item.don_vi_dong_tai %>" onclick="Xem_chi_tiet_dong_tai('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>', '<%- item.don_vi_dong_tai %>', '<%- item.loai_dong %>')">
        <td class="text-center"><%- stt %></td>
        @*<td class="text-center">
            <a href="#">
                <i class="fas fa-eye" title="Xem thông tin chi tiết"></i>
            </a>
        </td>*@
        <% stt++ %>
        <td class="text-center"><b style="font-weight: bold"><%- item.ten_don_vi_dong_tai_hthi %></b></td>
        <td class="text-center" style=" text-transform: uppercase;">
            <%- item.kieu %>
        </td>

        <td class="text-center">
            <%- item.goi_bh %>
        </td>
        <td class="text-center">
            <%- item.ten_doi_tuong %>
        </td>
        <td class="text-center">
            <%- item.tl_dong %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_cd %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_tt %>%
        </td>
    </tr>
    <% })} %>

    <% if(dk.length < 6){ %>
    <% for(var i = 0; i < 6 - dk.length;i++ ){  %>
    <tr>
        <td style="height:35.5px;"></td>
        @*<td></td>*@
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="SDBS_template">
    <% var stt = 1 %>
    <% if(dk.length > 0){
    _.forEach(dk, function(item,index) { %>
    <tr>
        <td class="text-center" style=" text-transform: uppercase;">
            <%- item.so_hd %>
        </td>
        <td class="text-center">
            <%- item.kieu_hd_ten %>
        </td>
        <td class="text-center">
            <%- item.ngay_cap_text %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="4">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="tblTongPhiTPA_template">
    <% _.forEach(data, function(item, index) { %>
    <tr onclick="rowSelected(this)">
        <td class="text-center">
            <input type="hidden" name="so_id" value="<%- item.so_id %>" />
            <input class="form-control floating-input text-center" value="<%- item.ngay_ps %>" name="ngay_ps_item" disabled="disabled" style="background-color: white">
        </td>
        <td class="text-center">
            <input class="form-control floating-input text-center" value="<%- item.so_hd %>" name="so_hd" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input text-right" value="<%- ESUtil.formatMoney(item.phi_tpa) %>" name="phi_tpa_item" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input" value="<%- item.ghi_chu %>" name="ghi_chu_item" disabled="disabled" style="background-color: white">
        </td>
    </tr>
    <%})%>
    <% if(data.length < 6){
    for(var i = 0; i < 5 - data.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" style="width: 55px; height: 19px;"></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblSuaDoiTuongTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td class="text-center">
            <input class="form-control floating-input" value="<%- item.ten %>" name="ten" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input text-center" value="<%- item.ngay_sinh %>" name="ngay_sinh" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input text-center" value="<%- item.so_cmt %>" name="so_cmt" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input text-center" value="<%- item.gcn %>" name="gcn" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input text-center" value="<%- item.ten_map %>" name="ten_map" disabled="disabled" style="background-color: white">
        </td>
        <td class="text-center">
            <input class="form-control floating-input text-center" value="<%- item.ngay_sinh_map %>" name="ngay_sinh_map" disabled="disabled" style="background-color: white">
        </td>
        <td class="text-center">
            <input class="form-control floating-input text-center" value="<%- item.so_cmt_map %>" name="so_cmt_map" disabled="disabled" style="background-color: white">
        </td>
        <td class="text-center">
            <input class="form-control floating-input text-center" value="<%- item.gcn_map%>" name="gcn_map" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input text-center" value="<%- item.ngay_kt %>" name="ngay_kt" disabled="disabled" style="background-color: white">
        </td>
    </tr>
    <%})%>
</script>

<script type="text/html" id="lstImage_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="pt-1" id="nhom_anh_<%- index %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImg('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTiet(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img<%- image.bt %>" class="nhom_anh_ton_that_<%- index %> mt-1" value="<%- image.bt %>" name="ds_anh_xe">
            <p class="fileNameImage mt-1" style="cursor:pointer"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
    <% } %>
</script>

@*Danh sách giấy chứng nhận phân trang*@
<script type="text/html" id="danhSachGCNTemplate">
    <% _.forEach(data, function(item, index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten %>">
        <input type="checkbox" onchange="chonGCNPaging(this)" id="gcn_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>" class="custom-control-input item-gcn" data-text="<%- item.ten %>">
        <label class="custom-control-label" for="gcn_<%- item.so_id_dt %>"><%- item.ten %> - <%- item.ngay_sinh_ht %></label>
    </div>
    <%})%>
</script>

@*Danh sách giấy chứng nhận phân trang*@
@*<script type="text/html" id="danhSachGCNTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsnt" id="dsnt_<%- item.so_id_dt %>" data-text="<%- item.ten %>">
        <input type="checkbox" id="gcn_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>" class="custom-control-input modalDsGCN_Item single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="gcn_<%- item.so_id_dt %>"><%- item.ten %> - <%- item.ngay_sinh_ht %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>*@

@*Danh sách giấy chứng nhận phân trang*@
<script type="text/html" id="dsDoiTuongTemplate">
    <div class="border rounded mb-2">
        <div class="d-flex justify-content-between align-items-center py-1" style="background-color: #f7f7f7;">
            <div class="custom-control custom-checkbox ml-2">
                <input type="checkbox" onchange="onChonTatCa(this)" id="chon_tat_ca" class="custom-control-input">
                <label class="custom-control-label font-weight-bold" for="chon_tat_ca">Chọn tất cả</label>
            </div>
        </div>
    </div>
    <% _.forEach(data, function(item, index) { %>
    <div class="custom-control custom-checkbox ml-2" data-text="<%- item.ten %>">
        <input type="hidden" value="<%- item.so_id_dt %>" data-name="so_id_dt" />
        <input type="checkbox" onchange="chonDoiTuong(this)" id="sogcn_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>" class="custom-control-input item-sogcn">
        <label class="custom-control-label" for="sogcn_<%- item.so_id_dt %>"><%- item.ten %></label>
    </div>
    <%})%>
</script>

<script type="text/html" id="tblDanhSachKyThanhToan_template">
    <% if(data.length > 0){
        var tong_tien = 0;
        var tong_tien_tt = 0;
    _.forEach(data, function(item,index) { 
        tong_tien += item.so_tien;
        tong_tien_tt += item.so_tien_da_tt;        
        %>
    <tr>
        <td class="text-center">
            <%- index + 1 %>
        </td>
        <td class="text-center">
            <%- item.so_hd %>
        </td>
        <td class="text-center">
            <%- item.ky_tt_hthi %>
        </td>
        <td class="text-center">
            <%- item.ngay_tt_hthi %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoneyNullable(item.so_tien) %>
        </td>
        <% if (item.so_tien_da_tt != '' && item.so_tien_da_tt != null && item.so_tien_da_tt != undefined){ %>
        <td class="text-right">
            <%- ESUtil.formatMoneyNullable(item.so_tien_da_tt) %>
        </td>
        <% }else{%>
        <td class="text-right">
            <%- item.so_tien_da_tt %>
        </td>
        <% }%>
       
        <% if(item.hien_thi == 1){
            %><td class="text-center">
                <a href="#" onclick="suaKyThanhToan('<%- item.ma_doi_tac%>', '<%- item.so_id%>', '<%- item.bt%>', '<%- item.nv%>')">
                    <i class="fa fa-edit"></i>
                </a>
            </td><%
        }else{
            %><td class="text-center ">
                <a href="#" onclick="suaKyThanhToan('<%- item.ma_doi_tac%>', '<%- item.so_id%>', '<%- item.bt%>', '<%- item.nv%>')">
                    <i class="fa fa-edit d-none"></i>
                </a>
            </td><%
        }%>
        
    </tr>
    <% })%>
    <tr>
        <td colspan="4" style="font-weight:bold">Tổng cộng: </td>
        <td class="text-right" style="font-weight:bold"><%-ESUtil.formatMoneyNullable(tong_tien)%></td>
        <% if (tong_tien_tt != '' && tong_tien_tt != null && tong_tien_tt != undefined){ %>
        <td class="text-right" style="font-weight:bold"><%-ESUtil.formatMoneyNullable(tong_tien_tt)%></td>
        <% }else{%>

        <td class="text-right" style="font-weight:bold"><%-tong_tien_tt%></td>
        <% }%>
        <td></td>
    </tr>
    <%}else{ %>
    <tr>
        <td class="text-center" colspan="7">Không có dữ liệu</td>
    </tr>
    <% } %>

</script>

<script type="text/html" id="modalGoiBHDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsbv" id="dsgoibh_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <input type="checkbox" id="goi_bh_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-goibh modalGoiBHItem single_checked" onchange="onChonGoiBH(this)">
        <label class="custom-control-label" style="cursor:pointer;" for="goi_bh_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

@*Tab quyền lợi bổ sung*@
<script type="text/html" id="tableDKBSTemplate">
    <%if(ds_dkbs.length > 0){
    _.forEach(ds_dkbs, function(item,index) { %>
    <tr class="dkbs">
        <td>
            <input type="hidden" data-name="ma" data-field="ma" value="<%- item.ma %>" />
            <a href="#" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td>
            <input type="text" data-name="ghi_chu" autocomplete="off" maxlength="500" class="floating-input" data-field="ghi_chu" value="<%- item.ghi_chu %>" />
        </td>
        <td>
            <input style="text-align:center" type="text" data-name="so_lan_ngay" autocomplete="off" maxlength="18" class="floating-input decimal" data-field="so_lan_ngay" value="<%- item.so_lan_ngay %>" />
        </td>
        <td>
            <input type="text" data-name="tien_lan_ngay" autocomplete="off" maxlength="18" class="number floating-input" data-field="tien_lan_ngay" value="<%- item.tien_lan_ngay %>" />
        </td>
        <td>
            <input type="text" data-name="tien_nam" autocomplete="off" maxlength="18" class="number floating-input" data-field="tien_nam" value="<%- item.tien_nam %>" />
        </td>
        <td>
            <input type="text" data-name="dong_bh" autocomplete="off" maxlength="3" class="floating-input number" data-field="dong_bh" value="<%- item.dong_bh %>" />
        </td>
        <td>
            <input style="text-align:center" type="text" data-name="so_ngay_cho" autocomplete="off" maxlength="18" class="floating-input decimal" data-field="so_ngay_cho" value="<%- item.so_ngay_cho %>" />
        </td>
        <td>
            <input type="text" data-name="phi" autocomplete="off" maxlength="18" class="number floating-input" data-field="phi" value="<%- item.phi %>" />
        </td>
        <td>
            <input type="text" data-name="tl_phi" autocomplete="off" maxlength="3" class="number floating-input" data-field="tl_phi" value="<%- item.tl_phi %>" />
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" data-val="<%- item.ma %>" onclick="xoaDKBS(this)"></i>
        </td>
    </tr>
    <% })} %>
    <% if(ds_dkbs.length < 12){
    for(var i = 0; i < 12 - ds_dkbs.length; i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalNguoiPTDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsbv" id="dsnguoi_pt_<%- item.ten %>_<%- item.ngay_sinh %>" data-text="<%- item.ten.toLowerCase() %>" >
        <input type="checkbox" id="nguoi_pt_<%- item.ten %>_<%- item.ngay_sinh %>" data-value="<%- item.so_cmt %>" value="<%- item.ten %>" class="custom-control-input item-goibh modalNguoiPTItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="nguoi_pt_<%- item.ten %>_<%- item.ngay_sinh %>"><%- item.ten %> - <%- item.ngay_sinh %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="tableNhapQLoi_Edit_template">
    <% if(dk.length > 0){
    _.forEach(dk, function(item,index) { %>
    <% if(item.lh_nv_ct == null){ %>
    <tr class="item_lhnv" row-val="<%- item.bt %>" data-lhnv = "<%- item.lh_nv %>" >
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b style="font-weight: bold; text-transform: uppercase;"><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px; font-weight: bold"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" style="font-weight: bold" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
            <input type="hidden" data-so-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_lan_ngay) %>" />
        </td>
         <td class="text-right d-none" style="font-weight: bold" col-ngay_lan_kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
            <input type="hidden" data-ngay-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.ngay_lan_kham) %>" />
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_lan_ngay) %>
            <input type="hidden" data-tien-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="floating-input" value="<%- ESUtil.formatMoney(item.tien_lan_ngay) %>" />
        </td>
        <td class="text-right d-none" style="font-weight: bold" col-tien-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_lan_kham) %>
            <input type="hidden" data-tien-lan-kham="<%- item.lh_nv %>" maxlength="16" class="floating-input" value="<%- ESUtil.formatMoney(item.tien_lan_kham) %>" />
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_nam) %>
             <input type="hidden" data-tien-nam="<%- item.lh_nv %>" maxlength="16" class="floating-input" value="<%- ESUtil.formatMoney(item.tien_nam) %>" />
        </td>
        <td class="text-center" style="font-weight: bold" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" style="font-weight: bold" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
            <input type="hidden" data-dong-bh="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.dong_bh) %>" />
        </td>
        <td class="text-right" style="font-weight: bold" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
                            @*<td class="text-right" style="font-weight: bold" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" style="font-weight: bold" col-phi="<%- item.bt %>">
            @* <%- ESUtil.formatMoney(item.phi) %> *@
            <input type="text" data-phi="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" onkeyup="hthiTongCongPhiQL()" value="<%- ESUtil.formatMoneyNullable(item.phi) %>" />
        </td>
    </tr>
    <% }else if(item.lh_nv_ct.toString().indexOf('.') != -1){ %>
    <tr class="item_lhnv" row-val="<%- item.bt %>" data-lhnv = "<%- item.lh_nv %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b style="font-style: italic;"><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px; font-style: italic;"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" style="font-style: italic;" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
            <input type="hidden" data-so-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_lan_ngay) %>" />
        </td>
        <td class="text-right d-none" style="font-style: italic;" col-ngay-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
            <input type="hidden" data-ngay-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.ngay_lan_kham) %>" />
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_lan_ngay) %>
            <input type="hidden" data-tien-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_lan_ngay) %>" />
        </td>
        <td class="text-right d-none" style="font-style: italic;" col-tien-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_lan_kham) %>
            <input type="hidden" data-tien-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_lan_kham) %>" />
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_nam) %>
            <input type="hidden" data-tien-nam="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_nam) %>" />
        </td>
        <td class="text-center" style="font-style: italic;" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" style="font-style: italic;" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
            <input type="hidden" data-dong-bh="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.dong_bh) %>" />
        </td>
        <td class="text-right" style="font-style: italic;" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
             <input type="hidden" data-so-ngay-cho="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- item.so_ngay_cho %>" />
        </td>
                                @*<td class="text-right" style="font-style: italic;" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" style="font-style: italic; " col-phi="<%- item.bt %>">
            @* <%- ESUtil.formatMoney(item.phi) %> *@
            <input type="text" data-phi="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input " onkeyup="hthiTongCongPhiQL()" value="<%- ESUtil.formatMoneyNullable(item.phi) %>" />
        </td>
    </tr>
    <% }else{ %>
    <tr class="item_lhnv" row-val="<%- item.bt %>" data-lhnv = "<%- item.lh_nv %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
            <input type="hidden" data-so-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_lan_ngay) %>" />
        </td>
        <td class="text-right d-none" col-ngay-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
            <input type="hidden" data-ngay-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.ngay_lan_kham) %>" />
        </td>
        <td class="text-right" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
            <input type="hidden" data-tien-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_lan_ngay) %>" />
        </td>
        <td class="text-right d-none" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_lan_kham) %>
            <input type="hidden" data-tien-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_lan_kham) %>" />
        </td>
        <td class="text-right" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoneyNullable(item.tien_nam) %>
            <input type="hidden" data-tien-nam="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien_nam) %>" />
        </td>
        <td class="text-center" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
            <input type="hidden" data-dong-bh="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.dong_bh) %>" />
        </td>
        <td class="text-right" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
             <input type="hidden" data-so-ngay-cho="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- item.so_ngay_cho %>" />
        </td>
                                    @*<td class="text-right" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" col-phi="<%- item.bt %>">
            @* <%- ESUtil.formatMoney(item.phi) %> *@
            <input type="text" data-phi="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" onkeyup="hthiTongCongPhiQL()" value="<%- ESUtil.formatMoneyNullable(item.phi) %>" />
        </td>
    </tr>
    <% } %>

    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có điều khoản</td>
    </tr>
    <% } %>
</script>
<script type="text/html" id="ds_gcn_template">
    <% if(ds.length > 0){ %>
     <div class="custom-control custom-checkbox col-12 dsNDBH" id="dsNDBH">
        <input type="checkbox" id="NDBH" value="" class="custom-control-input" onchange="getAll(this)">
        <label class="custom-control-label" style="cursor:pointer;" for="NDBH">Chọn tất cả</label>
    </div>
    <% _.forEach(ds, function(item,index) { %>
    <div class="custom-control custom-checkbox col-6 dsNDBH" id="dsNDBH_<%- item.so_id_dt%>" data-text="<%- item.so_id_dt%>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="NDBH_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>" class="custom-control-input modalNDBHItem">
        <label class="custom-control-label" style="cursor:pointer;" for="NDBH_<%- item.so_id_dt %>"><%- item.ten_hthi %></label>
    </div>
    <% })} %>

    <% if(ds.length < 11){
    for(var i = 0; i < 11 - ds.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="dsKH_Template">
    <% _.forEach(data, function(item, index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten %>">
        <input type="checkbox" id="ma_kh_<%- item.ma %>" value="<%- item.ma %>" data-val = "<%- item.ten %> - <%- item.ma %>" class="custom-control-input single_checked item-kh" onchange="$('#inputTimKiemKH').focus();">
        <label class="custom-control-label" for="ma_kh_<%- item.ma %>"><%- item.ten %> - <%- item.ma %></label>
    </div>
    <%})%>
</script>