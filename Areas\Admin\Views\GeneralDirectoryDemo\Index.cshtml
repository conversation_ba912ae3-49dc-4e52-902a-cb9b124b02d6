﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Demo quản lý danh mục bộ mã chung cơ sở y tế";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Bộ mã chung cơ sở y tế</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Bộ mã chung</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin tên" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Nhóm</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="nhom" required style="width: 100%; height:36px;">
                                    <option value="">Chọn nhóm</option>
                                    <option value="MOI_QUAN_HE">Mối quan hệ</option>
                                    <option value="NGUYEN_NHAN">Nguyên nhân tổn thất</option>
                                    <option value="HINH_THUC">Hình thức điều trị</option>
                                    <option value="GIAM_TRU">Nguyên nhân giảm trừ</option>
                                    <option value="QUYEN_LOI">Quyền lợi chi tiết phát sinh</option>
                                    <option value="CHI_PHI">Chi phí</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="1">Đang sử dụng</option>
                                    <option value="0">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapCSYT">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewCSYT" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapCSYT" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmSaveCSYT" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Bộ mã chung cơ sở y tế <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Nhóm</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="nhom" required style="width: 100%; height:36px;">
                                    <option value="">Chọn nhóm</option>
                                    <option value="MOI_QUAN_HE">Mối quan hệ</option>
                                    <option value="NGUYEN_NHAN">Nguyên nhân tổn thất</option>
                                    <option value="HINH_THUC">Hình thức điều trị</option>
                                    <option value="GIAM_TRU">Nguyên nhân giảm trừ từ chối</option>
                                    <option value="QUYEN_LOI">Quyền lợi chi tiết phát sinh</option>
                                    <option value="CHI_PHI">Chi phí</option>

                                </select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" required style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="1">Đang sử dụng</option>
                                    <option value="0">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px;">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã</label>
                                <input type="text" maxlength="100" autocomplete="off" name="ma" required placeholder="Nhập mã bộ mã chung" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên</label>
                                <input type="text" maxlength="500" autocomplete="off" name="ten" required class="form-control" placeholder="VD: Nhà thuốc Pharmacity">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Số thứ tự</label>
                                <input type="text" min="0" maxlength="5" autocomplete="off" placeholder="Thứ tự hiển thị" name="stt" class="form-control decimal">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px">
                        <div class="col-sm-12">
                            <div class="form-group green-border-focus">
                                <label>Ghi chú</label>
                                <textarea class="form-control" maxlength="500" name="ghi_chu" placeholder="Nhập thông tin ghi chú"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnSaveCSYT"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnDeleteCSYT"><i class="fas fa-trash-alt"></i> Xóa</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/GeneralDirectoryServiceDemo.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/GeneralDirectoryDemo.js" asp-append-version="true"></script>
}