﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình xe ô tô";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<style>
    .card_active {
        background-color: #9abcea40;
    }

    .cau_hinh_xe:hover {
        background-color: #eef5f9;
    }

    .bg-color {
        background-color: #ececec;
        border: 1px solid #607d8b;
        /*#2d88ff*/
    }
</style>

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Danh sách cấu hình</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item"><PERSON>h sách cấu hình</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row"></div>
                </form>
                <div class="row mt-3">
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_boi_thuong" onclick="chonLoaiCauHinh('CH_BOI_THUONG')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-car"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Cấu hình bồi thường</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="khau_hao" onclick="chonLoaiCauHinh('KH')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Phương pháp tính khấu hao</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="giam_tru" onclick="chonLoaiCauHinh('GT')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-chart-bar"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Phương pháp tính giảm trừ bảo hiểm</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_ho_so" onclick="chonLoaiCauHinh('CH_HS')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Cấu hình hồ sơ chứng từ</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_sla" onclick="chonLoaiCauHinh('CH_SLA')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fab fa-usps"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Cấu hình SLA</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4 d-none">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_duyet_gia_tu_dong" onclick="chonLoaiCauHinh('CH_DUYET_GIA_TU_DONG')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-file-invoice-dollar"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình duyệt giá tự động</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_phan_cong" onclick="chonLoaiCauHinh('CH_PHAN_CONG')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-map-marked-alt"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình phân công BTV theo địa bàn giám định</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_xu_ly_bt" onclick="chonLoaiCauHinh('CH_XU_LY_BT')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-code-branch"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình xử lý hồ sơ</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_ben_tham_gia_gdinh_mac_dinh" onclick="chonLoaiCauHinh('CH_CH_BEN_GDMD')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình bên tham gia giám định mặc định</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_dvi_thanh_toan" onclick="chonLoaiCauHinh('CH_DVI_THANH_TOAN')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-money-check-edit-alt"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình đơn vị thanh toán</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_bien_do_uoc" onclick="chonLoaiCauHinh('CH_BIEN_DO_UOC')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-money-check-alt"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình chặn biên độ ước</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_bien_do_uoc_theo_diem" onclick="chonLoaiCauHinh('CH_BIEN_DO_UOC_THEO_DIEM')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-money-check-alt"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình chặn biên độ ước theo điểm</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_mo_ho_so_nha_dong" onclick="chonLoaiCauHinh('CH_MO_HO_SO_NHA_DONG')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-house-user"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình mở hồ sơ đối với các nhà đồng</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_phuong_an_chi_tra_nha_dong" onclick="chonLoaiCauHinh('CH_PHUONG_AN_CHI_TRA_NHA_DONG')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-house-user"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình phương án chi trả với các nhà đồng</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_tam_ung_boi_thuong" onclick="chonLoaiCauHinh('CH_TAM_UNG_BOI_THUONG')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-money-check-edit-alt"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-90p">
                                        <h6>Cấu hình tạm ứng bồi thường</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<partial name="_Modal.cshtml" />
<partial name="_ModalLoaiXe.cshtml" />
<partial name="_ModalDKBS.cshtml" />
<partial name="_Template.cshtml" />
<partial name="_ModalCHPheDuyetGiaTuDong.cshtml" />
<partial name="_ModalCHMoHoSoNhaDong.cshtml" />

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CarConfigurationService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/RangeVehicleService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CategoryCommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/AdministrativeUnitsService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CategoryvehicleListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DamageLevelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/GaraListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/HieuXeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BusinessCodeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/InsuranceService.js" asp-append-version="true"></script>
    <script src="~/js/app/CommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/CarConfiguration.js" asp-append-version="true"></script>
}