﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình ngôn ngữ";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình ngôn ngữ</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình ngôn ngữ</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Ngôn ngữ</label>
                                <select class="select2 form-control custom-select" required name="lang" style="width: 100%; height:36px;">
                                    <option value="" selected>Chọn ngôn ngữ</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Mã</label>
                                <input type="text" name="ma" autocomplete="off" placeholder="Nhập mã" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" autocomplete="off" placeholder="Nhập nội dung" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top:21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnNhap">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewDich" class="table-app" style="height: 67vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<partial name="_Modal.cshtml" />

@section Scripts {
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/LanguageConfigurationService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/LanguageConfiguration.js" asp-append-version="true"></script>
}