﻿<div id="modalNguyenNhanTaiNan" class="modal-drag" style="width:300px; z-index:9999999; left: 120px;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn nguyên nhân tai nạn</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_NNTaiNan" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalNNTaiNanElementSelect">
                
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalNNTaiNanDanhSach"></div>
        </div>
    </div>
    <div class="modal-drag-footer" style="height: 32px; border-top: 1px solid #eaeaea; margin:0px 0px 10px 0px;">
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnChonNNTaiNan">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalNNTaiNanDanhSach_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox nntn" id="nntn_<%- item.ma %>">
        <input type="checkbox" id="nn_tai_nan_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNNTaiNanItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="nn_tai_nan_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>