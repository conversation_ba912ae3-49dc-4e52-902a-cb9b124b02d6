﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class OtherDamageLevelController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        public async Task<IActionResult> GetAll()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_XE_MUC_DO_TT_DMUC, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Luu_nhap()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_XE_MUC_DO_TT_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Xoa_nhap()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_XE_MUC_DO_TT_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Liet_ke_trang()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_XE_MUC_DO_TT_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Liet_ke_chi_tiet()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_XE_MUC_DO_TT_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_MUC_DO_TT_IMPORT_EXCEL, json);
            return Ok(data);
        }
    }
}