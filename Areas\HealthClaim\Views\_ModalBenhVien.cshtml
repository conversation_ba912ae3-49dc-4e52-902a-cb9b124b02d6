﻿<style>
    .text {
        color: red;
    }
    #modalBenhVienDanhSach label:hover {
        color: var(--escs-main-theme-color);
    }
</style>
<div id="modalBenhVien" class="modal-drag" style="width:650px; z-index:9999999; margin-top: 5px !important; margin-left: 60px">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn bệnh viện</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_BenhVien" type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control item-benhvien">
                <input type="hidden" id="modalBenhVienElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalBenhVienDanhSach">

            </div>
        </div>
    </div>
</div>

<script type="text/html" id="modalBenhVienDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <% var text_csyt = "" %>
    <% if(item.bl_noitru == 'C' && item.bl_ngoaitru == 'C' && item.bl_rang == 'C'){ %>
    <% text_csyt = "(Bảo lãnh nội trú / Bảo lãnh ngoại trú / Bảo lãnh răng)" %>
    <% }else if(item.bl_noitru == 'C' && item.bl_ngoaitru == 'C' && item.bl_rang == 'K'){ %>
    <% text_csyt = "(Bảo lãnh nội trú / Bảo lãnh ngoại trú)" %>
    <% }else if(item.bl_noitru == 'C' && item.bl_ngoaitru == 'K' && item.bl_rang == 'C'){ %>
    <% text_csyt = "(Bảo lãnh nội trú / Bảo lãnh răng)" %>
    <% }else if(item.bl_noitru == 'C' && item.bl_ngoaitru == 'K' && item.bl_rang == 'K'){ %>
    <% text_csyt = "(Bảo lãnh nội trú)" %>
    <% }else if(item.bl_noitru == 'K' && item.bl_ngoaitru == 'C' && item.bl_rang == 'C'){ %>
    <% text_csyt = "(Bảo lãnh ngoại trú / Bảo lãnh răng)" %>
    <% }else if(item.bl_noitru == 'K' && item.bl_ngoaitru == 'C' && item.bl_rang == 'K'){ %>
    <% text_csyt = "(Bảo lãnh ngoại trú)" %>
    <% }else if(item.bl_noitru == 'K' && item.bl_ngoaitru == 'K' && item.bl_rang == 'C'){ %>
    <% text_csyt = "(Bảo lãnh răng)" %>
    <% }else{ %>
    <% text_csyt = "" %>
    <% } 
        if(item.mst != '' && item.mst != null)
        {
            item.mst = ' - MST:' + item.mst
        }
    %>

    <% if(item.bl_noitru == 'K' && item.bl_ngoaitru == 'K' && item.bhyt == 'K'){ %>
    <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma.replace(' ', '') %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked" onchange="onChonBenhVien(this)">
        <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %><br /><%- item.mst %> - Địa chỉ: <%- item.dia_chi %></label>
    </div>
    <% }else{ %>
    <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma.replace(' ', '') %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked" onchange="onChonBenhVien(this)">
        <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %> -  <%- text_csyt %><br /><%- item.mst %> - Địa chỉ:<%- item.dia_chi %></label>
    </div>
    <% } %>

    @*<%if(item.bl_noitru == 'C' && item.bl_ngoaitru == 'C'){%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked" onchange="onChonBenhVien(this)">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %> </label> - <label class="text"> (Bảo lãnh nội trú / Bảo lãnh ngoại trú) </label>
        </div>
        <%}else if( item.bl_noitru == 'C' && item.bl_ngoaitru == 'K'){%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked" onchange="onChonBenhVien(this)">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %> - <label class="text"> (Bảo lãnh nội trú) </label> </label>
        </div>
        <%}else if(item.bl_noitru == 'K' && item.bl_ngoaitru == 'C'){%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked" onchange="onChonBenhVien(this)">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %> - <label class="text"> (Bảo lãnh ngoại trú) </label> </label>
        </div>
        <%}else{%>
        <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
            <input type="checkbox" id="benh_vien_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-benhvien modalBenhVienItem single_checked" onchange="onChonBenhVien(this)">
            <label class="custom-control-label" style="cursor:pointer;" for="benh_vien_<%- item.ma %>"><%- item.ten %></label>
        </div>
        <%}%>*@
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
