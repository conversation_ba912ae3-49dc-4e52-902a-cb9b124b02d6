﻿using ClosedXML.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml;
using ESCS.Attributes;
using ESCS.Common;
using ESCS.COMMON.Common;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.COMMON.Http;
using ESCS.Controllers;
using ESCS.MODEL.ESCS.OutValues;
using ESCS.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using RazorEngine.Configuration;
using RazorEngine.Templating;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web;
using ESCS.COMMON.Response;
using System.Linq;
using System;

namespace ESCS.Areas.Manager.Controllers
{
    /// <summary>
    /// Phê duyệt
    /// </summary>
    [Area("Manager")]
    [SystemAuthen]
    public class ApprovedController : BaseController
    {
        private TemplateServiceConfiguration config;
        public static IRazorEngineService _service = null;
        private readonly IWebHostEnvironment _env;
        public ApprovedController(IWebHostEnvironment env)
        {
            _env = env;
            config = new TemplateServiceConfiguration();
            config.CachingProvider = new RazorEngine.Templating.DefaultCachingProvider();
            if (_service == null)
                _service = RazorEngineService.Create(config);
        }
        /// <summary>
        /// Màn hình phê duyệt
        /// </summary>
        /// <returns></returns>
        public IActionResult Index(string ho_so)
        {
            ViewBag.ho_so = Utilities.DecryptByKey(ho_so, AppSettings.KeyEryptData);
            return View();
        }
        [AjaxOnly]
        public async Task<IActionResult> Liet_ke_trang()
        {
            var rq = Request.GetDataRequest(GetUser());
            var data = await Request.GetRespone(StoredProcedure.PBH_BT_TRINH_DUYET_LKE, (object)rq);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Liet_ke_chi_tiet()
        {
            var rq = Request.GetDataRequest(GetUser());
            var data = await Request.GetRespone(StoredProcedure.PBH_BT_PHE_DUYET_LKE_CT, (object)rq);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Dong_y_duyet()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_PHE_DUYET_NH, json, "/api/esmartclaim/approve");
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Huy_dong_y_duyet()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_PHE_DUYET_XOA, json, "/api/esmartclaim/unapprove");
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> TuChoiDuyet()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew<int?, out_value_phe_duyet>(StoredProcedure.PBH_BT_PHE_DUYET_TU_CHOI, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> ChiTietLichSuGiaDuyet()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_LS_PA_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> LayDanhSachLanTrinh()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LayChiTietTinhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToanChiPhi()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_CHI_PHI_CT_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToanThuongTat()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_THUONG_TAT_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToanGiamGia()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_GIAM_GIA_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToanKhauTru()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_KHAU_TRU_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToanThue()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_THUE_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToanGiamTruQTBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_GIAM_TRU_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiTietTinhToanChiPhiThue()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_CHI_PHI_THUE_LUU, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> XemBangTinhToanCT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_BANG_TTOAN, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> LuuChiPhiKhac()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_CP_KHAC_LUU, json);
            return Ok(data);
        }

        //Màn hình phê duyệt
        public IActionResult TransApprovedDisplay(string ma_doi_tac, string so_id, string nv, string lhnv, string loai, string bt, string ten, string lan, string hanh_dong, string url_redirect)
        {
            string ho_so = HttpUtility.UrlEncode(Utilities.EncryptByKey(ma_doi_tac + "/" + so_id + "/" + nv + "/" + lhnv + "/" + loai + "/" + bt + "/" + ten + "/" + lan + "/" + hanh_dong, AppSettings.KeyEryptData));
            return LocalRedirect(url_redirect + "?ho_so=" + ho_so);
        }
        //Màn hình bồi thường
        public IActionResult TransCompensationDisplay(string ma_doi_tac, string so_id, string hanh_dong, string url_redirect)
        {
            string ho_so = HttpUtility.UrlEncode(Utilities.EncryptByKey(ma_doi_tac + "/" + so_id + "/" + hanh_dong, AppSettings.KeyEryptData));
            return LocalRedirect(url_redirect + "?ho_so=" + ho_so);
        }
        //Màn hình giám định
        public IActionResult TransInvestigationDisplay(string ma_doi_tac, string so_id, string hanh_dong, string url_redirect)
        {
            string ho_so = HttpUtility.UrlEncode(Utilities.EncryptByKey(ma_doi_tac + "/" + so_id + "/" + hanh_dong, AppSettings.KeyEryptData));
            return LocalRedirect(url_redirect + "?ho_so=" + ho_so);
        }
        //Màn hình bồi thường xe máy
        public IActionResult TransMotoCompensationDisplay(string ma_doi_tac, string so_id, string hanh_dong, string url_redirect)
        {
            string ho_so = HttpUtility.UrlEncode(Utilities.EncryptByKey(ma_doi_tac + "/" + so_id + "/" + hanh_dong, AppSettings.KeyEryptData));
            return LocalRedirect(url_redirect + "?ho_so=" + ho_so);
        }
        //Màn hình giám định xe máy
        public IActionResult TransMotoInvestigationDisplay(string ma_doi_tac, string so_id, string hanh_dong, string url_redirect)
        {
            string ho_so = HttpUtility.UrlEncode(Utilities.EncryptByKey(ma_doi_tac + "/" + so_id + "/" + hanh_dong, AppSettings.KeyEryptData));
            return LocalRedirect(url_redirect + "?ho_so=" + ho_so);
        }


        [AjaxOnly]
        public async Task<IActionResult> LayQTXLTrinhDuyet()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_QTXL_TRINH_DUYET_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> downloadMauPhuongAn(phuong_an phuongAn)
        {
            var nsd = GetUser();
            phuongAn.ma_doi_tac_nsd = nsd.ma_doi_tac;
            phuongAn.ma_chi_nhanh_nsd = nsd.ma_chi_nhanh;
            phuongAn.nsd = nsd.nsd;
            phuongAn.pas = nsd.pas;
            var json = JsonConvert.SerializeObject(phuongAn);
            var data = await Request.GetResponeNew<IEnumerable<phuong_an_chi_tiet>>(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_DOWNLOAD, json);
            if (data.state_info.status != "OK")
            {
                data.state_info.message_code = "400";
                return Ok(data);
            }
            string fileName = "template_export_phuong_an.xlsx";
            var filename_output = Path.Combine(_env.ContentRootPath, "App_Data", fileName);
            using (XLWorkbook workbook = new XLWorkbook(filename_output))
            {
                var ws = workbook.Worksheet(1);
                int rowIndex = 7;
                foreach (var rowData in data.data_info)
                {
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.STT).SetValue(rowData.stt);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.STT).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.HANG_MUC).SetValue(rowData.ten);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.HANG_MUC).Comment.AddText(rowData.hang_muc ?? "");
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.HANG_MUC).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.HANG_MUC).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.HANG_MUC).Style.Protection.Locked = true;
                    
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU).SetValue(rowData.tien_vtu);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU).Style.Protection.Locked = true;

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG).SetValue(rowData.tien_nhan_cong);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG).Style.Protection.Locked = true;

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC).SetValue(rowData.tien_khac);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC).Style.Protection.Locked = true;

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DX).SetValue(rowData.tien_vtu_dx);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DX).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DX).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DX).Style.Protection.Locked = true;

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DX).SetValue(rowData.tien_nhan_cong_dx);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DX).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DX).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DX).Style.Protection.Locked = true;

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DX).SetValue(rowData.tien_khac_dx);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DX).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DX).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DX).Style.Protection.Locked = true;


                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DUYET).SetValue(rowData.tien_vtu_duyet);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DUYET).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DUYET).SetValue(rowData.tien_nhan_cong_duyet);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DUYET).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DUYET).SetValue(rowData.tien_khac_duyet);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DUYET).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.GHI_CHU).SetValue(rowData.ghi_chu);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.GHI_CHU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    rowIndex++;
                }
                var workbookBytes = new byte[0];
                using (var ms = new MemoryStream())
                {
                    workbook.SaveAs(ms);
                    workbookBytes = ms.ToArray();
                    return Ok(workbookBytes);
                }
            }
        }
        [AjaxOnly]
        public async Task<IActionResult> downloadMauPhuongAnDoc(phuong_an phuongAn)
        {
            var nsd = GetUser();
            phuongAn.ma_doi_tac_nsd = nsd.ma_doi_tac;
            phuongAn.ma_chi_nhanh_nsd = nsd.ma_chi_nhanh;
            phuongAn.nsd = nsd.nsd;
            phuongAn.pas = nsd.pas;
            var json = JsonConvert.SerializeObject(phuongAn);
            var data = await Request.GetResponeNew<IEnumerable<phuong_an_chi_tiet>>(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_DOWNLOAD, json);
            if (data.state_info.status != "OK")
            {
                data.state_info.message_code = "400";
                return Ok(data);
            }
            string fileName = "template_export_phuong_an_doc.xlsx";
            var filename_output = Path.Combine(_env.ContentRootPath, "App_Data", fileName);
            using (XLWorkbook workbook = new XLWorkbook(filename_output))
            {
                var ws = workbook.Worksheet(1);
                int rowIndex = 4;
                int groupIndex = 0;

                #region VTU
                ws.Range(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC, rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Merge();
                ws.Range(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC, rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                ws.Cell(rowIndex, 1).SetValue($"{++groupIndex}. HẠNG MỤC THAY THẾ");
                ws.Cell(rowIndex, 1).Style.Font.Bold = true;
                ws.Cell(rowIndex, 1).Comment.AddText("TIEN_VTU");
                rowIndex++;
                foreach (var rowData in data.data_info)
                {
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).SetValue(rowData.ten);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Comment.AddText(rowData.hang_muc ?? "");
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).SetValue(rowData.tien_vtu);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).SetValue(rowData.tien_vtu_dx);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DUYET).SetValue(rowData.tien_vtu_duyet);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DUYET).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).SetValue(rowData.ghi_chu);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    rowIndex++;
                }
                #endregion
                #region NHAN_CONG
                ws.Range(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC, rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Merge();
                ws.Range(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC, rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                ws.Cell(rowIndex, 1).SetValue($"{++groupIndex}. HẠNG MỤC NHÂN CÔNG");
                ws.Cell(rowIndex, 1).Style.Font.Bold = true;
                ws.Cell(rowIndex, 1).Comment.AddText("TIEN_NHAN_CONG");
                rowIndex++;
                foreach (var rowData in data.data_info)
                {
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).SetValue(rowData.ten);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Comment.AddText(rowData.hang_muc ?? "");
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).SetValue(rowData.tien_nhan_cong);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).SetValue(rowData.tien_nhan_cong_dx);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DUYET).SetValue(rowData.tien_nhan_cong_duyet);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DUYET).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).SetValue(rowData.ghi_chu);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    rowIndex++;
                }
                #endregion
                #region KHAC
                ws.Range(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC, rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Merge();
                ws.Range(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC, rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);
                ws.Cell(rowIndex, 1).SetValue($"{++groupIndex}. HẠNG MỤC SƠN");
                ws.Cell(rowIndex, 1).Style.Font.Bold = true;
                ws.Cell(rowIndex, 1).Comment.AddText("TIEN_KHAC");
                rowIndex++;
                foreach (var rowData in data.data_info)
                {
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).SetValue(rowData.ten);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Comment.AddText(rowData.hang_muc ?? "");
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).SetValue(rowData.tien_khac);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).SetValue(rowData.tien_khac_dx);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).Style.Fill.BackgroundColor = XLColor.LightGray;
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DUYET).SetValue(rowData.tien_khac_duyet);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DUYET).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).SetValue(rowData.ghi_chu);
                    ws.Cell(rowIndex, (int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).Style
                                                        .Border.SetTopBorder(XLBorderStyleValues.Thin).Border.SetRightBorder(XLBorderStyleValues.Thin)
                                                        .Border.SetBottomBorder(XLBorderStyleValues.Thin).Border.SetLeftBorder(XLBorderStyleValues.Thin);

                    rowIndex++;
                }
                #endregion

                var workbookBytes = new byte[0];
                using (var ms = new MemoryStream())
                {
                    workbook.SaveAs(ms);
                    workbookBytes = ms.ToArray();
                    return Ok(workbookBytes);
                }
            }
        }
        [AjaxOnly]
        public async Task<IActionResult> uploadMauPhuongAn(phuong_an phuongAn)
        {
            BaseResponse<IEnumerable<phuong_an_chi_tiet>> res = new BaseResponse<IEnumerable<phuong_an_chi_tiet>>();
            if (phuongAn == null || phuongAn.file_upload_phuong_an == null || phuongAn.file_upload_phuong_an.Length <= 0)
            {
                res.state_info.status = "500";
                res.state_info.message_body = "Không tìm thấy file upload";
                return Ok(res);
            }
            string extension = Path.GetExtension(phuongAn.file_upload_phuong_an.FileName).ToLower();
            if (extension != ".xlsx" && extension != ".xls")
            {
                res.state_info.status = "500";
                res.state_info.message_body = "Không đúng định dạng file";
                return Ok(res);
            }
            var nsd = GetUser();
            phuongAn.ma_doi_tac_nsd = nsd.ma_doi_tac;
            phuongAn.ma_chi_nhanh_nsd = nsd.ma_chi_nhanh;
            phuongAn.nsd = nsd.nsd;
            phuongAn.pas = nsd.pas;
            var json = JsonConvert.SerializeObject(phuongAn);
            var data = await Request.GetResponeNew<List<phuong_an_chi_tiet>>(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_DOWNLOAD, json);
            using (XLWorkbook workbook = new XLWorkbook(phuongAn.file_upload_phuong_an.OpenReadStream()))
            {
                var ws = workbook.Worksheet(1);
                var rows = ws.RangeUsed().Rows().Skip(4);
                foreach (var row in rows)
                {
                    var cellHangMuc = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.HANG_MUC);
                    var hang_muc = cellHangMuc.Comment?.Text?.Trim();

                    var ten_hang_muc = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.HANG_MUC).GetString();
                    var tien_vtu = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU).GetString();
                    var tien_nhan_cong = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG).GetString();
                    var tien_khac = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC).GetString();

                    var tien_vtu_dx = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DX).GetString();
                    var tien_nhan_cong_dx = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DX).GetString();
                    var tien_khac_dx = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DX).GetString();

                    var tien_vtu_duyet = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_VAT_TU_DUYET).GetString();
                    var tien_nhan_cong_duyet = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_NHAN_CONG_DUYET).GetString();
                    var tien_khac_duyet = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.TIEN_KHAC_DUYET).GetString();

                   
                    var ghi_chu = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_ENUM.GHI_CHU).GetString();

                    var tien_vtu_num = string.IsNullOrEmpty(tien_vtu.Trim()) || !decimal.TryParse(tien_vtu.Trim(), out _) ? 0 : Convert.ToDecimal(tien_vtu.Trim());
                    var tien_nhan_cong_num = string.IsNullOrEmpty(tien_nhan_cong.Trim()) || !decimal.TryParse(tien_nhan_cong.Trim(), out _) ? 0 : Convert.ToDecimal(tien_nhan_cong.Trim());
                    var tien_khac_num = string.IsNullOrEmpty(tien_khac.Trim()) || !decimal.TryParse(tien_khac.Trim(), out _) ? 0 : Convert.ToDecimal(tien_khac.Trim());

                    var tien_vtu_dx_num = string.IsNullOrEmpty(tien_vtu_dx.Trim()) || !decimal.TryParse(tien_vtu_dx.Trim(), out _) ? 0 : Convert.ToDecimal(tien_vtu_dx.Trim());
                    var tien_nhan_cong_dx_num = string.IsNullOrEmpty(tien_nhan_cong_dx.Trim()) || !decimal.TryParse(tien_nhan_cong_dx.Trim(), out _) ? 0 : Convert.ToDecimal(tien_nhan_cong_dx.Trim());
                    var tien_khac_dx_num = string.IsNullOrEmpty(tien_khac_dx.Trim()) || !decimal.TryParse(tien_khac_dx.Trim(), out _) ? 0 : Convert.ToDecimal(tien_khac_dx.Trim());

                    var tien_vtu_duyet_num = string.IsNullOrEmpty(tien_vtu_duyet.Trim()) || !decimal.TryParse(tien_vtu_duyet.Trim(), out _) ? 0 : Convert.ToDecimal(tien_vtu_duyet.Trim());
                    var tien_nhan_cong_duyet_num = string.IsNullOrEmpty(tien_nhan_cong_duyet.Trim()) || !decimal.TryParse(tien_nhan_cong_duyet.Trim(), out _) ? 0 : Convert.ToDecimal(tien_nhan_cong_duyet.Trim());
                    var tien_khac_duyet_num = string.IsNullOrEmpty(tien_khac_duyet.Trim()) || !decimal.TryParse(tien_khac_duyet.Trim(), out _) ? 0 : Convert.ToDecimal(tien_khac_duyet.Trim());

                    
                    if (tien_vtu_num < 0)
                        tien_vtu_num = 0;
                    if (tien_nhan_cong_num < 0)
                        tien_nhan_cong_num = 0;
                    if (tien_khac_num < 0)
                        tien_khac_num = 0;

                    if (tien_vtu_dx_num < 0)
                        tien_vtu_dx_num = 0;
                    if (tien_nhan_cong_dx_num < 0)
                        tien_nhan_cong_dx_num = 0;
                    if (tien_khac_dx_num < 0)
                        tien_khac_dx_num = 0;

                    if (tien_vtu_duyet_num < 0)
                        tien_vtu_duyet_num = 0;
                    if (tien_nhan_cong_duyet_num < 0)
                        tien_nhan_cong_duyet_num = 0;
                    if (tien_khac_duyet_num < 0)
                        tien_khac_duyet_num = 0;

                    var dataSet = data.data_info?.Where(n => n.hang_muc == hang_muc);
                    if (dataSet != null && dataSet.Count() > 0)
                    {
                        foreach (var hm in dataSet)
                        {
                            hm.trang_thai = hm.trang_thai;
                            hm.stt = hm.stt;
                            hm.hang_muc = hm.hang_muc;
                            hm.ten = hm.ten;
                            hm.so_id_doi_tuong = hm.so_id_doi_tuong;
                            hm.lh_nv = hm.lh_nv;
                            hm.bt_bao_gia = hm.bt_bao_gia;
                            hm.bt_ls = hm.bt_ls;
                            hm.ma_chi_phi = hm.ma_chi_phi;
                            hm.muc_do_ten = hm.muc_do_ten;
                            hm.thay_the_sc_ten = hm.thay_the_sc_ten;

                            hm.tien_vtu = hm.tien_vtu;
                            hm.tien_nhan_cong = hm.tien_nhan_cong;
                            hm.tien_khac = hm.tien_khac;

                            hm.tien_vtu_dx = hm.tien_vtu_dx;
                            hm.tien_nhan_cong_dx = hm.tien_nhan_cong_dx;
                            hm.tien_khac_dx = hm.tien_khac_dx;
                           
                            hm.tien_vtu_duyet = tien_vtu_duyet_num;
                            hm.tien_nhan_cong_duyet = tien_nhan_cong_duyet_num;
                            hm.tien_khac_duyet = tien_khac_duyet_num;

                            hm.tien_giam_gia = hm.tien_giam_gia;
                            hm.pt_khau_hao = hm.pt_khau_hao;
                            hm.pt_giam_tru_loi = hm.pt_giam_tru_loi;
                            hm.pt_giam_tru = hm.pt_giam_tru;
                            hm.pt_bao_hiem = hm.pt_bao_hiem;
                            hm.tien_ktru_tien_bao_hiem = hm.tien_ktru_tien_bao_hiem;
                            hm.tien_thue = hm.tien_thue;
                            hm.nguyen_nhan = hm.nguyen_nhan;
                            hm.ghi_chu = ghi_chu;
                        }
                    }
                }
            }
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> uploadMauPhuongAnDoc(phuong_an phuongAn)
        {
            #region lay du lieu db
            BaseResponse<IEnumerable<phuong_an_chi_tiet>> res = new BaseResponse<IEnumerable<phuong_an_chi_tiet>>();
            if (phuongAn == null || phuongAn.file_upload_phuong_an_doc == null || phuongAn.file_upload_phuong_an_doc.Length <= 0)
            {
                res.state_info.status = "500";
                res.state_info.message_body = "Không tìm thấy file upload";
                return Ok(res);
            }
            string extension = Path.GetExtension(phuongAn.file_upload_phuong_an_doc.FileName).ToLower();
            if (extension != ".xlsx" && extension != ".xls")
            {
                res.state_info.status = "500";
                res.state_info.message_body = "Không đúng định dạng file";
                return Ok(res);
            }
            var nsd = GetUser();
            phuongAn.ma_doi_tac_nsd = nsd.ma_doi_tac;
            phuongAn.ma_chi_nhanh_nsd = nsd.ma_chi_nhanh;
            phuongAn.nsd = nsd.nsd;
            phuongAn.pas = nsd.pas;
            var json = JsonConvert.SerializeObject(phuongAn);
            var data = await Request.GetResponeNew<List<phuong_an_chi_tiet>>(StoredProcedure.PBH_BT_XE_HS_LICH_SU_DUYET_DOWNLOAD, json);
            #endregion
            #region xu ly du lieu
            using (XLWorkbook workbook = new XLWorkbook(phuongAn.file_upload_phuong_an_doc.OpenReadStream()))
            {
                var ws = workbook.Worksheet(1);
                var rows = ws.RangeUsed().Rows().Skip(3);

                List<string> dsLoai = new List<string> { "TIEN_VTU", "TIEN_NHAN_CONG", "TIEN_KHAC" };
                string loai = string.Empty;

                foreach (var row in rows)
                {
                    var cellHangMuc = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC);
                    var hang_muc = cellHangMuc.Comment?.Text?.Trim();

                    if (dsLoai.Contains(hang_muc) == true)
                    {
                        loai = hang_muc;
                        continue;
                    }

                    if (string.IsNullOrEmpty(loai))
                    {
                        continue;
                    }

                    var ten_hang_muc = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.HANG_MUC).GetString();
                    var tien_bao_gia = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_BAO_GIA).GetString();
                    var tien_dx = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DX).GetString();
                    var tien_duyet = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.TIEN_DUYET).GetString();
                    var ghi_chu = row.Cell((int)EXPORT_PHUONG_AN_CHI_TIET_DOC_ENUM.GHI_CHU).GetString();

                    decimal.TryParse(tien_bao_gia.Trim(), out decimal tien_bao_gia_num);
                    decimal.TryParse(tien_dx.Trim(), out decimal tien_dx_num);
                    decimal.TryParse(tien_duyet.Trim(), out decimal tien_duyet_num);

                    tien_bao_gia_num = Math.Max(tien_bao_gia_num, 0);
                    tien_dx_num = Math.Max(tien_dx_num, 0);
                    tien_duyet_num = Math.Max(tien_duyet_num, 0);

                    var dataSet = data.data_info?.Where(n => n.hang_muc == hang_muc || n.hang_muc == ten_hang_muc);

                    if (dataSet != null && dataSet.Count() > 0)
                    {
                        #region hang muc cu
                        foreach (var hm in dataSet)
                        {
                            switch (loai)
                            {
                                case "TIEN_VTU":
                                    {
                                        hm.trang_thai = hm.trang_thai;
                                        hm.stt = hm.stt;
                                        hm.hang_muc = hm.hang_muc;
                                        hm.ten = hm.ten;
                                        hm.so_id_doi_tuong = hm.so_id_doi_tuong;
                                        hm.lh_nv = hm.lh_nv;
                                        hm.bt_bao_gia = hm.bt_bao_gia;
                                        hm.bt_ls = hm.bt_ls;
                                        hm.ma_chi_phi = hm.ma_chi_phi;
                                        hm.muc_do_ten = hm.muc_do_ten;
                                        hm.thay_the_sc_ten = hm.thay_the_sc_ten;

                                        hm.tien_vtu = hm.tien_vtu;
                                        hm.tien_nhan_cong = hm.tien_nhan_cong;
                                        hm.tien_khac = hm.tien_khac;

                                        hm.tien_vtu_dx = hm.tien_vtu_dx;
                                        hm.tien_nhan_cong_dx = hm.tien_nhan_cong_dx;
                                        hm.tien_khac_dx = hm.tien_khac_dx;

                                        hm.tien_vtu_duyet = tien_duyet_num;

                                        hm.tien_giam_gia = hm.tien_giam_gia;
                                        hm.pt_khau_hao = hm.pt_khau_hao;
                                        hm.pt_giam_tru_loi = hm.pt_giam_tru_loi;
                                        hm.pt_giam_tru = hm.pt_giam_tru;
                                        hm.pt_bao_hiem = hm.pt_bao_hiem;
                                        hm.tien_ktru_tien_bao_hiem = hm.tien_ktru_tien_bao_hiem;
                                        hm.tien_thue = hm.tien_thue;
                                        hm.nguyen_nhan = hm.nguyen_nhan;
                                        hm.ghi_chu = ghi_chu;

                                        break;
                                    }
                                case "TIEN_NHAN_CONG":
                                    {
                                        hm.trang_thai = hm.trang_thai;
                                        hm.stt = hm.stt;
                                        hm.hang_muc = hm.hang_muc;
                                        hm.ten = hm.ten;
                                        hm.so_id_doi_tuong = hm.so_id_doi_tuong;
                                        hm.lh_nv = hm.lh_nv;
                                        hm.bt_bao_gia = hm.bt_bao_gia;
                                        hm.bt_ls = hm.bt_ls;
                                        hm.ma_chi_phi = hm.ma_chi_phi;
                                        hm.muc_do_ten = hm.muc_do_ten;
                                        hm.thay_the_sc_ten = hm.thay_the_sc_ten;

                                        hm.tien_vtu = hm.tien_vtu;
                                        hm.tien_nhan_cong = hm.tien_nhan_cong;
                                        hm.tien_khac = hm.tien_khac;

                                        hm.tien_vtu_dx = hm.tien_vtu_dx;
                                        hm.tien_nhan_cong_dx = hm.tien_nhan_cong_dx;
                                        hm.tien_khac_dx = hm.tien_khac_dx;

                                        hm.tien_nhan_cong_duyet = tien_duyet_num;

                                        hm.tien_giam_gia = hm.tien_giam_gia;
                                        hm.pt_khau_hao = hm.pt_khau_hao;
                                        hm.pt_giam_tru_loi = hm.pt_giam_tru_loi;
                                        hm.pt_giam_tru = hm.pt_giam_tru;
                                        hm.pt_bao_hiem = hm.pt_bao_hiem;
                                        hm.tien_ktru_tien_bao_hiem = hm.tien_ktru_tien_bao_hiem;
                                        hm.tien_thue = hm.tien_thue;
                                        hm.nguyen_nhan = hm.nguyen_nhan;
                                        hm.ghi_chu = ghi_chu;

                                        break;
                                    }
                                case "TIEN_KHAC":
                                    {
                                        hm.trang_thai = hm.trang_thai;
                                        hm.stt = hm.stt;
                                        hm.hang_muc = hm.hang_muc;
                                        hm.ten = hm.ten;
                                        hm.so_id_doi_tuong = hm.so_id_doi_tuong;
                                        hm.lh_nv = hm.lh_nv;
                                        hm.bt_bao_gia = hm.bt_bao_gia;
                                        hm.bt_ls = hm.bt_ls;
                                        hm.ma_chi_phi = hm.ma_chi_phi;
                                        hm.muc_do_ten = hm.muc_do_ten;
                                        hm.thay_the_sc_ten = hm.thay_the_sc_ten;

                                        hm.tien_vtu = hm.tien_vtu;
                                        hm.tien_nhan_cong = hm.tien_nhan_cong;
                                        hm.tien_khac = hm.tien_khac;

                                        hm.tien_vtu_dx = hm.tien_vtu_dx;
                                        hm.tien_nhan_cong_dx = hm.tien_nhan_cong_dx;
                                        hm.tien_khac_dx = hm.tien_khac_dx;

                                        hm.tien_khac_duyet = tien_duyet_num;

                                        hm.tien_giam_gia = hm.tien_giam_gia;
                                        hm.pt_khau_hao = hm.pt_khau_hao;
                                        hm.pt_giam_tru_loi = hm.pt_giam_tru_loi;
                                        hm.pt_giam_tru = hm.pt_giam_tru;
                                        hm.pt_bao_hiem = hm.pt_bao_hiem;
                                        hm.tien_ktru_tien_bao_hiem = hm.tien_ktru_tien_bao_hiem;
                                        hm.tien_thue = hm.tien_thue;
                                        hm.nguyen_nhan = hm.nguyen_nhan;
                                        hm.ghi_chu = ghi_chu;

                                        break;
                                    }
                            }
                        }
                        #endregion
                    }
                }
            }
            #endregion
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Dong_y_duyet_khac()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_KHAC_HS_PHE_DUYET_NH, json, "/api/esmartclaim/approve-other");
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Huy_dong_y_duyet_khac()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_KHAC_HS_PHE_DUYET_XOA, json, "/api/esmartclaim/unapprove-other");
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> TuChoiDuyetKhac()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew<int?, out_value_phe_duyet>(StoredProcedure.PBH_BT_KHAC_HS_PHE_DUYET_TU_CHOI, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> lichSuTrinhDuyetKhac()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_KHAC_HS_TRINH_DUYET_LICH_SU_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> capNhatNguoiXuLyMoi()
        {
            var rq = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_KHAC_HS_CAP_NHAT_NGUOI_XU_LY, rq);
            return Ok(data);
        }
    }
}

