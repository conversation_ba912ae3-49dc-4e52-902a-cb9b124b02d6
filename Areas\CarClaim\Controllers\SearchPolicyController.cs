﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.COMMON.Http;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.CarClaim.Controllers
{
    [Area("CarClaim")]
    [SystemAuthen]
    public class SearchPolicyController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// Tìm kiếm xe
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> SearchPolicy()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var urlApi = "/api/esmartclaim/excute";
            if (AppSettings.ConnectApiCorePartner)
                urlApi = "/api/partner/policy/history";
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_GD_TIM_XE, json, urlApi);
            return Ok(data);
        }
    }
}