﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Khóa dữ liệu";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Khóa dữ liệu</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Khóa dữ liệu</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">                       
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Chi nhánh</label>
                                <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThemMoi">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewTTKhoaDL" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapTTKhoaDL" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmNhapTTKhoaDL" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Khóa dữ liệu <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required="" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Chi nhánh</label>
                                <select class="select2 form-control custom-select" required name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required="" name="nv" style="width: 100%; height:36px;">
                                    <option value="">Chọn nghiệp vụ</option>
                                    <option value="HD">Hợp đồng</option>
                                    <option value="BTXE">Bồi thường xe cơ giới</option>
                                    <option value="BTNG">Bồi thường sức khỏe</option>
                                    <option value="TT">Thanh toán</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Ngày khóa</label>
                                <div class="input-group">
                                    <input type="text" autocomplete="off" required="" class="form-control datepicker" name="ngay_khoa" display-format="date" value-format="number" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuTTKhoaDL"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaTTKhoaDL"><i class="fas fa-trash-alt"></i> Xóa</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/LockDataService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/LockData.js" asp-append-version="true"></script>
}
