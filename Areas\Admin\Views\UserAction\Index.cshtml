﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Ghi Log người sử dụng";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Log người sử dụng</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Log người sử dụng</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin tên người sử dụng" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đơn vị</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Chi nhánh</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Hành động</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="loai_hd" style="width: 100%; height:36px;">
                                    <option value="">Chọn loại hành động</option>
                                    <option value="GET">Đọc dữ liệu</option>
                                    <option value="POST">Thêm - sửa dữ liệu</option>
                                    <option value="DELETE">Xóa dữ liệu</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-1" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-80" title="Tìm kiếm" id="btnSearchHD">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:12px;">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewAction" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalLogNSD" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmUser" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Log người sử dụng</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body" style="padding-top:0px;">
                    <div class="row" id="ThongTinLogNSD"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div>

<partial name="_Template.cshtml" />

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserActionService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/UserAction.js" asp-append-version="true"></script>
}