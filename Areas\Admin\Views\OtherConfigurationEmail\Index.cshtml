﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình nhóm email chi tiết";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình nhóm email chi tiết</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình gửi nhóm email chi tiết</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập mã/tên" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="nv">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height: 36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="C">Có sử dụng</option>
                                    <option value="K">Không sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-20p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-20p" title="Thêm mới" id="btnNhapThongTinCode">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top: 3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewCode" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapCode" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmSaveCode" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin nhóm email <span id="modal-user-log" style="font-size: 14px; margin-left: 10px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    @* <input type="hidden" name="bt" value="BT" /> *@
                    @* <input type="hidden" class="floating-input" name="bt" /> *@
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Mã nhóm</label>
                                <input type="text" maxlength="250" placeholder="Nhập mã nhóm" autocomplete="off" name="ma_nhom" required class="form-control text">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Tên nhóm</label>
                                <input type="text" maxlength="250" placeholder="Nhập tên nhóm" autocomplete="off" name="ten_nhom" required class="form-control text">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Số tiền</label>
                                <input type="text" maxlength="250" placeholder="Số tiền" autocomplete="off" name="so_tien" required class="form-control number">
                            </div>
                        </div> 
                        
                    </div>
                    <div class="row" style="margin-top:5px">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="C">Có sử dụng</option>
                                    <option value="K">Không sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">STT</label>
                                <input type="text" maxlength="1000" placeholder="Nhập stt" autocomplete="off" name="stt" required class="form-control number">
                            </div>
                        </div>

                    </div>
                    <div class="table-responsive mt-2">
                        <table id="tableEmailCC" class="table table-bordered fixed-header">
                            <thead class="font-weight-bold text-center uppercase fixed-header" style="color:white;background-color:var(--escs_theme_color)">
                                <tr>
                                    <td class="align-middle" style="width:30px">STT</td>
                                    <td class="align-middle">Tên</td>
                                    <td class="align-middle">Email gửi</td>
                                    <td class="align-middle">Email CC</td>
                                    <td class="align-middle">Email BCC</td>
                                    <td class="align-middle"></td>

                                </tr>
                            </thead>
                            <tbody id="tblThongTinEmailCC">
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="2">
                                        <a href="javascript:void(0)" id="" onclick="themNguoiCC(this)" data-backdrop="static" data-keyboard="false">
                                            <i class="fas fa-plus-square mr-1"></i> Thêm người gửi
                                        </a>
                                    </td>
                                    <td><p id="" style="font-weight:bold;text-align:right"></p></td>
                                    <td colspan="3"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                </div>
                <div class="modal-footer" style="display: block; padding: 10px 5px;">
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80 d-none" id="btnDeleteCode"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnSaveCode"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
<partial name="_Template.cshtml" />
@section Scripts {
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/OtherConfigurationEmailService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/services/ConfigEmailService.js"></script>
    <script src="~/js/app/Admin/OtherConfigurationEmail.js" asp-append-version="true"></script>
}


