﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục mã ngân hàng";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Hệ thống ngân hàng</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Ngân hàng</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="tim">Thông tin tìm kiếm ngân hàng</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin mã ngân hàng/tên ngân hàng" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="tim_chi_nhanh">Thông tin tìm kiếm chi nhánh</label>
                                <input type="text" name="tim_chi_nhanh" id="tim_chi_nhanh" autocomplete="off" placeholder="Nhập thông tin mã chi nhánh/tên chi nhánh" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3 d-none">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="1">Đang sử dụng</option>
                                    <option value="0">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapThongTinNganHang">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcelBankList">
                                <i class="fas fa-download"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                <i class="fas fa-upload"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewMaNganHang" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapNganHang" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinNganHang" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin ngân hàng <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã ngân hàng</label>
                                <div class="input-group">
                                    <input type="text" maxlength="20" class="form-control" required="" autocomplete="off" name="ma_ngan_hang" placeholder="Mã ngân hàng">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên ngân hàng</label>
                                <input type="text" maxlength="250" name="ten_ngan_hang" autocomplete="off" required class="form-control" placeholder="Tên ngân hàng">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 5px;">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Mã chi nhánh</label>
                                <input type="text" maxlength="20" name="ma_chi_nhanh" autocomplete="off" class="form-control" placeholder="Mã chi nhánh">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên chi nhánh</label>
                                <input type="text" maxlength="250" name="ten_chi_nhanh" autocomplete="off" required class="form-control" placeholder="Tên chi nhánh">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="0">Ngừng sử dụng</option>
                                    <option value="1">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 5px;">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã ngân hàng kế toán</label>
                                <input type="text" maxlength="200" required name="ma_ngan_hang_kt" autocomplete="off" class="form-control" placeholder="Mã ngân hàng kế toán">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btnAction btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinNganHang"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btnAction btn btn-primary btn-sm wd-80" id="btnCopyThongTinNganHang"><i class="fas fa-copy"></i> Copy</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 ml-auto" id="btnLuuThongTinNganHang"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div>
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BankListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/BankList.js" asp-append-version="true"></script>
}

