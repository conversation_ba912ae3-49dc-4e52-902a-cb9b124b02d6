﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục tra cứu giá";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Tra cứu giá</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Tra cứu giá</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin tìm kiếm" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Tỉnh thành</label>
                                <select class="select2 form-control custom-select" name="tinh_thanh" style="width:100%">
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Quận huyện</label>
                                <select class="select2 form-control custom-select" name="quan_huyen" style="width:100%">
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Thay thế sửa chữa</label>
                                <select class="select2 form-control custom-select" name="thay_the_sc" style="width:100%">
                                    <option value="">Chọn</option>
                                    <option value="T">Thay thế</option>
                                    <option value="S">Sửa chữa</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Chính hãng</label>
                                <select class="select2 form-control custom-select" name="chinh_hang" style="width:100%">
                                    <option value="">Chọn</option>
                                    <option value="C">Chính hãng</option>
                                    <option value="K">Sửa chữa</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top:21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px;">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewTraCuuGia" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bs-example-modal-lg" id="modalNhapTraCuuGia" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmTraCuuGia" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Danh sách hồ sơ</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body" style="padding-top:0px;">
                    <div class="row" id="thongTinDsHoSo"></div>
                    <div class="row">
                        <div class="col-12">
                            <div class="table-responsive" style="max-height:40vh">
                                <div class="table-responsive">
                                    <div id="gridViewDsHoSo" class="table-app"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div>
<partial name="_Template.cshtml" />
@section Scripts{
    <script src="~/js/app/Admin/services/CategoryvehicleListService.js" asp-append-version="true"></script>
    <script src="~/js/app/CarClaim/services/AveragePriceService.js" asp-append-version="true"></script>
    <script src="~/js/app/CarClaim/AveragePrice.js" asp-append-version="true"></script>
}