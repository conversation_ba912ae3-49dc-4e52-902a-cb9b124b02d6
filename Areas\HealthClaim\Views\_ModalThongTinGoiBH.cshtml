﻿<div class="modal fade show" id="modalXemThongTinGoiBH" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document" style="max-width: 95vw">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="titleXemThongTinQuyenLoiLSTT">Thông tin gói bảo hiểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body px-2 py-0">
                <div class="row">
                    <div class="col-12">
                        <div class="card" style="margin-bottom:0px; min-height:600px">
                            <div class="card mb-0">
                                <div class="card-body" style="padding:0px">
                                    <div class="border">
                                        <form name="ChonGoiBH" method="post">
                                            <div class="row">
                                                <div class="col-3">
                                                    <div class="form-group">
                                                        <label>Tên khách hàng</label>
                                                        <input type="text" autocomplete="off" name="ten_ndbh" required class="form-control" placeholder="Tên khách hàng">
                                                    </div>
                                                </div>
                                                <div class="col-sm-2">
                                                    <div class="form-group">
                                                        <label class="_required">Ngày sinh</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control datepicker" autocomplete="off" required display-format="date" value-format="number" name="ngay_sinh" placeholder="mm/dd/yyyy">
                                                            <div class="input-group-append">
                                                                <span class="input-group-text"><span class="ti-calendar"></span></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-2">
                                                    <div class="form-group">
                                                        <label class="_required">Giới tính</label>
                                                        <select class="select2 form-control custom-select" required name="gioi_tinh" style="width: 100%; height:36px;">
                                                            <option selected value="">Chọn giới tính</option>
                                                            <option value="NAM">Nam</option>
                                                            <option value="NU">Nữ</option>
                                                            <option value="KHAC">Khác</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-2">
                                                    <div class="form-group">
                                                        <label>Sản phẩm</label>
                                                        <select class="select2 form-control custom-select" name="san_pham" style="width:100%">
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="form-group">
                                                        <label>Gói bảo hiểm</label>
                                                        <select class="select2 form-control custom-select" name="goi_bh" style="width:100%">
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <div class="tab-pane px-0" id="tabQuyenLoiGoiBH" role="tabpanel">
                                                        <div class="table-responsive" style="max-height:72vh">
                                                            <table class="table table-bordered" style="border-collapse: separate; border-spacing: 0;">
                                                                <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0;">
                                                                    <tr>
                                                                        <th rowspan="2" style="text-align:center; width:400px; vertical-align:middle;">Quyền lợi</th>
                                                                        <th colspan="6" style="text-align:center">Quyền lợi bảo hiểm gốc</th>
                                                                        <th colspan="2" style="text-align:center">Quyền lợi đã sử dụng</th>
                                                                        <th colspan="3" style="text-align:center">Quyền lợi ở core</th>
                                                                    </tr>
                                                                    <tr class="text-center uppercase">
                                                                        <th>Số lần(ngày)/năm</th>
                                                                        <th>Số tiền/ngày</th>
                                                                        <th>Số tiền/năm</th>
                                                                        <th>Tỷ lệ đồng</th>
                                                                        <th>T.Gian chờ</th>
                                                                        <th>Phí</th>
                                                                        <th>Số lần(ngày)</th>
                                                                        <th>Quyền lợi/năm</th>
                                                                        <th>Quyền lợi</th>
                                                                        <th>Loại</th>
                                                                        <th>Loại q</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="tblDanhSachQuyenLoiGoiBH">
                                                                </tbody>
                                                                <tfoot>
                                                                    <tr>
                                                                        <td>
                                                                            <a href="#" id="themQLoiBH">
                                                                                <i class="fas fa-plus-square mr-2"></i>Thay đổi quyền lợi bảo hiểm
                                                                            </a>
                                                                        </td>
                                                                        <td class="text-right" colspan="10">
                                                                        </td>
                                                                    </tr>
                                                                </tfoot>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 py-2">
                        <button type="button" class="btn btn-primary btn-sm wd-90 ml-2 float-right" data-dismiss="modal">
                            <i class="fas fa-window-close mr-2"></i>Đóng
                        </button>
                        <button type="button" class="btn btn-primary btn-sm wd-100 ml-2 float-right" id="btnLuuDongQlGoiNDBH">
                            <i class="fas fa-hdd mr-2"></i>Lưu đóng
                        </button>
                        <button type="button" class="btn btn-primary btn-sm wd-80 ml-2 float-right" id="btnLuuQlGoiNDBH">
                            <i class="fas fa-save mr-2"></i>Lưu
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblDanhSachQuyenLoiGoiBHTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr class="tblDanhSachQuyenLoiGoiBHItem" id="quyen_loi_goi_bh_<%- item.lh_nv.replace('.', '') %>">
        <% if(item.pd==15){ %>
        <td style="padding-left: <%- item.pd %>px">
            <b style="font-weight:bold">
                <%- item.ten_hien_thi %>
            </b>
        </td>
        <% } else { %>
        <td style="padding-left: <%- item.pd %>px">
            <%- item.ten_hien_thi %>
        </td>
        <% } %>
        <td style="font-weight: bold" class="text-center">
            <input type="hidden" data-field="pd" value="<%- item.pd %>" />
            <input type="hidden" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <input type="hidden" data-field="lh_nv_ct" value="<%- item.lh_nv_ct %>" />
            <input type="hidden" data-field="ten" value="<%- item.ten %>" />
            <input type="hidden" data-field="ten_hien_thi" value="<%- item.ten_hien_thi %>" />
            <input type="text" data-field="so_lan_ngay" class="floating-input number" value="<%- item.so_lan_ngay %>" />
        </td>
        <td style="font-weight: bold" class="text-right">
            <input type="text" data-field="tien_lan_ngay" class="floating-input number" value="<%- ESUtil.formatMoney(item.tien_lan_ngay) %>" />
        </td>
        <td style="font-weight: bold" class="text-right">
            <input type="text" data-field="tien_nam" class="floating-input number" value="<%- ESUtil.formatMoney(item.tien_nam) %>" />
        </td>
        <td style="font-weight: bold" class="text-center">
            <input type="text" data-field="dong_bh" class="floating-input number" value="<%- item.dong_bh %>" />
        </td>
        <td style="font-weight: bold" class="text-center">
            <input type="text" data-field="so_ngay_cho" class="floating-input number" value="<%- item.so_ngay_cho %>" />
        </td>
        <td style="font-weight: bold" class="text-center">
            <input type="text" data-field="phi" class="floating-input number" value="<%- ESUtil.formatMoney(item.phi) %>" />
        </td>
        <td style="font-weight: bold" class="text-center">
            <input type="text" data-field="so_lan_ngay_duyet" class="floating-input number" value="<%- item.so_lan_ngay_duyet %>" />
        </td>
        <td style="font-weight: bold" class="text-right">
            <input type="text" data-field="tien_nam_duyet" class="floating-input number" value="<%- ESUtil.formatMoney(item.tien_nam_duyet) %>" />
        </td>
        <td class="text-center">
            <input disabled="disabled" type="text" data-field="quyen_loi" class="floating-input" value="<%- item.quyen_loi %>" />
        </td>
        <td class="text-center">
            <input disabled="disabled" type="text" data-field="loai" class="floating-input" value="<%- item.loai %>" />
        </td>
        <td class="text-center">
            <input disabled="disabled" type="text" data-field="loaiq" class="floating-input" value="<%- item.loaiq %>" />
        </td>
        @*<td>
            <a href="#" onclick="xoaQLoi(this)" data-lhnv="<%- item.lh_nv %>">
                <i class="fas fa-trash-alt" title="Xóa quyền lợi"></i>
            </a>
        </td>*@
    </tr>
    <% }) %>
    <% if(data.length < 11){ %>
    <% for(var i = 0; i < 11 - data.length; i++ ){ %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% } %>
    <% } %>
</script>

<div id="modalQuyenLoiBH" class="modal-drag" style="width:500px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn quyền lợi bảo hiểm</span> <span data-dismiss="modal-drag" style="margin-right:10px;" id="closeModalQloi"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_QuyenLoiBH" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalQuyenLoiBHElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalQuyenLoiBHDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer" style="height: 32px; border-top: 1px solid #eaeaea; margin:0px 0px 10px 0px;">
        <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnChonQuyenLoiBH">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalQuyenLoiBHDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsql" id="dsql_<%- item.lh_nv %>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="quyen_loi_<%- item.lh_nv %>" value="<%- item.lh_nv %>" class="custom-control-input modalChonQuyenLoiBHItem">
        <label class="custom-control-label" style="cursor:pointer;padding-left: <%- item.pd %>px" for="quyen_loi_<%- item.lh_nv %>"><%- item.ten_hien_thi %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

