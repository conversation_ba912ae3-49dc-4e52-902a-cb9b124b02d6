﻿<div id="modalNhapKyThanhToan" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width: 45%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin kỳ thanh toán</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body py-0">
                <div class="row mt-2">
                    <div class="col-12 p-0">
                        <div class="table-responsive px-3 mt-2" style="max-height:300px;">
                            <table class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center">
                                        <th style="width: 5%">STT</th>
                                        <th><PERSON><PERSON> thanh toán</th>
                                        <th><PERSON><PERSON><PERSON> than<PERSON> toán</th>
                                        <th><PERSON>ố tiền</th>
                                        <th>Số tiền thanh toán</th>
                                    </tr>
                                </thead>
                                <tbody id="tblDanhSachKyThanhToan">
                                </tbody>
                            </table>
                        </div>
                        <div class="row w-100 m-0 mt-2">
                            <div class="col-12">
                                <div id="tblDanhSachKyThanhToan_pagination"></div>
                            </div>
                        </div>
                        <hr style="margin: 5px 0px;" />
                        <div class="row w-100 m-0">
                            <div class="col-12 py-2">
                                <button type="button" class="btn-outline-primary btn-sm wd-85 float-right" data-dismiss="modal">
                                    <i class="fas fa-window-close mr-2"></i>Đóng
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-95 float-right mr-2" id="btnCapNhatKyThanhToan">
                                    <i class="fas fa-sync-alt mr-2"></i>Cập nhật
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblDanhSachKyThanhToan_template">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-center">
            <%- index + 1 %>
        </td>
        <td class="text-center">
            <%- item.ky_tt_hthi %>
        </td>
        <td class="text-center">
            <%- item.ngay_tt_hthi %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien_da_tt) %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="6">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>