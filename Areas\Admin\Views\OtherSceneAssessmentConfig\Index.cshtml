﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình đánh giá hiện trường";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình đánh giá hiện trường</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình đánh giá hiện trường</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <input type="hidden" name="ma_doi_tac" />
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="" class="form-control">
                            </div>
                        </div>
                        <div class="col-6 col-md-3 col-lg-2 ml-auto" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-48p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-49p" title="Thêm mới" id="btnNhap">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridView" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhap" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="min-width:85vw;">
        <div class="modal-content col-md-11" style="margin-left: 55px;">
            <form name="frmLuu" method="post">
                <div class="modal-header" style="padding: 10px 5px;">
                    <h4 class="modal-title">Nhập cấu hình</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body" style="padding: 10px 5px;">
                    <input type="hidden" name="ma_doi_tac" />
                    <div class="row" style="user-select:none;">
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Mã loại</label>
                                <input type="text" class="form-control" name="loai" required />
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Tên loại</label>
                                <input type="text" class="form-control" name="ten_loai" required />
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Kiểu</label>
                                <select class="select2 form-control" name="kieu" style="width: 100%; height:36px;" required></select>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Giao diện</label>
                                <select class="select2 form-control" name="kieu_loai" style="width: 100%; height:36px;" required></select>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="">Thứ tự</label>
                                <input type="number" class="form-control" name="loai_stt" />
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Bắt buộc nhập</label>
                                <select class="select2 form-control" name="bat_buoc_nhap" style="width: 100%; height:36px;" required></select>
                            </div>
                        </div>

                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control" name="nghiep_vu" style="width: 100%; height:36px;" required></select>
                            </div>
                        </div>


                        <div class="col-12 mt-2">
                            <label class="mb-1">Cài đặt</label>
                            <div class="table-responsive" style="height:314px;" id="modalNhap_table_container">
                                <table class="table table-bordered">
                                    <thead class="bg-primary text-white text-nowrap text-center">
                                        <tr>
                                            <th scope="col">STT</th>
                                            <th scope="col">Mã</th>
                                            <th scope="col">Tên</th>
                                            <th scope="col">Trạng thái</th>
                                            <th scope="col">Hiển thị</th>
                                            <th scope="col" colspan="2">
                                                <a href="javascript:modalNhap_toggleForm(true);" class="text-white"><i class="fas fa-plus"></i></a>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="modalNhap_tbody" class="align-middle">
                                    </tbody>
                                </table>
                            </div>
                            <div style="height:314px;" id="modalNhap_subform" class="d-none">
                                <div class="row">
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="">Thứ tự</label>
                                            <input type="number" class="form-control" name="dg_stt" />
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="_required">Mã cấu hình</label>
                                            <input type="text" class="form-control" name="ma" required />
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="_required">Tên cấu hình</label>
                                            <input type="text" class="form-control" name="ten" required />
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="_required">Trạng thái</label>
                                            <select class="select2 form-control" name="trang_thai" style="width: 100%; height:36px;" required></select>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="_required">Hiển thị</label>
                                            <select class="select2 form-control" name="an_hien" style="width: 100%; height:36px;" required></select>
                                        </div>
                                    </div>
                                    <div class="col-4" style="padding-top: 21px;">
                                        <button class="btn btn-secondary btn-sm" type="button" onclick="javascript:modalNhap_toggleForm(false);">Hủy</button>
                                        <button class="btn btn-primary btn-sm" type="button" onclick="javascript:modalNhap_updateRow();">Xác nhận</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="padding: 10px 5px; display: block;">
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinConfig"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinConfig"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/OtherSceneassessmentConfigService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/OtherSceneassessmentConfig.js" asp-append-version="true"></script>
}