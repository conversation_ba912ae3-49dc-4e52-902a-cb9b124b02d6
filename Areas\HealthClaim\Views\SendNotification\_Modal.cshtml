﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div id="modalThemTB" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h3 class="modal-title">Thông báo gửi tới cơ sở y tế</h3>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-2">
                <form name="frmThemTB">
                    <input type="hidden" name="bt" value="" />
                    <div class=" row ">
                        <div class="col col-12">
                            <div class="form-group">
                                <label class="_required" for="ngay_d">Tiêu đề</label>
                                <input type="text" class="form-control" autocomplete="off" name="tieu_de" required placeholder="Nhập tiêu đề của thông báo">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required">Đối tượng nhận:</label>
                                <select class="select2 form-control custom-select" name="loai" required style="width:100%">
                                    <option value="" selected>Chọn loại đối tượng</option>
                                    <option value="CA_NHAN">Cá nhân</option>
                                    <option value="CSYT" selected>Cơ sở y tế</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required" for="ngay_d">Cơ sở y tế nhận thông báo</label>
                                <input type="text" class="form-control" autocomplete="off" name="csyt" required placeholder="Chọn csyt " onclick="chonBV(this)">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required" for="ngay_d">Cấp độ</label>
                                <select class="select2 form-control custom-select" name="cap_do" required style="width:100%">
                                    <option value="" selected>Chọn cấp đô</option>
                                    <option value="1">Cấp 1</option>
                                    <option value="2">Cấp 2</option>
                                    <option value="3">Cấp 3</option>
                                    <option value="4">Cấp 4</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-12 ">
                            <label class="_required" for="noi_dung" >Nội dung thông báo</label>
                            <textarea class="form-control" rows="20" cols="100" name="noi_dung" required placeholder="Nhập nội dung thông báo"> </textarea>
                        </div>
                    </div>
                </form>                
            </div>
            <div class="modal-footer p-2">
                <button type="button" class="btn btn-primary btn-sm  mg-t-22" id="btnGuiTB">
                    <i class="fa fa-save mr-2"></i>Gửi thông báo
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuu">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDong">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalBV" class="modal-drag" style="width:450px; z-index:9999999;  margin-top: 5px !important; margin-left: -11px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn CSYT nhận thông báo</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_BV" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalBV_ElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalBV_DanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonBV">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalBV_DanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma%>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="bv_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalBV_Item">
        <label class="custom-control-label" style="cursor:pointer;" for="bv_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
