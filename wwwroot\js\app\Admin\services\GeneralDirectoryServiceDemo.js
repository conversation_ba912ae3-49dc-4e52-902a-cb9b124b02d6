﻿function GeneralDirectoryServiceDemo() {
    var _service = new Service();

    //L<PERSON><PERSON> danh mục chung theo mã đối tác nsd
    this.layDsBoMaChung = function () {
        return _service.postData("/admin/generaldirectorydemo/getall", {});
    };

    //L<PERSON><PERSON> thông tin bộ mã chung CSYT
    this.saveCSYT = function (obj) {
        return _service.postData("/admin/generaldirectorydemo/save", obj);
    };

    //Xóa thông tin bộ mã chung CSYT
    this.deleteCSYT = function (obj) {
        return _service.postData("/admin/generaldirectorydemo/delete", obj);
    };

    //Tìm kiếm + phân trang bộ mã chung CSYT
    this.timKiemPTrang = function (obj) {
        return _service.postData("/admin/generaldirectorydemo/getpaging", obj);
    };

    //L<PERSON><PERSON> thông tin chi tiết bộ mã chung CSYT
    this.layThongTinChiTiet = function (obj) {
        return _service.postData("/admin/generaldirectorydemo/getdetail", obj);
    };
}