﻿function GeneralDirectoryServiceDemo() {
    // Tạo instance của Service base class để gọi API
    var _service = new Service();

    //Lấy danh mục chung theo mã đối tác nsd
    this.layDsBoMaChung = function () {
        return _service.postData("/admin/demogeneraldirectory/getall", {});
    };
    //Lưu thông tin bộ mã chung CSYT
    this.saveCSYT = function (obj) {
        // obj: object chứa dữ liệu form
        // Gọi API POST đến controller action Save
        return _service.postData("/admin/demogeneraldirectory/save", obj);
    };
    //Xóa thông tin bộ mã chung CSYT
    this.deleteCSYT = function (obj) {
        // obj: object chứa thông tin record cần xóa
        return _service.postData("/admin/demogeneraldirectory/delete", obj);
    };

    //Tìm kiếm + phân trang bộ mã chung CSYT
    this.timKiemPTrang = function (obj) {
        // obj: object chứa điều kiện tìm kiếm + thông tin phân trang
        return _service.postData("/admin/demogeneraldirectory/getpaging", obj);
    };

    //Lấy thông tin chi tiết bộ mã chung CSYT
    this.layThongTinChiTiet = function (obj) {
        // obj: object chứa điều kiện tìm kiếm + thông tin phân trang
        return _service.postData("/admin/demogeneraldirectory/getdetail", obj);
    };
}