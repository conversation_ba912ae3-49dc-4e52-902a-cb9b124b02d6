﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình nhóm báo cáo";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình nhóm báo cáo</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình nhóm báo cáo</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-2">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" spellcheck="false" placeholder="" class="form-control">
                            </div>
                        </div>
                        <div class="col-2" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-48p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search mr-2"></i>Tìm kiếm
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" title="Thêm mới" id="btnNhap">
                                <i class="fa fa-plus mr-2"></i>Thêm
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewPhanTrang" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalNhap" tabindex="-1">
    <div class="modal-dialog modal-xl" style="max-width:calc( 992px - 1rem ); height: 400px">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thông tin nhóm báo cáo</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form name="frmSave" method="post" class="row">
                    <input type="hidden" name="bt" />
                    <div class="col-3">
                        <div class="form-group">
                            <label class="_required">Đối tác</label>
                            <select data-group-select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;" required></select>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <label style="color: #424D54; font-weight: 600; cursor: pointer;">Chi nhánh</label>
                            <div class="input-group">
                                <input type="text" name="ma_chi_nhanh" data-val="" style="cursor: pointer; background-color: #e9ecef;" class="form-control btnTimKiemChiNhanh" autocomplete="off" placeholder="Click chọn chi nhánh">
                                <div class="input-group-append">
                                    <label class="input-group-text">
                                        <a href="javascript:void(0)">
                                            <i class="fas fa-search" title="Chọn chi nhánh"></i>
                                        </a>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <label class="_required">Tên nhóm</label>
                            <input type="text" name="ten" autocomplete="off" class="form-control" required>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <label class="_required">Trạng thái</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;" required>
                                <option value="">Chọn trạng thái</option>
                                <option value="D">Đang sử dụng</option>
                                <option value="K">Không sử dụng</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-6">
                        <label class="mb-1">Danh sách mẫu báo cáo</label>
                        <div class="card border m-0 overflow-hidden" style="height:400px;">
                            <div class="card-body p-2 overflow-auto scrollable">
                                <div class="row mx-n2" id="mau_bc">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <label class="mb-1">Danh sách người dùng</label>
                        <div class="d-flex flex-column" style="height:400px;">
                            <div class="mb-2">
                                <input type="text" class="form-control" placeholder="Tìm kiếm" name="tim"/>
                            </div>
                            <div class="card border m-0 overflow-hidden flex-fill">
                                <div class="card-body p-2 overflow-auto scrollable">
                                    <div class="row mx-n2" id="nsd">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer d-block">
                <button type="button" class="btn btn-outline-primary btn-sm wd-80 mr-auto" id="btnXoaThongTin">
                    <i class="fas fa-trash-alt mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 float-right" id="btnLuuDongThongTin">
                    <i class="fas fa-save mr-2"></i>Lưu và đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80 float-right" id="btnLuuThongTin">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalTimKiemChiNhanh" class="modal-drag" style="width:350px; z-index: 9999999; margin-top: 5px !important; margin-left: -43px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn đơn vị báo cáo</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" value="" onkeyup="onFocusTimKiemChiNhanh(this)" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control" id="timKiemChiNhanh">
            </div>
            <div class="col-12 mt-2 scrollable streeHeight" style="max-height: 200px;" id="treeChiNhanh"></div>
        </div>
    </div>
    <div class="modal-drag-footer" style="border-top: 1px solid #e9ecef;">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="ModalTimKiemChiNhanh_btnChonChiNhanh">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

@section Styles{
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" asp-append-version="true" />
}
@section Scripts{
    <script src="~/libs/tree-js/vakata-jstree/dist/jstree.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PrintedService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ReportGroupService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ReportGroup.js" asp-append-version="true"></script>
}