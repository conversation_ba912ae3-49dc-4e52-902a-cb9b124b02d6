﻿<style>
    #modalCanBoDanhSach label:hover {
        color: var(--escs-main-theme-color);
    }
</style>
<div id="modalCanBo" class="modal-drag" style="width: 450px; z-index: 9999999; margin-top: 5px !important; margin-left: -10px !important;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn cán bộ</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <div class="input-group">
                    <input id="inputSearch_CanBo" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                    <input type="hidden" id="modalCanBoElementSelect">
                    <div class="input-group-append">
                        <label class="input-group-text">
                            <a href="javascript:void(0)" onclick="getPagingCanBo(1)">
                                <i class="fa fa-search"></i>
                            </a>
                        </label>
                    </div>
                </div>                
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:400px;" id="modalCanBoDanhSach">

            </div>
            <div id="modalCanBoDanhSach_pagination"></div>
        </div>
    </div>
    @*<div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonCanBo">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>*@
</div>

<script type="text/html" id="modalCanBoDanhSachTemplate">
    <% if(danh_sach_can_bo.length > 0){
    _.forEach(danh_sach_can_bo, function(item,index) {
        %>
    <% var ma = danh_sach_can_bo[index].ma.trim().replace(/[^a-zA-Z0-9]/g, '') %>
    <div class="custom-control custom-checkbox dscb" id="dscb_<%- item.ma.trim().replace(/[^a-zA-Z0-9]/g, '') %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="can_bo_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-canbo modalCanBoItem single_checked" onchange="onChonCanBo(this)">
        <label class="custom-control-label" style="cursor:pointer;" for="can_bo_<%- item.ma %>"><%- item.ma.toLowerCase() %> - <%- item.ten %></label>
           @* <% if(item.ma_nsd_doi_tac == null)
            {
                %><input type="checkbox" id="can_bo_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-canbo modalCanBoItem single_checked" onchange="onChonCanBo(this)">
                <label class="custom-control-label" style="cursor:pointer;" for="can_bo_<%- item.ma %>"><%- item.ma.toLowerCase() %> - <%- item.ten %></label><%
            }
            else
            {
                %><input type="checkbox" id="can_bo_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-canbo modalCanBoItem single_checked" onchange="onChonCanBo(this)">
                <label class="custom-control-label" style="cursor:pointer;" for="can_bo_<%- item.ma %>"><%- item.ma + " - " + item.ma_nsd_doi_tac %></label><%
            }
        %>*@
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>


<div id="modalPhuongThuc" class="modal-drag" style="width: 400px; z-index: 9999999; margin-top: 5px !important; margin-left: -10px !important;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn phương thức khai thác</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_PhuongThuc" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalPhuongThucElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:260px;" id="modalPhuongThucDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonPhuongThuc">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

@*<script type="text/html" id="modalPhuongThucDanhSachTemplate">
    <% if(pt_kt.length > 0){
    _.forEach(pt_kt, function(item,index) { %>
    <% var ma = pt_kt[index].ma.trim().replace(/[^a-zA-Z0-9]/g, '') %>
    <div class="custom-control custom-checkbox " id="<%- item.ma %>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id=pt_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalPhuongThucItem ">
        <label class="custom-control-label" style="cursor:pointer;" for="pt_<%- item.ma %>"><%- item.ten  %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>*@

<script type="text/html" id="modalPhuongThucDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten %>">
        <input type="checkbox" name="chon_nguoi_pt" id="pt_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalPhuongThucItem " data-val="<%- item.ten%>">
        <label class="custom-control-label" style="cursor:pointer;" for="pt_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>