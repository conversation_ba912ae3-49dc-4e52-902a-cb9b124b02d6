﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình phân công xử lý sức khỏe";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<input type="hidden" id="notify_info" value="@TempData[ESCS.COMMON.Contants.ESCSConstants.NOTIFY_INFO]" />
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <!-- Row -->
    <div class="row">
        <!-- Column -->
        <div class="col-lg-12 col-md-12 pb-15">
            <div class="card">
                <div class="card-body" style="padding-top:5px;">
                    <form name="frmSearch" method="post">
                        <div class="row">
                            <div class="col col-1">
                                <div class="form-group">
                                    <label for="ngay_d">Ngày tìm kiếm</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" name="ngay_d" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label for="ngay_c">&nbsp;</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" name="ngay_c" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-sm-2">
                                <div class="form-group">
                                    <label for="" class="_required">Đơn vị</label>
                                    <select class="select2 form-control custom-select" name="don_vi" required style="width:100%">
                                        <option value="" selected>Chọn đơn vị</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-sm-2">
                                <div class="form-group">
                                    <label>Nguồn</label>
                                    <select class="select2 form-control custom-select" name="nguon" required style="width:100%">
                                        <option value="">Chọn nguồn</option>
                                        <option value="CSYT">Cơ sở y tế</option>
                                        <option value="PORTAL">Portal</option>
                                        <option value="MOBILE">App khách hàng</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-sm-2">
                                <div class="form-group">
                                    <label>Trạng thái</label>
                                    <select class="select2 form-control custom-select" name="trang_thai" required style="width:100%">
                                        <option value="">Chọn trạng thái</option>
                                        <option value="C">Ngừng sử dụng</option>
                                        <option value="D">Đang sử dụng</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-sm-2">
                                <div class="form-group">
                                    <label for="" >Phân công theo</label>
                                    <select class="select2 form-control custom-select" name="loai" style="width:100%">
                                        <option value="">Chọn </option>
                                        <option value="CA">Phân công theo ca</option>
                                        <option value="KH">Phân công theo khách hàng</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-2" style="padding-top: 21px;">
                                <button type="button" class="btn btn-primary btn-sm wd-80" title="Tìm kiếm" id="btnTimKiem">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-80" title="Thêm mới" id="btnThem">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row" style="margin-top:3px;">
                            <div class="col-12">
                                <div id="gridViewDanhSach" class="table-app" style="height: 64vh;"></div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@section scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/contract/services/healthservice.js" asp-append-version="true"></script>
    <script src="~/js/common/modalservice.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ConfigHealthzoneService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ConfigHealthzone.js" asp-append-version="true"></script>
}

<div class="modal fade bs-example-modal-sm" id="modalCauHinhCa" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 55%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h4 class="modal-title">Phân công xử lý</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form name="frmCauHinhCa" method="post">
                    <input type="hidden" name="bt" spellcheck="false" value="">
                    <input type="hidden" name="ma_doi_tac" spellcheck="false" value="">
                    <div class="row">
                        <div class="col col-6">
                            <div class="form-group">
                                <label for="" class="_required">Phân công theo</label>
                                <select class="select2 form-control custom-select" name="loai" required style="width:100%">
                                    <option value="">Chọn </option>
                                    <option value="CA" selected>Phân công theo ca</option>
                                    <option value="KH">Phân công theo khách hàng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-6">
                            <div class="form-group">
                                <label for="" class="_required">Đơn vị</label>
                                <select class="select2 form-control custom-select" name="don_vi" required style="width:100%">
                                    <option value="" selected>Chọn đơn vị</option>
                                </select>
                            </div>
                        </div>

                        <div class="col col-6">
                            <div class="form-group">
                                <label for="" class="_required">Nguồn</label>
                                <select class="select2 form-control custom-select" name="nguon" required style="width:100%">
                                    <option value="">Chọn nguồn</option>
                                    <option value="CSYT">Cơ sở y tế</option>
                                    <option value="PORTAL">Portal</option>
                                    <option value="MOBILE">App khách hàng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-6">
                            <div class="form-group">
                                <label for="" class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" required style="width:100%">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="C">Ngừng sử dụng</option>
                                    <option value="D">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="divCA">
                        <div class="col-2">
                            <div class="form-group">
                                <label class="_required">Giờ bắt đầu</label>
                                <div class="input-group bootstrap-timepicker timepicker">
                                    <input class="form-control input-small time" name="gio_hl" type="text" tabindex="12" spellcheck="false">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <span class="ti-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Ngày bắt đầu</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" display-format="date" value-format="number" name="ngay_hl" placeholder="mm/dd/yyyy" tabindex="11" spellcheck="false">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label class="_required">Giờ kết thúc</label>
                                <div class="input-group bootstrap-timepicker timepicker">
                                    <input class="form-control input-small time" name="gio_kt" type="text" tabindex="14" spellcheck="false">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <span class="ti-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Ngày kết thúc</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" display-format="date" value-format="number" name="ngay_kt" placeholder="mm/dd/yyyy" tabindex="13" spellcheck="false">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row d-none" id="divKH">
                        <div class="col col-12">
                            <div class="form-group">
                                <label for="" class="_required">Khách hàng</label>
                                <input type="text" autocomplete="off" name="khach_hang" class="form-control" placeholder="Chọn khách hàng" onclick="chonKH(this)" data-val="">
                            </div>
                        </div>
                    </div>
                </form>
                <div class="table-responsive mt-2" style="height:303px">
                    <table id="tablePhanCongTheoCa" class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th style="width:47px"></th>
                                <th style="width: 380px">Mã cán bộ</th>
                                <th style="width: 380px">Tên cán bộ</th>
                            </tr>
                        </thead>
                        <tbody id="tblPhanCongTheoCa">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="LuuPhanCongTheoCa"><i class="fa fa-save mr-2"></i>Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblPhanCongTheoCa_template">
    <% if (can_bo.length > 0) {%>
    <% _.forEach(can_bo, function(item, index) { %>
    <tr data-row="<%- ESUtil.xoaKhoangTrangText(item.ma) %>">
        <td class="">
            <div class="custom-control custom-checkbox ml-2">
                <input type="checkbox" data-field="ma" data-val="<%- item.ma %>" id="cb_<%- index %>" class="custom-control-input input-nsd checkbox canBo">
                <label class="custom-control-label" for="cb_<%- index %>"></label>
            </div>
        </td>
        <td><%- item.ma %></td>
        <td><%- item.ten %></td>
    </tr>
    <% })} %>

    <% if(can_bo.length < 7) {
    for(var i = 0; i < 7 - can_bo.length;i++ ) { %>
    <tr>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<div id="modalKH" class="modal-drag" style="width:500px; z-index:9999999;  margin-top: 5px !important; margin-left: -11px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn khách hàng</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_KH" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalKH_ElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalKH_DanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonKH">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalKH_DanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsnt" id="dsnt_<%- item.ma %>" data-text="<%- item.ten %>">
        <input type="checkbox" id="kh_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalKH_Item">
        <label class="custom-control-label" style="cursor:pointer;" for="kh_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>