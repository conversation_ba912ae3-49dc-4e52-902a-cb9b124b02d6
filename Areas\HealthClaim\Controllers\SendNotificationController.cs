﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.HealthClaim.Controllers
{
    [Area("HealthClaim")]
    [SystemAuthen]
    public class SendNotificationController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }
        /// <summary>
        /// Thêm thông báo quan trọng cho CSYT
        /// </summary> 
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> SaveThongBaoQT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_THONG_BAO_QUAN_TRONG_NH, json);
            return Ok(data);
        }
        /// <summary>
        /// Lke thông báo quan trọng CSYT
        /// </summary> 
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_THONG_BAO_QUAN_TRONG_LKE, json);
            return Ok(data);
        }
        /// <summary>
        /// Lke thông báo quan trọng CSYT
        /// </summary> 
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> GetDSBV()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_NSD_BV_DBL, json);
            return Ok(data);
        }
    }
}
