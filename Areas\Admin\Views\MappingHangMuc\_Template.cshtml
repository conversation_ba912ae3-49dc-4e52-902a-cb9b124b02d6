﻿<script type="text/html" id="HangMuc_template">
    <% if(hang_muc.length > 0){
    _.forEach(hang_muc, function(item,index) { %>
    <tr row-val="<%- item.ma %>" data-search="<%- item.ten.toLowerCase() %>">
        <td><input id='ma_hang_muc_<%- item.ma %>' type="text" name="ma_hang_muc" maxlength="50" col-ma-hang-muc="<%- item.ma %>" class="floating-input" value="<%- item.ma %>" readonly="" /></td>
        <td><input id='ten_hang_muc_<%- item.ma %>' type="text" name="ten_hang_muc" maxlength="50" col-ten-hang-muc="<%- item.ma %>" class="floating-input" value="<%- item.ten %>" readonly="" /></td>
        <td><input id='ma_mapping_ai_<%- item.ma %>' type="text" name="ma_mapping_ai" maxlength="50" col-ma-mapping-ai="<%- item.ma %>" class="floating-input" value="<%- item.ma_mapping_ai %>" /></td>
        <td><input id='ten_mapping_ai_<%- item.ma %>'  type="text" name="ten_mapping_ai" maxlength="200" col-ten-mapping-ai="<%- item.ma %>" class="floating-input" value="<%- item.ten_mapping_ai %>" /></td>
        <td><input id='ten_tat_mapping_ai_<%- item.ma %>' type="text" name="ten_tat_mapping_ai" maxlength="100" col-ten-tat-mapping-ai="<%- item.ma %>" class="floating-input" value="<%- item.ten_tat_mapping_ai %>" /></td>
    </tr>
    <% })}%>
</script>