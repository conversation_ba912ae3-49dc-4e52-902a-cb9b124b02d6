﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục mã phòng ban";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Danh mục phòng ban</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Phòng ban</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập mã/tên phòng ban" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3 d-none">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Chi nhánh</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="0">Ngừng sử dụng</option>
                                    <option value="1">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapThongTinDoiTacPhong">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcelDepartmentList">
                                <i class="fas fa-download"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                <i class="fas fa-upload"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewDoiTacPhong" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapMaDoiTacPhong" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinDoiTacPhong" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin phòng ban <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Chi nhánh</label>
                                <select class="select2 form-control custom-select" required name="ma_chi_nhanh" style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="0">Ngừng sử dụng</option>
                                    <option value="1">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 5px;">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã phòng ban</label>
                                <input type="text" maxlength="20" name="ma" autocomplete="off" required class="form-control" placeholder="Mã phòng ban">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên phòng ban</label>
                                <input type="text" maxlength="250" name="ten" autocomplete="off" required class="form-control" placeholder="Tên phòng ban">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã kế toán</label>
                                <input type="text" maxlength="250" name="ma_ktoan" autocomplete="off" class="form-control" required placeholder="Mã kế toán">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinDoiTacPhong"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    @*<button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinDoiTacPhong"><i class="fas fa-trash-alt"></i> Xóa</button>*@
                </div>
            </form>
        </div>
    </div>
</div>
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DepartmentListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/DepartmentList.js" asp-append-version="true"></script>
}


