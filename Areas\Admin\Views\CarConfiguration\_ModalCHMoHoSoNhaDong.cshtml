﻿<style>
    #tableHangMucGiaTuDong thead tr th {
        padding: 0.3rem !important;
    }

    #tableHangMucGiaTuDong tbody tr td {
        padding: 0.3rem !important;
    }

    .ul_duyet_gia_tu_dong li {
        list-style-type: none;
    }

    .ul_duyet_gia_tu_dong {
        padding: unset;
        margin-top: 15px;
    }

    .modalCHPheDuyetGiaTuDongContent {
        height: 500px;
        overflow: auto;
    }

    .ul_duyet_gia_tu_dong_ngay_ad li {
        list-style-type: none;
    }

    .ul_duyet_gia_tu_dong_ngay_ad {
        padding: unset;
        margin-top: 15px;
    }

    .width-dvi {
        width: 70%;
        float: left;
    }

    .width-tien {
        width: 30%;
        float: left;
    }
</style>
<div id="modalCHMoHoSoNhaDong" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:95%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình mở hồ sơ đối với các nhà đồng</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <div class="row p-2">
                    <div class="col-2" style="padding-right:0px;">
                        <div class="card border mb-0">
                            <div class="card-body p-2" style="height:76vh;overflow-y:auto">
                                <div class="justify-content-between align-items-center p-2 card-title-bg text-center border">
                                    <h6 class="m-0">Ngày áp dụng</h6>
                                </div>
                                <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="modalCHMoHoSoNhaDongNgayAD">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-10">
                        <div class="row px-2">
                            <div class="col-4 pl-0">
                                <div class="card border mb-0">
                                    <div class="card-body p-2">
                                        <div class="justify-content-between align-items-center p-2 card-title-bg text-center border">
                                            <h6 class="m-0">Nhà đồng chặn mở hồ sơ</h6>
                                        </div>
                                        <div class="mt-2">
                                            <input type="text" class="form-control" id="modalCHMoHoSoNhaDongNhaBHTKiem" placeholder="Tìm kiếm nhà đồng" />
                                        </div>
                                        <div style="margin-top:15px; margin-bottom:15px; height: 60vh;overflow-y: auto;" id="modalCHMoHoSoNhaDongNhaBH">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 pl-0">
                                <div class="card border mb-0">
                                    <div class="card-body p-2">
                                        <div class="justify-content-between align-items-center p-2 card-title-bg text-center border">
                                            <h6 class="m-0">Chương trình bảo hiểm chặn mở hồ sơ</h6>
                                        </div>
                                        <div class="mt-2">
                                            <input type="text" class="form-control" id="modalCHMoHoSoNhaDongChuongTrinhBHTKiem" placeholder="Tìm kiếm chương trình bảo hiểm" />
                                        </div>
                                        <div style="margin-top:15px; margin-bottom:15px; height: 60vh;overflow-y: auto;" id="modalCHMoHoSoNhaDongChuongTrinhBH">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 pl-0">
                                <div class="card border mb-0">
                                    <div class="card-body p-2">
                                        <div class="justify-content-between align-items-center p-2 card-title-bg text-center border">
                                            <h6 class="m-0">Người sử dụng cho phép mở</h6>
                                        </div>
                                        <div class="mt-2">
                                            <input type="text" class="form-control" id="modalCHMoHoSoNhaDongDSCanBoTKiem" placeholder="Tìm kiếm cán bộ" />
                                        </div>
                                        <div style="margin-top:15px; margin-bottom:15px; height: 60vh;overflow-y: auto;" id="modalCHMoHoSoNhaDongDSCanBo">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnThemNgayADMoHoSoNhaDong">
                    <i class="fas fa-plus mr-2"></i>Thêm ngày áp dụng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinhMoHoSoNhaDong">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalCHMoHoSoNhaDongNhapNgayAD" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Nhập ngày áp dụng</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <form name="frmCHMoHoSoNhaDongNhapNgayAD" method="post">
                    <div class="row mg-t-6 p-2">
                        <div class="col col-12">
                            <div class="form-group">
                                <label for="ngay_d">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker_min" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.8em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuMoHoSoNhaDongNhapNgayAD">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="modalCHMoHoSoNhaDongNhaBHTemplate">
    <% if(data.length > 0){%>
    <div style="height:21px;">
        <div style="width:50%; float:left;">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="chonTatCaNhaBh(this)" id="********************************" class="custom-control-input">
                <label class="custom-control-label font-weight-bold" for="********************************">Áp dụng cho tất cả</label>
            </div>
        </div>
        <div style="width:50%; float:left; text-align:right;">
            <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input checkbox" onchange="onChkNhaBhAD(this)" id="chkNhaBhAD">
                <label class="custom-control-label" for="chkNhaBhAD">Nhà BH đang áp dụng</label>
            </div>
        </div>
    </div>
    <%
    _.forEach(data, function(item,index) { %>
    <div class="modalCHMoHoSoNhaDongNhaBHItem" data-search="<%- ESUtil.xoaKhoangTrangText(item.ma) %>-<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <div class="custom-control custom-checkbox">
            <% if(item.checked == 'C'){%>
            <input type="checkbox" data_nha_bh="<%- item.ma %>" checked="checked" id="modalCHMoHoSoNhaDongNhaBHItem_<%- item.ma %>" class="custom-control-input">
            <% } else {%>
            <input type="checkbox" data_nha_bh="<%- item.ma %>" id="modalCHMoHoSoNhaDongNhaBHItem_<%- item.ma %>" class="custom-control-input">
            <% }%>
            <label class="custom-control-label" for="modalCHMoHoSoNhaDongNhaBHItem_<%- item.ma %>">[<%- item.ma %>] - <%- item.ten %></label>
        </div>
    </div>
    <% })}%>
</script>

<script type="text/html" id="modalCHMoHoSoNhaDongChuongTrinhBHTemplate">
    <% if(data.length > 0){%>
    <div style="height:21px;">
        <div style="width:50%; float:left;">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="chonTatCaChuongTrinhBH(this)" id="modalCHMoHoSoNhaDongChuongTrinhBHADTatCa" class="custom-control-input">
                <label class="custom-control-label font-weight-bold" for="modalCHMoHoSoNhaDongChuongTrinhBHADTatCa">Áp dụng cho tất cả</label>
            </div>
        </div>
        <div style="width:50%; float:left; text-align:right">
            <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input checkbox" onchange="onChkHienThiChuongTrinhBHAD(this)" id="chkHienThiChuongTrinhBHAD">
                <label class="custom-control-label" for="chkHienThiChuongTrinhBHAD">Chương trình BH áp dụng</label>
            </div>
        </div>
    </div>
     <%
    _.forEach(data, function(item,index) { %>
    <div class="modalCHMoHoSoNhaDongChuongTrinhBHItem" data-search="<%- ESUtil.xoaKhoangTrangText(item.ma) %>-<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <div class="custom-control custom-checkbox">
            <% if(item.checked == 'C'){%>
            <input type="checkbox" data_ctrinh_bh="<%- item.ma %>" checked="checked" id="modalCHMoHoSoNhaDongChuongTrinhBHItem_<%- item.ma %>" class="custom-control-input">
            <% } else {%>
            <input type="checkbox" data_ctrinh_bh="<%- item.ma %>" id="modalCHMoHoSoNhaDongChuongTrinhBHItem_<%- item.ma %>" class="custom-control-input">
            <% }%>
            <label class="custom-control-label" for="modalCHMoHoSoNhaDongChuongTrinhBHItem_<%- item.ma %>">[<%- item.ma %>] - <%- item.ten %></label>
        </div>
    </div>
    <% })}%>
</script>

<script type="text/html" id="modalCHMoHoSoNhaDongDSCanBoTemplate">
    <% if(data.length > 0){%>
    <div style="height:21px;">
        <div style="width:50%; float:left;">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="chonTatCaDSCanBo(this)" id="modalCHMoHoSoNhaDongDSCanBoADTatCa" class="custom-control-input">
                <label class="custom-control-label font-weight-bold" for="modalCHMoHoSoNhaDongDSCanBoADTatCa">Áp dụng cho tất cả</label>
            </div>
        </div>
        <div style="width:50%; float:left; text-align:right">
            <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input checkbox" onchange="onChkHienThiDSCanBoBHAD(this)" id="chkHienThiDSCanBoAD">
                <label class="custom-control-label" for="chkHienThiDSCanBoAD">DS cán bộ áp dụng</label>
            </div>
        </div>
    </div>
     <%
    _.forEach(data, function(item,index) { %>
    <div class="modalCHMoHoSoNhaDongDSCanBoItem" data-search="<%- ESUtil.xoaKhoangTrangText(item.ma) %>-<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <div class="custom-control custom-checkbox">
            <% if(item.checked == 'C'){%>
            <input type="checkbox" data_nsd="<%- item.ma %>" checked="checked" id="modalCHMoHoSoNhaDongDSCanBoItem_<%- item.ma %>" class="custom-control-input">
            <% } else {%>
            <input type="checkbox" data_nsd="<%- item.ma %>" id="modalCHMoHoSoNhaDongDSCanBoItem_<%- item.ma %>" class="custom-control-input">
            <% }%>
            <label class="custom-control-label" for="modalCHMoHoSoNhaDongDSCanBoItem_<%- item.ma %>">[<%- item.ma %>] - <%- item.ten %></label>
        </div>
    </div>
    <% })}%>
</script>

<script type="text/html" id="modalCHMoHoSoNhaDongNgayADTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <a class="nav-link text-center" onclick="layChiTietCHMoHoSoNhaDong('<%- item.ngay_ad %>')" style="cursor:pointer" data_ngay_ad="<%- item.ngay_ad %>" data-toggle="pill" role="tab" aria-selected="true">Ngày áp dụng: <b class="font-weight-bold"><%- item.ngay_ad_hthi %></b></a>
    <% })}%>
</script>
