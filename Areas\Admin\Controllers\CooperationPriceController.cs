﻿using ESCS.Attributes;
using ESCS.COMMON.Common;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class CooperationPriceController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }
        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_GARA_GIA_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_GARA_GIA_LKE_CT, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_GARA_GIA_NH, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Delete()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew("", json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> GetPagingCT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_GARA_GIA_CT_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Upload()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_GARA_GIA_IMPORT, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> uploadTatCa()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_GARA_GIA_CT_IMPORT, json);
            return Ok(data);
        }
        [HttpPost]
        [SystemAuthen]
        public async Task<IActionResult> uploadCauHinh(IFormCollection formData)
        {
            if (!Utilities.IsMultipartContentType(Request.ContentType))
            {
                return BadRequest();
            }
            //var rq = Request.GetFormDataRequest(GetUser(), out files);
            if (formData.Files.Count <= 0)
            {
                throw new Exception("Không tìm thấy file");
            }
            JObject obj = new JObject();

            try
            {
                DataTable data = FileUpload.Controllers.UploadController.ReadDataFile(formData.Files[0], "");

                data.Rows[0].Delete();
                obj.Add("data", JArray.FromObject(data));

                if (formData.Keys.Any())
                {
                    foreach (string key in formData.Keys)
                    {
                        if (key == "files")
                        {
                            JArray valueArray = JArray.Parse(formData[key][0]);
                            obj.Add(key, valueArray);
                        }
                        else if (key == "gara")
                        {
                            formData.TryGetValue(key, out var value);
                            JArray arr = new JArray();
                            value.ToList().ForEach(j => arr.Add(JObject.Parse(j)));
                            obj.Add(key, arr);
                        }

                        else if (key == "ngay_ad" || key == "ngay_kt")
                        {
                            string value = String.Join("", (formData[key][0]).Split('/').Reverse());
                            obj.Add(key, value);
                        }
                        else
                        {
                            var value = formData[key][0];
                            obj.Add(key, value);
                        }
                    }
                }

                var user = GetUser();
                obj.Add("ma_doi_tac_nsd", user.ma_doi_tac);
                obj.Add("ma_chi_nhanh_nsd", user.ma_chi_nhanh);
                obj.Add("nsd", user.nsd);
                obj.Add("pas", user.pas);

                string json = Newtonsoft.Json.JsonConvert.SerializeObject(obj.ToObject<object>());

                var res = await Request.GetResponeNew(StoredProcedure.PHT_MA_GARA_GIA_IMPORT, json);
                return Ok(res);
            }
            catch
            {
                return NotFound();
            }

        }
    }
}
