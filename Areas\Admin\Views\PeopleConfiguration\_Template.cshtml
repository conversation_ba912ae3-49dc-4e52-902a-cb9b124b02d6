﻿<script type="text/html" id="modalCauHinhBoiThuongConNguoi_template">
    <%if(ds_boi_thuong.length > 0){ %>
    <% _.forEach(ds_boi_thuong, function(item,index) { %>
    <tr class="row_item">
        <td style="font-weight:bold;">
            <% if(item.quy_trinh == 'CHUNG'){ %>
            Chung
            <% } else if (item.quy_trinh == 'BT'){ %>
            Bồi thường
            <% } %>
        </td>
        <td>
            <input type="hidden" data-field="ma" name="ma" data-val="<%- item.ma %>" value="<%- item.ma%>" />
            <input type="hidden" data-field="stt" data-val="<%- item.stt %>" value="<%- item.stt%>" />
            <input type="hidden" data-field="quy_trinh" data-val="<%- item.quy_trinh %>" value="<%- item.quy_trinh%>" />
            <input type="hidden" data-field="ung_dung" data-val="<%- item.ung_dung %>" value="<%- item.ung_dung%>" />
            <input type="hidden" data-field="ten" data-val="<%- item.ten %>" value="<%- item.ten%>" />
            <input type="text" style="font-weight:bold" name="ten" disabled required value="<%- item.ten %>" autocomplete="off" class="floating-input" />
        </td>
        <% if(item.ma == 'HAN_BAO_LANH' || item.ma == 'HAN_XN_PABT') { %>
            <td style="text-align: right; vertical-align: middle;">
                <input type="text" class="floating-input number gia_tri" autocomplete="off" maxlength="3" placeholder="<%- item.ten %>" data-field="gia_tri" value="<%- item.gia_tri %>">
            </td>
        <% }else{ %>
            <td style="text-align: center;">
            <div class="col-sm-12">
                <% if(item.gia_tri == 'C'){ %>
                <div class="custom-control custom-switch">
                    <input type="checkbox" checked="checked" @*onchange="onChangeCaiDat('<%- item.ma%>')"*@ data-field="gia_tri" name="gia_tri" class="custom-control-input checkbox" id="checkbox_bt_<%- item.ma%>">
                    <label class="custom-control-label" for="checkbox_bt_<%- item.ma%>"></label>
                </div>
                <% } else { %>
                <div class="custom-control custom-switch">
                    <input type="checkbox" data-field="gia_tri" @*onchange="onChangeCaiDat('<%- item.ma%>')"*@ name="gia_tri" class="custom-control-input checkbox" id="checkbox_bt_<%- item.ma%>">
                    <label class="custom-control-label" for="checkbox_bt_<%- item.ma%>"></label>
                </div>
                <% } %>
            </div>
        </td>
        <% } %>
        <%})}%>
    </tr>
    <% if(ds_boi_thuong.length < 12){
    for(var i = 0; i < 12 -  ds_boi_thuong.length;i++ ){
    %>
    <tr>
        <td></td>
        <td style="height:38.2px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Load danh sách ngày hiển thị cấu hình bồi thường con người*@
<script type="text/html" id="tblDsNgayCauHinhConNguoi_template">
    <% if(ds_ngay_ad.length > 0){
    _.forEach(ds_ngay_ad, function(item,index) { %>
    <tr style="cursor: pointer; text-align:center" data-val="<%- item.ngay_ad %>" id="ds_ngay_ad_bt_<%- item.ngay_ad %>" onclick="getDetailCompensation('<%- item.ngay_ad %>')" class="item-ngay_ad">
        <td style="font-weight:bold"><%- item.ngay_ad_hthi %></td>
    </tr>
    <%})}%>
    <% if(ds_ngay_ad.length < 8){
    for(var i = 0; i < 8 - ds_ngay_ad.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
    </tr>
    <% }} %>
</script>

@*Template cấu hình SLA*@
<script type="text/html" id="tblCauHinhSLATemplate">
    <%if(data.length > 0){ %>
    <% _.forEach(data, function(item,index) { %>
    <tr class="row_item">
        <td class="text-center">
            <input type="hidden" data-field="stt" data-val="<%- item.stt %>" value="<%- item.stt%>" />
            <%- item.stt %>
        </td>
        <td class="text-left font-weight-bold">
            <input type="hidden" data-field="ma" data-val="<%- item.ma %>" value="<%- item.ma%>" />
            <input type="hidden" data-field="buoc_thuc_hien" data-val="<%- item.buoc_thuc_hien %>" value="<%- item.buoc_thuc_hien%>" />
            <span><%- item.buoc_thuc_hien %></span>
        </td>
        <td class="text-right">
            <input type="text" name="tien_tu" data-field="tien_tu" data-val="<%- item.tien_tu %>" required value="<%=ESUtil.formatMoney(item.tien_tu)%>" autocomplete="off" placeholder="Tiền từ" class="floating-input number tien_tu">
        </td>
        <td class="text-right">
            <input type="text" name="tien_toi" data-field="tien_toi" data-val="<%- item.tien_toi %>" value="<%=ESUtil.formatMoney(item.tien_toi)%>" autocomplete="off" placeholder="Tiền tới" class="floating-input number tien_toi">
        </td>
        <td class="text-right">
            <input type="text" name="tgian" data-field="tgian" data-val="<%- item.tgian %>" value="<%- item.tgian %>" autocomplete="off" placeholder="Thời gian" class="floating-input number tgian">
        </td>
        <td class="text-right">
            <input type="text" name="tgian_noti" data-field="tgian_noti" data-val="<%- item.tgian_noti %>" value="<%- item.tgian_noti %>" autocomplete="off" placeholder="Thời gian noti" class="floating-input number tgian_noti">
        </td>
        <td style="text-align: center;">
            <div class="col-sm-12">
                <% if(item.tgian_hanh_chinh == 'C'){ %>
                <div class="custom-control custom-switch">
                    <input type="checkbox" checked="checked" data-field="tgian_hanh_chinh" name="tgian_hanh_chinh" class="custom-control-input checkbox" id="checkbox_bt_<%- item.ma%>_<%- item.tien_tu%>_<%- item.tien_toi%>">
                    <label class="custom-control-label" for="checkbox_bt_<%- item.ma%>_<%- item.tien_tu%>_<%- item.tien_toi%>"></label>
                </div>
                <% } else { %>
                <div class="custom-control custom-switch">
                    <input type="checkbox" data-field="tgian_hanh_chinh" name="tgian_hanh_chinh" class="custom-control-input checkbox" id="checkbox_bt_<%- item.ma%>_<%- item.tien_tu%>_<%- item.tien_toi%>">
                    <label class="custom-control-label" for="checkbox_bt_<%- item.ma%>_<%- item.tien_tu%>_<%- item.tien_toi%>"></label>
                </div>
                <% } %>
            </div>
        </td>
        <td class="text-center">
            <a href="#" onclick="addRowSLA(this)">
                <i class="far fa-plus-square"></i>
            </a>
            <a href="#" onclick="removeRowSLA(this)" class="d-none">
                <i class="fal fa-trash-alt ml-2"></i>
            </a>
        </td>
    </tr>
    <% })} %>

    <% if(data.length < 11){
    for(var i = 0; i < 11 - data.length;i++ ){
    %>
    <tr>
        <td style="height:36.5px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblDsNgayCauHinhSLA_template">
    <% if(ds_ngay_ad.length > 0){
    _.forEach(ds_ngay_ad, function(item,index) { %>
    <tr style="cursor: pointer; text-align:center" data-val="<%- item.ngay_ad %>" data-so-id="<%-item.so_id%>" id="ds_ngay_ad_sla_<%- item.ngay_ad %>" onclick="getDetailCauHinhSLA('<%- item.ngay_ad %>')" class="item-ngay_ad">
        <td style="font-weight:bold"><%- item.ngay_ad_hthi %></td>
    </tr>
    <%})}%>

    <% if(ds_ngay_ad.length < 9){
    for(var i = 0; i < 9 - ds_ngay_ad.length;i++ ){
    %>
    <tr>
        <td style="height:36.5px;"></td>
    </tr>
    <% }} %>
</script>