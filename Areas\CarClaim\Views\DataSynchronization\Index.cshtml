﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.AspNetCore.Http
@using ESCS.COMMON.Contants
@using Newtonsoft.Json
@using ESCS.MODEL.ESCS.ModelView

@{
    ViewData["Title"] = "Đồng bộ dữ liệu";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<input type="hidden" id="notify_info" value="@ViewBag.ho_so" />
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col col-1">
                            <div class="form-group">
                                <label for="ngay_d">Ngày tìm kiếm</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_d" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-1">
                            <div class="form-group">
                                <label for="ngay_c">&nbsp;</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_c" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label for="nguon">Nhà đồng</label>
                                <select class="select2 form-control custom-select" name="nha_dong" style="width:100%">
                                    <option value="" selected>Chọn nhà đồng</option>
                                    <option value="OPES">OPES</option>
                                    <option value="TTISV">TTISV</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label>Số hồ sơ</label>
                                <input type="text" name="so_hs" autocomplete="off" spellcheck="false" placeholder="Số hồ sơ" class="form-control">
                            </div>
                        </div>
                        <div class="col-2 d-none">
                            <div class="form-group">
                                <label>Biển xe</label>
                                <input type="text" name="bien_xe" autocomplete="off" spellcheck="false" placeholder="Biển xe" class="form-control">
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label for="nguon">Điểm tích hợp</label>
                                <select class="select2 form-control custom-select" name="diem" style="width:100%">
                                    <option value="" selected>Chọn điểm tích hợp</option>
                                    <option value="TIEP_NHAN_TT_OPES_AG">Tiếp nhận tổn thất</option>
                                    <option value="PHAN_CONG_GD_OPES_AG">Phân công giám định</option>
                                    <option value="KET_THUC_GD_OPES_AG">Kết thúc giám định</option>
                                    <option value="HUY_KTGD_OPES_AG">Hủy kết thúc giám định</option>
                                    <option value="DUYET_PA_OPES_AG">Duyệt PASC</option>
                                    <option value="HUY_DUYET_PA_OPES_AG">Hủy duyệt PASC</option>
                                    <option value="DUYET_BT_OPES_AG">Duyệt PABT</option>
                                    <option value="HUY_DUYET_BT_OPES_AG">Hủy duyệt PABT</option>
                                    <option value="HUY_HS_OPES_AG">Hủy hồ sơ</option>
                                    <option value="GO_HUY_HS_OPES_AG">Gỡ hủy hồ sơ</option>
                                    <option value="THANH_TOAN_OPES_AG">Duyệt thanh toán</option>
                                    <option value="HUY_THANH_TOAN_OPES_AG">Hủy duyệt thanh toán</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label for="nguon">Trạng thái tích hợp</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width:100%">
                                    <option value="" selected>Chọn trạng thái</option>
                                    <option value="1">Tích hợp thành công</option>
                                    <option value="2">Tích hợp lỗi</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-1" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-60p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewPhanTrang" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalTichHopThongTin" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmTichHopThongTin" method="post">
                <input type="hidden" name="ma_doi_tac" value="" />
                <input type="hidden" name="so_id" value="" />
                <input type="hidden" name="nsd" value="" />
                <input type="hidden" name="bt" value="" />
                <input type="hidden" name="nha_dong" value="" />
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin tích hợp</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="">Số hồ sơ</label>
                                <input type="text" maxlength="100" placeholder="Số hồ sơ" name="so_hs" autocomplete="off" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="">Điểm tích hợp</label>
                                <select class="select2 form-control custom-select" name="ma_api" style="width:100%">
                                    <option value="" selected>Chọn điểm tích hợp</option>
                                    <option value="TIEP_NHAN_TT_OPES_AG">Tiếp nhận tổn thất</option>
                                    <option value="PHAN_CONG_GD_OPES_AG">Phân công giám định</option>
                                    <option value="KET_THUC_GD_OPES_AG">Kết thúc giám định</option>
                                    <option value="HUY_KTGD_OPES_AG">Hủy kết thúc giám định</option>
                                    <option value="DUYET_PA_OPES_AG">Duyệt PASC</option>
                                    <option value="HUY_DUYET_PA_OPES_AG">Hủy duyệt PASC</option>
                                    <option value="DUYET_BT_OPES_AG">Duyệt PABT</option>
                                    <option value="HUY_DUYET_BT_OPES_AG">Hủy duyệt PABT</option>
                                    <option value="HUY_HS_OPES_AG">Hủy hồ sơ</option>
                                    <option value="GO_HUY_HS_OPES_AG">Gỡ hủy hồ sơ</option>
                                    <option value="THANH_TOAN_OPES_AG">Duyệt thanh toán</option>
                                    <option value="HUY_THANH_TOAN_OPES_AG">Hủy duyệt thanh toán</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="">Data Request</label>
                                <textarea class="form-control" autocomplete="off" rows="10" name="data_request" id="data_request" readonly></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="">Data Response</label>
                                <textarea class="form-control" autocomplete="off" rows="10" name="data_response" id="data_response" readonly></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm wd-150 float-right" id="btnDongBoTrangThaiHoSo"><i class="fas fa-sync mr-2"></i>Đồng bộ trạng thái</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section scripts{
    <script src="~/js/app/carclaim/services/DataSynchronizationService.js" asp-append-version="true"></script>
    <script src="~/js/app/CarClaim/services/CarClaimCommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/carclaim/DataSynchronization.js" asp-append-version="true"></script>
}