﻿<style>
    #modalCanBoDanhSach label:hover {
        color: var(--escs-main-theme-color);
    }
</style>
<div id="modalCanBo" class="modal-drag" style="width: 280px; z-index: 9999999; margin-top: 5px !important; margin-left: -10px !important;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn cán bộ</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_CanBo" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalCanBoElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:260px;" id="modalCanBoDanhSach">

            </div>
        </div>
    </div>
    @*<div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonCanBo">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>*@
</div>

<script type="text/html" id="modalCanBoDanhSachTemplate">
    <% if(danh_sach_can_bo.length > 0){
    _.forEach(danh_sach_can_bo, function(item,index) { %>
    <% var ma = danh_sach_can_bo[index].ma.trim().replace(/[^a-zA-Z0-9]/g, '') %>
    <div class="custom-control custom-checkbox dscb" id="dscb_<%- ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="can_bo_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-canbo modalCanBoItem single_checked" onchange="onChonCanBo(this)">
        <label class="custom-control-label" style="cursor:pointer;" for="can_bo_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>