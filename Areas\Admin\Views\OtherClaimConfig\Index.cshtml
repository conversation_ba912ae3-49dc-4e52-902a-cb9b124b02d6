﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình tài sản kỹ thuật";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<style>
    .card_active {
        background-color: var(--escs_theme_color-very-light);
    }

    .cau_hinh_xe:hover {
        background-color: #eef5f9;
    }

    .bg-color {
        background-color: #ececec;
        border: 1px solid #607d8b;
        /*var(--escs_theme_color-light)*/
    }
</style>


<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình Tài sản kỹ thuật</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item">Cấu hình Tài sản kỹ thuật</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row"></div>
                </form>
                <div class="row mt-3">

                    <div class="col-sm-4">
                        <div class="cau_hinh_tskt card mb-3 cursor-pointer bg-color" id="ch_ho_so" onclick="chonLoaiCauHinh('CH_HS')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-file-invoice"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Cấu hình hồ sơ chứng từ</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-sm-4">
                        <div class="cau_hinh_tskt card mb-3 cursor-pointer bg-color" id="ch_boi_thuong" onclick="chonLoaiCauHinh('CH_BOI_THUONG')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fa fa-home" aria-hidden="true"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Cấu hình bồi thường</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="cau_hinh_xe card mb-3 cursor-pointer bg-color" id="ch_sla" onclick="chonLoaiCauHinh('CH_SLA')">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fab fa-usps"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Cấu hình SLA</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<partial name="_Modal.cshtml" />
<partial name="_Template.cshtml" />


@section Scripts {
    <script src="~/js/app/Admin/services/BusinessCodeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ProductOtherService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/OtherClaimConfigService.js" asp-append-version="true"></script>

    <script src="~/js/app/CommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/OtherClaimConfig.js" asp-append-version="true"></script>
}


