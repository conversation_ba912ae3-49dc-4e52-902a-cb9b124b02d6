﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục dịch vụ sức khỏe";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON>h mục dịch vụ sức khỏe</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Dịch vụ sức khỏe</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập mã/tên dịch vụ" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapDichVu">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcelDichVu">
                                <i class="fas fa-download"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                <i class="fas fa-upload"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewDichVu" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapDichVu" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content col-md-10" style="margin-left: 55px;">
            <form name="frmSaveDichVu" method="post">
                <div class="modal-header" style="padding: 10px 5px;">
                    <h4 class="modal-title">Thông tin dịch vụ sức khỏe</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body" style="padding: 10px 5px;">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã dịch vụ</label>
                                <input type="text" maxlength="30" autocomplete="off" placeholder="Mã dịch vụ" name="ma_dich_vu" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Dịch vụ cấp trên</label>
                                <select class="select2 form-control custom-select" required name="ma_dich_vu_ct" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-8">
                            <div class="form-group">
                                <label class="_required">Tên dịch vụ</label>
                                <input type="text" maxlength="500" autocomplete="off" placeholder="Tên dịch vụ" name="ten_dich_vu" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Mã dịch vụ BYT</label>
                                <input type="text" maxlength="30" autocomplete="off" placeholder="Mã dịch vụ BYT" name="ma_dich_vu_byt" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Giá dịch vụ BYT</label>
                                <input type="text" maxlength="30" autocomplete="off" placeholder="Giá dịch vụ BYT" name="gia_dich_vu_byt" class="form-control number">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Giá viện phí</label>
                                <input type="text" maxlength="30" autocomplete="off" placeholder="Giá viện phí" name="gia_vien_phi" class="form-control number">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Giá dịch vụ</label>
                                <input type="text" maxlength="30" autocomplete="off" placeholder="Giá dịch vụ" name="gia_dich_vu" class="form-control number">
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="">Ghi chú</label>
                                <textarea placeholder="Ghi chú" class="form-control" autocomplete="off" rows="5" name="ghi_chu"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="padding: 10px 5px; display: block;">
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinDichVu"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinDichVu"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/HealthServicesService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/HealthServices.js" asp-append-version="true"></script>
}