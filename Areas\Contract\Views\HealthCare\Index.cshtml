﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Hợp đồng bảo hiểm sức khỏe con người";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .rowSelected {
        background-color: #f4f4f4;
    }

        .rowSelected input {
            background-color: #f4f4f4 !important;
        }

    .text-danger::placeholder {
        color: red;
    }
</style>
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Hợ<PERSON> đồng bảo hiểm sức khỏe con người</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Hợ<PERSON> đồng bảo hiểm sức khỏe con người</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="nd">Tìm kiếm</label>
                                <div class="form-group">
                                    <input type="text" class="form-control" autocomplete="off" name="nd" placeholder="Số HĐ">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2" style="margin-top: 20px;">
                            <div class="form-group">
                                <input type="text" class="form-control" name="ten_ndbh" autocomplete="off" placeholder="Tên NĐBH">
                            </div>
                        </div>
                        <div class="col-sm-1">
                            <div class="form-group">
                                <label for="ngay_d">Từ ngày</label>
                                <div class="input-group" style="width:105px;">
                                    <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_d" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text px-2"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-1">
                            <div class="form-group">
                                <label for="ngay_c">Đến ngày</label>
                                <div class="input-group" style="width:105px;">
                                    <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_c" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text px-2"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="ma_chi_nhanh">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="ma_chi_nhanh">Đơn vị</label>
                                <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2 px-0" style="padding-top: 21px">
                            <button type="button" class="btn btn-primary btn-sm wd-40" id="btnSearch">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40" id="btnAdd">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40" id="btnExportDSHD">
                                <i class="fa fa-download"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40 d-none" @*onclick="EXCEL.open('xlsHD')"*@ onclick="_modalImportExcel.show()" title="Import danh sách hợp đồng sức khỏe con người">
                                <i class="fa fa-upload"></i>
                            </button>
                            @*<button type="button" class="btn btn-primary btn-sm wd-47" onclick="EXCEL.open('xlsLSTT')" title="Import lịch sử tổn thất">
                                <i class="fa fa-upload"></i>
                            </button>*@
                            <button type="button" class="btn btn-primary btn-sm wd-40 d-none" id="btnCheckUpload" title="Kiểm tra convert data">
                                <i class="fas fa-check"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewHDSucKhoe" class="table-app" style="height: 69vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<partial name="_Template.cshtml" />
<partial name="/Views/Shared/_ModalMap.cshtml" />
<partial name="_Modal.cshtml" />
<partial name="_HealthCareSearch.cshtml" />
<partial name="_ModalDanhSachNDBH.cshtml" />
<partial name="_ModalNguyenTeTyGia.cshtml" />
<partial name="_ModalMaNguyenTe.cshtml" />
<partial name="_ModalDSLHNVMaBenh.cshtml" />
<partial name="_ModalDSLHNV.cshtml" />
<partial name="_ModalDSKieuAD.cshtml" />
<partial name="_ModalCanBo.cshtml" />
<partial name="_ModalMaBenh.cshtml" />
<partial name="_CaiDatGoiHD.cshtml" />
<partial name="_HealthClaimImageUpload.cshtml" />
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section Styles{
    <link href="~/css/app/CarClaim/CarClaimCommon.css" rel="stylesheet" />
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" />
}
@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/PackageService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CurrencyCodeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DepartmentListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DepartmentListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CarManufacturerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CategoryPersonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/HieuXeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/RangeVehicleService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BusinessCodeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CategoryCommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DiseasesListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/PackageService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/CustomerService.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/services/taxrateservice.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/services/usermanagementservice.js" asp-append-version="true"></script>
    <script src="~/js/common/modaldragservice.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/HealthService.js" asp-append-version="true"></script>
    <script src="~/libs/tree-js/vakata-jstree/dist/jstree.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/_CaiDatGoiHD.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/Health.js" asp-append-version="true"></script>
}
<script>
    var user = @Html.Raw(Json.Serialize(ViewData["escs_nguoi_dung"]));
</script>