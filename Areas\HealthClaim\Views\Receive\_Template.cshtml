﻿@*tab Thông tin chung*@
<script type="text/html" id="navThongTinChung_template">
    <div class="card-body p-1">
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Thông tin hồ sơ</h5>
            </div>
            <table class="table" id="HealthGuaranteeCommon">
                <tr>
                    <td style="width:104px">Ngày mở HSBT</td>
                    <td><%- ho_so.ngay_ht %></td>
                </tr>
                <tr>
                    <td>Ng<PERSON>y thông báo</td>
                    <td><%- ho_so.ngay_tb %></td>
                </tr>
                <tr>
                    <td>Nguồn TB</td>
                    <td><%- ho_so.nguon_tb_ten %></td>
                </tr>
                <tr>
                    <td>Sản phẩm</td>
                    <td>
                        <%- ho_so.ten_sp %>
                    </td>
                </tr>
                <tr>
                    <td><PERSON><PERSON><PERSON> b<PERSON><PERSON> hiể<PERSON></td>
                    <td>
                        <%- ho_so.ten_goi_bh %>
                    </td>
                </tr>
                <tr>
                    <td>Trạng thái xử lý</td>
                    <td style="color: var(--escs_theme_color);"><%- ho_so.trang_thai %>(<%- ho_so.pt_hoan_thanh %>%)</td>
                </tr>
                <tr>
                    <td>Số tiền yêu cầu</td>
                    <td><%- ESUtil.formatMoney(ho_so.so_tien_yc) %></td>
                </tr>
                <tr>
                    <td style="width:105px;">Ước tổn thất</td>
                    <td>
                        <span data-model="uoc_ton_that" class="uoc_ton_that"><%- ESUtil.formatMoney(ho_so.uoc_ton_that) %></span>
                        <span class="float-right d-none">
                            <a href="#" onclick="capNhatUocTonThat(this)"><i class="fas fa-edit" title="Cập nhật ước tổn thất"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px;">Ước theo điểm</td>
                    <td>
                        <span class="uoc_ton_that"><%- ESUtil.formatMoney(ho_so.uoc_ton_that_diem) %></span>
                        <span class="float-right">
                            <a href="#" onclick="capNhatUocTheoDiem(this)"><i class="fas fa-edit" title="Cập nhật ước tổn thất theo điểm"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:104px">Hồ sơ TPA</td>
                    <td><%- ho_so.so_hs_tpa %></td>
                </tr>
            </table>
        </div>
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Khách hàng</h5>
                <span>
                    <a href="#" onclick="suaThongTinNDBH()">
                        <i class="fas fa-edit"></i>
                    </a>
                </span>
            </div>
            <table class="table" id="HealthGuaranteeCustomer">
                <tr>
                    <td style="width:104px">Tên KH</td>
                    <td><%- ho_so.ten_khach_hang %></td>
                </tr>
                <tr>
                    <td>Số HĐBH </td>
                    <td><%- ho_so.so_hd %></td>
                </tr>
                <tr>
                    <td>Đơn vị cấp</td>
                    <td><%- ho_so.ten_dvi_cap %></td>
                </tr>
                <tr>
                    <td>Cán bộ cấp đơn</td>
                    <td><%- ho_so.nsd_cap %></td>
                </tr>
                <tr>
                    <td>Số GCN</td>
                    <td>
                        <span>
                            <%- ho_so.gcn %>
                        </span>
                        <span class="float-right">
                            <a href="#" onclick="xemThongTinGiayChungNhan()">
                                <i class="fas fa-eye" title="Xem chi tiết GCN bảo hiểm"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Gói bảo hiểm</td>
                    <td>
                        <%- ho_so.ten_goi_bh %>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Đồng tái BH</td>
                    <td>
                        <span class="dong_tai_bh"><%- ho_so.dong_tai_bh %></span>
                    </td>
                </tr>
                <tr>
                    <td>Tên NĐBH</td>
                    <td id="nhom_kh_vip_ttc">
                        <% if(ho_so.nhom_kh_vip == 'VIP' || ho_so.nhom_kh_vip == 'SVIP'){ %>
                        <span class="ten_kh active-vip"><%- ho_so.ten %></span>
                        <% } else{ %>
                        <span class="ten_kh"><%- ho_so.ten %></span>
                        <% } %>
                        <span class="float-right">
                            <a href="#" @* onclick="chonKhachHangVip()" *@>
                                <% if(ho_so.nhom_kh_vip == 'VIP' || ho_so.nhom_kh_vip == 'SVIP'){ %>
                                <i class="rating-star fas fa-star active-star"></i>
                                <% } else{ %>
                                <i class="rating-star fas fa-star defaultColor"></i>
                                <% } %>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>CCCD</td>
                    <td><%- ho_so.so_cmt %></td>
                </tr>
                <tr>
                    <td>Ngày sinh</td>
                    <td><%- ho_so.ngay_sinh %></td>
                </tr>
                <tr>
                    <td>Điện thoại</td>
                    <td>
                        <%- ho_so.dien_thoai %>
                        <span class="float-right d-none">
                            <a href="javascript:void(0)" onclick="ESUtil.voiceCall('<%- ho_so.dien_thoai %>')">
                                <i class="fal fa-phone-rotary" title="Gọi điện thoại"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Hiệu lực BH</td>
                    <td><%- ho_so.hieu_luc %></td>
                </tr>
                <tr>
                    <td>Thanh toán phí</td>
                    @* <td><%= ho_so.thanh_toan_phi %></td> *@
                     <td>
                        <span>
                            <%= ho_so.thanh_toan_phi %>
                        </span>
                        <span class="float-right">
                            <a href="#" onclick="xemTinhTrangTTPhiCore()"><i class="fas fa-sync-alt" title="Xem tình trạng thanh toán phí"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Xác minh phí</td>
                    <td>
                            <a href="#" onclick="xemMauInXacMinhPhi?.()">Xem mẫu in xác minh phí</a>
                    </td>
                </tr>
                <tr>
                    <td>KH xác nhận</td>
                    <td>
                        <span><%= ho_so.khach_hang_xac_nhan %></span>
                        <span class="float-right">
                            <a href="#" onclick="xemNoiDungKhachHangXacNhan()">
                                <i class="fas fa-eye" title="Xem nội dung khách hàng xác nhận"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Đơn vị</td>
                    <td><%= ho_so.dvi_ctac_ndbh %></td>
                </tr>
                <tr>
                    <td>Chức vụ</td>
                    <td><%= ho_so.chuc_vu_ndbh %></td>
                </tr>
            </table>
        </div>
        <div class="border rounded">
            @*d-flex*@
            <div class=" justify-content-between align-items-center p-2 card-title-bg d-none">
                <h5 class="m-0">Người thông báo</h5>
                <span>
                    <a href="#" onclick="suaThongTinNguoiThongBao()">
                        <i class="fas fa-edit"></i>
                    </a>
                </span>

            </div>
            <table class="table d-none" id="HealthGuaranteeCustomerTB">
                <tbody>
                    <tr>
                        <td style="width:104px">Người TB</td>
                        <td><%- ho_so.nguoi_tb %></td>
                    </tr>
                    <tr>
                        <td>SĐT thông báo</td>
                        <td><%- ho_so.dthoai_nguoi_tb %></td>
                    </tr>
                    <tr>
                        <td>Email thông báo</td>
                        <td><%- ho_so.email_nguoi_tb %></td>
                    </tr>
                    <tr>
                        <td>Mối quan hệ</td>
                        <td><%- ho_so.moi_qh_tb_ten %></td>
                    </tr>
                </tbody>
            </table>
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg border-top">
                <h5 class="m-0">Người liên hệ</h5>
                <span>
                    <a href="#" onclick="suaThongTinNguoiLienHe()">
                        <i class="fas fa-edit"></i>
                    </a>
                </span>

            </div>
            <table class="table" id="HealthGuaranteeCustomerLH">
                <tbody>
                    <tr>
                        <td style="width:104px">Người liên hệ</td>
                        <td><%- ho_so.nguoi_lh %></td>
                    </tr>
                    <tr>
                        <td>SĐT liên hệ</td>
                        <td><%- ho_so.dthoai_nguoi_lh %></td>
                    </tr>
                    <tr>
                        <td>Email liên hệ</td>
                        <td><%- ho_so.email_nguoi_lh %></td>
                    </tr>
                    <tr>
                        <td>Mối quan hệ</td>
                        <td><%- ho_so.moi_qh_lhe_ten%></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="text-right card-title-bg" style="height: 35.5px;">
                        <td colspan="2" class="text-center">
                            <a href="javascript:void(0)" id="btnHuyHoSo" class="escs_pquyen mr-4" onclick="huyHoSo()">
                                <i class="fas fa-trash-alt mr-2"></i>Hủy hồ sơ
                            </a>
                            <a href="javascript:void(0)" id="btnChuyenNguoiXuLy" class="escs_pquyen" onclick="chuyenNguoiXuLy()">
                                <i class="far fa-user-friends mr-2"></i>Chuyển người xử lý
                            </a>
                            <a href="javascript:void(0)" id="btnGoHuyHoSo" class="escs_pquyen" onclick="goHuyHoSo()">
                                <i class="fa fa-undo mr-2"></i>Gỡ hủy hồ sơ
                            </a>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</script>

@*Lịch sử tổn thất*@
<script type="text/html" id="tabLichSuTonThat_template">
    <div class="row mg-t-10">
        <div class="col-12">
            <h4>Các lần khám chữa bệnh</h4>
        </div>
        <div class="col-12">
            <div class="table-responsive" style="max-height:173px">
                <!-- Chứng từ hóa đơn, thanh toán -->
                <table class="table table-bordered fixed-header">
                    <thead class="font-weight-bold card-title-bg-primary">
                        <tr class="text-center uppercase">
                            <th>Số hồ sơ</th>
                            <th>Ngày mở hs</th>
                            <th>Ngày duyệt</th>
                            <th>Tiền yêu cầu</th>
                            <th>Tiền duyệt</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if(lan_kham.length > 0){
                        _.forEach(lan_kham, function(item,index) { %>
                        <tr>
                            <td><%- item.so_hs %></td>
                            <td class="text-center"><%- item.ngay_ht %></td>
                            <td class="text-center"><%- item.ngay_duyet %></td>
                            <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_yc) %></td>
                            <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_duyet) %></td>
                            <td><%- item.trang_thai %></td>
                        </tr>
                        <% })} else{%>
                        <tr>
                            <td class="text-center" colspan="6">Chưa có dữ liệu</td>
                        </tr>
                        <% }%>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="col-12 mt-3">
            <h4>Các quyền lợi đã dùng</h4>
        </div>

        <div class="col-12">
            <div class="table-responsive" style="max-height:200px">
                <!-- Chứng từ hóa đơn, thanh toán -->
                <table class="table table-bordered fixed-header">
                    <thead class="font-weight-bold card-title-bg-primary">
                        <tr class="text-center uppercase">
                            <th>Tên quyền lợi</th>
                            <th>Tổng số ngày quyền lợi</th>
                            <th>Tổng số tiền quyền lợi</th>
                            <th>Số ngày đã duyệt / pending</th>
                            <th>Số tiền đã duyệt / pending</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if(quyen_loi.length > 0){
                        _.forEach(quyen_loi, function(item,index) { %>
                        <tr>
                            <td><%- item.ten %></td>
                            <td class="text-right"><%- item.so_lan_ngay %></td>
                            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
                            <td class="text-right"><%- item.so_lan_ngay_duyet %></td>
                            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay_duyet) %></td>
                        </tr>
                        <% })} else{%>
                        <tr>
                            <td class="text-center" colspan="5">Chưa có dữ liệu</td>
                        </tr>
                        <% }%>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</script>

@*Danh sách lần tiếp nhận*@
<script type="text/html" id="tblLanTiepNhanTemplate">
    <% _.forEach(lan_kham, function(item,index) { %>
    <tr style="cursor: pointer" id="lan_kham_<%- item.lan %>" onclick="XemChiTietLan('<%- item.lan %>')">
        <td>
            <span><%- item.ngay_vv %></span>
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_yc) %></td>
    </tr>
    <%})%>
</script>

@*Danh sách bệnh lý*@
<script type="text/html" id="dsBenhLyTemplate">
    <% _.forEach(ds_benh_ly, function(item, index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten_tim %>">
        <input type="checkbox" onchange="chonBenhLy(this)" id="ma_benh_<%- item.ma.replace(/\./g, '') %>" value="<%- item.ma %>" class="custom-control-input item-benh">
        <label class="custom-control-label" for="ma_benh_<%- item.ma.replace(/\./g, '') %>"><%- item.ten_v %></label>
    </div>
    <%})%>
</script>

@*Danh sách hợp đồng*@
<script type="text/html" id="dsHDTemplate">
    <% _.forEach(ds, function(item, index) { %>
    <div class="custom-control custom-checkbox " data-text="<%- item.so_hd %>">
        <input type="checkbox" onchange="chonHD(this)" id="so_hd_<%- item.so_hd.replace(/\./g, '') %>" value="<%- item.so_hd %>"  class="custom-control-input item-benh" data-val="<%- item.so_id %>">
        <label class="custom-control-label" for="so_hd_<%- item.so_hd.replace(/\./g, '') %>"><%- item.so_hd %></label>
    </div>
    <%})%>
</script>

@*bảo lãnh quyền lợi chi tiết*@
<script type="text/html" id="tbl_bao_lanh_quyen_loi_ct_template">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td class="text-center"><%- item.ten_nguyen_nhan %></td>
        <td class="text-center"><%- item.ten_hinh_thuc %></td>
        <td><%- item.chan_doan %></td>
        <td style="text-align: center"><%- item.nt_tien_yc %></td>
        <td style="text-align: right"><%- ESUtil.formatMoney(item.tien_yc) %></td>
        <td style="text-align: right"><%- ESUtil.formatMoney(item.tien_yc_vnd) %></td>
        <td class="text-center">
            <a href="#" onclick="suaQuyenLoi('<%- item.lan %>', '<%- item.id_qloi %>')">
                <i class="fas fa-edit"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" onclick="xoaQuyenLoi('<%- item.lan %>', '<%- item.id_qloi %>')">
                <i class="fas fa-trash-alt"></i>
            </a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 5){
    for(var i = 0; i < 5 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách quyền lợi gốc*@
<script type="text/html" id="dsQuyenLoiGocTemplate">
    <% _.forEach(lstQlg, function(item, index) {

     %>
    <% var thu_tu_cha_con = item.lh_nv.split('.').length - 1 %>
    <% if(thu_tu_cha_con == 0){ %>

    <tr>
        <td class="text-center"><%- item.lh_nv %></td>
        <td style="font-weight: bold">
            <a href="#" onclick="xemChiTietSuDung('<%- item.lh_nv %>')">
                <%= item.ten_hien_thi %>(<%= item.lh_nv %>)
            </a>
        </td>
        <td style="font-weight: bold" class="text-center"><%- item.nt_tien_bh %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
        <td style="font-weight: bold" class="text-center"><%- item.dong_bh %> % </td>
        <td style="font-weight: bold" class="text-center"><%- item.so_ngay_cho %></td>
        <td style="font-weight: bold" class="text-center d-none"><%- item.ngay_lan_kham %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
        <td style="font-weight: bold" class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_duyet %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_con %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.kieu_ad_ten%> </td>
        <td style="font-weight: bold" class="text-center d-none"><%- item.lhnv_phu_thuoc %> </td>
        <td style="font-weight: bold" class="text-center"><%- item.lhnv_tru_lui %> </td>
    </tr>
    <% } else{ %>
    <% var pd = item.pd %>
    <tr>
        <td class="text-center"><%- item.lh_nv %></td>
        <td style="font-style: italic; padding-left: <%- pd %>px">
            <a href="#" onclick="xemChiTietSuDung('<%- item.lh_nv %>')">
                <%= item.ten_hien_thi %>(<%= item.lh_nv %>)
            </a>
        </td>
        <td style="font-weight: bold" class="text-center"><%- item.nt_tien_bh %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
        <td style="font-weight: bold;" class="text-center"><%- item.dong_bh %> % </td>
        <td style="font-weight: bold;" class="text-center"><%- item.so_ngay_cho %></td>
        <td style="font-weight: bold" class="text-center d-none"><%- item.ngay_lan_kham %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
        <td style="font-weight: bold" class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
        <td style="font-weight: bold;" class="text-center"><%- item.so_lan_ngay_duyet %></td>
        <td style="font-weight: bold;" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
        <td style="font-weight: bold;" class="text-center"><%- item.so_lan_ngay_con %></td>
        <td style="font-weight: bold;" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
        <td style="font-weight: bold;" class="text-center"><%- item.kieu_ad_ten%> </td>
        <td style="font-weight: bold;" class="text-center d-none"><%- item.lhnv_phu_thuoc %> </td>
        <td style="font-weight: bold" class="text-center"><%- item.lhnv_tru_lui %> </td>
    </tr>
    <% } %>
    <%})%>
</script>

<script type="text/html" id="dsGhiChuKhac_template">
    <% _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-right" style="font-weight: bold">
            <%- item.ma_dkbs %>
        </td>
        <td class="text-left" style="font-weight: bold">
            <%- item.ten_hien_thi %>
        </td>
    </tr>
    <% }) %>

    <% if(data.length < 14){
    for(var i = 0; i < 14 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="dsDieuKhoanBoSung_template">
    <% _.forEach(data, function(item,index) { %>
    @* <% var thu_tu_cha_con = item.lh_nv.split('.').length - 1 %> *@
    <% var thu_tu_cha_con = 0 %>
    <% if(thu_tu_cha_con == 0){ %>
    <tr>
        <td class="text-left" style="font-weight: bold">
            <%- item.ten_hien_thi %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
    </tr>
    <% } else{ %>
    <% var pd = thu_tu_cha_con * 15 %>
    <tr>
        <td class="text-left" style="padding-left: <%- pd %>px; font-style: italic;">
            <%- item.ten_hien_thi %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
    </tr>
    <% } %>
    <%})%>

    <% if(data.length < 14){
    for(var i = 0; i < 14 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*  Danh sách ảnh *@
<script type="text/html" id="lstImage_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="pt-1" id="nhom_anh_<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImg('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTiet(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img<%- image.bt %>" class="nhom_anh_ton_that_<%- index %> mt-1" data-hm="<%- item.ma_file %>" value="<%- image.bt %>" name="ds_anh_xe">
            <p class="fileNameImage mt-1" style="cursor:pointer"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
    <% } %>
</script>

@*  Danh sách ảnh OCR *@
<script type="text/html" id="lstImageOCR_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="p-1" id="nhom_anh_ocr_<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImgOCR('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTietOCR(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img_ocr<%- image.bt %>" class="nhom_anh_ton_that_ocr_<%- index %> mt-1" data-hm="<%- item.ma_file %>" value="<%- image.bt %>" name="ds_anh_xe">
            <p class="fileNameImage mt-1" style="cursor:pointer"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
    <% } %>
</script>

@*Bổ sung hồ sơ giấy tờ*@
<script type="text/html" id="templateDsHoSoGiayTo">
    <% if(ho_so_giay_to.length > 0){
    _.forEach(ho_so_giay_to, function(item,index) { %>
    <tr>
        <td style="text-align:left">
            <a href="#" data-field="ten" data-val="<%- item.ten %>" id="chon_ten_hsgt_<%- item.ma_hs %>"><%- item.ten %></a>
        </td>
        <td style="text-align: center; display: none">
            <a href="#" data-field="trang_thai" data-val="<%- item.trang_thai %>">
                <%= item.trang_thai_ten %>
            </a>
        </td>
        <td style="text-align: center; display: none">
            <a href="#" data-field="ngay_bs" data-val="<%- item.ngay_bs %>">
            </a>
        </td>
        <td style="text-align: center">
            <%- item.ngay_bs %>
        </td>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <% if(item.trang_thai == 'C'){ %>
                <input type="checkbox" id="trang_thai_<%- item.ma_hs %>" class="custom-control-input input_chon_trang_thai" onclick="onChonTrangThai(this)">
                <label class="custom-control-label" for="trang_thai_<%- item.ma_hs %>">&nbsp;</label>
                <% }else if(item.trang_thai == 'D'){ %>
                <input type="checkbox" id="trang_thai_<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_trang_thai" onclick="onChonTrangThai(this)">
                <label class="custom-control-label" for="trang_thai_<%- item.ma_hs %>">&nbsp;</label>
                <% } %>
            </div>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% }else{ %>
            <% if(item.hop_le == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" checked="checked" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <a href="#" data-field="loai" data-val="">
                Chọn loại hồ sơ
            </a>
            <% }else{ %>
            <a href="#" data-field="loai" data-val="<%- item.loai %>" onclick="chonLoaiHSGT(this)" id="loai_hsgt_<%- item.ma_hs %>">
                <% if(item.loai != '' && item.loai != null){ %>
                <%- item.loai_ten %>
                <% }else{ %>
                Chọn loại hồ sơ
                <% } %>
            </a>
            <% } %>
        </td>
        <% if(item.trang_thai =="C")
        {
        if(item.chon==1)
        {
        if(item.trang_thai_xoa =='K')
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)" />
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%}
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        %>
        <td style="text-align: center">
            <% if(item.ghi_chu != null && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuBoSungHSGT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuBoSungHSGT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td style="text-align: center"><%- item.nsd %></td>
        <td style="text-align: center" class="d-none"><%- item.nguon %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>

@*  Danh sách tài liệu hồ sơ pdf *@
<script type="text/html" id="dsTaiLieuHoSoTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li>
        <a href="javascript:void(0);" onclick="getAnhChiTietTab2('<%- item.so_id %>', '<%- item.bt %>', '<%- item.extension %>')">
            <%- item.ten_file %>
        </a>
    </li>
    <% })} %>
</script>

@*  Danh sách tài liệu hồ sơ hình ảnh *@
<script type="text/html" id="dsHinhAnhHoSoTemplate">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <% if(index == 0){ %>
    <div style="display: inline-block;width:100%;">
        <p style="margin-bottom:5px;">
            <a href="#"><%- item.nhom %></a>
        </p>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div onclick="getAnhChiTietTab1('<%- image.so_id %>', '<%- image.bt %>', '<%- image.extension %>')" style="width:60px; height:60px; margin-right:10px; margin-bottom:5px; cursor:pointer;float:left; border-radius:10px; border:1px solid #e9ecef">
            <img style="width: 100%;  height:100%" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
        </div>
        <% }) %>
    </div>
    <% }else{ %>
    <div style="display: inline-block;width:100%;">
        <p style="margin-bottom:5px;">
            <a href="#"><%- item.nhom %></a>
        </p>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div onclick="getAnhChiTietTab1('<%- image.so_id %>', '<%- image.bt %>', '<%- image.extension %>')" style="width:60px; height:60px; margin-right:10px; margin-bottom:5px; cursor:pointer;float:left; border-radius:10px; border:1px solid #e9ecef">
            <img style="width: 100%;  height:100%" location-x="<%- image.x %>" location-y="<%- image.y %>" data-original="" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
        </div>
        <% }) %>
    </div>
    <% } %>
    <% })} %>
</script>

@*  Step 3: Chứng từ *@
<script type="text/html" id="step4_chung_tu_template">
    <% if(chung_tu.length > 0){
    _.forEach(chung_tu, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.ngay_ct %>
        </td>
        <td><%- item.mau_hdon %></td>
        <td class="text-center"><%- item.ky_hieu_hdon %></td>
        <td class="text-center"><%- item.so_hdon %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.thue) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tong_cong) %></td>
        <td class="text-center">
            <a href="#" class="edit_chung_tu" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="far fa-file-alt" title="Xem/sửa chi tiết chứng từ"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" class="xoaChungTu"><i class="fas fa-trash-alt" title="Xóa chứng từ"></i></a>
        </td>
    </tr>
    <% })} %>
    <% if(chung_tu.length < 2){
    for(var i = 0; i < 2 - chung_tu.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Step 3: Thụ hưởng *@
<script type="text/html" id="step4_thu_huong_template">
    <% if(thu_huong.length > 0){
    _.forEach(thu_huong, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.pttt %>
        </td>
        <td><%- item.ten %></td>
        <td class="text-center"><%- item.tk_cmt %></td>
        <td class="text-center"><%- item.ten_ngan_hang %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-center"><%- item.loai_hthi %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="edit_thu_huong" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="fas fa-edit" title="Xem/sửa chi tiết thông tin"></i>
            </a>
            <%
            }%>

        </td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="xoaNguoiThuHuong"><i class="fas fa-trash-alt" title="Xóa người thụ hưởng"></i></a>
            <%
            }%>
        </td>
    </tr>
    <% })}%>
    <% if(thu_huong.length < 2){
    for(var i = 0; i < 2 - thu_huong.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>

    </tr>
    <% }} %>
</script>

@*Thêm quyền lợi*@
<script type="text/html" id="tbDsKhoanChiTemplate">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="khoanChiItem">
        <td>
            <input type="hidden" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" data-field="loai" value="<%- item.loai %>" />
            <% if(item.check_thuoc > 0){ %>
                <a href="#" onclick="xemChiTietChiPhi('<%- item.bt %>','<%- item.loai_ct %>','<%- item.loai %>', '<%- item.tien_yc %>')" data-field="loai_ct" data-val="<%- item.loai_ct %>"><%- item.ten_loai_chi_phi %> <span style="color: #ff8c0096; font-size: 11px"><i class="fas fa-exclamation-triangle"></i> Có chi phí không phải thuốc điều trị</span></a>
            <% }else{ %>
                <a href="#" onclick="xemChiTietChiPhi('<%- item.bt %>','<%- item.loai_ct %>','<%- item.loai %>', '<%- item.tien_yc %>')" data-field="loai_ct" data-val="<%- item.loai_ct %>"><%- item.ten_loai_chi_phi %></a>
            <% } %>
        </td>
        <td class="text-center">
            <%- item.so_luong %>
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_yc" autocomplete="off" class="floating-input money" onchange="tinhTongChiPhi(this)" value="<%- ESUtil.formatMoney(item.tien_yc) %>" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" onclick="xoaChiPhi(this)"></i>
        </td>
    </tr>
    <% }) %>
    <% } %>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:39.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Chi phí khám bệnh*@
<script type="text/html" id="tblChiPhiKhamBenhTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td>
            <input type="hidden" data-field="so_luong_yc" value="" />
            <input type="hidden" data-field="don_gia_yc" value="" />
            <input type="hidden" data-field="dvi_tinh" value="" />
            <input type="hidden" data-field="ghi_chu" value="" />
            <input type="hidden" data-field="ma" value="<%- item.ma %>" />
            <input type="hidden" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" data-field="ten_ct" value="<%- item.ten_ct %>" />
            <input type="text" class="floating-input" placeholder="Nhập tên chi phí" data-field="ten" value="<%- item.ten %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" onchange="tongChiPhiKhamBenh()" data-field="tien_yc" value="<%- ESUtil.formatMoney(item.tien_yc) %>" />
        </td>
            @* <td style="text-align:right">
            <a data-field="gia_tham_khao" data-val="<%- item.gia_tham_khao %>"><%- ESUtil.formatMoney(item.gia_tham_khao) %></a>
        </td> *@
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="btnPopupGhiChu cursor-pointer" onclick="showGhiChuChiPhi(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="btnPopupGhiChu cursor-pointer" onclick="showGhiChuChiPhi(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDong(this,'CHI_PHI_KHAM_BENH')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 7){
    for(var i = 0; i < 7 - data.length;i++ ){
    %>
    <tr>
        <td></td>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Chi phí thuốc*@
<script type="text/html" id="tblChiPhiThuocTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td>
            <input type="hidden" data-field="ghi_chu" value="" />
            <input type="hidden" class="floating-input" data-field="ma" value="<%- item.ma %>" />
            <input type="hidden" class="floating-input" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" class="floating-input" data-field="ten_ct" value="<%- item.ten_ct %>" />
            <input type="text" class="floating-input" placeholder="Nhập tên thuốc" data-field="ten" value="<%- item.ten %>" />
        </td>
        <td>
            <input type="text" class="floating-input text-center" data-field="dvi_tinh" value="<%- item.dvi_tinh %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input decimal" onchange="tongChiPhiThuoc()" data-field="so_luong" value="<%- item.so_luong_yc %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" onchange="tongChiPhiThuoc()" data-field="don_gia" value="<%- ESUtil.formatMoney(item.don_gia_yc) %>" />
        </td>
        <td style="text-align:right">
            <a href="#" style="color:unset" data-field="tien_yc" data-val="<%- item.tien_yc %>"><%- ESUtil.formatMoney(item.tien_yc) %></a>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="btnPopupGhiChu cursor-pointer" onclick="showGhiChuChiPhi(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="btnPopupGhiChu cursor-pointer" onclick="showGhiChuChiPhi(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDong(this,'CHI_PHI_THUOC')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 7){
    for(var i = 0; i < 7 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Chi phí khác*@
<script type="text/html" id="tblChiPhiKhacTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td>
            <input type="hidden" data-field="so_luong_yc" value="" />
            <input type="hidden" data-field="don_gia_yc" value="" />
            <input type="hidden" data-field="dvi_tinh" value="" />
            <input type="hidden" class="floating-input" data-field="ma" value="<%- item.ma %>" />
            <input type="hidden" class="floating-input" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" class="floating-input" data-field="ten_ct" value="<%- item.ten_ct %>" />
            <a data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" onchange="tongChiPhiKhac()" data-field="tien_yc" value="<%- ESUtil.formatMoney(item.tien_yc) %>" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDong(this,'CHI_PHI_KHAC')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 7){
    for(var i = 0; i < 7 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalHealthSearchDsGCNTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="tkiem_gcn" data_ma_doi_tac="<%- item.ma_doi_tac %>" data_ma_doi_tac_ql="<%- item.ma_doi_tac_ql %>" data_ma_chi_nhanh="<%- item.ma_chi_nhanh_ql %>" data_so_id_hd_d="<%- item.so_id_hd_d %>" data_so_id_hd="<%- item.so_id_hd %>" data_so_id_gcn="<%- item.so_id_dt %>" data_goi_bh="<%- item.goi_bh %>" data_so_hd="<%- item.so_hd %>" data_hd_cu="<%- item.hd_cu %>" data_ten_ndbh="<%- item.ten_khach %>" data_ngay_sinh="<%- item.ngay_sinh %>" data_gioi_tinh="<%- item.gioi_tinh %>" style="cursor: pointer" id="tkiem_gcn_<%- item.so_id_hd %>_<%- item.so_id_dt %>" onclick="onChonGCN('<%- item.so_id_hd %>','<%- item.so_id_dt %>','<%- item.gioi_tinh %>','<%- item.ngay_sinh %>','<%- item.ngay_hl_text %>', '<%- item.ten.trim() %>')">
    <td class="text-center"><%= item.kh_vip %></td>
    <td class="text-center"><%- item.hd_cu_text %></td>
        <td class="text-center">
            <a href="#" onclick="xemThongTinQuyenLoiLSTT('<%- item.ma_doi_tac %>', '<%- item.ma_chi_nhanh_ql %>', '<%- item.ma_doi_tac_ql %>', '<%- item.so_id_hd %>', '<%- item.so_id_dt %>')"><%- item.ten %></a>
        </td>
        <td class="text-center"><%- item.so_cmt %></td>
        <td class="text-center"><%- item.ngay_sinh %></td>
        <td class="text-center"><%- item.goi_bh %></td>
        <td class="text-center"><%- item.ngay_hl %></td>
        <td class="text-center"><%- item.ten_khach %></td>
        <td class="text-center"><%- item.so_hd %></td>
        <td class="text-center"><%- item.d_thoai %></td>
        <td class="text-center"><%- item.email %></td>
        <td class="text-center"><%- item.nhom_sp %></td>
        <td class="text-center"><%- item.gcn %></td>
        <td class="text-center"><%- item.ten_dvi_cap_don %></td>
    </tr>
    <%})}%>
    <% if(data.length < 4){
    for(var i = 0; i < 4 - data.length;i++ ){ %>
    <tr style="cursor: pointer">
        <td style="height:34.6px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <%}}%>
</script>

@*Danh sách lịch sử tổn thất *@
<script type="text/html" id="tblThongTinLSTTTemplate">
    <% if(arrHoSo.length > 0){ %>
    <% _.forEach(arrHoSo, function(item,index) { %>
    <tr class="text-center lichSuTonThat" id="lichSuTonThat_<%- item.so_id %>_<%- item.lhnv %>">
        <td><%- item.ngay_ht %></td>
        <td>
            <% if(item.loai == 'HSTT'){ %>
            <a href="#" onclick="TransReceiveDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs %></a>
            <%}%>
            <% if(item.loai == 'BLVP'){ %>
            <a href="#" onclick="TransHealthguaranteeDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs%></a>
            <%}%>
        </td>
        <td><%= item.loai_ten%></td>
        <td><%- item.ngay_vv %></td>
        <td><%- item.ngay_rv %></td>
        <td><%- item.hinh_thuc_ten %></td>
        <td><%- item.ten_nguyen_nhan %></td>
        <td><%- item.quyen_loi_ten %></td>

        <td class="text-left"><%- item.ten_benh_vien %></td>
        <td class="text-left"><%- item.chan_doan %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_yc) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_duyet) %></td>
        <td class="text-right"><%- item.so_ngay_duyet %></td>
        <td class="text-center"><%- item.trang_thai %></td>
        <td class="text-center">
            <% if(item.nguyen_nhan != null && item.nguyen_nhan != '' && item.nguyen_nhan != undefined){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- item.nguyen_nhan %>" onclick="showNguyenNhanGiamTru(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Lý do giảm trừ"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhanGiamTru(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Lý do giảm trừ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>

    <% if(arrHoSo.length < 13){
    for(var i = 0; i < 13 - arrHoSo.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
<script type="text/html" id="modalGoiBHDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsbv" id="dsgoibh_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <input type="checkbox" id="goi_bh_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-goibh modalGoiBHItem single_checked" onchange="onChonGoiBH(this)">
        <label class="custom-control-label" style="cursor:pointer;" for="goi_bh_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
<script type="text/html" id="tableNhapQLoi_template">
    <% if(dk.length > 0){
    _.forEach(dk, function(item,index) { %>
    <% if(item.lh_nv_ct == null){ %>
    <tr row-val="<%- item.bt %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b style="font-weight: bold; text-transform: uppercase;"><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px; font-weight: bold"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" style="font-weight: bold" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
         <td class="text-right" style="font-weight: bold" col-ngay_lan_kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_kham) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-center" style="font-weight: bold" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" style="font-weight: bold" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
        </td>
        <td class="text-right" style="font-weight: bold" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
                            @*<td class="text-right" style="font-weight: bold" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" style="font-weight: bold" col-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.phi) %>
        </td>
    </tr>
    <% }else if(item.lh_nv_ct.toString().indexOf('.') != -1){ %>
    <tr row-val="<%- item.bt %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b style="font-style: italic;"><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px; font-style: italic;"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" style="font-style: italic;" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-ngay-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_kham) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-center" style="font-style: italic;" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" style="font-style: italic;" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
        </td>
        <td class="text-right" style="font-style: italic;" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
                                @*<td class="text-right" style="font-style: italic;" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" style="font-style: italic; " col-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.phi) %>
        </td>
    </tr>
    <% }else{ %>
    <tr row-val="<%- item.bt %>">
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd%>px"><b><%- item.ten_hien_thi %></b></td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px"><%- item.ten_hien_thi %></td>
        <%
        }
        %>
        <td class="text-center" style="font-weight: bold" col-ma-nt="<%- item.ma_nt %>">
            <%- item.ma_nt %>
        </td>
        <td class="text-right" col-so-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" col-ngay-lan-kham="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.ngay_lan_kham) %>
        </td>
        <td class="text-right" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" col-tien-lan-ngay="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_lan_kham) %>
        </td>
        <td class="text-right" col-tien-nam="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-center" col-kieu-ad="<%- item.kieu_ad %>">
            <%- item.kieu_ad_ten %>
        </td>
        <td class="text-right" col-dong-bh="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.dong_bh) %>
        </td>
        <td class="text-right" col-so-ngay-cho="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
                                    @*<td class="text-right" col-tl-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.tl_phi) %>
        </td>*@
        <td class="text-right" col-phi="<%- item.bt %>">
            <%- ESUtil.formatMoney(item.phi) %>
        </td>
    </tr>
    <% } %>

    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có điều khoản</td>
    </tr>
    <% } %>
</script>
<script type="text/html" id="tblLSTT_template">
    <% if(arrHoSo.length > 0){ %>
    <% _.forEach(arrHoSo, function(item,index) { %>
    <tr class="text-center lichSuTonThat" id="lichSuTonThat_<%- item.so_id %>_<%- item.lhnv %>" data-search="<%- item.nd_tim %>">
        <td><%- item.ngay_ht %></td>
        <td>
            <%if(item.nguon == 'UPLOAD'){%>
                     <lable><%- item.so_hs%></lable>
                <%}
                else
                {
                    if(item.loai == 'HSTT'){ %>
                    <a href="#" onclick="TransReceiveDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs %></a>
                    <%}%>
                    <% if(item.loai == 'BLVP'){ %>
                    <a href="#" onclick="TransHealthguaranteeDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs%></a>
                <%}}
            %>
        </td>
        <td><%= item.loai_ten%></td>
        <td><%- item.ngay_vv %></td>
        <td><%- item.ngay_rv %></td>
        <td><%- item.hinh_thuc_ten %></td>
        <td><%- item.ten_nguyen_nhan %></td>
        <td class="text-left"><%- item.quyen_loi_ten %></td>
        <td class="text-left"><%- item.ten_benh_vien %></td>
        <td class="text-left"><%- item.chan_doan %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_yc) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_duyet) %></td>
        <td class="text-right"><%- item.so_ngay_duyet %></td>
        <td class="text-center"><%- item.trang_thai %></td>
        <td class="text-center">
            <% if(item.nguyen_nhan != null && item.nguyen_nhan != '' && item.nguyen_nhan != undefined){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- item.nguyen_nhan %>" onclick="showNguyenNhanGiamTru(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Lý do giảm trừ"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhanGiamTru(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Lý do giảm trừ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>

    <% if(arrHoSo.length < 11){
    for(var i = 0; i < 11 - arrHoSo.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
<script type="text/html" id="tblCapNhatUocTheoDiem_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr class="capNhatUoc">
            <td class="text-center">
                <input type="hidden" data-field="nv" name="nv" value="<%- item.nv %>" />
                <input type="hidden" data-field="lh_nv" name="lh_nv" value="<%- item.lh_nv %>" />
                <input type="hidden" data-field="diem" name="diem" value="<%- item.diem %>" />
                <input type="hidden" data-field="tich_hop" name="tich_hop" value="<%- item.tich_hop %>" />
                <input type="hidden" data-field="log_rq" name="log_rq" value="<%- item.log_rq %>" />
                <input type="hidden" data-field="log_res" name="log_res" value="<%- item.log_res %>" />
                <input type="hidden" data-field="bt" name="bt" value="<%- item.bt %>" />
                <%- index + 1 %>
            </td>
            <td class="text-center">
                <input type="text" data-field="ngay_dp" class="floating-input datepicker text-center" readonly value="<%- item.ngay_dp %>" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten_diem" data-field="ten_diem" value="<%- item.ten_diem %>" />
                <input type="text" class="floating-input combobox" data-field="ten_diem" data-val="<%- item.diem %>" @* onclick="chonDiemDuPhong(this)" *@ readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten_diem %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten" data-field="ten" value="<%- item.ten %>" />
                <input type="text" class="floating-input combobox" data-field="ten" data-val="<%- item.lh_nv %>" @* onclick="chonNV(this)" *@ readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-center">
                <input type="text" class="floating-input" data-field="lh_nv" data-val="<%- item.lh_nv %>" readonly value="<%- item.lh_nv %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien" name="tien" class="number floating-input tien_<%- item.bt %>" autocomplete="off" disabled value="<%- ESUtil.formatMoney(item.tien) %>" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien_chenh_lech" name="tien_chenh_lech" class="number floating-input tien_chenh_lech" disabled value="<%- ESUtil.formatMoney(item.tien_chenh_lech) %>" />
            </td>
            <td class="text-center">
                <% if(item.tich_hop == 1){%>
                     <i class="fas fa-check-circle text-success" title="Đã tích hợp"></i>
                <%}else{%>
                    <i class="fas fa-times" style="color:red" title="Chưa tích hợp"></i>
                <%}%>
            </td>
            <td class="text-center">
                <% if(item.log_rq != null && item.log_res !=""){ %>
                    <a href="#" class="cursor-pointer combobox" onclick="showLogRq(this)" data-rq="<%- JSON.stringify(item.log_rq) %>" data-res="<%- JSON.stringify(item.log_res) %>" >
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                    <% }else{ %>
                    <a class="cursor-pointer combobox" onclick="showLogRq(this)" data-val="" data-rq="" data-res="">
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                <% } %>
            </td>
            <td class="text-center">
                <% if(item.tich_hop != 1){%>
                    <a href="#" class="cursor-pointer" onclick="tichHopLaiUoc(<%- item.bt %>)">
                        <i class="fas fa-arrow-right" style="color: var(--escs-main-theme-color)" title="Tích hợp lại ước"></i>
                    </a>
                <%}else{%>

                <%}%>
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblBienDo_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr>
            <td>
                <input type="hidden" data-field="diem" name="diem" data-val="<%- item.diem %>" value="<%- item.diem %>"/>
                <%- item.ten_diem %>
            </td>
            <td class="text-right">
                <input type="text" data-field="ty_le" name="ty_le" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(item.ty_le) %>" />
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 3){
    for(var i = 0; i < 3 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*tab Thông tin OCR Giấy yêu cầu bảo hiểm*@
<script type="text/html" id="modalDataOCRGiayYCBH_template">
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin người được bảo hiểm</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_cong_ty_bh" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_cong_ty_bh">&nbsp;</label>
            </div>
        </td>
        <td>Công ty bảo hiểm</td>
        <td><%- giay_ycbh_thong_tin_ndbh?.cong_ty_bh %></td>
        <td>
            <input type="text" name="cong_ty_bh" autocomplete="off" class="floating-input combo_box" style="cursor:pointer" data-val="<%- giay_ycbh_thong_tin_ndbh?.cong_ty_bh_dc %>" value="<%- giay_ycbh_thong_tin_ndbh?.cong_ty_bh_dc_ten %>"  onclick="chonCongTyBH(this)" readonly placeholder="Chọn công ty bảo hiểm" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_so_gcn" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_so_gcn">&nbsp;</label>
            </div>
        </td>
        <td>Số GCN</td>
        <td><%- giay_ycbh_thong_tin_ndbh?.so_gcn %></td>
        <td>
            <input type="text" name="so_gcn" autocomplete="off" class="floating-input" value="<%- giay_ycbh_thong_tin_ndbh?.so_gcn_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_so_hd" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_so_hd">&nbsp;</label>
            </div>
        </td>
        <td>Số hợp đồng <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_ndbh?.so_hd %></td>
        <td>
            <input type="text" name="so_hd" placeholder="Nhập dữ liệu bắt buộc" autocomplete="off" class="floating-input check-input" value="<%- giay_ycbh_thong_tin_ndbh?.so_hd_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ten_ndbh" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ten_ndbh">&nbsp;</label>
            </div>
        </td>
        <td>Tên NĐBH <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_ndbh?.ten_ndbh %></td>
        <td>
            <input type="text" name="ten_ndbh" placeholder="Nhập dữ liệu bắt buộc" autocomplete="off" class="floating-input check-input" value="<%- giay_ycbh_thong_tin_ndbh?.ten_ndbh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ngay_sinh" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ngay_sinh">&nbsp;</label>
            </div>
        </td>
        <td>Ngày sinh <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_ndbh?.ngay_sinh %></td>
        <td>
            <input type="text" name="ngay_sinh" class="floating-input datepicker check-input" value="<%- giay_ycbh_thong_tin_ndbh?.ngay_sinh_dc %>" display-format="date" placeholder="Nhập dữ liệu bắt buộc">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_so_cmt" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_so_cmt">&nbsp;</label>
            </div>
        </td>
        <td>Số CMT/ CCCD <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_ndbh?.so_cmt %></td>
        <td>
            <input type="text" name="so_cmt" autocomplete="off" placeholder="Nhập dữ liệu bắt buộc" class="floating-input check-input" value="<%- giay_ycbh_thong_tin_ndbh?.so_cmt_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_sdt" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_sdt">&nbsp;</label>
            </div>
        </td>
        <td>Số điện thoại</td>
        <td><%- giay_ycbh_thong_tin_ndbh?.sdt %></td>
        <td>
            <input type="text" name="sdt" autocomplete="off" class="floating-input" value="<%- giay_ycbh_thong_tin_ndbh?.sdt_dc %>" />
        </td>
    </tr>
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin người thông báo, liên hệ</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_nguoi_lh" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_nguoi_lh">&nbsp;</label>
            </div>
        </td>
        <td>Họ và tên người liên hệ/ người thông báo <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lh?.nguoi_lh %></td>
        <td>
            <input type="text" name="nguoi_lh" autocomplete="off" placeholder="Nhập dữ liệu bắt buộc" class="floating-input check-input" value="<%- giay_ycbh_thong_tin_lh?.nguoi_lh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_nguoi_lh_la" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_nguoi_lh_la">&nbsp;</label>
            </div>
        </td>
        <td>Mối quan hệ người liên hệ/ người thông báo <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lh?.nguoi_lh_la %></td>
        <td>
            <input type="text" name="nguoi_lh_la" autocomplete="off" class="floating-input combo_box check-input" style="cursor:pointer" data-val="<%- giay_ycbh_thong_tin_lh?.nguoi_lh_la_dc %>" value="<%- giay_ycbh_thong_tin_lh?.nguoi_lh_la_dc_ten %>"  onclick="chonMqhLienHe(this)" readonly placeholder="Nhập dữ liệu bắt buộc" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_dthoai_nguoi_lh" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_dthoai_nguoi_lh">&nbsp;</label>
            </div>
        </td>
        <td>Điện thoại người liên hệ/ người thông báo <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lh?.dthoai_nguoi_lh %></td>
        <td>
            <input type="text" name="dthoai_nguoi_lh" autocomplete="off" placeholder="Nhập dữ liệu bắt buộc" class="floating-input check-input" value="<%- giay_ycbh_thong_tin_lh?.dthoai_nguoi_lh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_email_nguoi_lh" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_email_nguoi_lh">&nbsp;</label>
            </div>
        </td>
        <td>Email người liên hệ/ người thông báo <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lh?.email_nguoi_lh %></td>
        <td>
            <input type="text" name="email_nguoi_lh" autocomplete="off" placeholder="Nhập dữ liệu bắt buộc" class="floating-input check-input" value="<%- giay_ycbh_thong_tin_lh?.email_nguoi_lh_dc %>" />
        </td>
    </tr>
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin lần khám, quyền lợi</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ngay_kham" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ngay_kham">&nbsp;</label>
            </div>
        </td>
        <td>Ngày khám <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.ngay_kham %></td>
        <td>
            <input type="text" name="ngay_kham" class="floating-input datepicker check-input" value="<%- giay_ycbh_thong_tin_lan_kham?.ngay_kham_dc %>" display-format="date" placeholder="Nhập dữ liệu bắt buộc">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ngay_vv" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ngay_vv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày vào viện <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.ngay_vv %></td>
        <td>
            <input type="text" name="ngay_vv" class="floating-input datepicker check-input" value="<%- giay_ycbh_thong_tin_lan_kham?.ngay_vv_dc %>" display-format="date" placeholder="Nhập dữ liệu bắt buộc">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ngay_rv" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ngay_rv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày kết thúc/ ra viện <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.ngay_rv %></td>
        <td>
            <input type="text" name="ngay_rv" class="floating-input datepicker check-input" value="<%- giay_ycbh_thong_tin_lan_kham?.ngay_rv_dc %>" display-format="date" placeholder="Nhập dữ liệu bắt buộc">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_benh_vien" class="custom-control-input gycbh_checkbox checked_default" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_benh_vien">&nbsp;</label>
            </div>
        </td>
        <td>Cơ sở y tế <i class="fas fa-asterisk" style="color: red; font-weight: 600; font-size: 9px"></i></td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.benh_vien %></td>
        <td>
            <input type="text" name="benh_vien" data-val="<%- giay_ycbh_thong_tin_lan_kham?.benh_vien_dc %>" style="cursor:pointer" autocomplete="off" class="floating-input combo_box check-input" value="<%- giay_ycbh_thong_tin_lan_kham?.benh_vien_dc_ten %>" onclick="chonBenhVien(this)" readonly placeholder="Nhập dữ liệu bắt buộc" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_nhom_nguyen_nhan" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_nhom_nguyen_nhan">&nbsp;</label>
            </div>
        </td>
        <td>Nhóm nguyên nhân</td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.nhom_nguyen_nhan %></td>
        <td>
            <input type="text" name="nhom_nguyen_nhan" style="cursor:pointer" data-val="<%- giay_ycbh_thong_tin_lan_kham?.nhom_nguyen_nhan_dc %>" autocomplete="off" class="floating-input combo_box" value="<%- giay_ycbh_thong_tin_lan_kham?.nhom_nguyen_nhan_dc_ten %>" onclick="chonNhomNguyenNhan(this)" readonly placeholder="Chọn nhóm nguyên nhân" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_hinh_thuc" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_hinh_thuc">&nbsp;</label>
            </div>
        </td>
        <td>Hình thức điều trị</td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.hinh_thuc %></td>
        <td>
            <input type="text" name="hinh_thuc" style="cursor:pointer" autocomplete="off" class="floating-input combo_box" data-val="<%- giay_ycbh_thong_tin_lan_kham?.hinh_thuc_dc %>" value="<%- giay_ycbh_thong_tin_lan_kham?.hinh_thuc_dc_ten %>" onclick="chonHinhThucDieuTri(this)" readonly placeholder="Chọn hình thức điều trị" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ma_benh" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ma_benh">&nbsp;</label>
            </div>
        </td>
        <td>Mã bệnh ICD</td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.ma_benh %></td>
        <td>
            <input type="text" name="ma_benh" autocomplete="off" class="floating-input combo_box" data-val="<%- giay_ycbh_thong_tin_lan_kham?.ma_benh_dc %>" value="<%- giay_ycbh_thong_tin_lan_kham?.ma_benh_dc %>"  onclick="chonMaBenhICD(this)" readonly placeholder="Chưa xác định" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_chan_doan" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_chan_doan">&nbsp;</label>
            </div>
        </td>
        <td>Chẩn đoán</td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.chan_doan %></td>
        <td>
            <input type="text" name="chan_doan" autocomplete="off" class="floating-input" value="<%- giay_ycbh_thong_tin_lan_kham?.chan_doan_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_so_tien_yc" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_so_tien_yc">&nbsp;</label>
            </div>
        </td>
        <td>Số tiền yêu cầu</td>
        <td><%- giay_ycbh_thong_tin_lan_kham?.so_tien_yc %></td>
        <td>
            <input type="text" name="so_tien_yc" autocomplete="off" class="floating-input money" value="<%- ESUtil.formatMoney(giay_ycbh_thong_tin_lan_kham?.so_tien_yc_dc?.replace(/[^0-9+]+/g, '')) %>" />
        </td>
    </tr>
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin thụ hưởng</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_pttt" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_pttt">&nbsp;</label>
            </div>
        </td>
        <td>Phương thức thanh toán</td>
        <td><%- giay_ycbh_thong_tin_thu_huong?.pttt %></td>
        <td>
            <input type="text" name="pttt" style="cursor:pointer" autocomplete="off" class="floating-input combo_box" data-val="<%- giay_ycbh_thong_tin_thu_huong?.pttt_dc %>" value="<%- giay_ycbh_thong_tin_thu_huong?.pttt_dc_ten %>" onclick="chonPhuongThucThanhToan(this)" readonly placeholder="Chọn phương thức thanh toán" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ten_thu_huong" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ten_thu_huong">&nbsp;</label>
            </div>
        </td>
        <td>Tên đối tượng thụ hưởng</td>
        <td><%- giay_ycbh_thong_tin_thu_huong?.ten_thu_huong %></td>
        <td>
            <input type="text" name="ten_thu_huong" autocomplete="off" class="floating-input" value="<%- giay_ycbh_thong_tin_thu_huong?.ten_thu_huong_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_ngan_hang_thu_huong" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_ngan_hang_thu_huong">&nbsp;</label>
            </div>
        </td>
        <td>Ngân hàng/ Đơn vị thụ hưởng</td>
        <td><%- giay_ycbh_thong_tin_thu_huong?.ngan_hang_thu_huong %></td>
        <td>
            <input type="text" name="ngan_hang_thu_huong" autocomplete="off" class="floating-input combo_box" data-val="<%- giay_ycbh_thong_tin_thu_huong?.ngan_hang_thu_huong_dc %>" value="<%- giay_ycbh_thong_tin_thu_huong?.ngan_hang_thu_huong_dc_ten %>" onclick="chonNganHang(this)" readonly placeholder="Chọn ngân hàng thanh toán" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_stk_thu_huong" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_stk_thu_huong">&nbsp;</label>
            </div>
        </td>
        <td>Số tài khoản thụ hưởng</td>
        <td><%- giay_ycbh_thong_tin_thu_huong?.stk_thu_huong %></td>
        <td>
            <input type="text" name="stk_thu_huong" autocomplete="off" class="floating-input" value="<%- giay_ycbh_thong_tin_thu_huong?.stk_thu_huong_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gycbh_ad_so_tien_thu_huong" class="custom-control-input gycbh_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="gycbh_ad_so_tien_thu_huong">&nbsp;</label>
            </div>
        </td>
        <td>Số tiền thụ hưởng</td>
        <td><%- giay_ycbh_thong_tin_thu_huong?.so_tien_thu_huong %></td>
        <td>
            <input type="text" name="so_tien_thu_huong" autocomplete="off" class="floating-input money" value="<%- ESUtil.formatMoney(giay_ycbh_thong_tin_thu_huong?.so_tien_thu_huong_dc?.replace(/[^0-9+]+/g, '')) %>" />
        </td>
    </tr>
</script>

@* tab Thông tin OCR Giấy ra viện *@
<script type="text/html" id="modalDataOCRGiayRaVien_template">
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin người được bảo hiểm</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">

            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_ten_ndbh" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_ten_ndbh">&nbsp;</label>
            </div>
        </td>
        <td class="_required">Họ tên bệnh nhân/ Họ tên người bệnh/ Họ và tên</td>
        <td><%- giay_rv_thong_tin_ndbh?.ten_ndbh %></td>
        <td>
            <input type="text" name="ten_ndbh" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.ten_ndbh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_ngay_sinh" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_ngay_sinh">&nbsp;</label>
            </div>
        </td>
        <td>Năm sinh/ Tuổi</td>
        <td><%- giay_rv_thong_tin_ndbh?.ngay_sinh %></td>
        <td>
            <input type="text" name="ngay_sinh" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.ngay_sinh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_gioi_tinh" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_gioi_tinh">&nbsp;</label>
            </div>
        </td>
        <td>Giới tính/ Nam/ Nữ</td>
        <td><%- giay_rv_thong_tin_ndbh?.gioi_tinh %></td>
        <td>
            <input type="text" name="gioi_tinh" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.gioi_tinh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_dia_chi" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_dia_chi">&nbsp;</label>
            </div>
        </td>
        <td>Địa chỉ</td>
        <td><%- giay_rv_thong_tin_ndbh?.dia_chi %></td>
        <td>
            <input type="text" name="dia_chi" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.dia_chi_dc %>" />
        </td>
    </tr>
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin lần khám, quyền lợi</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_benh_vien" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_benh_vien">&nbsp;</label>
            </div>
        </td>
        <td>Tên cơ sở y tế</td>
        <td><%- giay_rv_thong_tin_ndbh?.benh_vien %></td>
        <td>
            <input type="text" name="benh_vien" style="cursor:pointer" autocomplete="off" class="floating-input combo_box" data-val="<%- giay_rv_thong_tin_ndbh?.benh_vien_dc %>" value="<%- giay_rv_thong_tin_ndbh?.benh_vien_dc_ten %>" onclick="chonBenhVien(this)" readonly placeholder="Chưa xác định" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_khoa" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_khoa">&nbsp;</label>
            </div>
        </td>
        <td>Khoa</td>
        <td><%- giay_rv_thong_tin_ndbh?.khoa %></td>
        <td>
            <input type="text" name="khoa" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.khoa_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_ma_y_te" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_ma_y_te">&nbsp;</label>
            </div>
        </td>
        <td>Mã y tế/ PID</td>
        <td><%- giay_rv_thong_tin_ndbh?.ma_y_te %></td>
        <td>
            <input type="text" name="ma_y_te" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.ma_y_te_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_ngay_vv" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_ngay_vv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày vào viện/Vào viện lúc</td>
        <td><%- giay_rv_thong_tin_ndbh?.ngay_vv %></td>
        <td>
            <input type="text" name="ngay_vv" class="floating-input datepicker" value="<%- giay_rv_thong_tin_ndbh?.ngay_vv_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_ngay_rv" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_ngay_rv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày ra viện/Ra viện lúc</td>
        <td><%- giay_rv_thong_tin_ndbh?.ngay_rv %></td>
        <td>
            <input type="text" name="ngay_rv" class="floating-input datepicker" value="<%- giay_rv_thong_tin_ndbh?.ngay_rv_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_chan_doan" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_chan_doan">&nbsp;</label>
            </div>
        </td>
        <td>Chẩn đoán</td>
        <td><%- giay_rv_thong_tin_ndbh?.chan_doan %></td>
        <td>
            <input type="text" name="chan_doan" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.chan_doan_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_phuong_phap_dtri" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_phuong_phap_dtri">&nbsp;</label>
            </div>
        </td>
        <td>Phương pháp điều trị</td>
        <td><%- giay_rv_thong_tin_ndbh?.phuong_phap_dtri %></td>
        <td>
            <input type="text" name="phuong_phap_dtri" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.phuong_phap_dtri_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_ghi_chu" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_ghi_chu">&nbsp;</label>
            </div>
        </td>
        <td>Kế hoạch điều trị tiếp theo/ ghi chú</td>
        <td><%- giay_rv_thong_tin_ndbh?.ghi_chu %></td>
        <td>
            <input type="text" name="ghi_chu" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.ghi_chu_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_ma_benh" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_ma_benh">&nbsp;</label>
            </div>
        </td>
        <td>Mã ICD</td>
        <td><%- giay_rv_thong_tin_ndbh?.ma_benh %></td>
        <td>
            <input type="text" name="ma_benh" style="cursor:pointer" autocomplete="off" class="floating-input combo_box" data-val="<%- giay_rv_thong_tin_ndbh?.ma_benh_dc %>" value="<%- giay_rv_thong_tin_ndbh?.ma_benh_dc %>"  onclick="chonMaBenhICD(this)" readonly placeholder="Chưa xác định" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_dan_toc" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_dan_toc">&nbsp;</label>
            </div>
        </td>
        <td>Dân tộc</td>
        <td><%- giay_rv_thong_tin_ndbh?.dan_toc %></td>
        <td>
            <input type="text" name="dan_toc" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.dan_toc_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_nghe_nghiep" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_nghe_nghiep">&nbsp;</label>
            </div>
        </td>
        <td>Nghề nghiệp</td>
        <td><%- giay_rv_thong_tin_ndbh?.nghe_nghiep %></td>
        <td>
            <input type="text" name="nghe_nghiep" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.nghe_nghiep_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_bhxh_so" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_bhxh_so">&nbsp;</label>
            </div>
        </td>
        <td>Mã số BHXH/Thẻ BHYT số</td>
        <td><%- giay_rv_thong_tin_ndbh?.bhxh_so %></td>
        <td>
            <input type="text" name="bhxh_so" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.bhxh_so_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="grv_ad_benh_kem_theo" class="custom-control-input grv_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="grv_ad_benh_kem_theo">&nbsp;</label>
            </div>
        </td>
        <td>Bệnh kèm theo</td>
        <td><%- giay_rv_thong_tin_ndbh?.benh_kem_theo %></td>
        <td>
            <input type="text" name="benh_kem_theo" autocomplete="off" class="floating-input" value="<%- giay_rv_thong_tin_ndbh?.benh_kem_theo_dc %>" />
        </td>
    </tr>
</script>

@* tab Thông tin OCR Bảng kê *@
<script type="text/html" id="modalDataOCRBangKe_template">
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin người được bảo hiểm</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">

            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_ten_ndbh" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_ten_ndbh">&nbsp;</label>
            </div>
        </td>
        <td>Họ tên bệnh nhân/Họ tên người bệnh/Họ tên khách hàng/Họ tên</td>
        <td><%- bk_thong_tin_ndbh?.ten_ndbh %></td>
        <td>
            <input type="text" name="ten_ndbh" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.ten_ndbh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_nam_sinh" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_nam_sinh">&nbsp;</label>
            </div>
        </td>
        <td>Tuổi/Năm sinh</td>
        <td><%- bk_thong_tin_ndbh?.nam_sinh %></td>
        <td>
            <input type="text" name="nam_sinh" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.nam_sinh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_gioi_tinh" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_gioi_tinh">&nbsp;</label>
            </div>
        </td>
        <td>Giới tính</td>
        <td><%- bk_thong_tin_ndbh?.gioi_tinh %></td>
        <td>
            <input type="text" name="gioi_tinh" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.gioi_tinh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_dia_chi" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_dia_chi">&nbsp;</label>
            </div>
        </td>
        <td>Địa chỉ/Địa chỉ hiện tại</td>
        <td><%- bk_thong_tin_ndbh?.dia_chi %></td>
        <td>
            <input type="text" name="dia_chi" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.dia_chi_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_bhyt_so" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_bhyt_so">&nbsp;</label>
            </div>
        </td>
        <td>Số thẻ KCB/Mã thẻ BHYT</td>
        <td><%- bk_thong_tin_ndbh?.bhyt_so %></td>
        <td>
            <input type="text" name="bhyt_so" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.bhyt_so_dc %>" />
        </td>
    </tr>
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin bảng kê chi tiết</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_tieu_de" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_tieu_de">&nbsp;</label>
            </div>
        </td>
        <td>Tên bảng kê</td>
        <td><%- bk_thong_tin_ndbh?.tieu_de %></td>
        <td>
            <input type="text" name="tieu_de" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.tieu_de_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_ten_noi_xuat" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_ten_noi_xuat">&nbsp;</label>
            </div>
        </td>
        <td>Tên nơi xuất</td>
        <td><%- bk_thong_tin_ndbh?.ten_noi_xuat %></td>
        <td>
            <input type="text" name="ten_noi_xuat" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.ten_noi_xuat_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_so_bang_ke" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_so_bang_ke">&nbsp;</label>
            </div>
        </td>
        <td>Số bảng kê</td>
        <td><%- bk_thong_tin_ndbh?.so_bang_ke %></td>
        <td>
            <input type="text" name="so_bang_ke" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.so_bang_ke_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_ngay_bang_ke" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_ngay_bang_ke">&nbsp;</label>
            </div>
        </td>
        <td>Ngày bảng kê</td>
        <td><%- bk_thong_tin_ndbh?.ngay_bang_ke %></td>
        <td>
            <input type="text" name="ngay_bang_ke" class="floating-input datepicker" value="<%- bk_thong_tin_ndbh?.ngay_bang_ke_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_ngay_vv" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_ngay_vv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày vào viện</td>
        <td><%- bk_thong_tin_ndbh?.ngay_vv %></td>
        <td>
            <input type="text" name="ngay_vv" class="floating-input datepicker" value="<%- bk_thong_tin_ndbh?.ngay_vv_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_ngay_rv" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_ngay_rv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày ra viện</td>
        <td><%- bk_thong_tin_ndbh?.ngay_rv %></td>
        <td>
            <input type="text" name="ngay_rv" class="floating-input datepicker" value="<%- bk_thong_tin_ndbh?.ngay_rv_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_pid" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_pid">&nbsp;</label>
            </div>
        </td>
        <td>Mã y tế/ PID</td>
        <td><%- bk_thong_tin_ndbh?.pid %></td>
        <td>
            <input type="text" name="pid" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.pid_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_tien_thanh_toan" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_tien_thanh_toan">&nbsp;</label>
            </div>
        </td>
        <td>Tổng tiền thanh toán</td>
        <td><%- bk_thong_tin_ndbh?.tien_thanh_toan %></td>
        <td>
            <input type="text" name="tien_thanh_toan" autocomplete="off" class="floating-input number" value="<%- ESUtil.formatMoney(bk_thong_tin_ndbh?.tien_thanh_toan_dc?.replace(/[^0-9+]+/g, '')) %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_insurance_payment" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_insurance_payment">&nbsp;</label>
            </div>
        </td>
        <td>Quỹ BHYT thanh toán</td>
        <td><%- bk_thong_tin_ndbh?.insurance_payment %></td>
        <td>
            <input type="text" name="insurance_payment" autocomplete="off" class="floating-input number" value="<%- ESUtil.formatMoney(bk_thong_tin_ndbh?.insurance_payment_dc?.replace(/[^0-9+]+/g, '')) %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_chan_doan" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_chan_doan">&nbsp;</label>
            </div>
        </td>
        <td>Chẩn đoán xác định/Chẩn đoán/Chẩn đoán khi ra viện</td>
        <td><%- bk_thong_tin_ndbh?.chan_doan %></td>
        <td>
            <input type="text" name="chan_doan" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.chan_doan_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bk_ad_other_diagnosis" class="custom-control-input bang_ke_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="bk_ad_other_diagnosis">&nbsp;</label>
            </div>
        </td>
        <td>Bệnh kèm theo</td>
        <td><%- bk_thong_tin_ndbh?.other_diagnosis %></td>
        <td>
            <input type="text" name="other_diagnosis" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.other_diagnosis_dc %>" />
        </td>
    </tr>
</script>

@* tab Thông tin OCR Phiếu khám *@
<script type="text/html" id="modalDataOCRPhieuKham_template">
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin người được bảo hiểm</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_ten_ndbh" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_ten_ndbh">&nbsp;</label>
            </div>
        </td>
        <td>Họ tên bệnh nhân/Họ và tên/Họ và tên người bệnh/Ông(Bà) <i class="fas fa-asterisk"</td>
        <td><%- pk_thong_tin_ndbh?.ten_ndbh %></td>
        <td>
            <input type="text" name="ten_ndbh" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.ten_ndbh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_ngay_sinh" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_ngay_sinh">&nbsp;</label>
            </div>
        </td>
        <td>Năm sinh/Tuổi/Ngày sinh</td>
        <td><%- pk_thong_tin_ndbh?.ngay_sinh %></td>
        <td>
            <input type="text" name="ngay_sinh" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.ngay_sinh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_gioi_tinh" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_gioi_tinh">&nbsp;</label>
            </div>
        </td>
        <td>Giới tính</td>
        <td><%- pk_thong_tin_ndbh?.gioi_tinh %></td>
        <td>
            <input type="text" name="gioi_tinh" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.gioi_tinh_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_dia_chi" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_dia_chi">&nbsp;</label>
            </div>
        </td>
        <td>Địa chỉ</td>
        <td><%- pk_thong_tin_ndbh?.dia_chi %></td>
        <td>
            <input type="text" name="dia_chi" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.dia_chi_dc %>" />
        </td>
    </tr>
    <tr class="text-left card-title-bg">
        <td colspan="4">
            <span style="font-weight: bold; font-size:12px">Thông tin lần khám, quyền lợi</span>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_benh_vien" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_benh_vien">&nbsp;</label>
            </div>
        </td>
        <td>Tên cơ sở y tế</td>
        <td><%- pk_thong_tin_ndbh?.benh_vien %></td>
        <td>
            <input type="text" name="benh_vien" style="cursor:pointer" autocomplete="off" class="floating-input combo_box" data-val="<%- pk_thong_tin_ndbh?.benh_vien_dc %>" value="<%- pk_thong_tin_ndbh?.benh_vien_dc_ten %>" onclick="chonBenhVien(this)" readonly placeholder="Chưa xác định" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_pid" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_pid">&nbsp;</label>
            </div>
        </td>
        <td>Mã y tế / PID/Số đăng ký</td>
        <td><%- pk_thong_tin_ndbh?.pid %></td>
        <td>
            <input type="text" name="pid" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.pid_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_ngay_kham" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_ngay_kham">&nbsp;</label>
            </div>
        </td>
            <td>Ngày khám/Ngày khám bệnh/Thời gian khám/Đến khám bệnh lúc</td>
        <td><%- pk_thong_tin_ndbh?.ngay_kham %></td>
        <td>
            <input type="text" name="ngay_kham" class="floating-input datepicker" value="<%- pk_thong_tin_ndbh?.ngay_kham_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_ly_do_kham" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_ly_do_kham">&nbsp;</label>
            </div>
        </td>
        <td>Lý do đến khám/ Triệu chứng/Lý do vào viện/Lý do khám bệnh/Lý do vào viện</td>
        <td><%- pk_thong_tin_ndbh?.ly_do_kham %></td>
        <td>
            <input type="text" name="ly_do_kham" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.ly_do_kham_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_benh_su" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_benh_su">&nbsp;</label>
            </div>
        </td>
        <td>Quá trình bệnh lý/ Bệnh sử</td>
        <td><%- pk_thong_tin_ndbh?.benh_su %></td>
        <td>
            <input type="text" name="benh_su" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.benh_su_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_tien_su" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_tien_su">&nbsp;</label>
            </div>
        </td>
        <td>Tiền sử</td>
        <td><%- pk_thong_tin_ndbh?.tien_su %></td>
        <td>
            <input type="text" name="tien_su" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.tien_su_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_trieu_chung" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_trieu_chung">&nbsp;</label>
            </div>
        </td>
        <td>Khám lâm sàng/Triệu chứng lâm sàng</td>
        <td><%- pk_thong_tin_ndbh?.trieu_chung %></td>
        <td>
            <input type="text" name="trieu_chung" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.trieu_chung_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_xet_nghiem" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_xet_nghiem">&nbsp;</label>
            </div>
        </td>
        <td>Các xét nghiệm, thăm dò chính</td>
        <td><%- pk_thong_tin_ndbh?.xet_nghiem %></td>
        <td>
            <input type="text" name="xet_nghiem" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.xet_nghiem_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_chan_doan_so_bo" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_chan_doan_so_bo">&nbsp;</label>
            </div>
        </td>
        <td>Chẩn đoán sơ bộ</td>
        <td><%- pk_thong_tin_ndbh?.chan_doan_so_bo %></td>
        <td>
            <input type="text" name="chan_doan_so_bo" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.chan_doan_so_bo_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_chan_doan" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_chan_doan">&nbsp;</label>
            </div>
        </td>
        <td>Chẩn đoán xác định/Chẩn đoán/Sau ký hiệu</td>
        <td><%- pk_thong_tin_ndbh?.chan_doan %></td>
        <td>
            <input type="text" name="chan_doan" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.chan_doan_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_huong_dieu_tri" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_huong_dieu_tri">&nbsp;</label>
            </div>
        </td>
        <td>Hướng điều trị/Hướng xử trí</td>
        <td><%- pk_thong_tin_ndbh?.huong_dieu_tri %></td>
        <td>
            <input type="text" name="huong_dieu_tri" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.huong_dieu_tri_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_don_thuoc" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_don_thuoc">&nbsp;</label>
            </div>
        </td>
        <td>Đơn thuốc</td>
        <td><%- pk_thong_tin_ndbh?.don_thuoc %></td>
        <td>
            <input type="text" name="don_thuoc" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.don_thuoc_dc %>" />
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_hen_ngay_kham" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_hen_ngay_kham">&nbsp;</label>
            </div>
        </td>
        <td>Hẹn ngày tái khám</td>
        <td><%- pk_thong_tin_ndbh?.hen_ngay_kham %></td>
        <td>
            <input type="text" name="hen_ngay_kham" class="floating-input datepicker" value="<%- pk_thong_tin_ndbh?.hen_ngay_kham_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="pk_ad_ma_benh" class="custom-control-input phieu_kham_checkbox" onclick="onSuDungDuLieuOCR(this)">
                <label class="custom-control-label" for="pk_ad_ma_benh">&nbsp;</label>
            </div>
        </td>
        <td>Mã ICD/Chẩn đoán theo ICD</td>
        <td><%- pk_thong_tin_ndbh?.ma_benh %></td>
        <td>
            <input type="text" name="ma_benh" autocomplete="off" class="floating-input" value="<%- pk_thong_tin_ndbh?.ma_benh_dc %>" />
        </td>
    </tr>
</script>

@* tab đối chiếu thông tin OCR *@
<script type="text/html" id="tblDoiChieuThongTinOCRThongTinLienHe_template">
    <table class="table">
        <tr>
            <td style="width:15%">Người liên hệ</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.nguoi_lh %></td>
            <td style="width:15%">SĐT liên hệ</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.dthoai_nguoi_lh %></td>
        </tr>
        <tr>
            <td style="width:15%">Email liên hệ</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.email_nguoi_lh %></td>
            <td style="width:15%">Mối quan hệ</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.nguoi_lh_la %></td>
        </tr>
    </table>
</script>

<script type="text/html" id="tblDoiChieuThongTinOCRThongTinHopDong_template">
    <table class="table">
        <tr>
            <td style="width:15%">Tên NĐBH</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.ten_ndbh %></td>
            <td style="width:15%">Ngày sinh</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.ngay_sinh %></td>
        </tr>
        <tr>
            <td style="width:15%">CCCD</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.so_cmt %></td>
            <td style="width:15%">Điện thoại</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.d_thoai %></td>
        </tr>
        <tr>
            <td style="width:15%">Số hợp đồng</td>
            <td><%- thong_tin_tong_hop?.so_hd %></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td style="width:15%">Số GCN</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.so_gcn %></td>
            <td style="width:15%">Công ty bảo hiểm</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.don_vi_cap_don %></td>
        </tr>
    </table>
</script>

<script type="text/html" id="tblDoiChieuThongTinOCRThongTinLanKham_template">
    <table class="table">
        <tr>
            <td style="width:15%">Ngày vào viện</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.ngay_vv %></td>
            <td style="width:15%">Ngày ra viện</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.ngay_rv %></td>
        </tr>
        <tr>
            <td style="width:15%">Cơ sở y tế</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.benh_vien %></td>
            <td style="width:15%">Khoa</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.khoa %></td>
        </tr>
        <tr>
            <td style="width:15%">Nhóm nguyên nhân</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.nhom_nguyen_nhan %></td>
            <td style="width:15%">Hình thức điều trị</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.hinh_thuc_dtri %></td>
        </tr>
        <tr>
            <td style="width:15%">Mã bệnh ICD</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.ma_benh %></td>
            <td style="width:15%">Chẩn đoán</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.chan_doan %></td>
        </tr>
        <tr>
            <td style="width:15%">Số tiền yêu cầu</td>
            <td><%- thong_tin_tong_hop?.so_tien_yc %></td>
            <td></td>
            <td></td>
        </tr>
    </table>
</script>

<script type="text/html" id="tblDoiChieuThongTinOCRThongTinThanhToanThuHuong_template">
    <table class="table">
        <tr>
            <td style="width:15%">Phương thức thanh toán</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.pttt_ten %></td>
            <td style="width:15%">Tên người thụ hưởng</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.ten_thu_huong %></td>
        </tr>
        <tr>
            <td style="width:15%">Ngân hàng</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.ngan_hang_thu_huong %></td>
            <td style="width:15%">STK</td>
            <td style="width:35%"><%- thong_tin_tong_hop?.so_tk %></td>
        </tr>
        <tr>
            <td style="width:15%">Số tiền thụ hưởng</td>
            <td><%- thong_tin_tong_hop?.so_tien_thu_huong %></td>
            <td></td>
            <td></td>
        </tr>
    </table>
</script>