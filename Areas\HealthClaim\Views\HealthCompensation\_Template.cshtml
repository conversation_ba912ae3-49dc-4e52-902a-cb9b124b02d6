﻿<script type="text/html" id="detail_template">
    <tr class="text-center edit">
        <td>
            <input type="text" name="" class="floating-input" value="">
        </td>
        <td class="text-right">
            <input type="text" name="" class="decimal floating-input" value="0">
        </td>
        <td>
            <input type="text" name="" class="floating-input" value="">
        </td>
        <td class="text-center">
            <i class="fa fa-trash text-danger remove_info"></i>
        </td>
    </tr>
</script>

@*tab Thông tin chung*@
<script type="text/html" id="navThongTinChung_template">
    <div class="card-body p-2" style="padding-top:5px">
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Thông tin hồ sơ</h5>
            </div>
            <table class="table" id="HealthGuaranteeCommon">
                <tr>
                    <td style="width:104px">Ngày mở HSBT</td>
                    <td><%- ho_so.ngay_ht %></td>
                </tr>
                <tr>
                    <td>Ngày thông báo</td>
                    <td><%- ho_so.ngay_tb %></td>
                </tr>
                <tr>
                    <td>Nguồn TB</td>
                    <td><%- ho_so.nguon_tb_ten %></td>
                </tr>
                <tr>
                    <td>Sản phẩm</td>
                    <td>
                        <%- ho_so.ten_sp %>
                    </td>
                </tr>
                <tr>
                    <td>Gói bảo hiểm</td>
                    <td>
                        <%- ho_so.ten_goi_bh %>
                    </td>
                </tr>
                <tr class="d-none">
                    <td>Ngày nhận hồ sơ gốc</td>
                    <td>
                        <%- ho_so.ngay_dau_tien_nhan_hs_goc %>
                    </td>
                </tr>
                <tr class="d-none">
                    <td>Ngày nhận đủ HSBT</td>
                    <td>
                        <%- ho_so.ngay_bs_hs_goc %>
                    </td>
                </tr>
                <tr>
                    <td>Trạng thái xử lý</td>
                    <td style="color: var(--escs_theme_color)"><%- ho_so.trang_thai %>(<%- ho_so.pt_hoan_thanh %>%)</td>
                </tr>
                <tr>
                    <td>Số tiền yêu cầu</td>
                    <td><%- ESUtil.formatMoney(ho_so.so_tien_yc) %></td>
                </tr>
                <tr>
                    <td>Số tiền duyệt</td>
                    <td><%- ESUtil.formatMoney(ho_so.so_tien_duyet) %></td>
                </tr>
                <tr>
                    <td style="width:105px;">Ước tổn thất</td>
                    <td>
                        <span data-model="uoc_ton_that" class="uoc_ton_that"><%- ESUtil.formatMoney(ho_so.uoc_ton_that) %></span>
                        <span class="float-right d-none">
                            <a href="#" onclick="capNhatUocTonThat(this)"><i class="fas fa-edit" title="Cập nhật ước tổn thất"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px;">Ước theo điểm</td>
                    <td>
                        <span class="uoc_ton_that"><%- ESUtil.formatMoney(ho_so.uoc_ton_that_diem) %></span>
                        <span class="float-right">
                            <a href="#" onclick="capNhatUocTheoDiem(this)"><i class="fas fa-edit" title="Cập nhật ước tổn thất theo điểm"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:104px">Hồ sơ TPA</td>
                    <td><%- ho_so.so_hs_tpa %></td>
                </tr>
            </table>
        </div>
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Khách hàng</h5>
            </div>
            <table class="table" id="HealthGuaranteeCustomer">
                <tr>
                    <td style="width:104px">Tên KH</td>
                    <td><%- ho_so.ten_khach_hang %></td>
                </tr>
                <tr>
                    <td>Số HĐBH</td>
                    <td><%- ho_so.so_hd %></td>
                </tr>
                <tr>
                    <td>Đơn vị cấp</td>
                    <td><%- ho_so.ten_dvi_cap %></td>
                </tr>
                <tr>
                    <td>Cán bộ cấp đơn</td>
                    <td><%- ho_so.nsd_cap %></td>
                </tr>
                <tr>
                    <td>Số GCN</td>
                    <td>
                        <span>
                            <%- ho_so.gcn %>
                        </span>
                        <span class="float-right">
                            <a href="#" onclick="xemThongTinGiayChungNhan()">
                                <i class="fas fa-eye" title="Xem chi tiết GCN bảo hiểm"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Tên NĐBH</td>
                    <td>
                        <% if(ho_so.nhom_kh_vip == 'VIP' || ho_so.nhom_kh_vip == 'SVIP'){ %>
                        <span class="ten_kh active-vip"><%- ho_so.ten %></span>
                        <% } else{ %>
                        <span class="ten_kh"><%- ho_so.ten %></span>
                        <% } %>
                        <span class="float-right">
                            <a href="#" @* onclick="chonKhachHangVip(this)" *@>
                                <% if(ho_so.nhom_kh_vip == 'VIP' || ho_so.nhom_kh_vip == 'SVIP'){ %>
                                <i class="rating-star fas fa-star active-star"></i>
                                <% } else{ %>
                                <i class="rating-star fas fa-star defaultColor"></i>
                                <% } %>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>CCCD</td>
                    <td><%- ho_so.so_cmt %></td>
                </tr>
                <tr>
                    <td>Ngày sinh</td>
                    <td><%- ho_so.ngay_sinh %></td>
                </tr>
                <tr>
                    <td>Điện thoại</td>
                    <td>
                        <%- ho_so.dien_thoai %>
                        <span class="float-right d-none">
                            <a href="javascript:void(0)" onclick="ESUtil.voiceCall('<%- ho_so.dien_thoai %>')">
                                <i class="fal fa-phone-rotary" title="Gọi điện thoại"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Hiệu lực BH</td>
                    <td><%- ho_so.hieu_luc %></td>
                </tr>
                <tr class="d-none">
                    <td>Thanh toán phí</td>
                    <td class="font-weight-bold">
                        <span><%= ho_so.thanh_toan_phi %></span>
                        <span class="float-right">
                            <a href="#" onclick="xemThanhToanPhi()">
                                <i class="far fa-edit" title="Xem chi tiết thanh toán phí"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Thanh toán phí</td>
                    @* <td><%= ho_so.thanh_toan_phi %></td> *@
                     <td>
                        <span>
                            <%= ho_so.thanh_toan_phi %>
                        </span>
                        <span class="float-right">
                            <a href="#" onclick="xemTinhTrangTTPhiCore()"><i class="fas fa-sync-alt" title="Xem tình trạng thanh toán phí"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Xác minh phí</td>
                    <td>
                            <a href="#" onclick="xemMauInXacMinhPhi?.()">Xem mẫu in xác minh phí</a>
                    </td>
                </tr>
                <tr>
                    <td>KH xác nhận</td>
                    <td class="font-weight-bold">
                        <%= ho_so.khach_hang_xac_nhan %>
                        <span class="float-right">
                            <a href="#" onclick="xemNoiDungKhachHangXacNhan()">
                                <i class="fas fa-eye" title="Xem nội dung khách hàng xác nhận"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Đơn vị</td>
                    <td><%= ho_so.dvi_ctac_ndbh %></td>
                </tr>
                <tr>
                    <td>Chức vụ</td>
                    <td><%= ho_so.chuc_vu_ndbh %></td>
                </tr>
            </table>
        </div>
        <div class="border rounded">
            @*d-flex*@
            <div class=" justify-content-between align-items-center p-2 card-title-bg d-none">
                <h5 class="m-0">Người thông báo</h5>
                <span>
                    <a href="#" onclick="suaThongTinNguoiThongBao()">
                        <i class="fas fa-edit"></i>
                    </a>
                </span>
            </div>
            <table class="table d-none" id="HealthGuaranteeCustomerTB">
                <tbody>
                    <tr>
                        <td style="width:104px">Người TB</td>
                        <td><%- ho_so.nguoi_tb %></td>
                    </tr>
                    <tr>
                        <td>SĐT thông báo</td>
                        <td><%- ho_so.dthoai_nguoi_tb %></td>
                    </tr>
                    <tr>
                        <td>Email thông báo</td>
                        <td><%- ho_so.email_nguoi_tb %></td>
                    </tr>
                </tbody>
            </table>
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg border-top">
                <h5 class="m-0">Người liên hệ</h5>
                <span>
                    <a href="#" onclick="suaThongTinNguoiLienHe()">
                        <i class="fas fa-edit"></i>
                    </a>
                </span>
            </div>
            <table class="table" id="HealthGuaranteeCustomerLH">
                <tbody>
                    <tr>
                        <td style="width:104px">Người liên hệ</td>
                        <td><%- ho_so.nguoi_lh %></td>
                    </tr>
                    <tr>
                        <td>SĐT liên hệ</td>
                        <td><%- ho_so.dthoai_nguoi_lh %></td>
                    </tr>
                    <tr>
                        <td>Email liên hệ</td>
                        <td><%- ho_so.email_nguoi_lh %></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="text-right card-title-bg" style="height: 35.5px;">
                        <td colspan="2" class="text-center">
                            <a href="javascript:void(0)" id="btnHuyHoSo" class="escs_pquyen mr-4" onclick="huyHoSo()">
                                <i class="fas fa-trash-alt mr-2"></i>Hủy hồ sơ
                            </a>
                            <a href="javascript:void(0)" id="btnChuyenNguoiXuLy" class="escs_pquyen" onclick="chuyenNguoiXuLy()">
                                <i class="far fa-user-friends mr-2"></i>Chuyển người xử lý
                            </a>
                            <a href="javascript:void(0)" id="btnGoHuyHoSo" class="escs_pquyen" onclick="goHuyHoSo()">
                                <i class="fa fa-undo mr-2"></i>Gỡ hủy hồ sơ
                            </a>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</script>

@*tab Thông tin Cơ sở y tế*@
<script type="text/html" id="navThongTinCSYT_template">
    <div class="card-body p-2">
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Cơ sở y tế</h5>
                <span class="float-right">
                    <a href="#" id="btnSuaThongTinCSYT">
                        <i class="fal fa-edit" title="Sửa thông tin"></i>
                    </a>
                </span>
            </div>
            <table class="table" id="HealthGuaranteeCustomer1">
                <tr>
                    <td>Cơ sở y yế</td>
                    <td><%- ho_so.benh_vien %></td>
                </tr>
                @*<tr>
                        <td>Nhà thuốc</td>
                        <td><%- ho_so.nha_thuoc %></td>
                    </tr>*@
                <tr>
                    <td>Người thụ hưởng</td>
                    <td><%- ho_so.chu_tk %></td>
                </tr>
                <tr>
                    <td>HTTT</td>
                    <td>Chuyển khoản</td>
                </tr>
                <tr>
                    <td>Số TK</td>
                    <td><%- ho_so.so_tk_bv %></td>
                </tr>
                <tr>
                    <td>Ngân hàng</td>
                    <td><%- ho_so.chi_nhanh_ngan_hang %></td>
                </tr>
            </table>
        </div>
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Người thông báo của CSYT</h5>
            </div>
            <table class="table" id="HealthGuaranteeCustomer1">
                <tr>
                    <td>Họ tên</td>
                    <td><%- ho_so.nguoi_tb %></td>
                </tr>
                <tr>
                    <td>Điện thoại</td>
                    <td>
                        <%- ho_so.dthoai_nguoi_tb %>
                        <span class="float-right d-none">
                            <a href="#">
                                <i class="fal fa-phone-rotary" title="Gọi điện thoại"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Email</td>
                    <td><%- ho_so.email_nguoi_tb %></td>
                </tr>
            </table>
        </div>
    </div>
</script>

@*Danh sách lần tiếp nhận*@
<script type="text/html" id="tblLanTiepNhanTemplate">
    <% _.forEach(lan_kham, function(item,index) { %>
    <tr style="cursor: pointer" id="<%- item.lan %>" data-model="<%- JSON.stringify(item) %>" onclick="XemChiTietLan(this, '<%- item.lan %>')">
        <td>
            <span><%- item.ngay_vv %></span>
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_yc) %></td>
    </tr>
    <%})%>
</script>

@*bảo lãnh quyền lợi chi tiết*@
<script type="text/html" id="tbl_bao_lanh_quyen_loi_ct_template">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td><%- item.ten_nguyen_nhan %></td>
        <td><%- item.hinh_thuc_ten %></td>
        <td class="text-left"><%- item.chan_doan %></td>
        <td style="text-align: right"><%- ESUtil.formatMoney(item.tien_yc) %></td>
    </tr>
    <%})%>
    <% if(data.length < 6){
    for(var i = 0; i < 6 - data.length;i++ ){
    %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách quyền lợi gốc*@
<script type="text/html" id="dsQuyenLoiGocTemplate">
    <% _.forEach(lstQlg, function(item, index) { %>
    <% var thu_tu_cha_con = item.lh_nv.split('.').length - 1 %>
    <% if(thu_tu_cha_con == 0){ %>
    <tr>
        <td class="text-center"><%- item.lh_nv %></td>
        <td style="font-weight: bold">
            <a href="#" onclick="xemChiTietSuDung('<%- item.lh_nv %>')">
                <%= item.ten_hien_thi %>(<%= item.lh_nv %>)
            </a>
        </td>
       <td style="font-weight: bold" class="text-center"><%- item.nt_tien_bh %></td>
        <td style="font-weight: bold" class="text-center"><%- item.dong_bh %> % </td>
        <td style="font-weight: bold" class="text-center"><%- item.so_ngay_cho %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
        <td style="font-weight: bold" class="text-center d-none"><%- item.ngay_lan_kham %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
        <td style="font-weight: bold" class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_duyet %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_con %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
        <td style="font-style: italic;" class="text-center"><%- item.kieu_ad_ten%> </td>
        <td style="font-style: italic;" class="text-center d-none"><%- item.lhnv_phu_thuoc %> </td>
        <td style="font-style: italic;" class="text-center"><%- item.lhnv_tru_lui %> </td>
    </tr>
    <% } else{ %>
    <% var pd = item.pd %>
    <tr>
        <td class="text-center"><%- item.lh_nv %></td>
        <td style="font-style: italic; padding-left: <%- pd %>px">
            <a href="#" onclick="xemChiTietSuDung('<%- item.lh_nv %>')">
                <%= item.ten_hien_thi %>(<%= item.lh_nv %>)
            </a>
        </td>
        <td style="font-weight: bold" class="text-center"><%- item.nt_tien_bh %></td>
        <td style="font-weight: bold" class="text-center"><%- item.dong_bh %> % </td>
        <td style="font-weight: bold" class="text-center"><%- item.so_ngay_cho %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
        <td style="font-weight: bold" class="text-center d-none"><%- item.ngay_lan_kham %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
        <td style="font-weight: bold" class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_duyet %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_con %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
        <td style="font-style: italic;" class="text-center"><%- item.kieu_ad_ten%> </td>
        <td style="font-style: italic;" class="text-center d-none"><%- item.lhnv_phu_thuoc %> </td>
        <td style="font-style: italic;" class="text-center"><%- item.lhnv_tru_lui %> </td>
    </tr>
    <% } %>
    <%})%>
</script>

<script type="text/html" id="dsDieuKhoanBoSung_template">
    <% _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-left" style="font-weight: bold">
            <%- item.ten_hien_thi %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
    </tr>
    <%})%>

    <% if(data.length < 14){
    for(var i = 0; i < 14 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="dsGhiChuKhac_template">
    <% _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-right" style="font-weight: bold">
            <%- item.ma_dkbs %>
        </td>
        <td class="text-left" style="font-weight: bold">
            <%- item.ten_hien_thi %>
        </td>
    </tr>
    <% }) %>

    <% if(data.length < 14){
    for(var i = 0; i < 14 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách lịch sử tổn thất *@
<script type="text/html" id="tblThongTinLSTTTemplate">
    <% if(arrHoSo.length > 0){ %>
    <% _.forEach(arrHoSo, function(item,index) { %>
    <tr class="text-center lichSuTonThat" id="lichSuTonThat_<%- item.so_id %>_<%- item.lhnv %>">
        <td><%- item.ngay_ht %></td>
        <td>
            <% if(item.loai == 'HSTT'){ %>
            <a href="#" onclick="TransReceiveDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs %></a>
            <%}%>
            <% if(item.loai == 'BLVP'){ %>
            <a href="#" onclick="TransHealthguaranteeDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs%></a>
            <%}%>
        </td>
        <td><%= item.loai_ten%></td>
        <td><%- item.ngay_vv %></td>
        <td><%- item.ngay_rv %></td>
        <td><%- item.hinh_thuc_ten %></td>
        <td><%- item.ten_nguyen_nhan %></td>
        <td><%- item.quyen_loi_ten %></td>

        <td class="text-left"><%- item.ten_benh_vien %></td>
        <td class="text-left"><%- item.chan_doan %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_yc) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_duyet) %></td>
        <td class="text-right"><%- item.so_ngay_duyet %></td>
        <td class="text-center"><%- item.trang_thai %></td>
        <td class="text-center">
            <% if(item.nguyen_nhan != null && item.nguyen_nhan != '' && item.nguyen_nhan != undefined){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- item.nguyen_nhan %>" onclick="showNguyenNhanGiamTru(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Lý do giảm trừ"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhanGiamTru(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Lý do giảm trừ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>

    <% if(arrHoSo.length < 13){
    for(var i = 0; i < 13 - arrHoSo.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Danh sách ảnh *@
<script type="text/html" id="lstImage_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="pt-1" id="nhom_anh_<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImg('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTiet(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img<%- image.bt %>" class="nhom_anh_ton_that_<%- index %> mt-1" data-hm="<%- item.ma_file %>" value="<%- image.bt %>" name="ds_anh_xe">
            <p class="fileNameImage mt-1" style="cursor:pointer"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
    <% } %>
</script>

@*Bổ sung giấy tờ*@
@*Bổ sung hồ sơ giấy tờ*@
<script type="text/html" id="templateDsHoSoGiayTo">
    <% if(ho_so_giay_to.length > 0){
    _.forEach(ho_so_giay_to, function(item,index) { %>
    <tr>
        <td style="text-align:left">
            <a href="#" data-field="ten" data-val="<%- item.ten %>" id="chon_ten_hsgt_<%- item.ma_hs %>"><%- item.ten %></a>
        </td>
        <td style="text-align: center; display: none">
            <a href="#" data-field="trang_thai" data-val="<%- item.trang_thai %>">
                <%= item.trang_thai_ten %>
            </a>
        </td>
        <td style="text-align: center; display: none">
            <a href="#" data-field="ngay_bs" data-val="<%- item.ngay_bs %>">
            </a>
        </td>
        <td style="text-align: center">
            <%- item.ngay_bs %>
        </td>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <% if(item.trang_thai == 'C'){ %>
                <input type="checkbox" id="trang_thai_<%- item.ma_hs %>" class="custom-control-input input_chon_trang_thai" onclick="onChonTrangThai(this)">
                <label class="custom-control-label" for="trang_thai_<%- item.ma_hs %>">&nbsp;</label>
                <% }else if(item.trang_thai == 'D'){ %>
                <input type="checkbox" id="trang_thai_<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_trang_thai" onclick="onChonTrangThai(this)">
                <label class="custom-control-label" for="trang_thai_<%- item.ma_hs %>">&nbsp;</label>
                <% } %>
            </div>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% }else{ %>
            <% if(item.hop_le == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" checked="checked" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <a href="#" data-field="loai" data-val="">
                Chọn loại hồ sơ
            </a>
            <% }else{ %>
            <a href="#" data-field="loai" data-val="<%- item.loai %>" onclick="chonLoaiHSGT(this)" id="loai_hsgt_<%- item.ma_hs %>">
                <% if(item.loai != '' && item.loai != null){ %>
                <%- item.loai_ten %>
                <% }else{ %>
                Chọn loại hồ sơ
                <% } %>
            </a>
            <% } %>
        </td>
        <% if(item.trang_thai =="C")
        {
        if(item.chon==1)
        {
        if(item.trang_thai_xoa =='K')
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)" />
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%}
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs" onclick="onChonBoSungHS(this)">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        %>
        <td style="text-align: center">
            <% if(item.ghi_chu != null && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuBoSungHSGT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuBoSungHSGT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td style="text-align: center"><%- item.nsd %></td>
        <td style="text-align: center" class="d-none"><%- item.nguon %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>
@*  Step 3: Chứng từ *@
<script type="text/html" id="step4_chung_tu_template">
    <% if(chung_tu.length > 0){
    _.forEach(chung_tu, function(item,index) { %>
    <tr>
        <%
            if(item.check_hop_le == '0'){
                %><td><i class="fas fa-times text-danger" title="Đã xác thực thất bại"></i></td><%
            }else if(item.check_hop_le == '1'){
                %><td><i class="fas fa-check text-success" title="Đã xác thực thành công"></i></td><%
            }else if(item.check_hop_le == '2'){
                %><td><i class="fad fa-spinner-third" style="color:blue" title="Đang xác thực"></i></td><%
            }
            else{
                %><td></td><%
            }
        %>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.ngay_ct %>
        </td>
        <td><%- item.mau_hdon %></td>
        <td class="text-center"><%- item.ky_hieu_hdon %></td>
        <td class="text-center"><%- item.so_hdon %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.thue) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tong_cong) %></td>
        <td class="text-center">
            <a href="#" class="edit_chung_tu" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="far fa-file-alt" title="Xem/sửa chi tiết chứng từ"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" class="xoaChungTu"><i class="fas fa-trash-alt" title="Xóa chứng từ"></i></a>
        </td>
    </tr>
    <% })} %>
    <% if(chung_tu.length < 3){
    for(var i = 0; i < 3 - chung_tu.length;i++ ){
    %>
    <tr>
        <td style="height:35.5px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Step 3: Thụ hưởng *@
<script type="text/html" id="step4_thu_huong_template">
    <% if(thu_huong.length > 0){
    _.forEach(thu_huong, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.pttt %>
        </td>
        <td><%- item.ten %></td>
        <td class="text-center"><%- item.tk_cmt %></td>
        <td class="text-center"><%- item.ten_ngan_hang %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="edit_thu_huong" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="fas fa-edit" title="Xem/sửa chi tiết thông tin"></i>
            </a>
            <%
            }%>

        </td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="xoaNguoiThuHuong"><i class="fas fa-trash-alt" title="Xóa người thụ hưởng"></i></a>
            <%
            }%>
        </td>
    </tr>
    <% })}%>
    <% if(thu_huong.length < 4){
    for(var i = 0; i < 4 - thu_huong.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>

    </tr>
    <% }} %>
</script>

@*bảng Thông tin các lần bảo lãnh viện phí*@
<script type="text/html" id="tblLanBaoLanhTemplate">
    <% if(lan_kham.length > 0){
    _.forEach(lan_kham, function(item,index) { %>
    <tr class="cursor-pointer lan_blvp" data-lan="<%- item.lan %>" onclick="xemChiTietLanBLVP('<%- item.lan %>')" id="lan_blvp_<%- item.lan %>">
        <td class="text-center">
            <%- item.lan_kham_hien_thi %>
        </td>
        <td class="text-center"><%- item.benh_vien_ten %></td>
        <td class="text-center"><%- item.ngay_vv %></td>
        <td class="text-center"><%- item.ten_nguyen_nhan %></td>
        <td class="text-center"><%- item.hinh_thuc_ten %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_yc) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_duyet) %></td>
    </tr>
    <% })} %>
    <% if(lan_kham.length < 3){
    for(var i = 0; i < 3 - lan_kham.length;i++ ){
    %>
    <tr>
        <td style="height: 35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Thông tin chi tiết quyền lợi bảo lãnh *@
<script type="text/html" id="tblQloiLanTemplate">
    <% if(qloi_bao_lanh.length > 0){
    _.forEach(qloi_bao_lanh, function(item,index) { %>
    <tr>
        <td>
            <a href="#" class="btnSuaQuyenLoi" onclick="suaLanBaoLanhQLoi('<%- item.lan %>','<%- item.id_qloi %>')">
                <%- item.ten_lhnv %>
            </a>
        </td>
        <td><%- item.chan_doan %></td>
        <td class="text-center"><%- item.ma_nt_yc %></td>
        <td class="text-right tbl_chi_phi_tien_yc"><%- ESUtil.formatMoney(item.tien_yc) %></td>
        <td class="text-right tbl_chi_tong_tien_giam"><%- ESUtil.formatMoney(item.tong_tien_giam) %></td>
        <td class="text-right tbl_chi_phi_tien_duyet"><%- ESUtil.formatMoney(item.tien_duyet) %></td>
        <td class="text-right tbl_chi_phi_tien_yc_vnd"><%- ESUtil.formatMoney(item.tien_yc_vnd) %></td>
        <td class="text-right tbl_chi_tong_tien_giam_vnd"><%- ESUtil.formatMoney(item.tong_tien_giam_vnd) %></td>
        <td class="text-right tbl_chi_phi_tien_duyet_vnd"><%- ESUtil.formatMoney(item.tien_duyet_vnd) %></td>
        <td class="text-center">
            <a href="#" class="btnSuaQuyenLoi" onclick="suaLanBaoLanhQLoi('<%- item.lan %>','<%- item.id_qloi %>')">
                <i class="fas fa-edit" title="Sửa quyền lợi"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" class="btnXoaQuyenLoi" onclick="xoaLanBaoLanhQLoi('<%- item.lan %>','<%- item.id_qloi %>')">
                <i class="fas fa-trash-alt" title="Xóa quyền lợi"></i>
            </a>
        </td>
    </tr>
    <% })} %>
    <% if(qloi_bao_lanh.length < 5){
    for(var i = 0; i < 5 - qloi_bao_lanh.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Thêm quyền lợi*@
<script type="text/html" id="tbDsKhoanChiTab3Template">
    <% var stt = 0 %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="khoanChiItem" data-bt="<%- stt %>">
        <% stt = stt + 1 %>
        <td>
            <input type="hidden" data-name="loai" value="<%- item.loai %>" />
            <%if(item.sl_chi_phi > 0){%>
                <a href="#" onclick="xemChiTietChiPhi('<%- item.bt %>', '<%- item.loai_ct %>','<%- item.loai %>', '<%- item.tien_yc %>')" data-field="loai_ct" data-bt="<%- item.bt %>" data-val="<%- item.loai_ct %>"><%- item.ten_loai_chi_phi %> (<%- item.sl_chi_phi %>)</a>
            <%}else{%>
                <a href="#" onclick="xemChiTietChiPhi('<%- item.bt %>', '<%- item.loai_ct %>','<%- item.loai %>', '<%- item.tien_yc %>')" data-field="loai_ct" data-bt="<%- item.bt %>" data-val="<%- item.loai_ct %>"><%- item.ten_loai_chi_phi %></a>
            <%}%>
            @* <% if(item.loai_ct != null && item.loai_ct != '' && item.loai_ct != undefined )
            {%>
                <input onclick="nhapChiPhiCT(this)" style="cursor: pointer" data-name="loai_ct" data-bt="<%- item.bt %>"  data-field="loai_ct" data-val="<%- item.loai_ct %>" value="<%- item.ten_loai_chi_phi %>" readonly  class="floating-input text-primary" type="text"/>
            <%} else {%>
                <input style="cursor: pointer" data-name="loai_ct"  data-field="loai_ct" onclick="nhapChiPhiCT(this)" placeholder="Chọn loại chi phí" readonly  class="floating-input text-primary" type="text" />
            <%}%> *@
        </td>
        <td class="text-right">
            <input type="text" data-name="tien_yc" autocomplete="off" data-field="tien_yc" class="floating-input money" onchange="tinhTongChiPhi(this)" value="<%- ESUtil.formatMoney(item.tien_yc) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="tien_giam_vuot_qloi" autocomplete="off" readonly data-field="tien_giam_vuot_qloi" class="floating-input money"  value="<%- ESUtil.formatMoney(item.tien_giam_vuot_qloi) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="tien_giam" autocomplete="off" data-field="tien_giam" class="floating-input money" onchange="tinhTongChiPhi(this)" value="<%- ESUtil.formatMoney(item.tien_giam) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="tien_giam_tgian_cho" autocomplete="off" data-field="tien_giam_tgian_cho" class="floating-input money" readonly value="<%- ESUtil.formatMoney(item.tien_giam_tgian_cho) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="tien_giam_dong_chi_tra" autocomplete="off" data-field="tien_giam_dong_chi_tra" class="floating-input money" readonly value="<%- ESUtil.formatMoney(item.tien_giam_dong_chi_tra) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="tien_duyet" autocomplete="off" data-field="tien_duyet" class="floating-input money" readonly value="<%- ESUtil.formatMoney(item.tien_duyet) %>" />
        </td>
        <td>
            <input type="hidden" data-name="nhap_nguyen_nhan" autocomplete="off" data-field="nhap_nguyen_nhan" class="floating-input" value="<%- item.nhap_nguyen_nhan %>" />
            <%if(item.nguyen_nhan_giam !=null &&  item.nguyen_nhan_giam !=''){%>
            <a href="#" data-field="nguyen_nhan_giam" data-val="<%- item.nguyen_nhan_giam %>" data-text="<%- item.nhap_nguyen_nhan %>" onclick="chonNguyenNhanGiamTru(this)">
                <p style="margin:unset"><%- item.nhap_nguyen_nhan %></p>
            </a>
            <%}else{%>
            <a href="#" data-field="nguyen_nhan_giam" data-val="" data-text="" onclick="chonNguyenNhanGiamTru(this)">Chọn nguyên nhân giảm trừ</a>
            <%}%>

        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" onclick="xoaChiPhi(this)"></i>
        </td>
    </tr>
    <% }) %>

    <% if(danh_sach.length < 3){
    for(var i = 0; i < 3 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:39.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@* Quyền lợi trừ lùi *@
<script type="text/html" id="tbQloiTruLuiTemplate">
    <% var stt = 0 %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="khoanChiItem" data-bt="<%- stt %>">
        <% stt = stt + 1 %>
        <td>
            <a href="#" onclick="chonLHNV_tru_lui(this);" data-field="lhnv_tru_lui" data-val="<%- item.lhnv_tru_lui %>" data-text="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-name="so_ngay" autocomplete="off" data-field="so_ngay" class="floating-input text-right" value="<%- ESUtil.formatMoney(item.so_ngay) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="so_tien" autocomplete="off" data-field="so_tien" class="floating-input money" value="<%- ESUtil.formatMoney(item.so_tien) %>" />
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien_ntbh) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien_vnd) %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="btnPopupGhiChu cursor-pointer" onclick="showGhiChuQLoiTruLui(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="btnPopupGhiChu cursor-pointer" onclick="showGhiChuQLoiTruLui(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" onclick="xoaQloiTruLui(this)"></i>
        </td>
    </tr>
    <% }) %>

    <% if(danh_sach.length < 3){
    for(var i = 0; i < 3 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:39.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách bệnh lý*@
<script type="text/html" id="dsBenhLyTemplate">
    <% _.forEach(ds_benh_ly, function(item, index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten_tim %>">
        <input type="checkbox" onchange="chonBenhLy(this)" id="ma_benh_<%- item.ma.replace(/\./g, '') %>" value="<%- item.ma %>" class="custom-control-input item-benh">
        <label class="custom-control-label" for="ma_benh_<%- item.ma.replace(/\./g, '') %>"><%- item.ten_v %></label>
    </div>
    <%})%>
</script>

@*  Danh sách tài liệu hồ sơ pdf *@
<script type="text/html" id="dsTaiLieuHoSoTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li>
        <a href="javascript:void(0);" onclick="getAnhChiTietTab1('<%- item.so_id %>', '<%- item.bt %>', '<%- item.extension %>')">
            <%- item.ten_file %>
        </a>
    </li>
    <% })} %>
</script>

@*  Danh sách tài liệu hồ sơ hình ảnh *@
<script type="text/html" id="dsHinhAnhHoSoTemplate">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <% if(index == 0){ %>
    <div style="display: inline-block;width:100%;">
        <p style="margin-bottom:5px;">
            <a href="#"><%- item.nhom %></a>
        </p>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div onclick="getAnhChiTietTab1('<%- image.so_id %>', '<%- image.bt %>', '<%- image.extension %>')" style="width:60px; height:60px; margin-right:10px; margin-bottom:5px; cursor:pointer;float:left; border-radius:10px; border:1px solid #e9ecef">
            <img style="width: 100%;  height:100%" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
        </div>
        <% }) %>
    </div>
    <% }else{ %>
    <div style="display: inline-block;width:100%;">
        <p style="margin-bottom:5px;">
            <a href="#"><%- item.nhom %></a>
        </p>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div onclick="getAnhChiTietTab1('<%- image.so_id %>', '<%- image.bt %>', '<%- image.extension %>')" style="width:60px; height:60px; margin-right:10px; margin-bottom:5px; cursor:pointer;float:left; border-radius:10px; border:1px solid #e9ecef">
            <img style="width: 100%;  height:100%" location-x="<%- image.x %>" location-y="<%- image.y %>" data-original="" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
        </div>
        <% }) %>
    </div>
    <% } %>
    <% })} %>
</script>

@*Chi phí khám bệnh*@
<script type="text/html" id="tblChiPhiKhamBenhTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td>
            <input type="hidden" data-field="so_luong" value="" />
            <input type="hidden" data-field="don_gia" value="" />
            <input type="hidden" data-field="dvi_tinh" value="" />
            <input type="hidden" data-field="ghi_chu" value="" />
            <input type="hidden" data-field="ma" value="<%- item.ma %>" />
            <input type="hidden" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" data-field="ten_ct" value="<%- item.ten_ct %>" />
            <a data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" onchange="tongChiPhiKhamBenh()" data-field="so_tien" value="<%- ESUtil.formatMoney(item.so_tien) %>" />
        </td>
        <td style="text-align:right">
            <a data-field="gia_tham_khao" data-val="<%- item.gia_tham_khao %>"><%- ESUtil.formatMoney(item.gia_tham_khao) %></a>
        </td>
        <td class="text-center">
            <% if(item.mac_dinh != undefined && item.mac_dinh != null && item.mac_dinh !="0"){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" onchange="onMacDinhChiPhi(this,'KB')" id="chi_phi_kb_<%-item.loai %>_<%-item.ma %>" data-field="mac_dinh" value="1" checked="checked" class="custom-control-input chi_phi_kb">
                <label class="custom-control-label" for="chi_phi_kb_<%-item.loai %>_<%-item.ma %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" onchange="onMacDinhChiPhi(this,'KB')" id="chi_phi_kb_<%-item.loai %>_<%-item.ma %>" data-field="mac_dinh" value="0" class="custom-control-input chi_phi_kb">
                <label class="custom-control-label" for="chi_phi_kb_<%-item.loai %>_<%-item.ma %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDong(this,'CHI_PHI_KHAM_BENH')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 7){
    for(var i = 0; i < 7 - data.length;i++ ){
    %>
    <tr>
        <td></td>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Chi phí thuốc*@
<script type="text/html" id="tblChiPhiThuocTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td>
            <input type="hidden" data-field="ghi_chu" value="" />
            <input type="hidden" class="floating-input" data-field="ma" value="<%- item.ma %>" />
            <input type="hidden" class="floating-input" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" class="floating-input" data-field="ten_ct" value="<%- item.ten_ct %>" />
            <a data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td>
            <input type="text" class="floating-input text-center" data-field="dvi_tinh" value="<%- item.dvi_tinh %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input decimal" onchange="tongChiPhiThuoc()" data-field="so_luong" value="<%- item.so_luong %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" onchange="tongChiPhiThuoc()" data-field="don_gia" value="<%- ESUtil.formatMoney(item.don_gia) %>" />
        </td>
        <td style="text-align:right">
            <a href="#" style="color:unset" data-field="so_tien" data-val="<%- item.so_tien %>"><%- ESUtil.formatMoney(item.so_tien) %></a>
        </td>
        <td style="text-align:right">
            <a href="#" style="color:unset" data-field="gia_tham_khao" data-val="<%- item.gia_tham_khao %>"><%- ESUtil.formatMoney(item.gia_tham_khao) %></a>
        </td>
        <td class="text-center">
            <% if(item.mac_dinh != undefined && item.mac_dinh != null && item.mac_dinh !="0"){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" onchange="onMacDinhChiPhi(this,'TH')" id="chi_phi_th_<%-item.loai %>_<%-item.ma %>" data-field="mac_dinh" value="1" checked="checked" class="custom-control-input chi_phi_th">
                <label class="custom-control-label" for="chi_phi_th_<%-item.loai %>_<%-item.ma %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" onchange="onMacDinhChiPhi(this,'TH')" id="chi_phi_th_<%-item.loai %>_<%-item.ma %>" data-field="mac_dinh" value="0" class="custom-control-input chi_phi_th">
                <label class="custom-control-label" for="chi_phi_th_<%-item.loai %>_<%-item.ma %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDong(this,'CHI_PHI_THUOC')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 7){
    for(var i = 0; i < 7 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Chi phí khác*@
<script type="text/html" id="tblChiPhiKhacTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td>
            <input type="hidden" data-field="so_luong" value="" />
            <input type="hidden" data-field="don_gia" value="" />
            <input type="hidden" data-field="dvi_tinh" value="" />
            <input type="hidden" class="floating-input" data-field="ma" value="<%- item.ma %>" />
            <input type="hidden" class="floating-input" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" class="floating-input" data-field="ten_ct" value="<%- item.ten_ct %>" />
            <a data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" onchange="tongChiPhiKhac()" data-field="so_tien" value="<%- ESUtil.formatMoney(item.so_tien) %>" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.mac_dinh != undefined && item.mac_dinh != null && item.mac_dinh !="0"){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" onchange="onMacDinhChiPhi(this,'KH')" id="chi_phi_kh_<%-item.loai %>_<%-item.ma %>" data-field="mac_dinh" value="1" checked="checked" class="custom-control-input chi_phi_kh">
                <label class="custom-control-label" for="chi_phi_kh_<%-item.loai %>_<%-item.ma %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" onchange="onMacDinhChiPhi(this,'KH')" id="chi_phi_kh_<%-item.loai %>_<%-item.ma %>" data-field="mac_dinh" value="0" class="custom-control-input chi_phi_kh">
                <label class="custom-control-label" for="chi_phi_kh_<%-item.loai %>_<%-item.ma %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDong(this,'CHI_PHI_KHAC')"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 7){
    for(var i = 0; i < 7 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblCapNhatUocTheoDiem_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr class="capNhatUoc">
            <td class="text-center">
                <input type="hidden" data-field="nv" name="nv" value="<%- item.nv %>" />
                <input type="hidden" data-field="lh_nv" name="lh_nv" value="<%- item.lh_nv %>" />
                <input type="hidden" data-field="diem" name="diem" value="<%- item.diem %>" />
                <input type="hidden" data-field="tich_hop" name="tich_hop" value="<%- item.tich_hop %>" />
                <input type="hidden" data-field="log_rq" name="log_rq" value="<%- item.log_rq %>" />
                <input type="hidden" data-field="log_res" name="log_res" value="<%- item.log_res %>" />
                <input type="hidden" data-field="bt" name="bt" value="<%- item.bt %>" />
                <%- index + 1 %>
            </td>
            <td class="text-center">
                <input type="text" data-field="ngay_dp" class="floating-input datepicker text-center" readonly value="<%- item.ngay_dp %>" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten_diem" data-field="ten_diem" value="<%- item.ten_diem %>" />
                <input type="text" class="floating-input combobox" data-field="ten_diem" data-val="<%- item.diem %>" onclick="chonDiemDuPhong(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten_diem %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten" data-field="ten" value="<%- item.ten %>" />
                <input type="text" class="floating-input combobox" data-field="ten" data-val="<%- item.lh_nv %>" onclick="chonNV(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten %>" style="text-align:center; cursor:pointer" />
            </td><td class="text-center">
                <input type="text" class="floating-input" data-field="lh_nv" data-val="<%- item.lh_nv %>" readonly value="<%- item.lh_nv %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien" name="tien" class="number floating-input tien_<%- item.bt %>" autocomplete="off" disabled value="<%- ESUtil.formatMoney(item.tien) %>" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien_chenh_lech" name="tien_chenh_lech" class="number floating-input tien_chenh_lech" disabled value="<%- ESUtil.formatMoney(item.tien_chenh_lech) %>" />
            </td>
            <td class="text-center">
                <% if(item.tich_hop == 1){%>
                     <i class="fas fa-check-circle text-success" title="Đã tích hợp"></i>
                <%}else{%>
                    <i class="fas fa-times" style="color:red" title="Chưa tích hợp"></i>
                <%}%>
            </td>
            <td class="text-center">
                <% if(item.log_rq != null && item.log_res !=""){ %>
                    <a href="#" class="cursor-pointer combobox" onclick="showLogRq(this)" data-rq="<%- JSON.stringify(item.log_rq) %>" data-res="<%- JSON.stringify(item.log_res) %>" >
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                    <% }else{ %>
                    <a class="cursor-pointer combobox" onclick="showLogRq(this)" data-val="" data-rq="" data-res="">
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                <% } %>
            </td>
            <td class="text-center">
                <% if(item.tich_hop != 1){%>
                    <a href="#" class="cursor-pointer" onclick="tichHopLaiUoc(<%- item.bt %>)">
                        <i class="fas fa-arrow-right" style="color: var(--escs-main-theme-color)" title="Tích hợp lại ước"></i>
                    </a>
                <%}else{%>

                <%}%>
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblBienDo_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr>
            <td>
                <input type="hidden" data-field="diem" name="diem" data-val="<%- item.diem %>" value="<%- item.diem %>"/>
                <%- item.ten_diem %>
            </td>
            <td class="text-right">
                <input type="text" data-field="ty_le" name="ty_le" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(item.ty_le) %>" />
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 3){
    for(var i = 0; i < 3 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblPhuongAnChiTraTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblPhuongAnChiTraItem">
        <td class="text-center">
            <%- index + 1 %>
        </td>
        <td class="text-center">
            <%- item.dvi_dong_bh %>
        </td>
        <td class="text-center">
            <%- item.kieu_dong %>
        </td>
          <td class="text-center">
           <%- item.tl_dong %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_dong) %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblNguyenNhan_template">
    <% _.forEach(data, function(item, index) { %>
    <% var thu_tu_cha_con = item.cap %>
    <% if (thu_tu_cha_con == 1) { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } else { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } %>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblTrieuChung_template">
    <% _.forEach(data, function(item, index) { %>
    <% var thu_tu_cha_con = item.cap %>
    <% if (thu_tu_cha_con == 1) { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } else { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } %>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblCanLamSang_template">
    <% _.forEach(data, function(item, index) { %>
    <% var thu_tu_cha_con = item.cap %>
    <% if (thu_tu_cha_con == 1) { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } else { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } %>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblNguyenTacDtri_template">
    <% _.forEach(data, function(item, index) { %>
    <% var thu_tu_cha_con = item.cap %>
    <% if (thu_tu_cha_con == 1) { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } else { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } %>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblDtriCuThe_template">
    <% _.forEach(data, function(item, index) { %>
    <% var thu_tu_cha_con = item.cap %>
    <% if (thu_tu_cha_con == 1) { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } else { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } %>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblBienChung_template">
    <% _.forEach(data, function(item, index) { %>
    <% var thu_tu_cha_con = item.cap %>
    <% if (thu_tu_cha_con == 1) { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } else { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } %>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblKhac_template">
    <% _.forEach(data, function(item, index) { %>
    <% var thu_tu_cha_con = item.cap %>
    <% if (thu_tu_cha_con == 1) { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } else { %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr style="color: var(--escs-main-theme-color)">
            <td class="text-center"><%- item.ma_benh %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>
                </a>
            </td>
        </tr>
    <% } %>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblNguonTaiLieu_template">
    <% _.forEach(data, function(item, index) { %>
    <tr style="color: var(--escs-main-theme-color)">
        <td class="text-center"><%- item.ma_benh %></td>
        <td><%- item.ten %></td>
    </tr>
    <%})%>
    <% if(data.length < 8) {
    for(var i = 0; i < 8 - data.length; i++ ) { %>
    <tr>
        <td style="height:33px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>