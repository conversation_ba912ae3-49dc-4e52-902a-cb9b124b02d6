﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.COMMON.Translate;
using ESCS.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class TranslateController : BaseController
    {
        private readonly IWebHostEnvironment _env;

        public TranslateController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> getLanguage()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PESCS_NGON_NGU_LKE, json);
            return Ok(data);
        }

        [HttpGet]
        public async Task<IActionResult> ganTranslateId()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var pathArea = Path.Combine(_env.ContentRootPath, "Areas");
            var pathViews = Path.Combine(_env.ContentRootPath, "Views");
            string[] arrArea = UtilHtmlHelper.GetAllFile(pathArea);
            string[] arrView = UtilHtmlHelper.GetAllFile(pathViews);
            var arr = arrArea.Concat(arrView).ToArray();
            UtilHtmlHelper helper = new UtilHtmlHelper();
            //foreach (var path in arr)
            //{
            //}
            await helper.SaveTextAndSetKey("", (fileName, text) => AddText(json, fileName, text));
            return Ok("Thành công");
        }

        private async Task<string> AddText(string json, string fileName, string text)
        {
            return "";
        }
    }
}