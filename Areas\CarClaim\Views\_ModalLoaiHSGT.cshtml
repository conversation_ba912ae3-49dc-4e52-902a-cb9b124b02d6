﻿<div id="modalLoaiHSGT" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header mx-2 border-bottom">
        <h5><span class="modal-drag-title">Ch<PERSON><PERSON> <PERSON><PERSON><PERSON>ồ sơ</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="hidden" id="modalLoaiHSGTElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLoaiHSGTDanhSach">
                
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonLoaiHSGT">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalLoaiHSGTTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox" data-text="">
        <input type="checkbox" id="loai_hsgt_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonLoaiHSGTItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_hsgt_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

