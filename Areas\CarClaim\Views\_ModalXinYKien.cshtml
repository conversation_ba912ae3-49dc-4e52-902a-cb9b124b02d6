﻿<div class="modal fade bs-example-modal-lg" id="modalXinYKien" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 50%; margin-top:15px;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h4 class="modal-title">Thông tin trình xin ý kiến của đơn vị</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body" style="padding-top:5px">
                <form id="frmModalXinYKien" name="frmModalXinYKien" method="post">
                    <input type="hidden" name="so_id_yk" value="" />
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="_required">Lần xin ý kiến</label>
                                <div class="input-group">
                                    <select class="form-control select2" required name="lan" style="width:100%"></select>
                                    @*<div class="input-group-append">
                                        <label class="input-group-text">
                                            <a href="javascript:void(0)" id="ModalXinYKien_btnThemLanXinYKien">
                                                <i class="far fa-plus" title="Thêm lần trình xin ý kiến"></i>
                                            </a>
                                            <a href="javascript:void(0)" id="ModalXinYKien_btnHuyThemLanXinYKien" class="d-none">
                                                <i class="far fa-times" title="Hủy thêm lần trình xin ý kiến"></i>
                                            </a>
                                        </label>
                                    </div>*@
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 d-none">
                            <div class="form-group">
                                <label>Chọn nhóm trình xin ý kiến</label>
                                <div class="input-group">
                                    <select class="form-control select2" name="so_id_nhom_y_kien_mau" style="width:100%"></select>
                                    <div class="input-group-append">
                                        <label class="input-group-text">
                                            <a href="javascript:void(0)" id="ModalXinYKien_btnTaoNhom">
                                                <i class="far fa-users-medical" title="Tạo nhóm trình xin ý kiến"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Mức độ ưu tiên</label>
                                <div class="input-group">
                                    <select class="form-control select2" required name="muc_do_ut" style="width:100%"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="_required">Giờ kết thúc</label>
                                <div class="input-group bootstrap-timepicker timepicker">
                                    <input class="form-control input-small time" autocomplete="off" placeholder="HH:mm" required name="gio_kt" type="text">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <span class="ti-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Ngày kết thúc</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" required display-format="date" value-format="number" name="ngay_kt" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="_required">Nội dung trình xin ý kiến</label>
                                <textarea class="form-control" placeholder="Nhập vào nội dung xin ý kiến" required autocomplete="off" rows="3" name="nd_y_kien"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row mt-1 mb-1">
                    <div class="col-6"><h5>Danh sách cán bộ trình  <span class="text-success d-none" id="ModalXinYKienGridTrangThaiYKien">Đang trình xin ý kiến</span></h5></div>
                    
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height:190px">
                            <table class="table table-bordered fixed-header">
                                <thead class="font-weight-bold text-center uppercase">
                                    <tr>
                                        <th style="width:40px">STT</th>
                                        <th width="28%">Tên</th>
                                        <th width="25%">Tài khoản</th>
                                        @*<th style="width:40px">TT</th>*@
                                        <th>Đơn vị</th>
                                        <th style="width:40px"></th>
                                    </tr>
                                </thead>
                                <tbody id="ModalXinYKienGrid">
                                </tbody>
                                <tfoot>
                                    <tr class="card-title-bg">
                                        <td colspan="6">
                                            <a href="#" id="ModalXinYKien_btnThemNguoiPheDuyet">
                                                <i class="fas fa-user-plus mr-2"></i>Thêm cán bộ xin ý kiến
                                            </a>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <div class="col-12 mt-2">
                        @*<button class="btn btn-primary btn-sm wd-90 float-right" id="modalXinYKien_btnLuuDongXinYKien"><i class="fa fa-save mr-1"></i>Lưu đóng</button>*@
                        <button class="btn btn-primary btn-sm wd-110 float-right mr-1 d-none" id="modalXinYKien_btnTrinhXinYKien"><i class="fas fa-share-square mr-1"></i>Gửi ý kiến</button>
                        <button class="btn btn-primary btn-sm wd-150 float-right mr-1" id="modalXinYKien_btnLuuVaGuiXinYKien"><i class="fas fa-share-square mr-1"></i>Lưu & Gửi ý kiến</button>
                        <button class="btn btn-primary btn-sm wd-85 float-right mr-1" id="modalXinYKien_btnLuuXinYKien"><i class="fa fa-save mr-1"></i>Lưu</button>
                        <button class="btn btn-primary btn-sm wd-180 float-right mr-1 d-none" id="modalXinYKien_btnKetThucTrinhXinYKien"><i class="fas fa-check-square mr-1"></i>Kết thúc lần xin ý kiến</button>
                        <button class="btn btn-primary btn-sm wd-110 float-right mr-1" id="modalXinYKien_btnHuyTrinhXinYKien"><i class="fas fa-reply-all mr-1"></i>Thu hồi trình</button>

                        <button class="btn btn-primary btn-sm float-right mr-1" id="ModalXinYKien_btnThemLanXinYKien"><i class="fas fa-plus mr-1"></i>Thêm ý kiến mới</button>
                        <button class="btn btn-primary btn-sm float-right mr-1" id="ModalXinYKien_btnHuyThemLanXinYKien"><i class="fas fa-window-close mr-1"></i>Hủy thêm mới</button>

                    </div>
                    <div class="col-12 mt-2 d-none">
                        <div class="form-group">
                            <label>Nội dung ý kiến</label>
                            <div style="width:100%; background-color:#e9ecef; height: 130px; border-radius:2px; padding:5px" class="scrollable" id="ModalXinYKienNoiDung">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="custom-modal">
    <div id="modalXinYKienThemNhom" class="modal fade" tabindex="-1" data-backdrop="false" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 9999999;">
        <div class="modal-dialog modal-lg" style="max-width:unset; width:950px;max-height:700px">
            <div class="modal-content" style="border:3px solid var(--escs-main-theme-color);">
                <div class="modal-header py-1" style="background-color:var(--escs-main-theme-color); cursor:pointer;border:unset;">
                    <h4 class="modal-title" style="color:#fff">Tạo nhóm ý kiến</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body" style="padding-bottom: 0px;">
                    <form name="frmModalXinYKienThemNhom" method="post">
                        <div class="row">
                            <input type="hidden" name="hanh_dong" />
                            <input type="hidden" name="can_bo_chon" />
                            <input type="hidden" name="ma_doi_tac" />
                            <input type="hidden" name="so_id_hs" />
                            <input type="hidden" name="loai_trinh" />
                            <input type="hidden" name="so_id" />
                            <div class="col-5">
                                <div class="form-group">
                                    <input type="text" onclick="onModalXinYKienChonDonVi(this)" style="cursor:pointer" autocomplete="off" name="ma_chi_nhanh_duyet" required class="form-control" placeholder="Click chọn đơn vị duyệt" readonly="readonly" />
                                </div>
                            </div>
                            <div class="col-5">
                                <input type="text" name="tim" autocomplete="off" placeholder="Nhập mã cán bộ/tên cán bộ" class="form-control">
                            </div>
                            <div class="col-2">
                                <button type="button" class="btn btn-primary btn-sm wd-120" id="ModalXinYKien_btnTimKiemCanBo">
                                    <i class="fas fa-search mr-2"></i>Tìm kiếm
                                </button>
                            </div>
                        </div>
                    </form>
                    <div class="row">
                        <div class="col-12">
                            <div class="card" style="margin-bottom:unset;min-height:256px;">
                                <div class="card-body px-0" style="padding:unset !important">
                                    <div class="border mb-3 rounded">
                                        <div class="justify-content-between p-2 card-title-bg">
                                            <h5 class="m-0">Danh sách cán bộ phê duyệt</h5>
                                        </div>
                                        <div class="scrollable" style="height:270px">
                                            <div class="table-responsive">
                                                <table class="table table-bordered">
                                                    <thead class="font-weight-bold">
                                                        <tr class="text-center uppercase">
                                                            <th style="width:50px">Chọn</th>
                                                            <th>Tên cán bộ</th>
                                                            <th>Đơn vị</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="modalXinYKienThemNhomDSCanBo">
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div id="modalXinYKienThemNhomDSCanBo_pagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="divModalXinYKienThemNhomTenNhom">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Tên nhóm</label>
                                <input type="text" name="ten_nhom" id="modalXinYKienThemNhomTenNhom" placeholder="Nhập tên nhóm trình" autocomplete="off" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm wd-120 mg-t-22" id="ModalXinYKien_btnThemCanBo">
                        <i class="fas fa-mouse-pointer mr-2"></i>Chọn cán bộ
                    </button>
                    <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="ModalXinYKien_btnLuuThemNhom">
                        <i class="fas fa-plus-square mr-2"></i>Lưu
                    </button>
                    <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="ModalXinYKien_btnLuuDongThemNhom" style="width:120px">
                        <i class="fas fa-hdd mr-2"></i>Lưu & Đóng
                    </button>
                    <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalXinYKienChonDonVi" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn đơn vị</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="modalXinYKienChonDonViTimKiem" onclick="onFocus(this)" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalXinYKienChonDonViDanhSach"></div>
        </div>
    </div>
    <div class="modal-drag-footer" style="border-top: 1px solid #e9ecef;">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="ModalXinYKien_btnChonDonVi">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="ModalXinYKienGridTemplate">
    <% if(ds_nsd.length > 0){
    var stt = 1;
    _.forEach(ds_nsd, function(item,index) { %>
    <tr class="divItemDsNguoiDuyet" data-id="<%- item.nsd_duyet.trim().replace(/[^a-zA-Z0-9]/g, '') %>" style="cursor:pointer">
        <td class="text-center" onclick="onModalXinYKienGridXemCTNoiDung('<%- item.ma_chi_nhanh_duyet %>','<%- item.nsd_duyet %>')">
            <%- stt %>
        </td>
        <td class="text-center" onclick="onModalXinYKienGridXemCTNoiDung('<%- item.ma_chi_nhanh_duyet %>','<%- item.nsd_duyet %>')">
            <a class="combobox" data-field="ten_nsd_duyet" data-val="<%- item.ten_nsd_duyet %>">
                <%- item.ten_nsd_duyet %>
            </a>
        </td>
        <td class="text-center" onclick="onModalXinYKienGridXemCTNoiDung('<%- item.ma_chi_nhanh_duyet %>','<%- item.nsd_duyet %>')">
            <a class="combobox" data-field="nsd_duyet" data-val="<%- item.nsd_duyet %>">
                <%- item.nsd_duyet %>
            </a>
        </td>
        <td class="text-center d-none" onclick="onModalXinYKienGridXemCTNoiDung('<%- item.ma_chi_nhanh_duyet %>','<%- item.nsd_duyet %>')">
            <% if(item.trang_thai_y_kien == "D"){
            %>
            <a href="#" data-field="trang_thai_y_kien" data-val=""><i class="fas fa-check"></i></a>
            <%}else{%>
            <a data-field="trang_thai_y_kien" data-val=""><i class="fas fa-check"></i></a>
            <%} %>
        </td>
        <td class="text-center" onclick="onModalXinYKienGridXemCTNoiDung('<%- item.ma_chi_nhanh_duyet %>','<%- item.nsd_duyet %>')">
            <a class="combobox d-none" data-field="ten_cnhanh_duyet" data-val="<%- item.ten_cnhanh_duyet %>"></a>
            <a class="combobox" data-field="ma_chi_nhanh_duyet" data-val="<%- item.ma_chi_nhanh_duyet %>">
                <%- item.ten_cnhanh_duyet %>
            </a>
        </td>
        <td class="text-center">
            <a href="#" onclick="onModalXinYKienGridXoaNsd('<%- item.ma_chi_nhanh_duyet %>','<%- item.nsd_duyet %>')" class="text-danger"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <% stt++ %>
    <% })} %>
    <% if(ds_nsd.length < 3){
    for(var i = 0; i < 3 - ds_nsd.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        @*<td></td>*@
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="ModalXinYKienGridLichSuTemplate">
    <% if(lich_su.length > 0){
    var stt = 1;
    _.forEach(lich_su, function(item,index) { %>
    <tr>
        <td class="text-center"><%- stt %></td>
        <td class="text-center" style="font-weight:bold"><%- item.nhom_ten %></td>
        <td><%- item.ten_nsd_cho_yk %></td>
        <td><%- item.ten_cnhanh_cho_yk %></td>
        <td class="text-center"><%- item.ngay_cho_yk %></td>
        <td><%- item.nd_cho_y_kien %></td>
    </tr>
    <% stt++ %>
    <% })}%>
    <% if(lich_su.length < 3){
    for(var i = 0; i < 3 - lich_su.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalXinYKienThemNhomDSCanBoTemplate">
    <% _.forEach(danh_sach, function(item, index) { %>
    <tr class="divItemDsCanBoTrinh" data-nhom-chi-nhanh="<%- item.ma_chi_nhanh %>" data-ma-cb="<%- item.ma %>" data-text="<%- item.ma+item.ten.toLowerCase() %>">
        <td style="width:30px; padding:0;">
            <div style="width:16px; margin:0 auto">
                <input type="hidden" data-field="ma_doi_tac_duyet" data-val="<%- item.ma_doi_tac %>" class="form-control" />
                <input type="hidden" data-field="ma_chi_nhanh_duyet" data-val="<%- item.ma_chi_nhanh %>" class="form-control" />
                <% if(item.chon ==0){
                %>
                <input type="checkbox" onchange="onModalXinYKienChonCanBo(this)" data-field="nsd_duyet" data-val="<%- item.ma %>" class="form-control" />
                <%}else{%>
                <input type="checkbox" checked="checked" onchange="onModalXinYKienChonCanBo(this)" data-field="nsd_duyet" data-val="<%- item.ma %>" class="form-control" />
                <%}%>

            </div>
        </td>
        <td style="padding: 7px 10px"><%- item.ten %> (<%- item.ma %>)</td>
        <td style="padding: 7px 10px" class="text-center"><%- item.ten_chi_nhanh %></td>
    </tr>
    <%})%>

    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){ %>
    <tr style="cursor: pointer">
        <td style="height:33.1px"></td>
        <td></td>
        <td></td>
    </tr>
    <%}}%>
</script>

<script type="text/html" id="modalXinYKienChonDonViDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox modalXinYKienChonDonViDanhSachItem" data-text="<%- ESUtil.xoaKhoangTrangText(item.ten_tat) %>">
        <input type="checkbox" id="dvi_duyet_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonDviDuyet" style="z-index: 999999!important">
        <label class="custom-control-label" style="cursor:pointer;" for="dvi_duyet_<%- item.ma %>"><%- item.ten_tat %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="tblDsLanYKienBodyTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr style="cursor: pointer; text-align:center" id="so_id_yk_<%- item.so_id_yk %>" onclick="getDetailYKienTheoLan('<%- item.so_id_yk %>')" class="item-lan-yk">
        <td style="font-weight:bold">
            <input type="hidden" data-field="so_id_yk" value="<%- item.so_id_yk %>" />
            <a class="combobox" data-field="thoi_gian_y_kien" data-val="<%- item.thoi_gian_y_kien %>">
                <%- item.thoi_gian_y_kien %>
            </a>
        </td>
    </tr>
    <%})} %>
    <% if(data.length < 10){
    for(var i = 0; i < 10 - data.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="ModalXinYKienNoiDungTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <% if(item.trang_thai_cho_yk == 'QUA_HAN'){ %>
    <p class="text-danger mg-0"><i><%- item.ngay_hthi %> :</i></p>
    <% }else if(item.trang_thai_cho_yk == 'TRONG_HAN'){ %>
    <p class="text-succes mg-0"><i><%- item.ngay_hthi %> :</i></p>
    <% } %>
    <p class="mg-0"><%- item.nd_y_kien %></p>
    <%})} %>
</script>
