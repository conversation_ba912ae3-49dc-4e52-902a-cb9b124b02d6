﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using RazorEngine.Configuration;
using RazorEngine.Templating;
using System.Threading.Tasks;

namespace ESCS.Areas.Manager.Controllers
{
    /// <summary>
    /// Phê duyệt
    /// </summary>
    [Area("Manager")]
    [SystemAuthen]
    public class AwaitingPaymentController : BaseController
    {
        private TemplateServiceConfiguration config;
        public static IRazorEngineService _service = null;
        private readonly IWebHostEnvironment _env;

        public AwaitingPaymentController(IWebHostEnvironment env)
        {
            _env = env;
            config = new TemplateServiceConfiguration();
            config.CachingProvider = new RazorEngine.Templating.DefaultCachingProvider();
            if (_service == null)
                _service = RazorEngineService.Create(config);
        }

        /// <summary>
        /// M<PERSON>n hình hồ sơ chờ thanh toán
        /// </summary>
        /// <returns></returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// Liệt kê + phân trang
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> getPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_THANH_TOAN_TON_LKE, json);
            return Ok(data);
        }

        /// <summary>
        /// Đóng hồ sơ
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> dongHoSoBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_DONG_HS, json/*, "/api/esmartclaim/transfer-payment"*/);
            return Ok(data);
        }

        /// <summary>
        /// Mở hồ sơ
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> huyDongHoSoBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_HUY_DONG_HS, json/*, "/api/esmartclaim/un-transfer-payment"*/);
            return Ok(data);
        }

        /// <summary>
        /// Chuyển lại hồ sơ bồi thường
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> chuyenBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_HUY_CHUYEN_THANH_TOAN_TT, json);
            return Ok(data);
        }
        /// <summary>
        /// Chuyển lại hồ sơ tạm úng bồi thường
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> chuyenTamUngBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_TAM_UNG_HUY_CHUYEN_THANH_TOAN_TT, json);
            return Ok(data);
        }


        /// <summary>
        /// Merge file pdf
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> mergeFile()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var file = await Request.GenerateMergePdfFile(StoredProcedure.PHT_BH_FILE_TAI_FILE_NEN, json, "/api/esmartclaim/export-multiple-file");
            try
            {
                var res = file.Result<object>();
                if (res.state_info.status == "NotOK")
                    return Ok(res);
            }
            catch { }
            return Ok(file.Content.ReadAsByteArrayAsync().Result);
        }

        /// <summary>
        /// Lấy lại thông tin số tiền thuế
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> layTienBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_SO_TIEN_LKE_CT, json);
            return Ok(data);
        }

        /// <summary>
        /// Lấy lại thông tin số tiền thuế
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> layCTTienBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_SO_TIEN_LKE_CT_V2, json);
            return Ok(data);
        }

        /// <summary>
        /// Cập nhật lại thông tin tiền thuế
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> capNhatThue()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_SO_TIEN_THUE_NH, json, "/api/esmartclaim/update-vat");
            return Ok(data);
        }

        /// <summary>
        /// Cập nhật lại thông tin tiền thuế
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> capNhatCTThue()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_SO_TIEN_THUE_NH_V2, json);
            return Ok(data);
        }

        /// <summary>
        /// Lấy lại thông tin số tiền thuế xe máy
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> layTienBTXeMay()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_MAY_HS_SO_TIEN_LKE_CT, json);
            return Ok(data);
        }

        /// <summary>
        /// Lấy lại thông tin số tiền thuế xe máy
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> layCTTienBTXeMay()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_MAY_HS_SO_TIEN_LKE_CT_V2, json);
            return Ok(data);
        }

        /// <summary>
        /// Cập nhật lại thông tin tiền thuế xe máy
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> capNhatThueXeMay()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_MAY_HS_SO_TIEN_THUE_NH, json, "/api/motoclaim/update-vat");
            return Ok(data);
        }

        /// <summary>
        /// Cập nhật lại thông tin tiền thuế xe máy
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> capNhatCTThueXeMay()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_SO_TIEN_THUE_NH_V2, json);
            return Ok(data);
        }

        /// <summary>
        /// Cập nhật lại thông tin đơn vị nhận, xuất hóa đơn
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> capNhatDviNhanXuatHoaDon()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_HS_DVI_NHAN_XUAT_HDON_UPDATE, json);
            return Ok(data);
        }
    }
}