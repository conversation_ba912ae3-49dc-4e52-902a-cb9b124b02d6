﻿<div class="modal fade bs-example-modal-lg" id="modalNguyenTeTyGia" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top:70px;">
            <div class="modal-header py-2">
                <h4 class="modal-title">Thông tin tỷ giá nguyên tệ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body p-2">
                <div class="row">
                    <div class="col-12">
                        <form name="frmNguyenTeTyGia" class="table-responsive" style="height: 230px;">
                            <input type="hidden" name="hanh_dong" value="" />
                            <table class="table table-bordered fixed-header">
                                <thead class="font-weight-bold card-title-bg-primary sticky-top">
                                    <tr class="text-center uppercase">
                                        <th class="align-middle text-nowrape" style="width:50px">STT</th>
                                        <th class="align-middle text-nowrap">Nguyên tệ</th>
                                        <th class="align-middle text-nowrap" style="width: 150px;">Tỷ giá so với VND</th>
                                    </tr>
                                </thead>
                                <tbody id="tableNguyenTeTyGia" class="text-center"></tbody>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2 justify-content-start">
                <button type="button" class="btn btn-sm btn-outline-primary mr-auto" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i> Đóng
                </button>
                <button type="button" class="btn btn-sm btn-primary" id="btnSaveNguyenTeTyGia">
                    <i class="fas fa-save mr-2"></i> Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableNguyenTeTyGiaTemplate">
    <%if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="nt_tg">
        <td>
            <%- item.stt %>
        </td>
        <td>
            <input type="hidden" name="nt_tien_yc" value="<%- item.nt_tien_yc %>" />
            <%- item.nt_tien_yc %>
        </td>
        <td>
            <%if(item.nt_tien_bh === 'VND'){ %>
            <input type="text" name="ty_gia" class="floating-input money" value="<%- ESUtil.formatMoney(item.ty_gia??1) %>" disabled />
            <%} else { %>
            <input type="text" name="ty_gia" class="floating-input money" value="<%- ESUtil.formatMoney(item.ty_gia??1) %>" />
            <%}%>
        </td>
    </tr>
    <% })} %>
    <% if(data.length < 5){
    for(var i = 0; i < 5 - data.length; i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>