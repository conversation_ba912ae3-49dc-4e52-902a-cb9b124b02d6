﻿using ESCS.Attributes;
using ESCS.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class SourceReaderController : BaseController
    {
        private readonly List<string> _blacklist;

        public SourceReaderController()
        {
            _blacklist = new List<string>
            {
                Path.Combine(Environment.CurrentDirectory,"FILE_CAM_XOA"),
                Path.Combine(Environment.CurrentDirectory,"wwwroot","libs"),
                Path.Combine(Environment.CurrentDirectory,"wwwroot","scss"),
                Path.Combine(Environment.CurrentDirectory,"wwwroot","sweetalert2"),
                Path.Combine(Environment.CurrentDirectory,"wwwroot","tabulator"),
                Path.Combine(Environment.CurrentDirectory,"wwwroot","jquery-form-serializer"),
            };
        }

        public IActionResult Index()
        {
            List<string> model = new List<string>();
            try
            {
                return View(model: ScanDirectory(new DirectoryInfo(Environment.CurrentDirectory)));
            }
            catch
            {
                return View(model: new List<string>());
            }
        }

        public List<string> ScanDirectory(DirectoryInfo directoryInfo)
        {
            List<string> result = new List<string>();
            if (!string.IsNullOrEmpty(_blacklist.FirstOrDefault(item => directoryInfo.ToString().Contains(item)))) return result;
            if (!directoryInfo.Exists) return result;

            directoryInfo.GetFiles().ToList().ForEach(item =>
            {
                if (Path.GetExtension(item.ToString()) == ".js") result.Add(item.ToString());
            });

            if (directoryInfo.GetDirectories().Length < 1) return result;

            directoryInfo.GetDirectories().ToList().ForEach(item =>
            {
                result = result.Concat(ScanDirectory(item)).ToList();
            });

            return result;
        }

        public async Task<IActionResult> DocFile(string url)
        {
            if (System.IO.File.Exists(url))
            {
                switch (Path.GetExtension(url))
                {
                    case ".js":
                        {
                            (List<(int, int)> list, Dictionary<string, long> result, string display) = await DocNoiDungJS(url);
                            return Content(display);
                        }
                    case ".cshtml":
                        {
                            break;
                        }
                    case ".sql":
                        {
                            break;
                        }
                    default:
                        {
                            return NoContent();
                        }
                }
            }
            return NotFound();
        }

        public async Task<(List<(int, int)>, Dictionary<string, long>, string)> DocNoiDungJS(string url)
        {
            List<(int, int)> list = new List<(int, int)>();
            Dictionary<string, long> result = new Dictionary<string, long>();
            string display = "";
            if (System.IO.File.Exists(url) && Path.GetExtension(url) == ".js")
            {
                string fileContent = await System.IO.File.ReadAllTextAsync(url);
                new List<(string, string)>
                {
                    ("_notifyService.success(",")"),
                    ("_notifyService.error(",")"),
                }.ForEach(pair =>
                {
                    string[] array = fileContent.Split(pair.Item1);
                    List<int> position = new List<int> { 0, };
                    for (int i = 0; i < array.Length; i++)
                    {
                        if (i == 0) continue;
                        int offset = position[i - 1] + array[i - 1].Length + pair.Item1.Length;
                        position.Add(offset);

                        int length = array[i].IndexOf(pair.Item2);
                        string foundText = fileContent.Substring(position[i], length);
                        while (!validate(foundText))
                        {
                            foundText += pair.Item2;
                            int next = array[i].Substring(foundText.Length, array[i].Length - foundText.Length).IndexOf(pair.Item2);
                            foundText += array[i].Substring(foundText.Length, next);
                        }
                        if (string.IsNullOrEmpty(foundText)) continue;

                        xuLyChuoi(position[i], foundText).ForEach(keyset =>
                        {
                            (int pos, string txt) = keyset;
                            list.Add((pos, txt.Length));

                            if (!(result.ContainsKey(txt.Trim())))
                            {
                                result.TryAdd(txt.Trim(), -1);
                            }

                            display += txt + '\n';
                        });
                    }
                });

                //call sv result

                list.Sort((a, b) => b.Item1 - a.Item1);
                string newFileData = fileContent;
                for (int i = 0; i < list.Count; i++)
                {
                    int posStart = list[i].Item1;
                    int posEnd = posStart + list[i].Item2;

                    newFileData = newFileData.Insert(posEnd, ")");
                    newFileData = newFileData.Insert(posStart, "fnDich?.(");
                }

                display += "\n\n" + newFileData;

                string root = HttpContext.RequestServices.GetService<IWebHostEnvironment>().WebRootPath;
                string path = Path.Combine(root, "source_scan_result", "js");
                string filePath = Path.Combine(path, $"{DateTime.UtcNow.ToString("yyyy_MM_dd_HH_mm_ss_ffff")}__{Path.GetFileName(url)}.txt");
                if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                string resultContent = "";
                foreach (string key in result.Keys)
                {
                    resultContent += $"{key}: {result[key]}\n";
                }
                System.IO.File.WriteAllText(filePath, resultContent);

                if (url.StartsWith(root))
                {
                    string newFilePath = Path.Combine(root, "source_scan_result", "wwwroot") + url.Substring(root.Length);
                    string folder = Path.GetDirectoryName(newFilePath);
                    string newFileName = $"{DateTime.UtcNow.ToString("yyyy_MM_dd_HH_mm_ss_ffff")}__{Path.GetFileName(url)}";
                    newFilePath = Path.Combine(folder, newFileName);
                    if (!Directory.Exists(folder)) Directory.CreateDirectory(folder);
                    System.IO.File.WriteAllText(newFilePath, newFileData);
                }
            }
            ;
            return (list, result, display);
        }

        private bool validate(string str)
        {
            if (str.Split('(').Length != str.Split(')').Length) return false;
            return true;
        }

        private List<(int, string)> xuLyChuoi(int offset, string str)
        {
            List<(int, string)> result = new List<(int, string)>();
            bool state = false;
            string temp = string.Empty;
            char current = '.';
            for (int index = 0; index < str.Length; index++)
            {
                char c = str[index];
                if ((c == '\"' || c == '\'') && (index == 0 || str[index - 1] != '\\'))
                {
                    temp += c;
                    if (state)
                    {
                        if (c != current) break;
                        result.Add((offset + index - (temp.Length - 1), temp));
                        temp = string.Empty;
                        state = false;
                        continue;
                    }
                    else
                    {
                        current = c;
                        state = true;
                        continue;
                    }
                }
                if (state) temp += c;
            }
            return result;
        }

        //private async Task<KhoaDich> NhapKhoaDich(string str, escs_nguoi_dung user)
        //{
        //    var json = JsonConvert.SerializeObject(new
        //    {
        //        ma_doi_tac_nsd = user.ma_doi_tac,
        //        ma_chi_nhanh_nsd = user.ma_chi_nhanh,
        //        nsd = user.nsd,
        //        pas = user.pas,
        //        nd = str.Trim(),
        //    });
        //    JObject data = JObject.FromObject(await Request.Clone().GetResponeNew(StoredProcedure.PHT_DICH_NH, json));
        //    KhoaDich key = data["data_info"]?.ToObject<KhoaDich>();

        //    return key;
        //}
    }

    public class KhoaDich
    {
        public long? bt { get; set; }
        public string nd { get; set; }
        public string lang { get; set; }
    }
}