﻿<div id="modalViewImages" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width: unset; width:950px">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin hạng mục</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" id="modalViewImagesContent" style="height:450px;">
                <div class="row" id="modalViewImagesThongTinChiTiet" style="display:none;"></div>
                <div class="row">
                    <div class="col-8">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="border mb-3 rounded">
                                    <div style="height:410px;" class="mx-auto d-flex align-items-center" id="divViewImages">
                                        <p>Click để xem ảnh</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="border mb-3 rounded">
                                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                                        <h5 class="m-0">Danh sách ảnh</h5>
                                    </div>
                                    <div style="height:375px;" id="modalViewImagesContentList" class="scrollable">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-outline-primary btn-sm wd-90 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>

<div class="custom-modal">
    <div id="modalXemHinhAnh" class="modal fade" tabindex="-1" data-backdrop="false" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 9999999;width:1000px;top: 5%; left: 18%;">
        <div class="modal-dialog modal-lg" style="width:100%;max-width:unset">
            <div class="modal-content" style="border: 3px solid var(--escs_theme_color);">
                <div class="modal-header py-1" style="background-color: var(--escs_theme_color); cursor:pointer;border:unset;">
                    <h5 class="modal-title" style="color:#fff">Hình ảnh chi tiết</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8 modal-hien-thi">
                            <div class="card mb-0">
                                <div id="div_hinh_anh" class="card-body p-0" style="height:70vh; text-align:center">
                                    <div id="img-hang-muc-container" style="height:70vh"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4" id="accordion">
                            <div class="form-group">
                                <label class="">Chọn hạng mục</label>
                                <select class="select2 form-control custom-select filterModalHinhAnhHangMuc" style="width: 100%; height:36px;"></select>
                            </div>
                            <div class="card mb-1">
                                <div class="">
                                    <div class="card-body p-1">
                                        <div style="width:100%; vertical-align:middle; height:62vh" class="scrollable">
                                            <div style="width:100%" id="dsHinhAnhHangMuc" class="list-pictures-hang-muc">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2 px-2">
                        <form class="form-inline" name="frmNgayChup" style=" margin-left: 10px;" method="post">
                            <div class="form-group">
                                <input type="text" autocomplete="off" class="form-control" style="width: 150px;" name="ngay" readonly placeholder="Ngày chụp">
                            </div>
                            <span style="font-weight: bold; margin: 0 6px;">-</span>
                            <div class="form-group">
                                <input type="text" autocomplete="off" class="form-control" style="width: 200px; " name="nsd" readonly placeholder="Người tải ảnh">
                            </div>
                        </form>
                        <form class="form-inline" name="frmToaDoAnh" method="post">
                            <div class="form-group">
                                <label style="padding-top:4px; margin: 0 7px;">X:</label>
                                <input type="text" autocomplete="off" class="form-control" style="width: 125px;" name="kinh_do" readonly placeholder="Kinh độ">
                            </div>
                            <div class="form-group">
                                <label style="padding-top: 4px; margin: 0 6px;">Y:</label>
                                <input type="text" autocomplete="off" class="form-control" style="width: 125px; " name="vi_do" readonly placeholder="Vĩ độ">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    a.item-image-view {
        display: block;
        float: left;
        margin: 10px 15px;
    }

    #modalViewImagesContent::-webkit-scrollbar {
        width: 10px;
    }

    /* Track */
    #modalViewImagesContent::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    /* Handle */
    #modalViewImagesContent::-webkit-scrollbar-thumb {
        background: #888;
    }

        /* Handle on hover */
        #modalViewImagesContent::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
</style>