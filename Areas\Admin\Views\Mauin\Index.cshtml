﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục mẫu in";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON>h mục mẫu in</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Mẫu in</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập mã/ tên" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top:21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnNhapThongTinMauIn">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row mt-3">
                    <div class="col-md-12" style="margin-top: 5px">
                        <div class="table-responsive">
                            <div id="gridViewMauIn" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapMauIn" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinMauIn" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Nhập thông tin mẫu in</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="pm" value="BT" />
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã</label>
                                <input type="text" maxlength="50" autocomplete="off" name="ma" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên mẫu in</label>
                                <input type="text" maxlength="200" autocomplete="off" name="ten" required class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Url</label>
                                <input type="text" maxlength="100" autocomplete="off" name="url_file" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Mã action</label>
                                <input type="text" maxlength="100" autocomplete="off" name="ma_action_api" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-info" id="btnCopyAndCompile">Copy file lên server và biên dịch lại</button> @*class="btn btn-info btn-sm"*@
                    <button type="button" class="btn btn-outline-info" id="btnCompile">Biên dịch lại</button> @*class="btn btn-info btn-sm"*@
                    <button type="button" class="btn btn-primary btn-sm wd-80" id="btnLuuThongTinMauIn"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-primary btn-sm wd-80" id="btnXoaThongTinMauIn"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div>
@section Scripts{
    <script src="~/js/app/Admin/services/Mauinservice.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/Mauin.js" asp-append-version="true"></script>
}



