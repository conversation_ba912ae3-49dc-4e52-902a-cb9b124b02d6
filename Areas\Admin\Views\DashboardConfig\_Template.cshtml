﻿<div id="modalNguoiSuDung" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Ch<PERSON><PERSON> người sử dụng</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_NguoiSuDung" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalNguoiSuDungElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:300px;" id="modalNguoiSuDungDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer" style="height: 32px; border-top: 1px solid #eaeaea; margin:0px 0px 10px 0px;">
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnChonNguoiSuDung">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalNguoiSuDungDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsnsd" id="dsnsd_<%- ESUtil.xoaKhoangTrangText(item.ma) %>" data-text="<%- ESUtil.xoaKhoangTrangText(item.ten_tim.toLowerCase()) %>">
        <input type="checkbox" id="nguoi_su_dung_<%- item.ma %>" value="<%- item.ma %>" data-stt="<%- index + 1%>" data-ten="<%- item.ten %>" class="custom-control-input modalChonNguoiSuDungItem">
        <label class="custom-control-label" style="cursor:pointer;" for="nguoi_su_dung_<%- item.ma %>"><%- item.ten %> - <span class="text-danger">(<%- item.ma %>)</span></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="tbDsCauHinhTemplate">
    <% var stt = 0 %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="DsCauHinhItem" data-bt="<%- stt %>">
        <% stt = stt + 1 %>

        <td>
            <input type="hidden" data-name="ma" value="<%- item.ma %>" />
            <%- item.ma %>
        </td>
        <td class="text-right">
            <input type="hidden" data-name="ten" value="<%- item.ten %>" />
            <%- item.ten %>
        </td>
        <td class="text-right">
            <input type="text" data-name="xe" autocomplete="off" data-field="xe" class="floating-input number" value="<%- item.xe %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="xe_may" autocomplete="off" data-field="xe_may" class="floating-input number" value="<%- item.xe_may %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="ng" autocomplete="off" data-field="ng" class="floating-input number" value="<%- item.ng %>" />
        </td>
        <td class="text-right">
            <i class="fa fa-times text-danger cursor-pointer" onclick="xoaNSD(this)"></i>
        </td>
    </tr>
    <% }) %>

    <% if(danh_sach.length < 5){
    for(var i = 0; i < 5 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:39.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>