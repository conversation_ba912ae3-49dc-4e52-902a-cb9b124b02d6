﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình phân công xử lý ";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<style>
    .rowSelected input {
        background-color: #f4f4f4 !important;
    }

    .rowSelected {
        background-color: #f4f4f4;
    }

    #modalThemCauHinh .floating-input.hasValue {
        color: var(--escs_theme_color-light);
    }

    #modalThemCauHinh .floating-input {
        cursor: pointer;
        background-color: #e9ecef;
    }
</style>

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình phân công xử lý</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình phân công xử lý</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <!-- Row -->
    <div class="row">
        <!-- Column -->
        <div class="col-lg-12 col-md-12 pb-15">
            <div class="card">
                <div class="card-body" style="padding-top:5px;">
                    <form name="frmTimKiem" method="post">
                        <div class="row">                              
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label class="">Đối tác quản lý</label>
                                    <select class="select2 form-control custom-select" name="ma_doi_tac_ql" style="width: 100%; height:36px;"></select>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label class="">Đơn vị quản lý </label>
                                    <select class="select2 form-control custom-select" name="ma_dvi" style="width: 100%; height:36px;"></select>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label for="nv">Ngiệp vụ</label>
                                    <select class="select2 form-control custom-select" name="nv" style="width: 100%; height: 36px;">
                                        <option value="">Chọn nghiệp vụ</option>
                                        <option value="TAISAN">Tài sản</option>
                                        <option value="KYTHUAT">Kỹ thuật</option>
                                        <option value="TRACHNHIEM">Trách nhiệm</option>
                                        <option value="HONHOP">Hỗn hợp</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3" style="padding-top: 21px;">
                                <button type="button" class="btn btn-primary btn-sm wd-60" title="Tìm kiếm" id="btnTimKiemPhanCongXuLy">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-60" title="Thêm mới" id="btnThemPhanCongXuLy">
                                    <i class="fa fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-60" title="Export" id="btnExportExcelCateCommon">
                                    <i class="fas fa-upload"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-60" title="Import" id="btnImportExcel">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row" style="margin-top:3px;">
                            <div class="col-12">
                                <div id="gridViewPhanCongXuLy" class="table-app" style="height: 64vh;"></div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<partial name="_Modal.cshtml" />
<partial name="_Template.cshtml" />
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/otherconfigureprocessingservice.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/otherconfigureprocessing.js" asp-append-version="true"></script>
}