﻿<script type="text/html" id="templateThongTinBenh">
    <div class="col-sm-12">
        <table class="table">
            <tr>
                <td><PERSON><PERSON><PERSON> t<PERSON></td>
                <td colspan="5">
                    <b style="font-weight:bold;"><%- ten_doi_tac %></b>
                </td>
            </tr>
            <tr>
                <td>Mã cấp trên</td>
                <td>
                    <%- ma_ct %>
                </td>
            </tr>
            <tr>
                <td>Mã bệnh</td>
                <td>
                    <%- ma %>
                </td>
            </tr>
            <tr>
                <td>Tên tiếng anh</td>
                <td>
                    <%- ten_e %>
                </td>
            </tr>
            <tr>
                <td>Tên tiếng việt</td>
                <td>
                    <%- ten_v %>
                </td>
            </tr>
            <tr>
                <td>Mã bộ y tế</td>
                <td>
                    <%- ma_byt %>
                </td>
            </tr>
            <tr>
                <td>Người sử dụng</td>
                <td>
                    <%- nsd %>
                </td>
            </tr>
            <tr>
                <td>Bệnh đặc biệt</td>
                @*<%- benh_db %>*@
                <td style="text-align: left;">
                    <div class="col-sm-12">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="benh_db">
                            <label class="custom-control-label" for="benh_db" style="font-size: 11px; font-weight: 600; padding-top: 4px;"></label>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>Bệnh bẩm sinh</td>
                @*<%- benh_bs %>*@
                <td style="text-align: left;">
                    <div class="col-sm-12">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="benh_bs">
                            <label class="custom-control-label" for="benh_bs" style="font-size: 11px; font-weight: 600; padding-top: 4px;"></label>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>Bệnh tình dục</td>
                @*<%- benh_td %>*@
                <td>
                    <div class="col-sm-12">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="benh_td">
                            <label class="custom-control-label" for="benh_td" style="font-size: 11px; font-weight: 600; padding-top: 4px;"></label>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>Bệnh có sẵn</td>
                @*<%- benh_cs %>*@
                <td>
                    <div class="col-sm-12">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="benh_cs">
                            <label class="custom-control-label" for="benh_cs" style="font-size: 11px; font-weight: 600; padding-top: 4px;"></label>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</script>

<script type="text/html" id="tblCauHinhNhomBenh_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item, index) { %>
    <tr>
        <td class="text-center"><%- index + 1 %></td>
        <td class="text-center"><%- item.ma %></td>
        <td><%- item.ten %></td>
        <td class="text-center"><%- item.stt %></td>
        <td class="text-center"><%- item.trang_thai_hthi %></td>
        <td class="text-center">
            <a href="#" id="btnSuaCauHinh" onclick="suaCauHinhNhomBenh('<%- item.ma_doi_tac %>', '<%- item.ma %>')">
                <i class="fas fa-edit" title="Sửa cấu hình"></i>
            </a>
        </td>
    </tr>
    <% })} %>

    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.5px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalChonMaNhomBenhDanhSach_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox nngt" id="nngt_<%- item.ma %>">
        <input type="checkbox" id="ma_nhom_benh_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonMaNhomBenhItem">
        <label class="custom-control-label" style="cursor:pointer;" for="ma_nhom_benh_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>