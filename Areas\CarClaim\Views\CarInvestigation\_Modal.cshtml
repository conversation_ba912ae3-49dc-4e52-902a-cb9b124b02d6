﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!-- Modal thông tin chi tiết hợp đồng -->
<div id="inside-modal" class="esmodal fade" tabindex="-1" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="esmodal-dialog">
        <div class="esmodal-content">
            <div class="esmodal-header py-1">
                <div id="titleUpdateContract">
                </div>
                <div id="divThongBaoCanhBao">
                </div>
            </div>
            <div class="esmodal-body" style="background-color:#54667a0a">
                <div class="row h-100">
                    <!-- Column -->
                    <div class="col-3 common-tab pr-0" id="divCarCommonInfo">
                        <partial name="../_CarCommonInfo" />
                    </div>
                    <div class="col" id="divCarClaimContent">
                        <partial name="_CarClaimContent" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="popoverAddHoSoGiayTo" class="popover popover-x popover-default" style="display:none; max-width:500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x">&times;</span>Bổ sung hồ sơ giấy tờ
    </h3>
    <div class="popover-body popover-content">
        <form id="frmAddHoSoGiayTo" name="frmAddHoSoGiayTo" method="post">
            <input type="hidden" name="so_id" value="" />
            <input type="hidden" name="pm" value="GD" />
            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <label class="_required">Loại hồ sơ, giấy tờ</label>
                        <div class="input-group">
                            <select class="select2 form-control custom-select" required name="ma_hs" style="width:100%">
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    <div class="form-group">
                        <label class="_required">Trạng thái</label>
                        <div class="input-group">
                            <select class="form-control custom-select" required name="trang_thai" style="width:100%">
                                <option value="C" selected>Chưa bổ sung</option>
                                <option value="D">Đã bổ sung</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label class="_required">Ngày bổ sung</label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number"
                                   required data-drops="up" name="ngay_bs" placeholder="mm/dd/yyyy">
                            <div class="input-group-append">
                                <span class="input-group-text"><span class="ti-calendar"></span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuAddHoSoGiayTo">
            <i class="fa fa-save mr-2"></i>Lưu
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-90 mg-t-22" data-dismiss="modal" id="btnLuuAddHoSoGiayToDong" style="width:120px">
            <i class="fa fa-save mr-2"></i>Lưu & đóng
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnCloseAddHoSoGiayTo">
            <i class="fa fa-window-close mr-2"></i>Đóng
        </button>
    </div>
</div>

<div id="modalTroChuyenGDV" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1 modal-draggable">
                <h4 class="modal-title">Danh sách giám định viên</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" id="dsGiamDinhVienChat">
                Không tìm thấy giám định viên
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-70" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemChiTietTNDS" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:1100px">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin đối tượng tổn thất</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThemTNDS" name="frmThemTNDS" novalidate="novalidate" data-select2-id="frmThemHMTT" method="post">
                    <input type="hidden" name="ma_doi_tac" value="">
                    <input type="hidden" name="so_id" value="">
                    <input type="hidden" name="pm" value="">
                    <input type="hidden" name="hang_muc" value="">
                </form>
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height:400px">
                            <table id="tblCauHinhTNDS" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center uppercase">
                                        <th width="20%" class="">Tên</th>
                                        <th width="20%" class="">Địa chỉ</th>
                                        <th width="7%" class="">Số lượng</th>
                                        <th width="250px" class="">Tình trạng</th>
                                        <th width="10%" class="">Tiền tổn thất</th>
                                        <th width="24%" class="">Ghi chú</th>
                                        <th width="80px"></th>
                                    </tr>
                                </thead>
                                <tbody id="modalThemTNDS_body">
                                </tbody>
                                <tfoot>
                                    <tr class="card-title-bg">
                                        <td colspan="7">
                                            <a href="javascript:void(0)" id="btnThemTNDS">
                                                <i class="fa fa-plus mr-2"></i>Thêm đối tượng tổn thất
                                            </a>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuTNDS">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>

                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalTinhToanCPKhac" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:85%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="">Tính toán chi phí khác</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color: #54667a0a; padding-top:5px;">
                <input type="hidden" id="modalTinhToanCPKhacPM" />
                <div class="table-responsive" style="max-height:495px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th style="vertical-align: middle; width:40px">STT</th>
                                <th style="vertical-align: middle;">Đối tượng</th>
                                <th style="vertical-align: middle;">Tên chi phí</th>
                                <th style="vertical-align: middle; width:110px">Số tiền báo giá</th>
                                <th style="vertical-align: middle; width:90px">Số tiền</th>
                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="modalTinhToanCPKhacGiamTruLoi" checked="">
                                        <label class="custom-control-label" for="modalTinhToanCPKhacGiamTruLoi">% G.Trừ lỗi</label>
                                    </div>
                                </th>

                                <th style="width:110px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="modalTinhToanCPKhacGiamTru" checked="">
                                        <label class="custom-control-label" for="modalTinhToanCPKhacGiamTru">% G.Trừ</label>
                                    </div>
                                </th>

                                <th style="width:130px">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="modalTinhToanCPKhacTrachNhiem" checked="">
                                        <label class="custom-control-label" for="modalTinhToanCPKhacTrachNhiem">% Trách nhiệm</label>
                                    </div>
                                </th>
                                
                                <th style="width:110px" class="modalTinhToanCPKhacThue">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="modalTinhToanCPKhacTLThue" checked="">
                                        <label class="custom-control-label" for="modalTinhToanCPKhacTLThue">% TL Thuế</label>
                                    </div>
                                </th>
                                <th style="vertical-align: middle; width:90px;">Tiền còn lại</th>
                                <th style="vertical-align: middle; width:80px;" class="modalTinhToanCPKhacThue">Tiền thuế</th>
                                <th style="vertical-align: middle; width:90px;">Tổng tiền</th>
                            </tr>
                        </thead>
                        <tbody id="tblTinhToanCPKhac">
                        </tbody>
                        <tfoot>
                            <tr class="text-left card-title-bg">
                                <td colspan="3" class="font-weight-bold">Tổng cộng:</td>
                                <td class="font-weight-bold tong_tien_bao_gia text-right"></td>
                                <td class="font-weight-bold tong_tien_de_xuat text-right"></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td class="modalTinhToanCPKhacThue"></td>
                                <td class="font-weight-bold tong_tien_con_lai text-right"></td>
                                <td class="font-weight-bold tong_tien_thue text-right modalTinhToanCPKhacThue"></td>
                                <td class="font-weight-bold tong_tien text-right"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuTinhToanCPKhacStep4">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnLuuDongTinhToanCPKhacStep4">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal">
                    <i class="fas fa-window-close mr-1"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemChiTietTNDSNguoi" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:1100px">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin đối tượng tổn thất</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThemTNDSNguoi" name="frmThemTNDSNguoi" novalidate="novalidate" method="post">
                    <input type="hidden" name="ma_doi_tac" value="">
                    <input type="hidden" name="so_id" value="">
                    <input type="hidden" name="pm" value="">
                    <input type="hidden" name="hang_muc" value="">
                </form>
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height:400px">
                            <table id="tblCauHinhTNDSNguoi" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center uppercase">
                                        <th width="12%" class="">Tên</th>
                                        <th width="20%" class="">Địa chỉ</th>
                                        <th width="250px" class="">Tình trạng</th>
                                        <th width="14%" class="">Đánh giá</th>
                                        <th width="10%" class="">Tiền tổn thất</th>
                                        <th width="20%" class="">Ghi chú</th>
                                        <th width="80px"></th>
                                    </tr>
                                </thead>
                                <tbody id="modalThemTNDSNguoi_body">
                                </tbody>
                                <tfoot>
                                    <tr class="card-title-bg">
                                        <td colspan="7">
                                            <a href="javascript:void(0)" id="btnThemTNDSNguoi">
                                                <i class="fa fa-plus mr-2"></i>Thêm đối tượng tổn thất
                                            </a>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuTNDSNguoi">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>

                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>


<div id="popoverGhiChu" class="popover popover-x popover-default" style="display:none; width:500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChu" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuDGNV" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChu">
                    <textarea class="form-control" id="divGhiChu_NoiDung" rows="4"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuGhiChu">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div id="popoverNDGiamDinh" class="popover popover-x popover-default" style="display:none; max-width:unset; width:400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popoverNDGiamDinh" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú giám định
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12">
                <textarea class="form-control" id="divNDGiamDinh_NoiDung" rows="10" readonly></textarea>
            </div>
        </div>
    </div>
</div>

<div id="popoverMoTa" class="popover popover-x popover-default" style="display:none; width:500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popMoTa" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung mô tả
    </h3>
    <div class="popover-body popover-content">
        <form name="frmMoTaDGNV" method="post">
            <div class="row">
                <div class="col-12" id="divMoTa">
                    <textarea class="form-control" id="divMoTa_NoiDung" rows="4"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuMoTa">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div id="modalAddDoiTuongTT" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:500px">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin bên thứ ba</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="row" style="max-height: 400px; overflow-y: scroll;">
                    <div class="col-12" id="modalAddDoiTuongTTForm">
                        <form name="frmAddDoiTuongTT" novalidate="novalidate" method="post">
                            <input type="hidden" name="ma_doi_tac" value="" />
                            <input type="hidden" name="so_id" value="" />
                            <input type="hidden" name="so_id_doi_tuong" value="" />
                            <div class="row">
                                <div class="col-12">
                                    <div classs="form-group">
                                        <label class="_required" for="nhom">Nhóm đối tượng</label>
                                        <select class="select2 form-control custom-select" required="" name="nhom" style="width:100%"></select>
                                    </div>
                                </div>
                                <div class="col-12 loai_doi_tuong mt-2">
                                    <div classs="form-group">
                                        <label class="_required" for="loai">Loại đối tượng</label>
                                        <select class="select2 form-control custom-select" required="required" name="loai" style="width:100%">
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 mt-2" data-nhom="NGUOI" data-loai="NGUOI">
                                    <div classs="form-group">
                                        <label class="" for="muc_do">Mức độ thương tật</label>
                                        <select class="select2 form-control custom-select" name="muc_do" style="width:100%">
                                            <option value="">Chọn mức độ</option>
                                            <option value="TU_VONG">Tử vong</option>
                                            <option value="THUONG_TAT">Thương tật</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-6 mt-2">
                                    <div class="form-group">
                                        <label class="_required" for="ten_kh">Tên đối tượng</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" required="" name="ten_doi_tuong" placeholder="Tên đối tượng tổn thất">
                                    </div>
                                </div>
                                <div class="col-6 mt-2">
                                    <div class="form-group">
                                        <label for="ten_kh">Tên chủ đối tượng</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="ten_kh" placeholder="Tên chủ đối tượng">
                                    </div>
                                </div>
                                <div class="col-6" data-nhom="TAI_SAN" data-loai="XE">
                                    <div classs="form-group">
                                        <label class="" for="nhom">Hãng xe</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="hang_xe" placeholder="Hãng xe">
                                    </div>
                                </div>
                                <div class="col-6" data-nhom="TAI_SAN" data-loai="XE">
                                    <div classs="form-group">
                                        <label class="" for="nhom">Loại xe</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="loai_xe" placeholder="Loại xe">
                                    </div>
                                </div>
                                <div class="col-6 mt-2" data-nhom="TAI_SAN" data-loai="XE">
                                    <div class="form-group">
                                        <label>Số khung</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="so_khung" placeholder="Số khung">
                                    </div>
                                </div>
                                <div class="col-6 mt-2" data-nhom="TAI_SAN" data-loai="XE">
                                    <div class="form-group">
                                        <label>Số máy</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="so_may" placeholder="Số máy">
                                    </div>
                                </div>
                                <div class="col-6 mt-2" data-nhom="TAI_SAN" data-loai="XE">
                                    <div class="form-group">
                                        <label>Tải trọng</label>
                                        <input type="number" class="form-control" maxlength="100" autocomplete="off" name="trong_tai" placeholder="Tải trọng">
                                    </div>
                                </div>
                                <div class="col-6 mt-2" data-nhom="TAI_SAN" data-loai="XE">
                                    <div class="form-group">
                                        <label for="nam_sx">Năm sản xuất</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="nam_sx" placeholder="Năm sản xuất">
                                    </div>
                                </div>
                                <div class="col-6 mt-2" data-nhom="KHAC" data-loai="KHAC">
                                    <div class="form-group">
                                        <label>Loại tài sản/Hàng hóa</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="loai_ts" placeholder="Loại tài sản/Hàng hóa">
                                    </div>
                                </div>
                                <div class="col-6 mt-2" data-nhom="KHAC" data-loai="KHAC">
                                    <div class="form-group">
                                        <label>Số lượng/Trọng lượng</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="so_luong" placeholder="Số lượng/Trọng lượng">
                                    </div>
                                </div>
                                <div class="col-6 mt-2">
                                    <div class="form-group">
                                        <label for="ten_kh">Số CMND/CCCD</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="cmnd" placeholder="Số CMND/CCCD đối tượng/chủ đối tượng">
                                    </div>
                                </div>
                                <div class="col-6 mt-2">
                                    <div class="form-group">
                                        <label for="tien_thoa_thuan">Tiền thỏa thuận</label>
                                        <input type="text" class="form-control number" maxlength="100" autocomplete="off" name="tien_thoa_thuan" placeholder="Tiền thỏa thuận">
                                    </div>
                                </div>
                                <div class="col-12 mt-2">
                                    <div class="form-group">
                                        <label for="ten_kh">Địa chỉ</label>
                                        <input type="text" class="form-control" maxlength="100" autocomplete="off" name="dia_chi" placeholder="Địa chỉ">
                                    </div>
                                </div>
                                <div class="col-12 mt-2">
                                    <div class="form-group">
                                        <label for="ghi_chu">Ghi chú</label>
                                        <textarea class="form-control" name="ghi_chu" maxlength="250" placeholder="Nhập vào ghi chú" rows="4" spellcheck="false"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-12 p-0" id="modalAddDoiTuongTTDsChon" style="height: 250px; max-height: 250px; overflow-y: scroll;">
                        <table class="table table-hover" style="border-bottom:1px solid #e8eef3;">
                            <thead>
                                <tr>
                                    <th style="width:40px">Chọn</th>
                                    <th class="text-center">Tên đối tượng tổn thất</th>
                                    <th class="text-center">Nhóm đối tượng</th>
                                    <th class="text-center">Loại</th>
                                    <th class="text-center" style="width:40px" colspan="2">HĐ</th>
                                </tr>
                            </thead>
                            <tbody id="dsDoiTuongTT">
                                <tr>
                                    <td class="text-center" colspan="5">Chưa có đối tượng tổn thất</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block;">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnMoiAddDoiTuongTT">
                    <i class="fa fa-plus mr-2"></i>Thêm mới đối tượng
                </button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnHuyLuu">
                    <i class="fa fa-times mr-2"></i>Hủy lưu
                </button>

                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-120 mg-t-22 float-right" id="btnLuuAddDoiTuongTT">
                    <i class="fa fa-save mr-2"></i>Lưu đối tượng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" id="btnLuuChonDoiTuongTT">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalDanhSachChiPhiGDLan" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin chi phí giám định</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmDanhSachChiPhiGDLan" name="frmDanhSachChiPhiGDLan" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <select class="select2 form-control custom-select" name="lan_gd" style="width:100%"></select>
                            </div>
                        </div>
                        <input type="hidden" name="ma_gdv" value="" />
                    </div>
                </form>
                <div class="row mt-1">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height:400px">
                            <table id="tblDanhSachChiPhiGDLan" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center uppercase">
                                        <th width="26%">Loại chi phí</th>
                                        <th width="45%">Tên chi phí</th>
                                        <th width="15%">Số tiền</th>
                                        <th width="9%">Ghi chú</th>
                                        <th width="5%"></th>
                                    </tr>
                                </thead>
                                <tbody id="bodyDanhSachChiPhiGDLan">
                                </tbody>
                                <tfoot>
                                    <tr class="card-title-bg">
                                        <td colspan="2">
                                            <a href="#" id="btnThemChiPhiGDLan">
                                                <i class="fas fa-plus-square mr-2"></i>Thêm chi phí giám định
                                            </a>
                                        </td>
                                        <td id="tongTienChiPhiGDLan" class="text-right font-weight-bold"></td>
                                        <td colspan="2"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuChiPhiLanGD">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 ml-2" id="btnLuuDongChiPhiLanGD">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalLoaiChiPhiLanGD" class="modal-drag" style="width: 345px; z-index: 9999999;">
    <div class="modal-drag-header">
        <h5><span class="modal-drag-title">Chọn loại chi phí</span> <span data-dismiss="modal-drag"><i class="fa fa-times mr-2"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_LoaiChiPhiLanGD" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalLoaiChiPhiLanGDElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLoaiChiPhiLanGDDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right mb-2" id="btnChonLoaiChiPhiLanGD">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<div id="popoverGhiChuNoiDungCPGD" class="popover popover-x popover-default" style="display:none; width:500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChuNoiDungCPGD" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuNoiDungCPGD" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChuNoiDungCPGD">
                    <textarea class="form-control" id="divGhiChu_NoiDungCPGD" rows="4"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuGhiChuCPGD">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div class="modal fade bd-example-modal-sm" id="modalGuiMailDGHT" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <form name="frmNhapThongTinEmail" novalidate="novalidate" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required" style="font-size: 15px;">Thông tin email khách hàng</label>
                                <div class="form-group">
                                    <input type="text" autocomplete="off" maxlength="100" fn-validate="validateEmailControl" name="email_nhan" required class="form-control" placeholder="Nhập thông tin email">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row px-3 mt-3 d-flex justify-content-center">
                        <button type="button" class="btn btn-primary btn-sm wd-85 mx-2" id="btnGuiEmailDGHT"><i class="fas fa-paper-plane mr-2"></i>Gửi</button>
                        <button type="button" class="btn btn-primary btn-sm wd-85 mx-2" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i> Đóng</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div id="modalHangMucTonThat" class="modal-drag" style="width: 550px; z-index: 9999999; margin-top: -150px; margin-left: 200px;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn hạng mục</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <div class="input-group">
                    <input type="text" class="form-control" id="inputTimKiemHangMuc" autocomplete="off" placeholder="Tìm kiếm hạng mục" value="" />
                    <input type="hidden" id="inputTimKiemHangMuc_ma" />
                    <div class="input-group-append">
                        <label class="input-group-text">
                            <a href="javascript:void(0)" onclick="getPagingHangMuc(1)">
                                <i class="fa fa-search"></i>
                            </a>
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-12 mt-2 scrollable py-2" style="max-height:550px;" id="dsHangMuc">
            </div>
            <div id="dsHangMuc_pagination" class="mt-2 px-2"></div>
        </div>
    </div>
</div>

<div id="popoverGhiChuBaoGia" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 350px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChu" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
        <span class="mr-2">
            <a href="#" onclick="chonGhiChuBaoGia(this)" style="font-size: 12px; margin-left: 15px;">
                <i class="fa fa-bars mr-1"></i>
                Chọn ghi chú
            </a>
        </span>
        <span>
            <a href="#" id="btnThemGhiChuBaoGia" style="font-size: 12px;">
                <i class="fas fa-plus-square mr-1"></i>
                Tạo ghi chú
            </a>
        </span>
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuChiPhi" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChuBaoGia">
                    <textarea class="form-control" id="divGhiChuBaoGia_NoiDung" rows="6"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="chonGhiChuBaoGia">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<script type="text/html" id="dsHangMucTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <div class="custom-control custom-checkbox hangmuc" id="hangmuc_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" class="custom-control-input modalDanhSachHangMucItem single_checked" onchange="onChonHangMuc(this)" id="hang_muc_<%- item.ma %>" value="<%- item.ma %>" data-val="<%- item.ma %>" data-name="<%- item.ten%>">
        <label class="custom-control-label cursor-pointer" for="hang_muc_<%- item.ma %>">
            <%if(item.ten_alias == null || item.ten_alias == ""){%>
                <%- item.ten %> 
            <%}else{%>
                <%- item.ten %> - <span class="font-10 font-italic text-danger">(<%- item.ten_alias %>)</span>
            <%}%>
        </label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<style>
    #modalLoaiChiPhiLanGD {
        left: 319.45px !important;
    }

    #dsHangMuc label:hover {
        color: var(--escs-main-theme-color);
    }
</style>

@*Báo giá*@
<div id="themBaoGia" class="popover popover-x popover-default" style="display:none; max-width:400px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x">&times;</span>
        <span>Thêm báo giá gara</span>
    </h3>
    <div class="popover-body popover-content">
        <form id="frmThemGara" name="frmThemGara" method="post">
            <input type="hidden" name="ma_doi_tac" value="" />
            <input type="hidden" name="so_id" value="" />
            <input type="hidden" name="bt_gara" value="" />
            <input type="file" style="display:none;" accept=".xls,.xlsx" name="file_upload_bao_gia" id="file_upload_bao_gia" />
            <input type="file" style="display:none;" accept=".xls,.xlsx" name="file_upload_bao_gia_doc" id="file_upload_bao_gia_doc" />
            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <label class="_required">Chọn đối tượng tổn thất</label>
                        <div class="input-group">
                            <select class="select2 form-control custom-select" required name="so_id_doi_tuong" style="width:100%">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <label class="_required">Chọn Gara</label>
                        <div class="input-group">
                            <select class="select2 form-control custom-select" required name="gara" style="width:100%">
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="margin-top:8px">
                <div class="col-6">
                    <div class="form-group">
                        <label class="_required">Giờ báo giá</label>
                        <div class="input-group bootstrap-timepicker timepicker">
                            <input class="form-control input-small time" required name="gio_bg" type="text" autocomplete="off" />
                            <div class="input-group-append">
                                <span class="input-group-text">
                                    <span class="ti-calendar"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label class="_required">Ngày báo giá</label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker_max" autocomplete="off" display-format="date" value-format="number" required name="ngay_bg" placeholder="mm/dd/yyyy">
                            <div class="input-group-append">
                                <span class="input-group-text"><span class="ti-calendar"></span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuGara">
            <i class="fas fa-save mr-2"></i>Lưu
        </button>
    </div>
</div>
<div id="dsGaraHopTac" class="popover popover-x popover-default" style="display:none; width:370px; max-width:unset;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x">&times;</span>
        <span>Danh sách gara hợp tác</span>
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGaraHopTac" method="post">
            <div style="width:100%">
                <div class="form-group">
                    <label class="_required">Chọn đối tượng tổn thất</label>
                    <div class="input-group">
                        <select class="select2 form-control custom-select" required name="so_id_doi_tuong" style="width:100%">
                        </select>
                    </div>
                </div>
            </div>
        </form>
        <div style="width:100%">
            <input type="text" class="form-control" placeholder="Tìm gara hợp tác" />
        </div>
        <div class="scrollable pd-t-10" style="max-height:170px" id="divGaraHopTac">
        </div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuChonGara">
            <i class="fas fa-save mr-2"></i>Lưu
        </button>
    </div>
</div>
<div id="popoverHangMucBoSung" class="popover popover-x popover-default" style="display:none; width:450px; max-width:unset">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Thông tin hạng mục bổ sung
    </h3>
    <div class="popover-body popover-content">
        <form id="frmHangMucBoSung" name="frmHangMucBoSung" method="post">
            <input type="hidden" name="hang_muc_goc" value="" />
            <div class="row">
                <div class="col col-12">
                    <div class="form-group">
                        <label for="" class="_required">Hạng mục tổn thất</label>
                        <div class="input-group">
                            <input type="text" class="form-control autocomplete" autocomplete="off" id="frmHangMucBoSung_hang_muc" placeholder="Tìm kiếm hạng mục" required name="hang_muc" value="" />
                            <div class="input-group-append">
                                <label class="input-group-text" for="frmHangMucBoSung_hang_muc">
                                    <a href="#" id="btnXoaInputHangMucBoSung">
                                        <i class="fas fa-times" title="Xóa tìm kiếm"></i>
                                    </a>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col col-6">
                    @*<div class="form-group">
                    <label for="" class="_required">Mức độ tổn thất</label>
                    <input type="text" id="frmHangMucBoSung_muc_do" autocomplete="off" class="form-control" required name="muc_do" value="" />
                    </div>*@
                    <div class="form-group">
                        <label for="" class="_required">Mức độ tổn thất</label>
                        <select class="select2 form-control custom-select" name="muc_do" style="width:100%">
                            <option value="" selected>Chọn mức độ tổn thất</option>
                        </select>
                    </div>
                </div>
                <div class="col col-6">
                    <div class="form-group">
                        <label for="" class="_required">Phương án khắc phục</label>
                        <select class="select2 form-control custom-select" name="thay_the_sc" style="width:100%">
                            <option value="" selected>Chọn phương án khắc phục</option>
                            <option value="T">Thay thế</option>
                            <option value="S">Sửa chữa</option>
                            <option value="C">Chưa xác định</option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer mx-2">
        <button type="button" class="btn btn-primary btn-sm wd-85 float-left mb-1" id="xoaHangMucBoSung">
            <i class="far fa-times mr-2"></i>Xóa
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right mb-1" id="chonHangMucBoSung">
            <i class="far fa-check mr-2"></i>Chọn
        </button>
    </div>
</div>

<div id="popoverChonGaraSuaChua" class="popover popover-x popover-default" style="display:none; width:650px; height:470px; max-width:unset">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Chọn gara sửa chữa/giám định
    </h3>
    <div class="popover-body popover-content">
        <form name="frmChonGaraSuaChuaSearch" method="post" onsubmit="return false;">
            <div class="row">
                <div class="col-4">
                    <select class="select2 form-control custom-select" required name="tinh_thanh" style="width: 100%;"></select>
                </div>
                <div class="col-4 pl-0">
                    <select class="select2 form-control custom-select" required name="quan_huyen" style="width: 100%;"></select>
                </div>
                <div class="col-4 pl-0">
                    <div class="input-group">
                        <input type="text" class="form-control" id="inputTimKiemGara" autocomplete="off" placeholder="Tìm kiếm gara sửa chữa" value="" />
                        <input type="hidden" id="inputTimKiemGara_ma" />
                        <div class="input-group-append">
                            <label class="input-group-text">
                                <a href="javascript:void(0)" onclick="getPagingGaraSuaChua(1)">
                                    <i class="fa fa-search"></i>
                                </a>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="row">
            <div class="col-12 mt-2 scrollable py-2" style="max-height: 350px; height: 350px;" id="dsGara">
            </div>
        </div>
        <div class="row">
            <div id="dsGara_pagination" class="mt-2 px-2"></div>
        </div>
    </div>
</div>

<script type="text/html" id="dsGaraTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <div class="custom-control custom-checkbox gara" id="gara_<%- item.ma %>_<%- index %>" data-text="<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <input type="checkbox" class="custom-control-input modalDanhSachGaraItem single_checked" onclick="onChonGara(this)" id="gara_<%- item.ma %>" value="<%- item.ma %>" data-gara="<%- item.ma %>" data-name="<%- item.ten%>" data-province="<%- item.tinh_thanh%>" data-district="<%- item.quan_huyen%>" data-address="<%- item.dia_chi %>" data-hop-tac="<%- item.hop_tac %>" data-chinh-hang="<%- item.chinh_hang %>">
        <%if(item.ten_tat== undefined || item.ten_tat == null || item.ten_tat==""){%>
        <label class="custom-control-label cursor-pointer" for="gara_<%- item.ma %>">
            <%- item.ten %>
        </label>
        <%}else{%>
        <label class="custom-control-label cursor-pointer" for="gara_<%- item.ma %>">
            <%- item.ten %> <span style="color:red">(<%- item.ten_tat %>)</span>
        </label>
        <%}%>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<div id="popoverNguyenNhan" class="popover popover-x popover-default" style="display:none; width:600px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Nguyên nhân giảm trừ
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divNguyenNhan">
            </div>
        </div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="chonNguyenNhan">
            <i class="fas fa-mouse-pointer mr-2"></i>Chọn
        </button>
    </div>
</div>

<div id="popoverNguyenNhanPA" class="popover popover-x popover-default" style="display:none; width:600px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Nguyên nhân giảm trừ
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divNguyenNhanPA">
            </div>
        </div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="chonNguyenNhanPA">
            <i class="fas fa-mouse-pointer mr-2"></i>Chọn
        </button>
    </div>
</div>

<div id="popoverDKBS" class="popover popover-x popover-default" style="display:none; width:600px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Chọn điều khoản bổ sung
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divDKBS">
            </div>
        </div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="chonDKBS">
            <i class="far fa-check mr-2"></i>Chọn
        </button>
    </div>
</div>

<div id="popoverNhapTenFile" class="popover popover-x popover-default" style="display:none; width:500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popNhapTenFile" class="close pull-right" data-dismiss="popover-x">&times;</span>Nhập tên
    </h3>
    <div class="popover-body popover-content">
        <form name="frmNhapTenFile" method="post">
            <div class="row">
                <div class="col-12" id="divNhapTenFile">
                    <input type="text" id="inputNhapTenFile" class="form-control" placeholder="Mời nhập tên file" maxlength="200" name="name" value="" />
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuNhapTenFile">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div class="modal fade" id="modalVideoDGRRHD" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="min-width:75vw;">
        <div class="modal-content  overflow-hidden">
            <div class="modal-header p-2">
                <h5 class="modal-title">Xem video</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="modalVideoDGRRHDClose">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-2 d-flex flex-nowrap bg-light" style="gap:0.5rem;">
                <div class="d-flex flex-column">
                    <div class="flex-fill card border mb-0" style="min-width:250px;max-width:300px;height:0">
                        <div class="card-body p-2 d-flex flex-column" style="gap:0.5rem;">
                            <div class="flex-fill overflow-auto" style="height:0" id="modalVideoDanhSachDGRRHD">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex-fill card border mb-0">
                    <div class="card-body p-2">
                        <div class="videoContent">
                            <video controls autoplay style="width:100%" id="modalVideoViewDGRRHD" src=""></video>
                            <form name="frmModalVideoUploadDGRRHD" method="post">
                                <input style="display:none" id="inputModalVideoUploadDGRRHD" accept="video/mp4,video/x-m4v,video/*,audio/mp3,audio/*" type="file" name="file" value="" />
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalYeuCauGDinhBThuongHo" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width: 40%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Yêu cầu giám định, bồi thường hộ</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-2">
                <div class="row mt-2">
                    <div class="col-12 px-1 d-none" id="modalYeuCauGDinhBThuongHoDanhSach">
                        <div class="table-responsive px-3" style="max-height:215px;">
                            <table class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center">
                                        <th style="width: 5%"></th>
                                        <th>Đơn vị xử lý</th>
                                        <th>Cán bộ xử lý</th>
                                        <th>Yêu cầu</th>
                                        <th>Trạng thái</th>
                                        <th style="width: 5%"></th>
                                    </tr>
                                </thead>
                                <tbody id="tblYeuCauGDinhBThuongHo">
                                </tbody>
                            </table>
                        </div>
                        <hr style="margin: 5px 0px;" />
                        <div class="col-12 py-2">
                            <button type="button" class="btn btn-primary btn-sm wd-140 float-left" id="btnTrinhDuyetGiamDinhHo">
                                <i class="fas fa-share-square mr-2"></i>Trình GĐ/BT hộ
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                                <i class="fas fa-window-close mr-2"></i>Đóng
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-140 float-right mr-2" id="btnThemMoiYeuCauGDinh">
                                <i class="fa fa-plus mr-2"></i>Tạo yêu cầu
                            </button>
                        </div>
                    </div>
                    <div class="col-12 px-1 d-none" id="modalYeuCauGDinhBThuongHoForm">
                        <form id="frmYeuCauGDinhBThuongHo" name="frmYeuCauGDinhBThuongHo" method="post">
                            <input type="hidden" name="ma_doi_tac" />
                            <input type="hidden" name="so_id" />
                            <input type="hidden" name="bt" />
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="_required">Đơn vị xử lý</label>
                                    <select class="select form-control custom-select select2" required name="ma_dvi_yc" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <label class="_required">Người xử lý</label>
                                    <select class="select form-control custom-select select2" required name="ma_nsd_yc" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group mb-0">
                                    <label class="_required">Yêu cầu</label>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-check form-check-inline" style="margin-right:unset">
                                    <input class="form-check-input material-inputs with-gap" type="radio" name="loai" id="GDHO" value="G">
                                    <label class="form-check-label" style="font-size:12px;" for="GDHO">Chỉ giám định hộ</label>
                                </div>
                    @*             <div class="form-check form-check-inline ml-3" style="margin-right:unset">
                                    <input class="form-check-input material-inputs with-gap" type="radio" name="loai" id="BTHO" value="B">
                                    <label class="form-check-label" style="font-size:12px;" for="BTHO">Chỉ bồi thường hộ</label>
                                </div> *@
                                <div class="form-check form-check-inline ml-3" style="margin-right:unset">
                                    <input class="form-check-input material-inputs with-gap" checked type="radio" name="loai" id="CAHAI" value="C">
                                    <label class="form-check-label" style="font-size:12px;" for="CAHAI">Giám định hộ và bồi thường hộ</label>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <label>Ghi chú</label>
                                    <textarea class="form-control" autocomplete="off" name="ghi_chu" placeholder="Ghi chú" rows="5" tabindex="195" spellcheck="false"></textarea>
                                </div>
                            </div>
                            <hr style="margin: 5px 0px;" />
                            <div class="col-12 py-2">
                                <button type="button" class="btn btn-outline-primary btn-sm wd-85" id="btnXoaYeuCauGDinhHo">
                                    <i class="fas fa-trash-alt mr-2"></i>Xóa
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnQuayLaiDanhSachGDinhHo">
                                    <i class="fas fa-step-backward mr-2"></i>Quay lại
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-85 float-right mr-2" id="btnLuuYeuCauGDinhHo">
                                    <i class="fa fa-save mr-2"></i>Lưu
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalThongTinNguoiLienHe" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Thông tin người liên hệ</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body py-2">
                <form name="frmThongTinNguoiLienHe" method="post">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Họ và tên</label>
                                <input type="text" autocomplete="off" class="form-control" maxlength="100" name="nguoi_lhe" placeholder="VD: Nguyễn Văn A">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Điện thoại</label>
                                <input type="text" autocomplete="off" class="form-control phone" max-length="15" name="dthoai_lhe" placeholder="VD: 0972xxxxxx" im-insert="true">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="">Email</label>
                                <input type="text" autocomplete="off" class="form-control email-inputmask" maxlength="100" name="email_lhe" placeholder="VD: <EMAIL>;<EMAIL>">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm ml-2 wd-110" id="btnLuuDongThongTinNguoiLienHe">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThongTinNguoiThongBao" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Thông tin người thông báo</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body py-2">
                <form name="frmThongTinNguoiThongBao" method="post">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Họ và tên</label>
                                <input type="text" autocomplete="off" class="form-control" maxlength="100" name="nguoi_tb" placeholder="VD: Nguyễn Văn A">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Điện thoại</label>
                                <input type="text" autocomplete="off" class="form-control phone" max-length="15" name="dthoai_tb" placeholder="VD: 0972xxxxxx" im-insert="true">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="">Email</label>
                                <input type="text" autocomplete="off" class="form-control email-inputmask" maxlength="100" name="email_tb" placeholder="VD: <EMAIL>;<EMAIL>">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm ml-2 wd-110" id="btnLuuDongThongTinNguoiThongBao">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>


<div id="modalNDCV" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" style="max-width:55vw">
        <div class="modal-content">
            <div class="modal-header py-1 px-2">
                <h4 class="modal-title">Danh sách nội dung công việc</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <form name="frmNDCV" method="post" class="modal-body p-2 d-flex flex-column" style="background-color:#54667a0a;height:50vh;">
                <div class="row mb-2">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="">Loại hình nghiệp vụ</label>
                            <select class="select2 form-control custom-select" name="lh_nv" style="width: 100%; height:36px;"></select>
                        </div>
                    </div>
                </div>
                <div class="table-responsive bg-white flex-grow-1">
                    <table class="table table-sm table-bordered">
                        <thead class="bg-primary text-white text-center sticky-top">
                            <tr>
                                <th style="width: 250px;">Tên công việc</th>
                                <th style="width: 110px;">Đã hoàn thành</th>
                                <th>Ghi chú</th>
                            </tr>
                        </thead>
                        <tbody id="modalNDCV_content">
                        </tbody>
                    </table>
                </div>
            </form>
            <div class="modal-footer py-1 px-2">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" onclick="save_ndcv()"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modalNhomGhiChu" class="modal-drag" style="width: 350px; z-index: 9999999; left: 120px;">
    <div class="modal-drag-header border-bottom">
        <h6 style="margin-left:10px;"><span class="modal-drag-title">Chọn nội dung</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h6>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_NhomGhiChu" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalNhomGhiChuElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalNhomGhiChuDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer" style="height: 32px; border-top: 1px solid #eaeaea; margin:0px 0px 10px 0px;">
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnChonNhomGhiChu">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalNhomGhiChuDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsngc" id="dsngc_<%- item.ma %>">
        <input type="checkbox" id="nhom_chi_chu_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNhomGhiChuItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="nhom_chi_chu_<%- item.ma %>"><%- item.nhom %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<div id="popoverGhiChuChiPhiCT" class="popover popover-x popover-default" style="display:none; width:500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChuChiPhiCT" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divGhiChuChiPhiCT">
                <textarea class="form-control" id="divGhiChuChiPhiCT_NoiDung" rows="4"></textarea>
            </div>
        </div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuGhiChuChiPhiCT">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div id="modalNguoiLamChung" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin người làm chứng</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmNguoiLamChung" name="frmNguoiLamChung" method="post">
                    <input type="hidden" name="ma_doi_tac" />
                    <input type="hidden" name="so_id" />
                    <input type="hidden" name="vu_tt" />
                    <input type="hidden" name="bt" />
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Họ tên người đại diện</label>
                                <input type="text" class="form-control" autocomplete="off" required name="ten" placeholder="Họ tên người đại diện">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label>Địa chỉ</label>
                                <input type="text" class="form-control" autocomplete="off" name="dia_chi" placeholder="Địa chỉ">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Điện thoại</label>
                                <input type="text" fn-validate="validatePhoneControl" autocomplete="off" class="form-control phone" required name="dien_thoai" placeholder="Số điện thoại">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Email</label>
                                <input type="text" autocomplete="off" class="form-control" name="email" placeholder="Email">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label>Lời khai nhân chứng</label>
                                <textarea class="form-control" autocomplete="off" name="loi_khai_nhan_chung" placeholder="Lời khai nhân chứng" rows="5"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuNguoiLamChung">
                    <i class="fa fa-save"></i>&nbsp;&nbsp;Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modalCapNhatUocTheoDiem" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width: 70%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cập nhật ước theo điểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmCapNhatUocTheoDiem" name="frmCapNhatUocTheoDiem" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:450px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th style="vertical-align: middle;">STT</th>
                                            <th style="vertical-align: middle;">Ngày dự phòng</th>
                                            <th style="vertical-align: middle;">Điểm dự phòng</th>
                                            <th style="vertical-align: middle;width: 250px">Nghiệp vụ</th>
                                            <th style="vertical-align: middle;width: 120px">Số tiền dự phòng</th>
                                            <th style="vertical-align: middle;width: 120px">Tiền chênh lệch</th>
                                            <th>Trạng thái tích hợp</th>
                                            <th style="vertical-align: middle;">Log</th>
                                            <th>Hành động</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblCapNhatUocTheoDiem"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0" style="display:block;">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnDieuChinhBienDo"><i class="fas fa-window-alt mr-2"></i>Điều chỉnh biên độ</button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right" id="btnThemDiemCapNhatUoc"><i class="fas fa-hdd mr-2"></i>Điều chỉnh tăng giảm dự phòng</button>
            </div>
        </div>
    </div>
</div>

<div id="popoverLogRq" class="popover popover-x popover-default" style="display:none; width:500px; max-width: 500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Xem log
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-sm-12" id="divLogRq">
                <div class="form-group">
                    <label>Data Request</label>
                    <textarea class="form-control" autocomplete="off" id="divLogRq_NoiDung" name="log_rq" disabled rows="6"></textarea>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12" id="divLogRes">
                <div class="form-group">
                    <label>Data Response</label>
                    <textarea class="form-control" autocomplete="off" id="divLogRes_NoiDung" name="log_res" disabled rows="6"></textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="popoverLogRes" class="popover popover-x popover-default" style="display:none; width:500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Log response
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divLogRes">
                <textarea class="form-control" id="divLogRes_NoiDung" readonly rows="4"></textarea>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalSuaGioThongBao" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 25%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Thông tin ngày giờ thông báo</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form name="frmSuaGioThongBao" method="post">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Giờ thông báo</label>
                                <div class="input-group bootstrap-timepicker timepicker">
                                    <input class="form-control input-small time" autocomplete="off" placeholder="HH:mm" required name="gio_tb" type="text" />
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <span class="ti-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Ngày thông báo</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker_max" autocomplete="off" display-format="date" value-format="number" required name="ngay_tb" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuSuaNgayGioTb"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalBienDo" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 25%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Điều chỉnh biên độ ước</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="frmBienDo" name="frmBienDo" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:380px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th style="width: 200px">Điểm</th>
                                            <th style="width: 100px">Biên độ ước (%)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblBienDo"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer p-0 d-block">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right" id="btnLuuBienDo"><i class="fas fa-save mr-2"></i>Lưu</button>
            </div>
        </div>
    </div>
</div>

<div id="modalChonDiemDP" class="modal-drag" style="width:280px; z-index:9999999;">
    <div class="modal-drag-header px-2 border-bottom">
        <h6><span class="modal-drag-title">Chọn điểm dự phòng</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h6>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_ChonDiemDP" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonDiemDPElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonDiemDPDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonDiemDP">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<div id="modalChonNV" class="modal-drag" style="width:280px; z-index:9999999;">
    <div class="modal-drag-header px-2 border-bottom">
        <h6><span class="modal-drag-title">Chọn loại hình nghiệp vụ</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h6>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_ChonNV" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonNVElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonNVDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonNV">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalThemDiem" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 40%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Thêm điểm dự phòng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form name="frmThemDiem" method="post">
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Ngày dự phòng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" required name="ngay_dp" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" name="lh_nv" required style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Số tiền dự phòng</label>
                                <input type="text" name="tien" id="tien" required autocomplete="off" spellcheck="false" placeholder="Số tiền dự phòng" class="form-control number">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuThemDiem"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<div class="custom-modal">
    <div id="modalDocHoaDonDienTu" style="width:350px;height:190px; left: 40%; top: 18%;" class="modal fade" tabindex="-1" data-backdrop="false" data-keyboard="false" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" style="max-width:unset;margin: unset;">
            <div class="modal-content" style="border: 2px solid var(--escs-main-theme-color); height: auto;">
                <div class="modal-header py-1" style="background-color: var(--escs-main-theme-color); cursor: pointer; border: unset;">
                    <h5 class="modal-title" style="color:#fff">Đọc hóa đơn điện tử</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body" style="padding-bottom:0px">
                    <form name="frmDocHoaDonDT" method="post">
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="_required">Đơn vị phát hành</label>
                                    <select class="select select2 form-control custom-select" required name="dvi_ph" style="width:100%">
                                        <option value="">Chọn đơn vị phát hành</option>
                                        <option value="GARA">Gara sửa chữa</option>
                                        <option value="CUUHO">Đơn vị cứu hộ</option>
                                        <option value="KHACHHANG">Khách hàng</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <label class="fileNameHoaDonDienTu"></label>
                                <input style="display:none" id="frmImportHoaDon" accept="text/xml" type="file" name="file" value="" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="padding:0px">
                    <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnUploadHoaDon">
                        <i class="fas fa-upload"></i> Chọn file
                    </button>
                    <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnDocHoaDonXML">
                        <i class="fas fa-save"></i> Đọc file
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modalThongTinNguoiLienHeGuiSMS" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Thông tin người liên hệ</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body py-2">
                <form name="frmThongTinLienHeGuiSMS" method="post">
                    <input type="hidden" name="loai" />
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Họ và tên</label>
                                <input type="text" autocomplete="off" class="form-control" maxlength="100" name="nguoi_lhe" placeholder="VD: Nguyễn Văn A">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Email</label>
                                <input type="text" autocomplete="off" class="form-control email-inputmask" maxlength="100" name="email_lhe" placeholder="VD: <EMAIL>;<EMAIL>">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="">Số điện thoại người nhận</label>
                                <input type="text" autocomplete="off" class="form-control" max-length="250" name="dthoai_lhe" placeholder="VD: 0972xxxxxx" im-insert="true">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm ml-2 wd-110" id="btnGuiLinkSMS">
                    <i class="far fa-paper-plane mr-2"></i>Gửi SMS
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="custom-modal">
    <div id="modalBaoGiaGara" style="width: 1400px; top: 77px !important; left: 62px !important;" class="modal fade" tabindex="-1" data-backdrop="false" data-keyboard="false" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" style="max-width:unset;margin: unset;">
            <div class="modal-content" style="border:1px solid var(--escs-main-theme-color);">
                <div class="modal-header py-1" style="background-color:var(--escs-main-theme-color); cursor:pointer;border:unset;">
                    <h4 class="modal-title" style="color:#fff">Thông tin báo giá</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body">
                    <form name="frmBaoGiaGara" method="post">
                        <div class="row" style="margin-bottom:10px">
                            <input type="hidden" name="so_id_bg" value="" />
                            <div class="col-4">
                                <div class="form-group">
                                    <label>Gara báo giá</label>
                                    <select class="select2 form-control custom-select" name="gara" style="width: 100%"></select>
                                </div>
                            </div>
                            <div class="col-2 pl-0">
                                <div class="form-group">
                                    <label>Người báo giá</label>
                                    <input type="text" autocomplete="off" readonly="readonly" class="form-control" name="ten_nsd_gara" value="" />
                                </div>

                            </div>
                            <div class="col-2 pl-0">
                                <div class="form-group">
                                    <label>Điện thoại</label>
                                    <input type="text" fn-validate="validatePhoneControl" readonly="readonly" class="form-control" name="dthoai_nsd_gara" value="" />
                                </div>
                            </div>
                            <div class="col-2 pl-0">
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="text" fn-validate="validateEmailControl" readonly="readonly" class="form-control" name="email_nsd_gara" value="" />
                                </div>
                            </div>
                            <div class="col-2 pl-0">
                                <div class="form-group">
                                    <label>Trạng thái báo giá</label>
                                    <input type="text" autocomplete="off" readonly="readonly" class="form-control" name="trang_thai_bg_hthi" value="" />
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="row">
                        <div class="col-9">
                            <div class="card-body p-0">
                                <div class="border rounded">
                                    <div class="table-responsive" style="height:40vh">
                                        <table class="table table-bordered fixed-header" style="border-bottom:1px solid #e8eef3;width:163%">
                                            <thead class="font-weight-bold card-title-bg-primary">
                                                <tr class="text-center">
                                                    <th>Hạng mục</th>
                                                    <th>Mức độ</th>
                                                    <th style="width:82px">Phương án</th>
                                                    <th style="width:83px">Chính hãng</th>

                                                    <th style="width:71px; color:#ffc107">SL (Gara)</th>
                                                    <th style="width:110px; color:#ffc107">Tiền V.Tư (Gara)</th>
                                                    <th style="width:118px; color:#ffc107">Nhân công (Gara)</th>
                                                    <th style="width:118px; color:#ffc107">Tiền sơn (Gara)</th>
                                                    <th style="width:117px; color:#ffc107">Tổng cộng (Gara)</th>
                                                    <th style="width:102px; color:#ffc107">Ghi chú (Gara)</th>

                                                    <th style="width:40px">SL</th>
                                                    <th style="width:90px">Tiền V.Tư</th>
                                                    <th style="width:85px">Nhân công</th>
                                                    <th style="width:85px">Tiền sơn</th>
                                                    <th style="width:90px">Tổng ĐX</th>
                                                    <th style="width:150px">Ghi chú</th>
                                                </tr>
                                            </thead>
                                            <tbody id="modalBaoGiaGara_lan_ct">
                                            </tbody>
                                            <tfoot>
                                                <tr class="card-title-bg font-weight-bold">
                                                    <td colspan="4">
                                                        Tổng cộng
                                                    </td>
                                                    <td></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTienVatTuGara"></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTienCongGara"></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTienKhacGara"></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTongCongGara"></td>
                                                    <td></td>

                                                    <td></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTienVatTu"></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTienCong"></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTienKhac"></td>
                                                    <td class="text-right" id="modalBaoGiaGaraTongTongCong"></td>
                                                    <td></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="col-3 pl-0">
                            <div class="card-body p-0">
                                <div class="border rounded">
                                    <div class="table-responsive" style="height: 40vh">
                                        <table class="table table-bordered fixed-header" style="border-bottom:1px solid #e8eef3;">
                                            <thead class="font-weight-bold card-title-bg-primary">
                                                <tr>
                                                    <th class="text-center" style="width:70px">Lần BG</th>
                                                    <th class="text-center" style="width:90px">Ngày BG</th>
                                                    <th class="text-center">Trạng thái</th>
                                                </tr>
                                            </thead>
                                            <tbody id="modalBaoGiaGara_lan">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-180 float-left d-none" id="btnYeuCauSuaChua">
                        <i class="fas fa-wrench mr-1"></i>Gửi yêu cầu sửa chữa
                    </button>

                    <button type="button" class="btn btn-primary btn-sm wd-150 mg-t-22" id="btnBGChapNhan">
                        <i class="fas fa-check mr-2"></i>Đồng ý giá gara báo
                    </button>
                    @*<button type="button" class="btn btn-primary btn-sm 150 mg-t-22" id="btnBGTuChoi">
                    <i class="fab fa-creative-commons-nc mr-2"></i>Từ chối báo giá
                    </button>
                    <button type="button" class="btn btn-primary btn-sm 150 mg-t-22" id="btnBGYeuCauMoi">
                    <i class="fas fa-plus mr-2"></i>Tạo lần báo giá mới
                    </button>*@

                    <button type="button" class="btn btn-primary btn-sm 150 mg-t-22" id="btnBGLuu">
                        <i class="fas fa-save mr-2"></i> Lưu
                    </button>

                    <button type="button" class="btn btn-primary btn-sm 150 mg-t-22" id="btnBGChuyenGara">
                        <i class="fas fa-share mr-2"></i> Chuyển giá đề xuất
                    </button>

                    <button type="button" class="btn btn-primary btn-sm wd-90 mg-t-22 float-right" data-dismiss="modal" id="CarClaimCarCategoryAdd_close">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                    <button type="button" class="btn btn-warning btn-sm 150 mg-t-22 float-right" id="btnBGHuy" style="color:#fff;">
                        <i class="fas fa-calendar-times mr-2"></i>Hủy báo giá
                    </button>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalUploadCTTT" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title">Upload hóa đơn</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="file" class="d-none" id="inputUploadCTTT" />
                <div class="d-flex flex-column" style="height:300px;row-gap:1rem;">
                    <div>
                        <button type="button" class="btn btn-sm btn-primary btnAdd"><i class="fas fa-folder-open mr-2"></i>Chọn file</button>
                    </div>
                    <div class="flex-fill mx-n3 px-3 overflow-auto" style="height:0px;">
                        <div class="row" id="divUploadCTTT"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-sm btn-primary btnUpload"><i class="fas fa-upload mr-2"></i>Upload</button>
                <button type="button" class="btn btn-sm btn-primary" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
            </div>
        </div>
    </div>
</div>