﻿<style>
    .img-container-ocr-qlct {
        width: 100% !important;
        left: 0px !important;
    }

    #dsHinhAnhHoSoOCRQlct img:hover {
        border: 2px solid var(--escs-main-theme-color);
    }

    #dsHinhAnhHangMucGiayToOCRQlct img:hover {
        border: 2px solid var(--escs-main-theme-color);
    }

    #modalHealthClaimCompareDataQlct th {
        padding: 0.4rem !important;
    }

    #tblDanhSachHinhAnhHoSoGiayToOCRQlct input {
        color: white !important;
        position: absolute !important;
        z-index: 9;
        opacity: 1;
        width: 15px;
        height: 15px;
        margin-left: 3px !important;
    }

    #tblDanhSachThongTinHoSoGiayToOCRQlct input {
        color: white !important;
        position: absolute !important;
        z-index: 9;
        opacity: 1;
        width: 15px;
        height: 15px;
        margin-left: 3px !important;
    }

    .table-so-sanh-qlct tbody tr td {
        padding: 0.4rem;
    }
</style>

<div class="modal fade bs-example-modal-lg" id="modalHealthClaimCompareDataQlct" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 100%; margin: 10px auto;">
        <div class="modal-content" id="navHealthClaimCompareDataQlct">
            <div class="modal-header py-1">
                <h5 class="modal-title">Thông tin bảng kê</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="height: 81.5vh">
                <div class="wizard" id="navTabTimKiemNguoi">
                    <div class="row" style="margin:0; padding:0">
                        <div class="col-6 p-1">
                            <div class="card mb-0 modal-main-content">
                                <div class="card-body px-0" style="padding-top:0 !important">
                                    <div class="rounded px-2" style="height: 78.5vh; overflow-x: hidden;">
                                        <div class="card mb-0" id="tabThongTinOCRCtQlct">
                                            <div class="row">
                                                <div class="col-12">
                                                    <form action="/" method="post" name="frmOcrBangKeCtQlct" novalidate="novalidate">
                                                        <div class="table-responsive" style="max-height:75vh">
                                                            <table class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0">
                                                                <thead class="font-weight-bold card-title-bg-primary text-nowrap" style="position: sticky; top: 0; z-index: 52 !important;">
                                                                    <tr class="head">
                                                                        <th style="text-align:center; width:4%">STT</th>
                                                                        <th style="width: 50%">Tên chi phí/dịch vụ</th>
                                                                        <th style="width: 10%">Số lượng</th>
                                                                        <th style="width: 10%">Đơn giá</th>
                                                                        <th style="width: 10%">Thành tiền</th>
                                                                        <th style="width: 10%">ĐVT</th>
                                                                        <th style="text-align:center; width:4%"></th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="tableDataOCRBangKeCtQlct">
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 p-1">
                            <div class="card m-0">
                                <div class="card-body p-0">
                                    <div class="border rounded">
                                        <div class="tab-header">
                                            <div class="text-center p-1 card-title-bg font-weight-bold">
                                                <span class="title-ocr">Danh sách hình ảnh</span>
                                            </div>
                                        </div>
                                        <div class="tab-content" style="border: unset;">
                                            <div id="img-container-ocr-qlct" style="height:56vh"></div>
                                            <div class="p-0" role="tabpanel" id="tabThongTinHinhAnhOCRQlct" style="overflow:hidden">
                                                <div class="card-body p-0">
                                                    <div style="width:100%; vertical-align:middle;height:150px" class="scrollable">
                                                        <div style="width:100%" id="dsAnhTonThatOCRQlct" class="list-pictures-ocr-qlct">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="luuDuLieuOCRBangKeQlct">
                    <i class="fa fa-save mr-2"></i> Lưu dữ liệu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

@*Modal chọn loại chi phí*@
<div id="modalChonLoaiChiPhiOCR" class="modal-drag" style="width:350px; z-index:9999999;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn loại chi phí</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputTimKiemLoaiChiPhi" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonLoaiChiPhiElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:300px;" id="modalChonLoaiChiPhiOCRDanhSach">

            </div>
        </div>
    </div>
</div>
@*Template chọn loại chi phí*@
<script type="text/html" id="modalChonLoaiChiPhiDanhSachOCRTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox chi_phi_ocr" id="chi_phi_ocr_<%- item.ma_day_du %>" data-text="<%- item.ten_day_du.toLowerCase() %>">
        <input type="checkbox" id="loai_chi_phi_ocr_<%- item.ma_day_du %>" data-loai="<%- item.loai%>" value="<%- item.ma %>" onchange="onChonLoaiChiPhiOCR(this)" class="custom-control-input single-checked modalChonLoaiChiPhiOCRItem">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_chi_phi_ocr_<%- item.ma_day_du %>"><%- item.ten_day_du %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

@*Danh sách tài liệu hồ sơ hình ảnh*@
<script type="text/html" id="tblDanhSachHinhAnhHoSoGiayToOCR_template">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <div style="display: inline-block;width:100%;">
        <p style="font-weight: bold;" class="p-2 mb-0">
            <a href="#" onclick="onToggleImageOCR('<%- index %>')"><%- ESUtil.rutGonText(85, item.nhom) %></a>
        </p>
        <ul class="docs-pictures clearfix">
            <% _.forEach(item.children, function(image,index1) { %>
            <li class="p-1" style="width: 108px !important;">
                <input type="checkbox" onclick="onChonAnhOCR('<%- image.so_id%>','<%- image.ma_file%>','<%- image.bt%>')" id="img_<%- image.so_id %>_<%- image.ma_file %>_<%- image.bt %>" class="nhom_anh_ocr_<%- index %> mt-1 images-ocr" value="<%- image.bt%>" data-ma-file="<%- image.ma_file %>" name="ocr_image">
                <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
                <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
                <% } else if(_.includes([".xml"], image.extension)){%>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
                <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
                <% } %>
            </li>
            <% }) %>
        </ul>
    </div>
    <% })} %>
</script>
@*Thông tin chung OCR*@
<script type="text/html" id="modalCompareDataOCRGiayToTemplate">
    <% if(data!==undefined && data!==null && data.length > 0){%>
    <%_.forEach(data, function(item,index) {%>
    <tr class="row_item">
        <%if(item.nd_so_sanh != false){%>
        <td><%- item.noi_dung %></td>
        <%if(item.loai == 'BENH_VIEN'){%>
        <td class="text-center">
            <input type="hidden" class="combobox" data-field="loai" data-val="<%- item.loai%>" />
            <%if(item.benh_vien != null && item.benh_vien != "" && item.benh_vien != undefined){%>
            <input type="text" autocomplete="off" class="floating-input combobox text-center text-primary cursor-pointer" onclick="chonBenhVienOCR(this)" data-field="gia_tri" data-val="<%- item.benh_vien%>" value="<%- item.nd_ocr %>">
            <%}else{%>
            <input type="text" autocomplete="off" class="floating-input combobox text-center text-danger cursor-pointer" onclick="chonBenhVienOCR(this)" data-field="gia_tri" data-val="<%- item.benh_vien%>" value="<%- item.nd_ocr %>">
            <%}%>
        </td>
        <%}else{%>
        <td class="text-center">
            <input type="hidden" class="combobox" data-field="loai" data-val="<%- item.loai%>" />
            <input type="text" autocomplete="off" class="floating-input text-center combobox cursor-pointer" onchange="suaNoiDungOCR(this)" data-field="gia_tri" data-val="<%- item.nd_ocr %>" value="<%- item.nd_ocr %>" />
        </td>
        <%}%>
        <td class="text-center"><% if(item.so_sanh) { %> <i class="fas fa-check text-success"></i> <%} else { %><i class="fas fa-times text-danger"></i> <%}%></td>
        <td class="text-center"><%- item.nd_goc %></td>
        <td class="text-right">
            <div class="custom-control custom-checkbox ml-2">
                <input type="checkbox" onchange="onChonDuLieuOCR(this, '<%- item.loai %>')" id="ocr_<%- index + 1%>" class="custom-control-input ocr_item">
                <label class="custom-control-label" for="ocr_<%- index + 1%>">&nbsp;</label>
            </div>
        </td>
        <%}%>
    </tr>
    <% })}else{%>
    <tr class="text-center">
        <td colspan="5">Chưa có dữ liệu</td>
    </tr>
    <%}%>
</script>
<script type="text/html" id="modalCompareDataOCRBangKeChiTietTemplate">
    <% if(data!==undefined && data!==null && data.length > 0){%>
    <%_.forEach(data, function(item,index) {%>
    <tr class="row_item">
        <td class="text-left">
            <input class="floating-input form-control" type="text" data-field="ten_dich_vu" value="<%- item.ten_dich_vu %>" />
        </td>
        <td class="text-center"><%- ESUtil.formatMoney(item.don_gia) %></td>
        <td class="text-center"><%- item.so_luong %></td>
        <td class="text-center"><%- ESUtil.formatMoney(item.thanh_tien) %></td>
    </tr>
    <% })}else{%>
    <tr class="text-center">
        <td colspan="4">Chưa có dữ liệu</td>
    </tr>
    <%}%>
</script>

@* tab Thông tin OCR bảng kê quyền lợi chi tiết *@
<script type="text/html" id="modalDataOCRBangKeQlct_template">
    <tr>
        <td style="text-align: center">

            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_ten_ndbh" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_ten_ndbh">&nbsp;</label>
            </div>
        </td>
        <td>Họ tên bệnh nhân/Họ tên người bệnh/Họ tên khách hàng/Họ tên</td>
        <td><%- bk_thong_tin_ndbh?.ten_ndbh %></td>
        <td>
            <input type="text" name="ten_ndbh" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.ten_ndbh_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_ten_ndbh == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_nam_sinh" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_nam_sinh">&nbsp;</label>
            </div>
        </td>
        <td>Tuổi/Năm sinh</td>
        <td><%- bk_thong_tin_ndbh?.nam_sinh %></td>
        <td>
            <input type="text" name="nam_sinh" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.nam_sinh_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_nam_sinh == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_gioi_tinh" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_gioi_tinh">&nbsp;</label>
            </div>
        </td>
        <td>Giới tính</td>
        <td><%- bk_thong_tin_ndbh?.gioi_tinh %></td>
        <td>
            <input type="text" name="gioi_tinh" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.gioi_tinh_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_gioi_tinh == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_dia_chi" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_dia_chi">&nbsp;</label>
            </div>
        </td>
        <td>Địa chỉ/Địa chỉ hiện tại</td>
        <td><%- bk_thong_tin_ndbh?.dia_chi %></td>
        <td>
            <input type="text" name="dia_chi" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.dia_chi_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_dia_chi == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_bhyt_so" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_bhyt_so">&nbsp;</label>
            </div>
        </td>
        <td>Số thẻ KCB/Mã thẻ BHYT</td>
        <td><%- bk_thong_tin_ndbh?.bhyt_so %></td>
        <td>
            <input type="text" name="bhyt_so" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.bhyt_so_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_bhyt_so == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_tieu_de" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_tieu_de">&nbsp;</label>
            </div>
        </td>
        <td>Tên bảng kê</td>
        <td><%- bk_thong_tin_ndbh?.tieu_de %></td>
        <td>
            <input type="text" name="tieu_de" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.tieu_de_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_tieu_de == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_ten_noi_xuat" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_ten_noi_xuat">&nbsp;</label>
            </div>
        </td>
        <td>Tên nơi xuất</td>
        <td><%- bk_thong_tin_ndbh?.ten_noi_xuat %></td>
        <td>
            <input type="text" name="ten_noi_xuat" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.ten_noi_xuat_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_ten_noi_xuat == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_so_bang_ke" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_so_bang_ke">&nbsp;</label>
            </div>
        </td>
        <td>Số bảng kê</td>
        <td><%- bk_thong_tin_ndbh?.so_bang_ke %></td>
        <td>
            <input type="text" name="so_bang_ke" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.so_bang_ke_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_so_bang_ke == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_ngay_bang_ke" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_ngay_bang_ke">&nbsp;</label>
            </div>
        </td>
        <td>Ngày bảng kê</td>
        <td><%- bk_thong_tin_ndbh?.ngay_bang_ke %></td>
        <td>
            <input type="text" name="ngay_bang_ke" class="floating-input datepicker" value="<%- bk_thong_tin_ndbh?.ngay_bang_ke_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_ngay_bang_ke == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_ngay_vv" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_ngay_vv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày vào viện</td>
        <td><%- bk_thong_tin_ndbh?.ngay_vv %></td>
        <td>
            <input type="text" name="ngay_vv" class="floating-input datepicker" value="<%- bk_thong_tin_ndbh?.ngay_vv_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_ngay_vv == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_ngay_rv" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_ngay_rv">&nbsp;</label>
            </div>
        </td>
        <td>Ngày ra viện</td>
        <td><%- bk_thong_tin_ndbh?.ngay_rv %></td>
        <td>
            <input type="text" name="ngay_rv" class="floating-input datepicker" value="<%- bk_thong_tin_ndbh?.ngay_rv_dc %>" display-format="date" placeholder="dd/mm/yyyy">
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_ngay_rv == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_pid" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_pid">&nbsp;</label>
            </div>
        </td>
        <td>Mã y tế/ PID</td>
        <td><%- bk_thong_tin_ndbh?.pid %></td>
        <td>
            <input type="text" name="pid" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.pid_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_pid == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_tien_thanh_toan" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_tien_thanh_toan">&nbsp;</label>
            </div>
        </td>
        <td>Tổng tiền thanh toán</td>
        <td><%- bk_thong_tin_ndbh?.tien_thanh_toan %></td>
        <td>
            <input type="text" name="tien_thanh_toan" autocomplete="off" class="floating-input number" value="<%- ESUtil.formatMoney(bk_thong_tin_ndbh?.tien_thanh_toan_dc?.replace(/[^0-9+]+/g, '')) %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_tien_thanh_toan == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_insurance_payment" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_insurance_payment">&nbsp;</label>
            </div>
        </td>
        <td>Quỹ BHYT thanh toán</td>
        <td><%- bk_thong_tin_ndbh?.insurance_payment %></td>
        <td>
            <input type="text" name="insurance_payment" autocomplete="off" class="floating-input number" value="<%- ESUtil.formatMoney(bk_thong_tin_ndbh?.insurance_payment_dc?.replace(/[^0-9+]+/g, '')) %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_insurance_payment == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_chan_doan" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_chan_doan">&nbsp;</label>
            </div>
        </td>
        <td>Chẩn đoán xác định/Chẩn đoán/Chẩn đoán khi ra viện</td>
        <td><%- bk_thong_tin_ndbh?.chan_doan %></td>
        <td>
            <input type="text" name="chan_doan" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.chan_doan_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_chan_doan == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
    <tr>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="bkqlct_ad_other_diagnosis" class="custom-control-input bang_ke_qlct_checkbox" onclick="onSuDungDuLieuOCRQlct(this)">
                <label class="custom-control-label" for="bkqlct_ad_other_diagnosis">&nbsp;</label>
            </div>
        </td>
        <td>Bệnh kèm theo</td>
        <td><%- bk_thong_tin_ndbh?.other_diagnosis %></td>
        <td>
            <input type="text" name="other_diagnosis" autocomplete="off" class="floating-input" value="<%- bk_thong_tin_ndbh?.other_diagnosis_dc %>" />
        </td>
        <td class="text-center">
            <% if(bk_thong_tin_ndbh?.check_other_diagnosis == "1"){ %>
                <i class="fas fa-check text-success"></i>
            <% }else{ %>
                <i class="fas fa-times text-danger"></i>
            <% } %>
        </td>
    </tr>
</script>

@* Danh sách chi tiết bảng kê OCR *@
<script type="text/html" id="tableDataOCRBangKeCtQlct_template">
    <% _.forEach(bk_qlct_thong_tin_ndbh, function(item, index) { %>
    <tr class="itemCtQlct">
        <td class="text-center"><%- item.stt %></td>
        <td class="">
            <input type="hidden" name="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" name="loai" value="<%- item.loai %>" />
            <input type="hidden" name="guid_ocr" value="<%- item.guid_ocr %>" />
            <input type="text" name="ten_chi_phi" autocomplete="off" class="floating-input" value="<%- item.ten_chi_phi %>" />
        </td>
        <td class="text-center">
            <input type="text" name="so_luong" autocomplete="off" class="floating-input number" value="<%- item.so_luong %>" />
        </td>
        <td class="text-center">
            <input type="text" name="don_gia" autocomplete="off" class="floating-input money" value="<%- ESUtil.formatMoney(item.don_gia) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="thanh_tien" autocomplete="off" class="floating-input money" value="<%- ESUtil.formatMoney(item.thanh_tien) %>" />
        </td>
        <td class="text-center">
            <input type="text" name="dvi_tinh" autocomplete="off" class="floating-input" value="<%- item.dvi_tinh %>" />
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDongDataOCRBangKeCtQlct(this)"><i class="fa fa-times"></i></a>
        </td>
    </tr>

    <%})%>
</script>

@*  Danh sách ảnh OCR Qlct *@
<script type="text/html" id="lstImageOCRQlct_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="p-1" id="nhom_anh_ocr_qlct_<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImgOCRQlct('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTietOCRQlct(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img_ocr_qlct<%- image.bt %>" class="nhom_anh_ton_that_ocr_qlct_<%- index %> mt-1" data-hm="<%- item.ma_file %>" value="<%- image.bt %>" name="ds_anh_xe">
            <p class="fileNameImage mt-1" style="cursor:pointer"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
    <% } %>
</script>


