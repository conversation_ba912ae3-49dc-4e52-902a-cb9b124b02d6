﻿<script type="text/html" id="danhDachGCNKhac_template">
    <% if(gcn.length > 0){
    _.forEach(gcn, function(item,index) { %>
    <tr data-search="<%- item.ten.toLowerCase() %>" style="cursor:pointer" class="item-gcn" onclick="xemChiTietGCNKhac('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>')" id="item-gcn-<%- item.ma_doi_tac %><%- item.so_id %><%- item.so_id_dt %>">
        <td style="font-weight:bold; padding: 8px 0 8px 0; width: 56%"><%- item.ten_1 %></td>
        <td style="float:right; padding: 8px 0 8px 0"><%- item.hieu_luc %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="2">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="dsDoiTuongTemplate">
    <div class="border rounded mb-2">
        <div class="d-flex justify-content-between align-items-center py-1" style="background-color: #f7f7f7;">
            <div class="custom-control custom-checkbox ml-2">
                <input type="checkbox" onchange="onChonTatCa(this)" id="chon_tat_ca" class="custom-control-input">
                <label class="custom-control-label font-weight-bold" for="chon_tat_ca">Chọn tất cả</label>
            </div>
        </div>
    </div>
    <% _.forEach(data, function(item, index) { %>
    <div class="custom-control custom-checkbox ml-2" data-text="<%- item.ten %>">
        <input type="hidden" value="<%- item.so_id_dt %>" data-name="so_id_dt" />
        <input type="checkbox" onchange="chonDoiTuong(this)" id="sogcn_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>" class="custom-control-input item-sogcn">
        <label class="custom-control-label" for="sogcn_<%- item.so_id_dt %>"><%- item.ten %></label>
    </div>
    <%})%>
</script>

<script type="text/html" id="DongBaoHiem_template">
    <% var stt = 1 %>
    <% if(dk.length > 0){ %>
    <% _.forEach(dk, function(item,index) { %>
    <tr onclick="Xem_chi_tiet_dong_tai('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>', '<%- item.don_vi_dong_tai %>', '<%- item.loai_dong %>', '<%- item.nv %>','<%- item.lhnv %>')">
        <td class="text-center"><%- stt %></td>
        <% stt++ %>
        <td class="text-center"><b style="font-weight: bold"><%- item.ten_don_vi_dong_tai_hthi %></b></td>
        <td class="text-center" style=" text-transform: uppercase;">
            <%- item.kieu %>
        </td>
        <td class="text-center">
            <%- item.ten_doi_tuong %>
        </td>
        <td class="text-center">
            <%- item.ten_lhnv %>
        </td>
        <td class="text-center">
            <%- item.tl_dong %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_cd %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_tt %>%
        </td>
    </tr>
    <% })} %>
    <% if(dk.length < 6){ %>
    <% for(var i = 0; i < 6 - dk.length;i++ ){  %>
    <tr>
        <td style="height:35.5px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="TaiBaoHiem_template">
    <% var stt = 1 %>
    <% if(dk.length > 0){ %>
    <% _.forEach(dk, function(item,index) { %>
    <tr onclick="Xem_chi_tiet_dong_tai('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>', '<%- item.don_vi_dong_tai %>', '<%- item.loai_dong %>', '<%- item.nv %>','<%- item.lhnv %>')">
        <td class="text-center"><%- stt %></td>
        <% stt++ %>
        <td class="text-center"><b style="font-weight: bold"><%- item.ten_don_vi_dong_tai_hthi %></b></td>
        <td class="text-center" style=" text-transform: uppercase;">
            <%- item.kieu %>
        </td>
        <td class="text-center">
            <%- item.ten_doi_tuong %>
        </td>
        <td class="text-center">
            <%- item.ten_lhnv %>
        </td>
        <td class="text-center">
            <%- item.tl_dong %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_cd %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_tt %>%
        </td>
    </tr>
    <% })} %>
    <% if(dk.length < 6){ %>
    <% for(var i = 0; i < 6 - dk.length;i++ ){  %>
    <tr>
        <td style="height:35.5px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Danh sách ảnh *@
<script type="text/html" id="lstImage_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="pt-1" id="nhom_anh_<%- index %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImg('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTiet(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img<%- image.bt %>" class="nhom_anh_ton_that_<%- index %> mt-1" value="<%- image.bt %>" name="ds_anh_xe">
            <p class="fileNameImage mt-1" style="cursor:pointer"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
    <% } %>
</script>

<script type="text/html" id="tblTongPhiTPA_template">
    <% _.forEach(data, function(item, index) { %>
    <tr onclick="rowSelected(this)">
        <td class="text-center">
            <input class="form-control floating-input text-center" value="<%- item.ngay_ps %>" name="ngay_ps_item" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input text-right" value="<%- ESUtil.formatMoney(item.phi_tpa) %>" name="phi_tpa_item" disabled="disabled" style="background-color: white">
        </td>
        <td>
            <input class="form-control floating-input" value="<%- item.ghi_chu %>" name="ghi_chu_item" disabled="disabled" style="background-color: white">
        </td>
    </tr>
    <%})%>
    <% if(data.length < 6){
    for(var i = 0; i < 5 - data.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" style="width: 55px; height: 19px;"></div>
        </td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="SDBS_template">
    <% var stt = 1 %>
    <% if(dk.length > 0){
    _.forEach(dk, function(item,index) { %>
    <tr>
        <td class="text-center" style=" text-transform: uppercase;">
            <%- item.so_hd %>
        </td>
        <td class="text-center">
            <%- item.kieu_hd_ten %>
        </td>
        <td class="text-center">
            <%- item.ngay_cap_text %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="4">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="danhSachNV_template">
    <% if(lhnv.length > 0){
    _.forEach(lhnv, function(item,index) { %>
    <tr class="ds_lhnv" row-val="<%- item.ma %>">
        <td class="text-center">
            <%- index+1 %>
        </td>
        <td class="text-nowrap"><%- item.ten %></td>
        <td><input type="text" name="" maxlength="18" class="number floating-input" col-tien-bh="<%- item.ma %>" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.tien_bh) %>" /></td>
        <%if(item.ktru=="K"){%>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" col-khau-tru="<%- item.ma %>" name="ktru" onclick="onCheckKhauTru(this, '<%- item.ma %>')" id="checkbox_<%- item.ma %>_khau_tru">
                <label class="custom-control-label" for="checkbox_<%- item.ma %>_khau_tru"></label>
            </div>
        </td>
        <td><input type="text" name="" maxlength="3" col-tl-mien-thuong="<%- item.ma %>" disabled="disabled" class="number floating-input" onchange="hthiTongTien()" value="<%- item.tl_mien_thuong %>" /></td>
        <td><input type="text" name="" maxlength="18" col-mien-thuong="<%- item.ma %>" disabled="disabled" class="number floating-input" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.mien_thuong) %>" /></td>
        <% } else { %>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" checked="checked" col-khau-tru="<%- item.ma %>" name="ktru" onclick="onCheckKhauTru(this, '<%- item.ma %>')" id="checkbox_<%- item.ma %>_khau_tru">
                <label class="custom-control-label" for="checkbox_<%- item.ma %>_khau_tru"></label>
            </div>
        </td>
        <td><input type="text" name="" maxlength="3" col-tl-mien-thuong="<%- item.ma %>" class="number floating-input" onchange="hthiTongTien()" value="<%- item.tl_mien_thuong %>" /></td>
        <td><input type="text" name="" maxlength="18" col-mien-thuong="<%- item.ma %>" class="number floating-input" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.mien_thuong) %>" /></td>
        <% } %>
        <td class="text-center">
            <input type="text" class="number floating-input" col-so-ngay-mt="<%- item.ma %>"  value="<%- item.so_ngay_mt%>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <input type="text" class="floating-input combobox" col-ap-dung-mt="<%- item.ma %>" data-val="<%- item.ap_dung_mt %>" onclick="chonPhamViApDungMienThuong(this)" readonly="readonly" placeholder="Click chọn" value="<%- item.ap_dung_mt_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td><input type="text" name="" maxlength="18" col-phi-bh="<%- item.ma %>" class="number floating-input" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.phi) %>" /></td>
        <td><input type="text" name="" col-thue-bh="<%- item.ma %>" maxlength="12" class="number floating-input" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.thue) %>" /></td>
        <td class="text-center">
            <a href="#" col-ghi-chu-bh="<%- item.ma %>" data-val="<%- item.ghi_chu %>" onclick="xemGhiChuQl(this)"><i class="far fa-file-alt" title="Ghi chú"></i></a>
        </td>
    </tr>
    <% })}%>
    <% if(lhnv.length < 2){
    for(var i = 0; i < 2 - lhnv.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" style="width: 30px; height: 19px;"></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="danhSachNVDKBS_template">
    <% if(lhnv.length > 0){
    _.forEach(lhnv, function(item,index) { %>
    <tr class="ds_lhnv" row-val="<%- item.ma %>">
        <td class="text-center">
            <%- index+1 %>
        </td>
        <td class="text-nowrap"><%- item.ten %></td>
        <%if(item.ktru=="K"){%>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" col-khau-tru="<%- item.ma %>" name="ktru" onclick="onCheckKhauTruDKBS(this, '<%- item.ma %>')" id="checkbox_<%- item.ma %>_khau_tru">
                <label class="custom-control-label" for="checkbox_<%- item.ma %>_khau_tru"></label>
            </div>
        </td>
        <td><input type="text" name="" maxlength="3" col-tl-mien-thuong="<%- item.ma %>" disabled="disabled" class="number floating-input" onchange="hthiTongTienDKBS()" value="<%- item.tl_mien_thuong %>" /></td>
        <td><input type="text" name="" maxlength="18" col-mien-thuong="<%- item.ma %>" disabled="disabled" class="number floating-input" onchange="hthiTongTienDKBS()" value="<%- ESUtil.formatMoney(item.mien_thuong) %>" /></td>
        <% } else { %>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" checked="checked" col-khau-tru="<%- item.ma %>" name="ktru" onclick="onCheckKhauTruDKBS(this, '<%- item.ma %>')" id="checkbox_<%- item.ma %>_khau_tru">
                <label class="custom-control-label" for="checkbox_<%- item.ma %>_khau_tru"></label>
            </div>
        </td>
        <td><input type="text" name="" maxlength="3" col-tl-mien-thuong="<%- item.ma %>" class="number floating-input" onchange="hthiTongTienDKBS()" value="<%- item.tl_mien_thuong %>" /></td>
        <td><input type="text" name="" maxlength="18" col-mien-thuong="<%- item.ma %>" class="number floating-input" onchange="hthiTongTienDKBS()" value="<%- ESUtil.formatMoney(item.mien_thuong) %>" /></td>
        <% } %>
        <td><input type="text" name="" maxlength="18" col-so-ngay-mt="<%- item.ma %>" class="number floating-input"  value="<%- item.so_ngay_mt %>" /></td>
        <td class="text-center">
            <input type="text" class="floating-input combobox" col-ap-dung-mt="<%- item.ma %>" data-val="<%- item.ap_dung_mt %>" onclick="chonPhamViApDungMienThuong(this)" readonly="readonly" placeholder="Click chọn" value="<%- item.ap_dung_mt_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <a href="#" col-ghi-chu-bh="<%- item.ma %>" data-val="<%- item.ghi_chu %>" onclick="xemGhiChuQlDKBS(this)"><i class="far fa-file-alt" title="Ghi chú"></i></a>
        </td>
    </tr>
    <% })}%>
    <% if(lhnv.length < 2){
    for(var i = 0; i < 2 - lhnv.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" style="width: 30px; height: 19px;"></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="danhSachDKLT_template">
    <% if(ds_dklt.length > 0){
    _.forEach(ds_dklt, function(item,index) { %>
    <tr class="ds_dklt" row-val="<%- item.ma %>">
        <td class="text-center">
            <%- index+1 %>
        </td>
        <td><input type="text" name="ma" data-field="ma" class="floating-input" value="<%- item.ma %>" /></td>
        <td><input type="text" name="ten" data-field="ten" class="floating-input" value="<%- item.ten %>" /></td>
    </tr>
    <% })}%>
    <% if(ds_dklt.length < 2){ for(var i = 0; i < 2 - ds_dklt.length;i++ ){ %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" style="width: 30px; height: 19px;"></div>
        </td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="danhSachTS_template">
    <% if(dsts.length > 0){
    _.forEach(dsts, function(item,index) { %>
    <tr class="dsts" row-val="<%- item.ma %>">
        <td class="text-center">
            <%- index+1 %>
        </td>
        <td>
            <input type="hidden" name="ma" data-field="ma" class="floating-input" value="<%- item.ma %>" />
            <input type="hidden" name="ma" data-field="nhom" class="floating-input" value="TAI_SAN" />
            <input type="text" name="ten" data-field="ten" class="floating-input" value="<%- item.ten %>" />
        </td>
        <td><input type="text" name="so_luong" data-field="so_luong" class="number floating-input" value="<%- item.so_luong %>" /></td>
        <td><input type="text" name="gia_tri" data-field="gia_tri" class="number floating-input" onchange="hthiTongTienDSTS()" value="<%- ESUtil.formatMoney(item.gia_tri) %>" /></td>
        <td><input type="text" name="gia_tri_tham_gia" data-field="gia_tri_tham_gia" class="number floating-input" onchange="hthiTongTienDSTS()" value="<%- ESUtil.formatMoney(item.gia_tri_tham_gia) %>" /></td>
    </tr>
    <% })}%>
    <% if(dsts.length < 2){ for(var i = 0; i < 2 - dsts.length;i++ ){ %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" style="width: 30px; height: 19px;"></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="danhSachQTBH_template">
    <% if(qtbh.length > 0){
    _.forEach(qtbh, function(item,index) { %>
    <tr class="dsts" row-val="<%- item.ma %>">
        <td class="text-center">
            <%- index+1 %>
        </td>
        <td>
            <%- item.ma %>
        </td>
        <td>
            <%- item.ten %>
        </td>
    </tr>
    <% })}%>
    <% if(qtbh.length < 2){ for(var i = 0; i < 2 - qtbh.length;i++ ){ %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" style="width: 30px; height: 19px;"></div>
        </td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Danh sách giấy chứng nhận phân trang*@
<script type="text/html" id="danhSachGCNTemplate">
    <% _.forEach(data, function(item, index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten %>">
        <input type="checkbox" onchange="chonGCNPaging(this)" id="gcn_<%- item.so_id_dt %>" value="<%- item.so_id_dt %>" class="custom-control-input item-gcn single_checked">
        <label class="custom-control-label" for="gcn_<%- item.so_id_dt %>"><%- item.ten %></label>
    </div>
    <%})%>
</script>

<script type="text/html" id="tblDanhSachKyThanhToan_template">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-center">
            <%- index + 1 %>
        </td>
        <td class="text-center">
            <%- item.ky_tt_hthi %>
        </td>
        <td class="text-center">
            <%- item.ngay_tt_hthi %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien_da_tt) %>
        </td>
        <td class="text-center">
            <a href="#" onclick="suaKyThanhToan('<%- item.ma_doi_tac%>', '<%- item.so_id%>', '<%- item.bt%>', '<%- item.nv%>')">
                <i class="fa fa-edit"></i>
            </a>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="6">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="modalDKBSDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsdkbs" id="dsdkbs_<%- item.ma %>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="dieu_khoan_bo_sung_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonDKBSItem">
        <label class="custom-control-label" style="cursor:pointer;" for="dieu_khoan_bo_sung_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="modalDKLTDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsdklt" id="dsdklt_<%- item.ma %>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="dieu_khoan_loai_tru_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonDKLTItem">
        <label class="custom-control-label" style="cursor:pointer;" for="dieu_khoan_loai_tru_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="modalQTBHDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsqtbh" id="dsqtbh_<%- item.ma %>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="quy_tac_bao_hiem_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonQTBHItem">
        <label class="custom-control-label" style="cursor:pointer;" for="quy_tac_bao_hiem_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="modalVideoDanhSachTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item, index) {
    if(index == 0){%>
        <div class="d-flex flex-nowrap" style="gap:.25rem;">
            <a class="nav-link rounded videoLink flex-shrink-1 flex-grow-1 active" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoHs('<%- item.bt %>')"><%- item.ten %></a>
            <a href="javascript:void(0)" class="nav-link flex-shrink-0 flex-grow-0" onclick="xoaVideoHs('<%- item.bt %>')"><i class="fas fa-trash-alt"></i></a>
        </div>
    <%} else {%>
        <div class="d-flex flex-nowrap" style="gap:.25rem;">
            <a class="nav-link rounded videoLink flex-shrink-1 flex-grow-1" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoHs('<%- item.bt %>')"><%- item.ten %></a>
            <a href="javascript:void(0)" class="nav-link flex-shrink-0 flex-grow-0" onclick="xoaVideoHs('<%- item.bt %>')"><i class="fas fa-trash-alt"></i></a>
        </div>
    <%}})}%>
</script>