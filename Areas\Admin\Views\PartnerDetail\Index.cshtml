﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình đơn vị xử lý hồ sơ";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<style>
    .rowSelected input {
        background-color: #f4f4f4 !important;
    }

    .rowSelected {
        background-color: #f4f4f4;
    }
    #modalThemCauHinh .floating-input.hasValue {
        color: var(--escs-main-theme-color);
    }
    #modalThemCauHinh .floating-input {
        cursor: pointer;
        background-color: #e9ecef;
    }
        
</style>

<input type="hidden" id="notify_info" value="@TempData[ESCS.COMMON.Contants.ESCSConstants.NOTIFY_INFO]" />
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <!-- Row -->
    <div class="row">
        <!-- Column -->
        <div class="col-lg-12 col-md-12 pb-15">
            <div class="card">
                <div class="card-body" style="padding-top:5px;">
                    <form name="frmSearch" method="post">
                        <div class="row">
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="ngay_d">Ngày tìm kiếm</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_d" display-format="date" value-format="number" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="ngay_c">&nbsp;</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" name="ngay_c" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label class="">Đối tác quản lý đơn</label>
                                    <select class="select2 form-control custom-select" name="ma_doi_tac_ql" style="width: 100%; height:36px;"></select>
                                </div>
                            </div>
                            <div class="col-sm-2" style="padding-top: 21px;">
                                <button type="button" class="btn btn-primary btn-sm wd-80" title="Tìm kiếm" id="btnFrmBranchSearch">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-80" title="Thêm mới" id="btnFrmBranchAdd">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row" style="margin-top:3px;">
                            <div class="col-12">
                                <div id="gridViewDanhSach" class="table-app" style="height: 64vh;"></div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<partial name="_Modal.cshtml" />
<partial name="_Template.cshtml" />
@section scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerDetailService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/PartnerDetail.js" asp-append-version="true"></script>

}