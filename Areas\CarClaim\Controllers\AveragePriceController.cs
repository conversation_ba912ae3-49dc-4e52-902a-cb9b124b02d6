﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.CarClaim.Controllers
{
    /// <summary>
    /// Danh mục tra cứu giá
    /// </summary>
    [Area("CarClaim")]
    [SystemAuthen]
    public class AveragePriceController : BaseController
    {
        /// <summary>
        /// Danh mục tra cứu giá
        /// </summary>
        /// <returns></returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// Lấy toàn bộ thông tin tra cứu
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> GetAll()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_TRA_CUU_GIA_CACHE, json);
            return Ok(data);
        }

        /// <summary>
        /// Tìm kiếm + phân trang tra cứu giá
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_TRA_CUU_GIA_LKE, json);
            return Ok(data);
        }

        /// <summary>
        /// Lấy thông tin chi tiết tra cứu giá
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_TRA_CUU_GIA_LKE_CT, json);
            return Ok(data);
        }

        /// <summary>
        /// Lấy danh sách hồ sơ
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> GetListContract()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_GIA_HO_SO_LKE, json);
            return Ok(data);
        }
    }
}