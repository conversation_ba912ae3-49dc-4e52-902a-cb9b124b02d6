﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình CSYT tự bảo lãnh viện phí";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình CSYT tự bảo lãnh viện phí</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình CSYT tự bảo lãnh viện phí</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        @* <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" placeholder="Nhập thông tin mã/tên" autocomplete="off" class="form-control">
                            </div>
                        </div> *@
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Cơ sở y tế</label>
                                <select class="select2 form-control custom-select" name="ma_bv" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThem">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:12px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewHospital" class="table-app" style="height: 65vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalThemTB" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:90%; margin:10px auto;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h3 class="modal-title">Cấu hình mức giá tự bảo lãnh theo sản phẩm và CSYT</h3>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmThemBV">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required" id="doi_tac">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" readonly style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col col-sm-2">
                            <div class="form-group">
                                <label class="_required" for="ngay_ad">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" required name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="1">Đang sử dụng</option>
                                    <option value="0">Ngưng sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <label class="mt-1">Danh sách bệnh viện</label>
                    <div class="card m-0 border">
                        <div class="card-body p-2">
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label>Tỉnh thành</label>
                                        <select class="select2 form-control custom-select select2-hidden-accessible" style="width: 100%; height:36px;" id="filterCheckListHospital_TinhThanh">
                                            <option value="">Chọn tỉnh thành</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label>Quận huyện</label>
                                        <select class="select2 form-control custom-select select2-hidden-accessible" style="width: 100%; height:36px;" id="filterCheckListHospital_QuanHuyen">
                                            <option value="">Chọn phường xã </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label>Danh sách bệnh viện</label>
                                        <input id="filterCheckListHospital" type="text" class="form-control" autocomplete="off" placeholder="Tìm kiếm bệnh viện">
                                    </div>
                                </div>
                            </div>
                            <div id="divBenhVien_container">
                                <div class="card m-1 border" style="height:170px">
                                    <div class="card-body p-2 overflow-auto scrollable">
                                        <div class="row mx-n2" id="frmBenhVien_container">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <form name="frmSanPham">
                    <div class="table-responsive border mt-2" id="tblChiPhiCTiet" style="max-height:400px">
                        <table id="tbl_lan_bao_lanh_quyen_loi_ct" class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0;">
                            <thead class="font-weight-bold card-title-bg-primary">
                                <tr class="text-center uppercase">
                                    <th nowrap style="width:50px">Mã sản phẩm</th>
                                    <th nowrap style="width:450px">Tên sản phẩm</th>
                                    <th nowrap style="width:110px">Số tiền tối đa cho phép bảo lãnh </th>
                                </tr>
                            </thead>
                            <tbody id="tbCauHinhTBL">
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer p-2">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuBenhVien">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDong">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalBV" class="modal-drag" style="width:650px; z-index:9999999;  margin-top: 5px !important; margin-left: -11px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn CSYT nhận thông báo</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_BV" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalBV_ElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:350px;" id="modalBV_DanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonBV">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalXemChiTiet" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" style="max-width:65%;">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Thông tin chi tiết bệnh viện <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body" style="max-height:450px; overflow: auto;">
                <form name="frmXemChiTiet" method="post">
                    <input type="hidden" name="ma_doi_tac"/>
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Bệnh viện</label>
                                <select class="select2 form-control custom-select" name="ma_bv" readonly style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="_required" for="ngay_ad">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" required name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="1">Đang sử dụng</option>
                                    <option value="0">Ngưng sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="table-responsive border mt-2" id="tblChiPhiCTiet" style="max-height:400px">
                    <table id="tbl_lan_bao_lanh_quyen_loi_ct" class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0;">
                        <thead class="font-weight-bold card-title-bg-primary">
                            <tr class="text-center uppercase">
                                <th nowrap style="width:50px">Mã sản phẩm</th>
                                <th nowrap style="width:450px">Tên sản phẩm</th>
                                <th nowrap style="width:110px">Số tiền tối đa cho phép bảo lãnh </th>
                            </tr>
                        </thead>
                        <tbody id="tbCauHinhTBLXemChiTiet">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuXemChiTiet"><i class="fa fa-save mr-2"></i>Lưu</button>
                <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaXemChiTiet"><i class="fas fa-trash-alt mr-2"></i>Xóa</button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="modalBV_DanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsbv" id="dsbv_<%- item.ma%>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="bv_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalBV_Item">
        <label class="custom-control-label" style="cursor:pointer;" for="bv_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="tbCauHinhTBLTemplate">
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="khoanChiItem" data-bt="<%- item.ma %>">
        <td class="text-center">
            <a href="#" data-field="san_pham" data-val="<%- item.ma %>"></a><%- item.ma %>
        </td>
        <td class="text-left">
            <%- item.ten %>
        </td>
        <td class="text-right">
            <input type="text" data-name="tien" autocomplete="off" data-field="tien" class="floating-input number money" value="0" />
        </td>
    </tr>
    <% }) %>

    <% if(danh_sach.length < 6){
    for(var i = 0; i < 6 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:40px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tbCauHinhTBLXemCTTemplate">
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="khoanChiItem" data-bt="<%- item.ma %>">
        <td class="text-center">
            <a href="#" data-field="san_pham" data-val="<%- item.san_pham %>"></a><%- item.san_pham %>
        </td>
        <td class="text-left">
            <%- item.ten %>
        </td>
        <td class="text-right">
            <input type="text" data-name="tien" name="tien" autocomplete="off" data-field="tien" class="floating-input number money" value="<%- ESUtil.formatMoney(item.tien) %>" />
        </td>
    </tr>
    <% }) %>

    <% if(danh_sach.length < 6){
    for(var i = 0; i < 6 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:40px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@section Styles{
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" />
}

@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/services/hospitalservice.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/HealthService.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/services/hospitalguaranteeservice.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/hospitalguarantee.js" asp-append-version="true"></script>
}