﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.HealthClaim.Controllers
{
    [Area("HealthClaim")]
    [SystemAuthen]
    public class HealthSearchPolicyController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_HS_TIM_GCN, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemTinhTrangTTPhi()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KY_THANH_TOAN_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemThongTinQuyenLoiBaoHiem()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_QLOI_GOC_LKE_TT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layLSTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_HS_LSTT, json);
            return Ok(data);
        }
    }
}