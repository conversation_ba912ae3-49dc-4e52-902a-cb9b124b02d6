﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Hợp đồng bảo hiểm TSKT";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<input type="hidden" id="notify_info" value="@TempData[ESCS.COMMON.Contants.ESCSConstants.NOTIFY_INFO]" />

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="ngay_d">Từ ngày</label>
                                <div class="input-group">
                                    <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_d" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="ngay_c">Đến ngày</label>
                                <div class="input-group">
                                    <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_c" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="ma_chi_nhanh">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="ma_chi_nhanh">Đơn vị</label>
                                <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="nv" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đã duyệt</option>
                                    <option value="C">Chưa duyệt</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <input type="text" class="form-control" name="nd" autocomplete="off" placeholder="Số HĐ/GCN/Tên khách hàng">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                            <label class=""></label>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnSearch">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnAdd">
                                <i class="fa fa-plus"></i>
                            </button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewHDKhac" class="table-app" style="height: 65vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<partial name="_Template.cshtml" />
<partial name="_Modal.cshtml" />
<partial name="_OtherContractSearch.cshtml" />
<partial name="_ModalDanhGiaRuiRo" />
<partial name="_ModalMaNguyenTe.cshtml" />
<partial name="/Views/Shared/_ModalMap.cshtml" />
<partial name="/Views/Shared/_ModalChiNhanh.cshtml" />
<partial name="~/Areas\Contract\Views\Share\_ModalChonDKBS.cshtml" />
<partial name="~/Areas\Contract\Views\Share\_ModalChonDKBS.cshtml" />
<partial name="~/Areas\CarClaim\Views\CarInvestigation\_CarClaimImageUpload.cshtml" />
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />

@section Styles{
    <link href="~/css/app/CarClaim/CarClaimCommon.css" rel="stylesheet" />
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" />
}
@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/otherclaim/services/otherclaimcommonservice.js"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DepartmentListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BusinessCodeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CategoryCommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CarManufacturerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/HieuXeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/CustomerService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/OtherContractService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/OtherContract.js" asp-append-version="true"></script>
}