﻿<script type="text/html" id="dsXacMinhPhiLaySoHS_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien_da_tt) %>
        </td>
        <td class="<%- item.style %>"><%- item.ghi_chu %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="3">
            <p class="m-0">Chưa có dữ liệu hiển thị</p>
        </td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="tblDanhSachLichSuBoiThuongTemplate">
    <% if(arrHoSo.length > 0){ %>
    <% _.forEach(arrHoSo, function(item,index) { %>
    <tr class="text-center">
        <td><%- item.ngay_ht %></td>
        <td class="text-center"><%- item.trang_thai %></td>
        <td>
            <% if(item.loai == 'HSTT'){ %>
                <a href="#" onclick="TransReceiveDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs %></a>
                <%}%>
                <% if(item.loai == 'BLVP'){ %>
                <a href="#" onclick="TransHealthguaranteeDisplay('<%- item.ma_doi_tac%>','<%- item.so_id%>')"><%- item.so_hs%></a>
            <%}%>
        </td>
        <td><%= item.loai_ten%></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_yc) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.so_tien_duyet) %></td>
        <td><%- item.ngay_vv %></td>
        <td><%- item.ngay_rv %></td>
        <td><%- item.hinh_thuc_ten %></td>
        <td><%- item.ten_nguyen_nhan %></td>
        <td><%- item.quyen_loi_ten %></td>

        <td class="text-left"><%- item.ten_benh_vien %></td>
        <td class="text-left"><%- item.chan_doan %></td>
        <td class="text-left"><%- item.nsd %></td>
        <td class="text-center"><%- item.sdt_nsd %></td>
        @* <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuLSTT(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td> *@
    </tr>
    <% })}%>

    <% if(arrHoSo.length < 13){
    for(var i = 0; i < 13 - arrHoSo.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblDanhSachQuyenLoiGocTemplate">
    <% _.forEach(lstQlg, function(item, index) { %>
    <% var thu_tu_cha_con = item.lh_nv.split('.').length - 1 %>
    <% if(thu_tu_cha_con == 0){ %>
    <tr>
        <td style="font-weight: bold">
            <%= item.ten_hien_thi %>
        </td>
        <td style="font-weight: bold" class="text-center"><%- item.nt_tien_bh %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
        <td style="font-weight: bold" class="text-center d-none"><%- item.ngay_lan_kham %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
        <td style="font-weight: bold" class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.dong_bh %> % </td>
        <td style="font-weight: bold" class="text-center"><%- item.so_ngay_cho %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_duyet %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_con %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
        <td style="font-style: italic;" class="text-center"><%- item.kieu_ad_ten%> </td>
        <td style="font-style: italic;" class="text-center d-none"><%- item.lhnv_phu_thuoc %> </td>
        <td style="font-style: italic;" class="text-center"><%- item.lhnv_tru_lui %> </td>
    </tr>
    <% } else{ %>
    <% var pd = item.pd %>
    <tr>
        <td style="font-style: italic; padding-left: <%- pd %>px">
            <%= item.ten_hien_thi %>
        </td>
        <td style="font-weight: bold" class="text-center"><%- item.nt_tien_bh %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
        <td style="font-weight: bold" class="text-center"><%- item.ngay_lan_kham %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.dong_bh %> % </td>
        <td style="font-weight: bold" class="text-center"><%- item.so_ngay_cho %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_duyet %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_con %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
        <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
        <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
        <td style="font-style: italic;" class="text-center"><%- item.kieu_ad_ten%> </td>
        <td style="font-style: italic;" class="text-center d-none"><%- item.lhnv_phu_thuoc %> </td>
        <td style="font-style: italic;" class="text-center"><%- item.lhnv_tru_lui %> </td>
    </tr>
    <% } %>
    <%})%>
</script>

<script type="text/html" id="dsGhiChuKhac_template">
    <% _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-right" style="font-weight: bold">
            <%- item.ma_dkbs %>
        </td>
        <td class="text-left" style="font-weight: bold">
            <%- item.ten_hien_thi %>
        </td>
    </tr>
    <% }) %>

    <% if(data.length < 14){
    for(var i = 0; i < 14 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="dsDieuKhoanBoSung_template">
    <% _.forEach(data, function(item,index) { %>
    <% var thu_tu_cha_con = item.lh_nv.split('.').length - 1 %>
    <% if(thu_tu_cha_con == 0){ %>
    <tr>
        <td class="text-left" style="font-weight: bold">
            <%- item.ten_hien_thi %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
    </tr>
    <% } else{ %>
    <% var pd = thu_tu_cha_con * 15 %>
    <tr>
        <td class="text-left" style="padding-left: <%- pd %>px; font-style: italic;">
            <%- item.ten_hien_thi %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_lan_ngay) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.tien_nam) %>
        </td>
        <td class="text-right" style="font-weight: bold">
            <%- ESUtil.formatMoney(item.so_ngay_cho) %>
        </td>
    </tr>
    <% } %>
    <%})%>

    <% if(data.length < 14){
    for(var i = 0; i < 14 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>