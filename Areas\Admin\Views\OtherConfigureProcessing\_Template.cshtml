﻿
<script type="text/html" id="modalThemCauHinhTemplate">
    <% if(obj.chi_nhanh.length > 0){
    _.forEach(obj.chi_nhanh, function(item,index) {
        var nsd_xly="";        
        if(obj.cau_hinh.length > 0)
        {
             %>
                <tr>
                    <td class="text-center"><%- index+1 %></td>
                    <td>
                        <a style="cursor:pointer" data-field="ma_chi_nhanh_ql" data-val="<%- item.ma_chi_nhanh_ql %>"><%- item.ten_chi_nhanh_ql %></a>
                    </td>
                    <%
                        _.forEach(obj.cau_hinh, function(item2,index2) {                            
                        if(item2.ma_chi_nhanh == item.ma_chi_nhanh_ql)
                        {
                           nsd_xly = item2.nsd_xly;
                        } 
                    })
                    %>
                    <td class="text-center">                        
                        <input style="cursor:pointer" type="text" class="floating-input hasValue" data-field="nguoi_xl" data-val="<%- nsd_xly %>" value="<%- nsd_xly %>" onclick="chonNguoiXuLy(this, '<%- index %>','top top-right')" placeholder="Chọn người xử lý" readonly="readonly">
                    </td>
                </tr>
            <%
            
        }
        else
        {%>
            <tr>
                <td class="text-center"><%- index+1 %></td>
                <td>
                    <a style="cursor:pointer" data-field="ma_chi_nhanh_ql" data-val="<%- item.ma_chi_nhanh_ql %>"><%- item.ten_chi_nhanh_ql %></a>
                </td>
                <td class="text-center">
                    <input style="cursor:pointer" type="text" class="floating-input" data-field="nguoi_xl" data-val="" value="" onclick="chonNguoiXuLy(this, '<%- index %>','top top-right')" placeholder="Chọn người xử lý" readonly="readonly">
                </td>
            </tr>
       <% }
        %>       
            
    <% })} %>
</script>

<script type="text/html" id="modalChonNguoiXuLyTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <% var ma = danh_sach[index].ma.trim().replace(/[^a-zA-Z0-9]/g, '') %>
    <div class="custom-control custom-checkbox dscb" id="dscb_<%- ma %>" data-text="<%- item.ten %>">
        <input type="checkbox" name="chon_nguoi_xl" id="nguoi_xl_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNguoiXuLyItem ">
        <label class="custom-control-label" style="cursor:pointer;" for="nguoi_xl_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>


<script type="text/html" id="edit_cau_hinh_template">
    <% if(arrCauHinh.length > 0){
    _.forEach(arrCauHinh, function(item,index) { %>
    <tr>
        <td>
            <select class="select2 form-control custom-select ma_dvi_gd" required="" style="height:36px;">
                <option value="">Chọn đơn vị</option>
                <% if(arrNguoi.length > 0){
                _.forEach(arrNguoi, function(item1,index) { %>
                <option value="<%- item1.ma %>" <% if(item1.ma == item.ma_chi_nhanh_gd){ %>selected="selected"<% } %>>
                        <%- item1.ten_tat %>
                    </option>
                <% })}%>
            </select>
        </td>
        <td>
            <select class="select2 form-control custom-select ma_dvi_bt" required="" style="height:36px;">
                <option value="">Chọn đơn vị</option>
                <% if(arrNguoi.length > 0){
                _.forEach(arrNguoi, function(item1,index) { %>
                <option value="<%- item1.ma %>" <% if(item1.ma == item.ma_chi_nhanh_bt){ %>selected="selected"<% } %>>
                        <%- item1.ten_tat %>
                    </option>
                <% })}%>
            </select>
        </td>
        <td>
            <select class="select2 form-control custom-select ma_dvi_chi" required="" style="height:36px;">
                <option value="">Chọn đơn vị</option>
                <% if(arrNguoi.length > 0){
                _.forEach(arrNguoi, function(item1,index) { %>
                <option value="<%- item1.ma %>" <% if(item1.ma == item.ma_chi_nhanh_tt){ %>selected="selected"<% } %>>
                        <%- item1.ten_tat %>
                    </option>
                <% })}%>
            </select>
        </td>
        <td>
            <div class="input-group">
                <input type="text" autocomplete="off" class="form-control datepicker tu_ngay" name="ngay_d" display-format="date" value-format="number" placeholder="mm/dd/yyyy" value="<%- parseInt(item.tu_ngay).numberToDate() %>">
                <div class="input-group-append">
                    <span class="input-group-text"><span class="ti-calendar"></span></span>
                </div>
            </div>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-primary btn-sm remove_config">
                <i class="ti-close"></i>
            </button>
        </td>
    </tr>
    <% })} %>
</script>