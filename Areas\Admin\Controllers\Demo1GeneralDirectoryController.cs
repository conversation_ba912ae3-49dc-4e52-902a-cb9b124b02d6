﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class Demo1GeneralDirectoryController : BaseController
    {
       public IActionResult Index()
       {
            return View();
       }

       [AjaxOnly]
       public async Task<IActionResult> GetPaging()
       {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_MA_DANH_MUC_LKE,json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_MA_DANH_MUC_LKE_CT,json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_MA_DANH_MUC_NH,json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> Delete()
        {
            // Lấy thông tin request từ client và thêm thông tin user
            var json = Request.GetDataRequestNew(GetUser());
            // Gọi thủ tục
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_MA_DANH_MUC_X,json);
            // Trả về JSON respone
            return Ok(data);
        }
        



    }
}
