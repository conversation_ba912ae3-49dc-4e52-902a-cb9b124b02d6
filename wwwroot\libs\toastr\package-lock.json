{"name": "toastr", "version": "2.1.4", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "toastr", "version": "2.1.4", "dependencies": {"jquery": ">=1.12.0"}, "devDependencies": {"gulp": "^3.8.10", "gulp-bytediff": "^0.2.0", "gulp-jscs": "^1.3.0", "gulp-jshint": "^1.9.0", "gulp-less": "^3.0.3", "gulp-load-plugins": "^0.7.1", "gulp-load-utils": "0.0.4", "gulp-minify-css": "^0.3.11", "gulp-rename": "^1.2.0", "gulp-sourcemaps": "^1.2.8", "gulp-task-listing": "^0.3.0", "gulp-uglify": "^1.0.1", "gulp-util": "^3.0.1", "jquery": "^2.1.1", "jshint-stylish": "^1.0.0", "karma": "^0.12.25", "karma-coverage": "^0.2.6", "karma-phantomjs-launcher": "^0.1.4", "karma-qunit": "^0.1.3", "merge-stream": "^0.1.6", "phantomjs": "^1.9.7-15", "plato": "^1.2.2", "qunitjs": "~1.14.0"}}, "node_modules/@gulp-sourcemaps/map-sources": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/@gulp-sourcemaps/map-sources/-/map-sources-1.0.0.tgz", "integrity": "sha1-iQrnxdjId/bThIYCFazp1+yUW9o=", "dev": true, "dependencies": {"normalize-path": "^2.0.1", "through2": "^2.0.3"}, "engines": {"node": ">= 0.10"}}, "node_modules/abbrev": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz", "integrity": "sha1-kbR5JYinc4wl813W9jdSovh3YTU=", "dev": true}, "node_modules/accepts": {"version": "1.2.13", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.2.13.tgz", "integrity": "sha1-5fHzkoxtlf2WVYw27D2dDeSm7Oo=", "dev": true, "dependencies": {"mime-types": "~2.1.6", "negotiator": "0.5.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/accord": {"version": "0.27.3", "resolved": "https://registry.npmjs.org/accord/-/accord-0.27.3.tgz", "integrity": "sha1-f7kSlwkoXK6oTrNyxOiCAxtxOOg=", "dev": true, "dependencies": {"convert-source-map": "^1.5.0", "glob": "^7.0.5", "indx": "^0.2.3", "lodash.clone": "^4.3.2", "lodash.defaults": "^4.0.1", "lodash.flatten": "^4.2.0", "lodash.merge": "^4.4.0", "lodash.partialright": "^4.1.4", "lodash.pick": "^4.2.1", "lodash.uniq": "^4.3.0", "resolve": "^1.3.3", "semver": "^5.3.0", "uglify-js": "^2.8.22", "when": "^3.7.8"}}, "node_modules/accord/node_modules/glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/accord/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/accord/node_modules/semver": {"version": "5.4.1", "resolved": "https://registry.npmjs.org/semver/-/semver-5.4.1.tgz", "integrity": "sha512-WfG/X9+oATh81XtllIo/I8gOiY9EXRdv1cQdyykeXK17YcUW3EXUAi2To4pcH6nZtJPr7ZOpM5OMyWJZm+8Rsg==", "dev": true, "bin": {"semver": "bin/semver"}}, "node_modules/acorn": {"version": "4.0.13", "resolved": "https://registry.npmjs.org/acorn/-/acorn-4.0.13.tgz", "integrity": "sha1-EFSVrlNh1pe9GVyCUZLhrX8lN4c=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-3.0.1.tgz", "integrity": "sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=", "dev": true, "dependencies": {"acorn": "^3.0.4"}}, "node_modules/acorn-jsx/node_modules/acorn": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-3.3.0.tgz", "integrity": "sha1-ReN/s56No/JbruP/U2niu18iAXo=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/active-x-obfuscator": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/active-x-obfuscator/-/active-x-obfuscator-0.0.1.tgz", "integrity": "sha1-CJuJs3FF/x2ex0r2UwvlUmyuHxo=", "dev": true, "dependencies": {"zeparser": "0.0.5"}, "engines": {"node": "*"}}, "node_modules/ajv": {"version": "4.11.8", "resolved": "https://registry.npmjs.org/ajv/-/ajv-4.11.8.tgz", "integrity": "sha1-gv+wKynmYq5TvcIK8VlHcGc5xTY=", "dev": true, "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}}, "node_modules/ajv-keywords": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.5.1.tgz", "integrity": "sha1-MU3QpLM2j609/NxU7eYXG4htrzw=", "dev": true, "peerDependencies": {"ajv": ">=4.10.0"}}, "node_modules/align-text": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "integrity": "sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=", "dev": true, "dependencies": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/amdefine": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=", "dev": true, "engines": {"node": ">=0.4.2"}}, "node_modules/ansi-escapes": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-1.4.0.tgz", "integrity": "sha1-06ioOzGapneTZisT52HHkRQiMG4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/anymatch": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz", "integrity": "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA==", "dev": true, "dependencies": {"micromatch": "^2.1.5", "normalize-path": "^2.0.0"}}, "node_modules/archy": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/archy/-/archy-1.0.0.tgz", "integrity": "sha1-+cjBN1fMHde8N5rHeyxipcKGjEA=", "dev": true}, "node_modules/argparse": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.9.tgz", "integrity": "sha1-c9g7wmP4bpf4zE9rrhsOkKfSLIY=", "dev": true, "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/arr-diff": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "integrity": "sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=", "dev": true, "dependencies": {"arr-flatten": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-differ": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/array-differ/-/array-differ-1.0.0.tgz", "integrity": "sha1-7/UuN1gknTO+QCuLuOVkuytdQDE=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-each": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/array-each/-/array-each-1.0.1.tgz", "integrity": "sha1-p5SvDAWrF1KEbudTofIRoFugxE8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-find-index": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.2.tgz", "integrity": "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-slice": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/array-slice/-/array-slice-1.1.0.tgz", "integrity": "sha512-B1qMD3RBP7O8o0H2KbrXDyB0IccejMF15+87Lvlor12ONPRHP6gTjXMNkt/d3ZuOGbAe66hFmaCfECI24Ufp6w==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-union": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "dependencies": {"array-uniq": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-uniq": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-unique": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "integrity": "sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/arrify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "dev": true, "optional": true}, "node_modules/asn1": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.3.tgz", "integrity": "sha1-2sh4dxPJlmhJ/IGAd36+nB3fO4Y=", "dev": true}, "node_modules/assert-plus": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz", "integrity": "sha1-104bh+ev/A24qttwIfP+SBAasjQ=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha1-trvgsGdLnXGXCMo43owjfLUmw9E=", "dev": true}, "node_modules/async-each": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.1.tgz", "integrity": "sha1-GdOGodntxufByF04iu28xW0zYC0=", "dev": true}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "dev": true, "optional": true}, "node_modules/atob": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/atob/-/atob-1.1.3.tgz", "integrity": "sha1-lfE2KbEsOlGl0hWr3OKqnzL4B3M=", "dev": true, "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/aws-sign2": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.6.0.tgz", "integrity": "sha1-FDQt0428yU0OW4fXY81jYSwOeU8=", "dev": true, "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.6.0.tgz", "integrity": "sha1-g+9cqGCysy5KDe7e6MdxudtXRx4=", "dev": true, "optional": true}, "node_modules/babylon": {"version": "6.18.0", "resolved": "https://registry.npmjs.org/babylon/-/babylon-6.18.0.tgz", "integrity": "sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==", "dev": true, "bin": {"babylon": "bin/babylon.js"}}, "node_modules/balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "node_modules/base64-url": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/base64-url/-/base64-url-1.2.1.tgz", "integrity": "sha1-GZ/WYXAqDnt9yubgaYuwicUvbXg=", "dev": true}, "node_modules/base64id": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/base64id/-/base64id-0.1.0.tgz", "integrity": "sha1-As4P3u4M709ACA4ec+g08LG/zj8=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/basic-auth": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.4.tgz", "integrity": "sha1-Awk1sB3nyblKgksp8/zLdQ06UpA=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/basic-auth-connect": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/basic-auth-connect/-/basic-auth-connect-1.0.0.tgz", "integrity": "sha1-/bC0OWLKe0BFanwrtI/hc9otISI=", "dev": true}, "node_modules/batch": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/batch/-/batch-0.5.3.tgz", "integrity": "sha1-PzQU84AyF0O/wQQvmoP/HVgk1GQ=", "dev": true}, "node_modules/bcrypt-pbkdf": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz", "integrity": "sha1-Y7xdy2EzG5K8Bf1SiVPDNGKgb40=", "dev": true, "optional": true, "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/beeper": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/beeper/-/beeper-1.1.1.tgz", "integrity": "sha1-5tXqjF2tABMEpwsiY4RH9pyy+Ak=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/binary-extensions": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.11.0.tgz", "integrity": "sha1-RqoXUftqL5PuXmibsQh9SxTGwgU=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bl": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/bl/-/bl-1.0.3.tgz", "integrity": "sha1-/FQhoo/UImA2w7OJGmaiW8ZNIm4=", "dev": true, "dependencies": {"readable-stream": "~2.0.5"}}, "node_modules/bl/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/bl/node_modules/readable-stream": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz", "integrity": "sha1-j5A0HmilPMySh4jaz80Rs265t44=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/body-parser": {"version": "1.13.3", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.3.tgz", "integrity": "sha1-wIzzMMM1jhUQFqBXRvE/ApyX+pc=", "dev": true, "dependencies": {"bytes": "2.1.0", "content-type": "~1.0.1", "debug": "~2.2.0", "depd": "~1.0.1", "http-errors": "~1.3.1", "iconv-lite": "0.4.11", "on-finished": "~2.3.0", "qs": "4.0.0", "raw-body": "~2.1.2", "type-is": "~1.6.6"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/body-parser/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/body-parser/node_modules/qs": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/qs/-/qs-4.0.0.tgz", "integrity": "sha1-wx2bdOwn33XlQ6hseHKO2NRiNgc=", "dev": true}, "node_modules/boom": {"version": "2.10.1", "resolved": "https://registry.npmjs.org/boom/-/boom-2.10.1.tgz", "integrity": "sha1-OciRjO/1eZ+D+UkqhI9iWt0Mdm8=", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/brace-expansion": {"version": "1.1.8", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.8.tgz", "integrity": "sha1-wHshHHyVLsH479Uad+8NHTmQopI=", "dev": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=", "dev": true, "dependencies": {"expand-range": "^1.8.1", "preserve": "^0.2.0", "repeat-element": "^1.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/bufferstreams": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/bufferstreams/-/bufferstreams-0.0.2.tgz", "integrity": "sha1-fOjf+Wi7rAC56QFYosQUVvdAq90=", "dev": true, "dependencies": {"readable-stream": "^1.0.26-2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/builtin-modules": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/builtin-modules/-/builtin-modules-1.1.1.tgz", "integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bytes": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.1.0.tgz", "integrity": "sha1-rJPEEOL/ycx89LRks4KJBn9eR7Q=", "dev": true}, "node_modules/caller-path": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/caller-path/-/caller-path-0.1.0.tgz", "integrity": "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=", "dev": true, "dependencies": {"callsites": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/callsites": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-0.2.0.tgz", "integrity": "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/camelcase": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "integrity": "sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/camelcase-keys": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz", "integrity": "sha1-MIvur/3ygRkFHvodkyITyRuPkuc=", "dev": true, "dependencies": {"camelcase": "^2.0.0", "map-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/camelcase-keys/node_modules/camelcase": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz", "integrity": "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "dev": true, "optional": true}, "node_modules/center-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "integrity": "sha1-qg0yYptu6XIgBBHL1EYckHvCt60=", "dev": true, "dependencies": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chalk": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chokidar": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-1.7.0.tgz", "integrity": "sha1-eY5ol3gVHIB2tLNg5e3SjNortGg=", "dev": true, "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}}, "node_modules/circular-json": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/circular-json/-/circular-json-0.3.3.tgz", "integrity": "sha512-UZK3NBx2Mca+b5LsG7bY183pHWt5Y1xts4P3Pz7ENTwGVnJOUWbRb3ocjvX7hx9tq/yTAdclXm9sZ38gNuem4A==", "deprecated": "CircularJSON is in maintenance only, flatted is its successor.", "dev": true}, "node_modules/clean-css": {"version": "3.0.10", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-3.0.10.tgz", "integrity": "sha1-1HezgbqkH3Wagp1R+cs4DbkNYm4=", "dev": true, "dependencies": {"commander": "2.5.x", "source-map": ">=0.1.43 <0.2"}, "bin": {"cleancss": "bin/cleancss"}, "engines": {"node": ">=0.10.0"}}, "node_modules/clean-css/node_modules/commander": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/commander/-/commander-2.5.1.tgz", "integrity": "sha1-I8Yfbke+FDzALnrUuxxH9c1aKIM=", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/clean-css/node_modules/source-map": {"version": "0.1.43", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/cli": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cli/-/cli-1.0.1.tgz", "integrity": "sha1-IoF1NPJL+klQw01TLUjsvGIbjBQ=", "dev": true, "dependencies": {"exit": "0.1.2", "glob": "^7.1.1"}, "engines": {"node": ">=0.2.5"}}, "node_modules/cli-cursor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-1.0.2.tgz", "integrity": "sha1-ZNo/fValRBLll5S9Ytw1KV6PKYc=", "dev": true, "dependencies": {"restore-cursor": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cli-table": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/cli-table/-/cli-table-0.3.1.tgz", "integrity": "sha1-9TsFJmqLGguTSz0IIebi3FkUriM=", "dev": true, "dependencies": {"colors": "1.0.3"}, "engines": {"node": ">= 0.2.0"}}, "node_modules/cli-width": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-2.2.0.tgz", "integrity": "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=", "dev": true}, "node_modules/cli/node_modules/glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/cli/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/cliui": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "integrity": "sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=", "dev": true, "dependencies": {"center-align": "^0.1.1", "right-align": "^0.1.1", "wordwrap": "0.0.2"}}, "node_modules/clone": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/clone/-/clone-1.0.3.tgz", "integrity": "sha1-KY1+IjFmD0DAA8LtMUDezz9TCF8=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/clone-stats": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/clone-stats/-/clone-stats-0.0.1.tgz", "integrity": "sha1-uI+UqCzzi4eR1YBG6kAprYjKmdE=", "dev": true}, "node_modules/co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/code-point-at": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/coffee-script": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/coffee-script/-/coffee-script-1.8.0.tgz", "integrity": "sha1-nJ8dK0pSoADe0Vtll5FwNkgmPB0=", "deprecated": "CoffeeScript on NPM has moved to \"coffeescript\" (no hyphen)", "dev": true, "dependencies": {"mkdirp": "~0.3.5"}, "bin": {"cake": "bin/cake", "coffee": "bin/coffee"}, "engines": {"node": ">=0.8.0"}}, "node_modules/coffee-script/node_modules/mkdirp": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.3.5.tgz", "integrity": "sha1-3j5fiWHIjHh+4TaN+EmsRBPsqNc=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true}, "node_modules/colors": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/colors/-/colors-1.0.3.tgz", "integrity": "sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=", "dev": true, "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.5.tgz", "integrity": "sha1-k4NwpXtKUd6ix3wV1cX9+JUWQAk=", "dev": true, "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.6.0.tgz", "integrity": "sha1-nfflL7Kgyw+4kFjugMMQQiXzfh0=", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/compressible": {"version": "2.0.12", "resolved": "https://registry.npmjs.org/compressible/-/compressible-2.0.12.tgz", "integrity": "sha1-xZpcmdt2dn6YdlAOJx72OzSTvWY=", "dev": true, "dependencies": {"mime-db": ">= 1.30.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/compression/-/compression-1.5.2.tgz", "integrity": "sha1-sDuNhub4rSloPLqN+R3cb/x3s5U=", "dev": true, "dependencies": {"accepts": "~1.2.12", "bytes": "2.1.0", "compressible": "~2.0.5", "debug": "~2.2.0", "on-headers": "~1.0.0", "vary": "~1.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/compression/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "node_modules/concat-stream": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.5.0.tgz", "integrity": "sha1-U/fUPFHF5D+ByP3QMyHGMb5o1hE=", "dev": true, "engines": ["node >= 0.8"], "dependencies": {"inherits": "~2.0.1", "readable-stream": "~2.0.0", "typedarray": "~0.0.5"}}, "node_modules/concat-stream/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/concat-stream/node_modules/readable-stream": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz", "integrity": "sha1-j5A0HmilPMySh4jaz80Rs265t44=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/connect": {"version": "2.30.2", "resolved": "https://registry.npmjs.org/connect/-/connect-2.30.2.tgz", "integrity": "sha1-jam8vooFTT0xjXTf7JA7XDmhtgk=", "deprecated": "connect 2.x series is deprecated", "dev": true, "dependencies": {"basic-auth-connect": "1.0.0", "body-parser": "~1.13.3", "bytes": "2.1.0", "compression": "~1.5.2", "connect-timeout": "~1.6.2", "content-type": "~1.0.1", "cookie": "0.1.3", "cookie-parser": "~1.3.5", "cookie-signature": "1.0.6", "csurf": "~1.8.3", "debug": "~2.2.0", "depd": "~1.0.1", "errorhandler": "~1.4.2", "express-session": "~1.11.3", "finalhandler": "0.4.0", "fresh": "0.3.0", "http-errors": "~1.3.1", "method-override": "~2.3.5", "morgan": "~1.6.1", "multiparty": "3.3.2", "on-headers": "~1.0.0", "parseurl": "~1.3.0", "pause": "0.1.0", "qs": "4.0.0", "response-time": "~2.3.1", "serve-favicon": "~2.3.0", "serve-index": "~1.7.2", "serve-static": "~1.10.0", "type-is": "~1.6.6", "utils-merge": "1.0.0", "vhost": "~3.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/connect-timeout": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/connect-timeout/-/connect-timeout-1.6.2.tgz", "integrity": "sha1-3ppexh4zoStu2qt7XwYumMWZuI4=", "dev": true, "dependencies": {"debug": "~2.2.0", "http-errors": "~1.3.1", "ms": "0.7.1", "on-headers": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/connect-timeout/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/connect-timeout/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/connect/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/connect/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/connect/node_modules/qs": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/qs/-/qs-4.0.0.tgz", "integrity": "sha1-wx2bdOwn33XlQ6hseHKO2NRiNgc=", "dev": true}, "node_modules/console-browserify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz", "integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "dev": true, "dependencies": {"date-now": "^0.1.4"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.5.1.tgz", "integrity": "sha1-uCeAl7m8IpNl3lxiz1/K7YtVmeU=", "dev": true}, "node_modules/cookie": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.1.3.tgz", "integrity": "sha1-5zSlwUF/zkctWu+Cw4HKu2TRpDU=", "dev": true, "engines": {"node": "*"}}, "node_modules/cookie-parser": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/cookie-parser/-/cookie-parser-1.3.5.tgz", "integrity": "sha1-nXVVcPtdF4kHcSJ6AjFNm+fPg1Y=", "dev": true, "dependencies": {"cookie": "0.1.3", "cookie-signature": "1.0.6"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "dev": true}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "node_modules/crc": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/crc/-/crc-3.3.0.tgz", "integrity": "sha1-+mIuG8OIvyVzCQgta2UgDOZwkLo=", "dev": true}, "node_modules/cryptiles": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz", "integrity": "sha1-O9/s3GCBR8HGcgL6KR59ylnqo7g=", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "dependencies": {"boom": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/csrf": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/csrf/-/csrf-3.0.6.tgz", "integrity": "sha1-thEg3c7q/JHnbtUxO7XAsmZ7cQo=", "dev": true, "dependencies": {"rndm": "1.2.0", "tsscmp": "1.0.5", "uid-safe": "2.1.4"}, "engines": {"node": ">= 0.8"}}, "node_modules/css": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/css/-/css-2.2.1.tgz", "integrity": "sha1-c6TIHehdtmTU7mdPfUcIXjstVdw=", "dev": true, "dependencies": {"inherits": "^2.0.1", "source-map": "^0.1.38", "source-map-resolve": "^0.3.0", "urix": "^0.1.0"}}, "node_modules/css/node_modules/source-map": {"version": "0.1.43", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/csurf": {"version": "1.8.3", "resolved": "https://registry.npmjs.org/csurf/-/csurf-1.8.3.tgz", "integrity": "sha1-I/KhO/HY/OHQyZZYg5RELLqGpWo=", "deprecated": "This package is archived and no longer maintained. For support, visit https://github.com/expressjs/express/discussions", "dev": true, "dependencies": {"cookie": "0.1.3", "cookie-signature": "1.0.6", "csrf": "~3.0.0", "http-errors": "~1.3.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/currently-unhandled": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/currently-unhandled/-/currently-unhandled-0.4.1.tgz", "integrity": "sha1-mI3zP+qxke95mmE2nddsF635V+o=", "dev": true, "dependencies": {"array-find-index": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cycle": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/cycle/-/cycle-1.0.3.tgz", "integrity": "sha1-IegLK+hYD5i0aPN5QwZisEbDStI=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/d": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/d/-/d-1.0.0.tgz", "integrity": "sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8=", "dev": true, "dependencies": {"es5-ext": "^0.10.9"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "dev": true, "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/dashdash/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/date-now": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz", "integrity": "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=", "dev": true}, "node_modules/dateformat": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/dateformat/-/dateformat-2.2.0.tgz", "integrity": "sha1-QGXiATz5+5Ft39gu+1Bq1MZ2kGI=", "dev": true, "engines": {"node": "*"}}, "node_modules/deap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/deap/-/deap-1.0.0.tgz", "integrity": "sha1-sUi/gkMKJ2mbdIOgPra2dYW/yIg=", "dev": true}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/debug-fabulous": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/debug-fabulous/-/debug-fabulous-0.0.4.tgz", "integrity": "sha1-+gccXYdIRoVCSAdCHKSxawsaB2M=", "dev": true, "dependencies": {"debug": "2.X", "lazy-debug-legacy": "0.0.X", "object-assign": "4.1.0"}}, "node_modules/debug-fabulous/node_modules/object-assign": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.0.tgz", "integrity": "sha1-ejs9DpgGPUP0wD8uiubNUahog6A=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/deep-equal": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=", "dev": true}, "node_modules/deep-is": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=", "dev": true}, "node_modules/defaults": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/defaults/-/defaults-1.0.3.tgz", "integrity": "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=", "dev": true, "dependencies": {"clone": "^1.0.2"}}, "node_modules/del": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/del/-/del-2.2.2.tgz", "integrity": "sha1-wSyYHQZ4RshLyvhiz/kw2Qf/0ag=", "dev": true, "dependencies": {"globby": "^5.0.0", "is-path-cwd": "^1.0.0", "is-path-in-cwd": "^1.0.0", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "rimraf": "^2.2.8"}, "engines": {"node": ">=0.10.0"}}, "node_modules/del/node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/depd/-/depd-1.0.1.tgz", "integrity": "sha1-gK7GTJ1tl+ZcwqnKqTwKpqv3Oqo=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/deprecated": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/deprecated/-/deprecated-0.0.1.tgz", "integrity": "sha1-+cmvVGSvoeepcUWKi97yqpTVuxk=", "dev": true, "engines": {"node": ">= 0.9"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "dev": true}, "node_modules/detect-file": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/detect-file/-/detect-file-0.1.0.tgz", "integrity": "sha1-STXe39lIhkjgBrASlWbpOGcR6mM=", "dev": true, "dependencies": {"fs-exists-sync": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/detect-newline": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/detect-newline/-/detect-newline-2.1.0.tgz", "integrity": "sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/di": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/di/-/di-0.0.1.tgz", "integrity": "sha1-gGZJMmzqp8qjMG112YXqJ0i6kTw=", "dev": true}, "node_modules/doctrine": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-1.5.0.tgz", "integrity": "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=", "dev": true, "dependencies": {"esutils": "^2.0.2", "isarray": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/doctrine/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/dom-serializer": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.1.0.tgz", "integrity": "sha1-BzxpdUbOB4DOI75KKOKT5AvDDII=", "dev": true, "dependencies": {"domelementtype": "~1.1.1", "entities": "~1.1.1"}}, "node_modules/dom-serializer/node_modules/domelementtype": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.3.tgz", "integrity": "sha1-vSh3PiZCiBrsUVRJJCmcXNgiGFs=", "dev": true}, "node_modules/dom-serializer/node_modules/entities": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/entities/-/entities-1.1.1.tgz", "integrity": "sha1-blwtClYhtdra7O+AuQ7ftc13cvA=", "dev": true}, "node_modules/domelementtype": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.0.tgz", "integrity": "sha1-sXrtguirWeUt2cGbF1bg/BhyBMI=", "deprecated": "update to domelementtype@1.3.1", "dev": true}, "node_modules/domhandler": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-2.3.0.tgz", "integrity": "sha1-LeWaCCLVAn+r/28DLCsloqir5zg=", "dev": true, "dependencies": {"domelementtype": "1"}}, "node_modules/domutils": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz", "integrity": "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=", "dev": true, "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/duplexer2": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/duplexer2/-/duplexer2-0.0.2.tgz", "integrity": "sha1-xhTc9n4vsUmVqRcR5aYX6KYKMds=", "dev": true, "dependencies": {"readable-stream": "~1.1.9"}}, "node_modules/ecc-jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz", "integrity": "sha1-D8c6ntXw1Tw4GTOYUj735UN3dQU=", "dev": true, "optional": true, "dependencies": {"jsbn": "~0.1.0"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "dev": true}, "node_modules/end-of-stream": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-0.1.5.tgz", "integrity": "sha1-jhdyBsPICDfYVjLouTWd/osvbq8=", "dev": true, "dependencies": {"once": "~1.3.0"}}, "node_modules/entities": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/entities/-/entities-1.0.0.tgz", "integrity": "sha1-sph6o4ITR/zeZCsk/fyeT7cSvyY=", "dev": true}, "node_modules/errno": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/errno/-/errno-0.1.4.tgz", "integrity": "sha1-uJbiOp5ei6M4cfyZar02NfyaHH0=", "dev": true, "optional": true, "dependencies": {"prr": "~0.0.0"}, "bin": {"errno": "cli.js"}}, "node_modules/error-ex": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.1.tgz", "integrity": "sha1-+FWobOYa3E6GIcPNoh56dhLDqNw=", "dev": true, "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/errorhandler": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/errorhandler/-/errorhandler-1.4.3.tgz", "integrity": "sha1-t7cO2PNZ6duICS8tIMD4MUIK2D8=", "dev": true, "dependencies": {"accepts": "~1.3.0", "escape-html": "~1.0.3"}, "engines": {"node": ">= 0.8"}}, "node_modules/errorhandler/node_modules/accepts": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.4.tgz", "integrity": "sha1-hiRnWMfdbSGmR0/whKR0DsBesh8=", "dev": true, "dependencies": {"mime-types": "~2.1.16", "negotiator": "0.6.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/errorhandler/node_modules/negotiator": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.1.tgz", "integrity": "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/es5-ext": {"version": "0.10.37", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.10.37.tgz", "integrity": "sha1-DudB0Ui4AGm6J9AgOTdWryV978M=", "dev": true, "dependencies": {"es6-iterator": "~2.0.1", "es6-symbol": "~3.1.1"}}, "node_modules/es6-iterator": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/es6-iterator/-/es6-iterator-2.0.3.tgz", "integrity": "sha1-p96IkUGgWpSwhUQDstCg+/qY87c=", "dev": true, "dependencies": {"d": "1", "es5-ext": "^0.10.35", "es6-symbol": "^3.1.1"}}, "node_modules/es6-map": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/es6-map/-/es6-map-0.1.5.tgz", "integrity": "sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14", "es6-iterator": "~2.0.1", "es6-set": "~0.1.5", "es6-symbol": "~3.1.1", "event-emitter": "~0.3.5"}}, "node_modules/es6-set": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/es6-set/-/es6-set-0.1.5.tgz", "integrity": "sha1-0rPsXU2ADO2BjbU40ol02wpzzLE=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14", "es6-iterator": "~2.0.1", "es6-symbol": "3.1.1", "event-emitter": "~0.3.5"}}, "node_modules/es6-symbol": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/es6-symbol/-/es6-symbol-3.1.1.tgz", "integrity": "sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/es6-weak-map": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/es6-weak-map/-/es6-weak-map-2.0.2.tgz", "integrity": "sha1-XjqzIlH/0VOKH45f+hNXdy+S2W8=", "dev": true, "dependencies": {"d": "1", "es5-ext": "^0.10.14", "es6-iterator": "^2.0.1", "es6-symbol": "^3.1.1"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "dev": true}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/escodegen": {"version": "1.7.1", "resolved": "https://registry.npmjs.org/escodegen/-/escodegen-1.7.1.tgz", "integrity": "sha1-MOz89mypjcZ80v0WKr626vqM5vw=", "dev": true, "dependencies": {"esprima": "^1.2.2", "estraverse": "^1.9.1", "esutils": "^2.0.2", "optionator": "^0.5.0"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=0.12.0"}, "optionalDependencies": {"source-map": "~0.2.0"}}, "node_modules/escodegen/node_modules/source-map": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.2.0.tgz", "integrity": "sha1-2rc/vPwrqBm03gO9b26qSBZLP50=", "dev": true, "optional": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/escomplex-plugin-metrics-module": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/escomplex-plugin-metrics-module/-/escomplex-plugin-metrics-module-0.0.10.tgz", "integrity": "sha1-6pZ8sSwSOCDSTnrATkJ4oZvPzoM=", "dev": true, "dependencies": {"typhonjs-escomplex-commons": "^0.0.14"}}, "node_modules/escomplex-plugin-metrics-project": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/escomplex-plugin-metrics-project/-/escomplex-plugin-metrics-project-0.0.10.tgz", "integrity": "sha1-Z6Y1wctV4vO+y3dO/mpANOYjiqg=", "dev": true, "dependencies": {"typhonjs-escomplex-commons": "^0.0.14"}}, "node_modules/escomplex-plugin-syntax-babylon": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/escomplex-plugin-syntax-babylon/-/escomplex-plugin-syntax-babylon-0.0.10.tgz", "integrity": "sha1-sUcBSYHP57yKK0NJfmsyiRqD5yA=", "dev": true, "dependencies": {"escomplex-plugin-syntax-estree": "^0.0.10", "typhonjs-escomplex-commons": "^0.0.14"}}, "node_modules/escomplex-plugin-syntax-estree": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/escomplex-plugin-syntax-estree/-/escomplex-plugin-syntax-estree-0.0.10.tgz", "integrity": "sha1-b1MfnZM/vB68lDjpwQGxtGNs/Gg=", "dev": true, "dependencies": {"typhonjs-escomplex-commons": "^0.0.14"}}, "node_modules/escope": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/escope/-/escope-3.6.0.tgz", "integrity": "sha1-4Bl16BJ4GhY6ba392AOY3GTIicM=", "dev": true, "dependencies": {"es6-map": "^0.1.3", "es6-weak-map": "^2.0.1", "esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=0.4.0"}}, "node_modules/escope/node_modules/estraverse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/eslint/-/eslint-3.0.1.tgz", "integrity": "sha1-/xLq/cBOpx0XOgmdRlihNucVeTQ=", "deprecated": "This version is no longer supported. Please see https://eslint.org/version-support for other options.", "dev": true, "dependencies": {"chalk": "^1.1.3", "concat-stream": "^1.4.6", "debug": "^2.1.1", "doctrine": "^1.2.2", "es6-map": "^0.1.3", "escope": "^3.6.0", "espree": "^3.1.6", "estraverse": "^4.2.0", "esutils": "^2.0.2", "file-entry-cache": "^1.1.1", "glob": "^7.0.3", "globals": "^9.2.0", "ignore": "^3.1.2", "imurmurhash": "^0.1.4", "inquirer": "^0.12.0", "is-my-json-valid": "^2.10.0", "is-resolvable": "^1.0.0", "js-yaml": "^3.5.1", "json-stable-stringify": "^1.0.0", "levn": "^0.3.0", "lodash": "^4.0.0", "mkdirp": "^0.5.0", "optionator": "^0.8.1", "path-is-inside": "^1.0.1", "pluralize": "^1.2.1", "progress": "^1.1.8", "require-uncached": "^1.0.2", "shelljs": "^0.6.0", "strip-bom": "^3.0.0", "strip-json-comments": "~1.0.1", "table": "^3.7.8", "text-table": "~0.2.0", "user-home": "^2.0.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": ">=4"}}, "node_modules/eslint/node_modules/estraverse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint/node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "node_modules/eslint/node_modules/glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/eslint/node_modules/levn": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/eslint/node_modules/lodash": {"version": "4.17.4", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz", "integrity": "sha1-eCA6TRwyiuHYbcpkYONptX9AVa4=", "dev": true}, "node_modules/eslint/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/eslint/node_modules/optionator": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "integrity": "sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=", "dev": true, "dependencies": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.4", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "wordwrap": "~1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/eslint/node_modules/shelljs": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.6.1.tgz", "integrity": "sha1-7GIRvtGSBEIIj+D3Cyg3Iy7SyKg=", "dev": true, "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint/node_modules/strip-bom": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/eslint/node_modules/user-home": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/user-home/-/user-home-2.0.0.tgz", "integrity": "sha1-nHC/2Babwdy/SGBODwS4tJzenp8=", "dev": true, "dependencies": {"os-homedir": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint/node_modules/wordwrap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=", "dev": true}, "node_modules/espree": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/espree/-/espree-3.5.2.tgz", "integrity": "sha512-sadKeYwaR/aJ3stC2CdvgXu1T16TdYN+qwCpcWbMnGJ8s0zNWemzrvb2GbD4OhmJ/fwpJjudThAlLobGbWZbCQ==", "dev": true, "dependencies": {"acorn": "^5.2.1", "acorn-jsx": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/espree/node_modules/acorn": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/acorn/-/acorn-5.2.1.tgz", "integrity": "sha512-jG0u7c4Ly+3QkkW18V+NRDN+4bWHdln30NL1ZL2AvFZZmQe/BfopYCtghCKKVBUSetZ4QKcyA0pY6/4Gw8Pv8w==", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/esprima": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/esprima/-/esprima-1.2.5.tgz", "integrity": "sha1-CZNQL+r2aBODJXVvMPmlH+7sEek=", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.4.0"}}, "node_modules/esprima-harmony-jscs": {"version": "1.1.0-bin", "resolved": "https://registry.npmjs.org/esprima-harmony-jscs/-/esprima-harmony-jscs-1.1.0-bin.tgz", "integrity": "sha1-B8sFcdlD7tS8e/6ecmN8Zj/hUe0=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/esrecurse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.0.tgz", "integrity": "sha1-+pVo2Y04I/mkHZHpAtyrnqblsWM=", "dev": true, "dependencies": {"estraverse": "^4.1.0", "object-assign": "^4.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/esrecurse/node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/estraverse": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-1.9.3.tgz", "integrity": "sha1-r2fy3JIlgkFZUJJgkaQAXSnJu0Q=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/esutils": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/etag/-/etag-1.7.0.tgz", "integrity": "sha1-A9MLX2fdbmMtKUXTDWZScxo01dg=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/event-emitter": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/event-emitter/-/event-emitter-0.3.5.tgz", "integrity": "sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=", "dev": true, "dependencies": {"d": "1", "es5-ext": "~0.10.14"}}, "node_modules/exit": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/exit-hook": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/exit-hook/-/exit-hook-1.1.1.tgz", "integrity": "sha1-8FyiM7SMBdVP/wd2XfhQfpXAL/g=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-0.1.5.tgz", "integrity": "sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=", "dev": true, "dependencies": {"is-posix-bracket": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-range": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/expand-range/-/expand-range-1.8.2.tgz", "integrity": "sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=", "dev": true, "dependencies": {"fill-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-tilde": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-1.2.2.tgz", "integrity": "sha1-C4HrqJflo9MdHD0QL48BRB5VlEk=", "dev": true, "dependencies": {"os-homedir": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/express-session": {"version": "1.11.3", "resolved": "https://registry.npmjs.org/express-session/-/express-session-1.11.3.tgz", "integrity": "sha1-XMmPP1/4Ttg1+Ry/CqvQxxB0AK8=", "dev": true, "dependencies": {"cookie": "0.1.3", "cookie-signature": "1.0.6", "crc": "3.3.0", "debug": "~2.2.0", "depd": "~1.0.1", "on-headers": "~1.0.0", "parseurl": "~1.3.0", "uid-safe": "~2.0.0", "utils-merge": "1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/express-session/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/express-session/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/express-session/node_modules/uid-safe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/uid-safe/-/uid-safe-2.0.0.tgz", "integrity": "sha1-p/PGymSh9qXQTsDvPkw9U2cxcTc=", "dev": true, "dependencies": {"base64-url": "1.2.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/extend": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.1.tgz", "integrity": "sha1-p1Xqe8Gt/MWjHOfnYtuq3F5jZEQ=", "dev": true}, "node_modules/extglob": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/extglob/-/extglob-0.3.2.tgz", "integrity": "sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=", "dev": true, "dependencies": {"is-extglob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extract-zip": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.5.0.tgz", "integrity": "sha1-ksz22B73Cp+kwXRxFMzvbYaIpsQ=", "dev": true, "dependencies": {"concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "bin": {"extract-zip": "cli.js"}}, "node_modules/extract-zip/node_modules/debug": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/debug/-/debug-0.7.4.tgz", "integrity": "sha1-BuHqgILCyxTjmAbiLi9vdX+Srzk=", "dev": true, "engines": {"node": "*"}}, "node_modules/extract-zip/node_modules/minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "node_modules/extract-zip/node_modules/mkdirp": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.0.tgz", "integrity": "sha1-HXMHam35hs2TROFecfzAWkyavxI=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "dev": true, "engines": ["node >=0.6.0"]}, "node_modules/eyes": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/eyes/-/eyes-0.1.8.tgz", "integrity": "sha1-Ys8SAjTGg3hdkCNIqADvPgzCC8A=", "dev": true, "engines": {"node": "> 0.1.90"}}, "node_modules/fancy-log": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/fancy-log/-/fancy-log-1.3.0.tgz", "integrity": "sha1-Rb4X0Cu5kX1gzP/UmVyZnmyMmUg=", "dev": true, "dependencies": {"chalk": "^1.1.1", "time-stamp": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/fast-levenshtein": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.7.tgz", "integrity": "sha1-AXjc3uAjuSkFGTrwlZ6KdjnP3Lk=", "dev": true}, "node_modules/fd-slicer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.1.tgz", "integrity": "sha1-i1vL2ewyfFBBv5qwI/1nUPEXfmU=", "dev": true, "dependencies": {"pend": "~1.2.0"}}, "node_modules/figures": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/figures/-/figures-1.7.0.tgz", "integrity": "sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=", "dev": true, "dependencies": {"escape-string-regexp": "^1.0.5", "object-assign": "^4.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/figures/node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/file-entry-cache": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-1.3.1.tgz", "integrity": "sha1-RMYepgeuS+nBQC9B9EJwy/4zT/g=", "dev": true, "dependencies": {"flat-cache": "^1.2.1", "object-assign": "^4.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/file-entry-cache/node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/filename-regex": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/filename-regex/-/filename-regex-2.0.1.tgz", "integrity": "sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fileset": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/fileset/-/fileset-0.1.8.tgz", "integrity": "sha1-UGuRqTluqn4y+0KoQHfHoMc2t0E=", "dev": true, "dependencies": {"glob": "3.x", "minimatch": "0.x"}}, "node_modules/fileset/node_modules/glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz", "integrity": "sha1-Spc/Y1uRkPcV0QmH1cAP0oFevj0=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inherits": "2", "minimatch": "0.3"}, "engines": {"node": "*"}}, "node_modules/fileset/node_modules/glob/node_modules/minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha1-J12O2qxPG7MyZHIInnlJyDlGmd0=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/fileset/node_modules/minimatch": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.4.0.tgz", "integrity": "sha1-vSx9Bg0sjI/Xzefx8u0tWycP2xs=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/filesize": {"version": "3.1.6", "resolved": "https://registry.npmjs.org/filesize/-/filesize-3.1.6.tgz", "integrity": "sha1-WISSTvyBpkTjcJqsQDIWGDw9eYo=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/fill-range": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-2.2.3.tgz", "integrity": "sha1-ULd9/X5Gm8dJJHCWNpn+eoSFpyM=", "dev": true, "dependencies": {"is-number": "^2.1.0", "isobject": "^2.0.0", "randomatic": "^1.1.3", "repeat-element": "^1.1.2", "repeat-string": "^1.5.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/finalhandler": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.4.0.tgz", "integrity": "sha1-llpS2ejQXSuFdUhUH7ibU6JJfZs=", "dev": true, "dependencies": {"debug": "~2.2.0", "escape-html": "1.0.2", "on-finished": "~2.3.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/finalhandler/node_modules/escape-html": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.2.tgz", "integrity": "sha1-130y+pjjjC9BroXpJ44ODmuhAiw=", "dev": true}, "node_modules/finalhandler/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/find-index": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/find-index/-/find-index-0.1.1.tgz", "integrity": "sha1-Z101iyyjiS15Whq0cjL4tuLg3eQ=", "dev": true}, "node_modules/find-up": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz", "integrity": "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=", "dev": true, "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/findup-sync": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-0.4.3.tgz", "integrity": "sha1-QAQ5Kee8YK3wt/SCfExudaDeyhI=", "dev": true, "dependencies": {"detect-file": "^0.1.0", "is-glob": "^2.0.1", "micromatch": "^2.3.7", "resolve-dir": "^0.1.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/fined": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fined/-/fined-1.1.0.tgz", "integrity": "sha1-s33IRLdqL15wgeiE98CuNE8VNHY=", "dev": true, "dependencies": {"expand-tilde": "^2.0.2", "is-plain-object": "^2.0.3", "object.defaults": "^1.1.0", "object.pick": "^1.2.0", "parse-filepath": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/fined/node_modules/expand-tilde": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=", "dev": true, "dependencies": {"homedir-polyfill": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/first-chunk-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/first-chunk-stream/-/first-chunk-stream-1.0.0.tgz", "integrity": "sha1-Wb+1DNkF9g18OUzT2ayqtOatk04=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/flagged-respawn": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/flagged-respawn/-/flagged-respawn-0.3.2.tgz", "integrity": "sha1-/xke3c1wiKZ1smEP/8l2vpuAdLU=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/flat-cache": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-1.3.0.tgz", "integrity": "sha1-0wMLMrOBVPTjt+nHCfSQ9++XxIE=", "dev": true, "dependencies": {"circular-json": "^0.3.1", "del": "^2.0.2", "graceful-fs": "^4.1.2", "write": "^0.2.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/flat-cache/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/for-in": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/for-own": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/for-own/-/for-own-0.1.5.tgz", "integrity": "sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=", "dev": true, "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "dev": true, "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.1.4.tgz", "integrity": "sha1-M8GDrPGTJ27KqYFDpp6Uv+4XUNE=", "dev": true, "optional": true, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/fresh": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.3.0.tgz", "integrity": "sha1-ZR+DjiJCTnVm3hYdg1jKoZn4PU8=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/fs-exists-sync": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/fs-exists-sync/-/fs-exists-sync-0.1.0.tgz", "integrity": "sha1-mC1ok6+RjnLQjeyehnP/K1qNat0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fs-extra": {"version": "0.26.7", "resolved": "http://registry.npmjs.org/fs-extra/-/fs-extra-0.26.7.tgz", "integrity": "sha1-muH92UiXeY7at20JGM9C0MMYT6k=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^2.1.0", "klaw": "^1.0.0", "path-is-absolute": "^1.0.0", "rimraf": "^2.2.8"}}, "node_modules/fs-extra/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "node_modules/fsevents": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-1.1.3.tgz", "integrity": "sha512-WIr7iDkdmdbxu/Gh6eKEZJL6KPE74/5MEsf2whTOFNxbIoIixogroLdKYqB6FDav4Wavh/lZdzzd3b2KxIXC5Q==", "bundleDependencies": ["node-pre-gyp"], "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues", "dev": true, "hasInstallScript": true, "optional": true, "os": ["darwin"], "dependencies": {"nan": "^2.3.0", "node-pre-gyp": "^0.6.39"}, "engines": {"node": ">=0.8.0"}}, "node_modules/fsevents/node_modules/abbrev": {"version": "1.1.0", "integrity": "sha512-c92Vmq5hfBgXyoUaHqF8P5+7THGjvxAlB64tm3PiFSAcDww34ndmrlSOd3AUaBZoutDwX0dHz9nUUFoD1jEw0Q==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/ajv": {"version": "4.11.8", "integrity": "sha512-I/bSHSNEcFFqXLf91nchoNB9D1Kie3QKcWdchYUaoIg1+1bdWDkdfdlvdIOJbi9U8xR0y+MWc5D+won9v95WlQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"co": "^4.6.0", "json-stable-stringify": "^1.0.1"}}, "node_modules/fsevents/node_modules/ansi-regex": {"version": "2.1.1", "integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/aproba": {"version": "1.1.1", "integrity": "sha512-wddRlyVZ7n0ZClzsf0Aqf5vGke7/X8QT0GElKYr8qAuE80tlqbvKf4hlrDE0/zqI8Z6j4HjIIaB9gZ484kjjsw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/are-we-there-yet": {"version": "1.1.4", "integrity": "sha512-QbMPI8teYlZBIBqDgmIWfDKO149dGtQV2ium8WniCaARFFRd1e+IES1LA4sSGcVTFdVL628+163WUbxev7R/aQ==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/fsevents/node_modules/asn1": {"version": "0.2.3", "integrity": "sha512-6i37w/+EhlWlGUJff3T/Q8u1RGmP5wgbiwYnOnbOqvtrPxT63/sYFyP9RcpxtxGymtfA075IvmOnL7ycNOWl3w==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/assert-plus": {"version": "0.2.0", "integrity": "sha512-u1L0ZLywRziOVjUhRxI0Qg9G+4RnFB9H/Rq40YWn0dieDgO7vAYeJz6jKAO6t/aruzlDFLAPkQTT87e+f8Imaw==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/fsevents/node_modules/asynckit": {"version": "0.4.0", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/aws-sign2": {"version": "0.6.0", "integrity": "sha512-JnJpAS0p9RmixkOvW2XwDxxzs1bd4/VAGIl6Q0EC5YOo+p+hqIhtDhn/nmFnB/xUNXbLkpE2mOjgVIBRKD4xYw==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/aws4": {"version": "1.6.0", "integrity": "sha512-tkleq4Df8UWu/7xf/tfbo7t2vDa07bcONGnKhl0QXKQsh3fJ0yJ1M5wzpy8BtBSENQw/9VTsthMhLG+yXHfStQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/balanced-match": {"version": "0.4.2", "integrity": "sha512-STw03mQKnGUYtoNjmowo4F2cRmIIxYEGiMsjjwla/u5P1lxadj/05WkNaFjNiKTgJkj8KiXbgAiRTmcQRwQNtg==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/bcrypt-pbkdf": {"version": "1.0.1", "integrity": "sha512-vY4sOrSlpwNZXsinfJ0HpbSkFft4nhSVLeUrQ4j2ydGmBOiVY83aMJStJATBy0C3+XdaYa990kIA1qkC2mUq6g==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/fsevents/node_modules/block-stream": {"version": "0.0.9", "integrity": "sha512-OorbnJVPII4DuUKbjARAe8u8EfqOmkEEaSFIyoQ7OjTHn6kafxWl0wLgoZ2rXaYd7MyLcDaU4TmhfxtwgcccMQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"inherits": "~2.0.0"}, "engines": {"node": "0.4 || >=0.5.8"}}, "node_modules/fsevents/node_modules/boom": {"version": "2.10.1", "integrity": "sha512-KbiZEa9/vofNcVJXGwdWWn25reQ3V3dHBWbS07FTF3/TOehLnm9GEhJV4T6ZvGPkShRpmUqYwnaCrkj0mRnP6Q==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "inBundle": true, "optional": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/fsevents/node_modules/brace-expansion": {"version": "1.1.7", "integrity": "sha512-ebXXDR1wKKxJNfTM872trAU5hpKduCkTN37ipoxsh5yibWq8FfxiobiHuVlPFkspSSNhrxbPHbM4kGyDGdJ5mg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}}, "node_modules/fsevents/node_modules/buffer-shims": {"version": "1.0.0", "integrity": "sha512-Zy8ZXMyxIT6RMTeY7OP/bDndfj6bwCan7SS98CEndS6deHwWPpseeHlwarNcBim+etXnF9HBc1non5JgDaJU1g==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/caseless": {"version": "0.12.0", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/co": {"version": "4.6.0", "integrity": "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/fsevents/node_modules/code-point-at": {"version": "1.1.0", "integrity": "sha512-RpAVKQA5T63xEj6/giIbUEtZwJ4UFIc3ZtvEkiaUERylqe8xb5IvqcgOurZLahv93CLKfxcw5YI+DZcUBRyLXA==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/combined-stream": {"version": "1.0.5", "integrity": "sha512-JgSRe4l4UzPwpJuxfcPWEK1SCrL4dxNjp1uqrQLMop3QZUVo+hDU8w9BJKA4JPbulTWI+UzrI2UA3tK12yQ6bg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/fsevents/node_modules/concat-map": {"version": "0.0.1", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/console-control-strings": {"version": "1.1.0", "integrity": "sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/core-util-is": {"version": "1.0.2", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/cryptiles": {"version": "2.0.5", "integrity": "sha512-FFN5KwpvvQTTS5hWPxrU8/QE4kQUc6uwZcrnlMBN82t1MgAtq8mnoDwINBly9Tdr02seeIIhtdF+UH1feBYGog==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "inBundle": true, "optional": true, "dependencies": {"boom": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/fsevents/node_modules/dashdash": {"version": "1.14.1", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/fsevents/node_modules/dashdash/node_modules/assert-plus": {"version": "1.0.0", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/fsevents/node_modules/debug": {"version": "2.6.8", "integrity": "sha512-E22fsyWPt/lr4/UgQLt/pXqerGMDsanhbnmqIS3VAXuDi1v3IpiwXe2oncEIondHSBuPDWRoK/pMjlvi8FuOXQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/fsevents/node_modules/deep-extend": {"version": "0.4.2", "integrity": "sha512-cQ0iXSEKi3JRNhjUsLWvQ+MVPxLVqpwCd0cFsWbJxlCim2TlCo1JvN5WaPdPvSpUdEnkJ/X+mPGcq5RJ68EK8g==", "dev": true, "inBundle": true, "optional": true, "engines": {"iojs": ">=1.0.0", "node": ">=0.12.0"}}, "node_modules/fsevents/node_modules/delayed-stream": {"version": "1.0.0", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.4.0"}}, "node_modules/fsevents/node_modules/delegates": {"version": "1.0.0", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/detect-libc": {"version": "1.0.2", "integrity": "sha512-YexetqP2dQZlFZoGoE/Ab7ZWxIhExaRwEhluPEqegGJzKIVvVtVinvILxAh/WrzDoNIZ19XU3E+J0tEXPE5MAw==", "dev": true, "inBundle": true, "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/fsevents/node_modules/ecc-jsbn": {"version": "0.1.1", "integrity": "sha512-8Pvg9QY16SYajEL9W1Lk+9yM7XCK/MOq2wibslLZYAAEEkbAIO6mLkW+GFYbvvw8qTuDFzFMg40rS9IxkNCWPg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"jsbn": "~0.1.0"}}, "node_modules/fsevents/node_modules/extend": {"version": "3.0.1", "integrity": "sha512-u1aUSYGdAQxyguoP919qsgj24krDCtaO/DJFNPwFmojMmKp14gtCTVsc8lQSqRDFrwAch+mxMWC8/6ZJPz5Hpw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/extsprintf": {"version": "1.0.2", "integrity": "sha512-g21Br4ELmVaKCVSUSSTXecKG+MiLcHFoby5RPPUmfZdhQTontXUOPf0QK/TvreRjgItRiyO928zxR4TCrnuwmA==", "dev": true, "engines": ["node >=0.6.0"], "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/forever-agent": {"version": "0.6.1", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/form-data": {"version": "2.1.4", "integrity": "sha512-8HWGSLAPr+AG0hBpsqi5Ob8HrLStN/LWeqhpFl14d7FJgHK48TmgLoALPz69XSUR65YJzDfLUX/BM8+MLJLghQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.5", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/fsevents/node_modules/fs.realpath": {"version": "1.0.0", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/fstream": {"version": "1.0.11", "integrity": "sha512-2Xg8XA70uvyriqd1J6T/6V74WeQWHCn65hTSAWcZgKCOCo7nN6hoqgFrYPLJ9Yzbryrp/VIruHXsDyZIqZ1SvQ==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}, "engines": {"node": ">=0.6"}}, "node_modules/fsevents/node_modules/fstream-ignore": {"version": "1.0.5", "integrity": "sha512-VVRuOs41VUqptEGiR0N5ZoWEcfGvbGRqLINyZAhHRnF3DH5wrqjNkYr3VbRoZnI41BZgO7zIVdiobc13TVI1ow==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"fstream": "^1.0.0", "inherits": "2", "minimatch": "^3.0.0"}}, "node_modules/fsevents/node_modules/gauge": {"version": "2.7.4", "integrity": "sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/fsevents/node_modules/getpass": {"version": "0.1.7", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/fsevents/node_modules/getpass/node_modules/assert-plus": {"version": "1.0.0", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/fsevents/node_modules/glob": {"version": "7.1.2", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "inBundle": true, "optional": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/graceful-fs": {"version": "4.1.11", "integrity": "sha512-9x6DLUuW+ROFdMTII9ec9t/FK8va6kYcC8/LggumssLM8kNv7IdFl3VrNUqgir2tJuBVxBga1QBoRziZacO5Zg==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.4.0"}}, "node_modules/fsevents/node_modules/har-schema": {"version": "1.0.5", "integrity": "sha512-f8xf2GOR6Rgwc9FPTLNzgwB+JQ2/zMauYXSWmX5YV5acex6VomT0ocSuwR7BfXo5MpHi+jL+saaux2fwsGJDKQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=4"}}, "node_modules/fsevents/node_modules/har-validator": {"version": "4.2.1", "integrity": "sha512-5Gbp6RAftMYYV3UEI4c4Vv3+a4dQ7taVyvHt+/L6kRt+f4HX1GweAk5UDWN0SvdVnRBzGQ6OG89pGaD9uSFnVw==", "deprecated": "this library is no longer supported", "dev": true, "inBundle": true, "optional": true, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "engines": {"node": ">=4"}}, "node_modules/fsevents/node_modules/has-unicode": {"version": "2.0.1", "integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/hawk": {"version": "3.1.3", "integrity": "sha512-X8xbmTc1cbPXcQV4WkLcRMALuyoxhfpFATmyuCxJPOAvrDS4DNnsTAOmKUxMTOWU6TzrTOkxPKwIx5ZOpJVSrg==", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"boom": "2.x.x", "cryptiles": "2.x.x", "hoek": "2.x.x", "sntp": "1.x.x"}, "engines": {"node": ">=0.10.32"}}, "node_modules/fsevents/node_modules/hoek": {"version": "2.16.3", "integrity": "sha512-V6Yw1rIcYV/4JsnggjBU0l4Kr+EXhpwqXRusENU1Xx6ro00IHPHYNynCuBTOZAPlr3AAmLvchH9I7N/VUdvOwQ==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.40"}}, "node_modules/fsevents/node_modules/http-signature": {"version": "1.1.1", "integrity": "sha512-iUn0NcRULlDGtqNLN1Jxmzayk8ogm7NToldASyZBpM2qggbphjXzNOiw3piN8tgz+e/DRs6X5gAzFwTI6BCRcg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"assert-plus": "^0.2.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/fsevents/node_modules/inflight": {"version": "1.0.6", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/fsevents/node_modules/inherits": {"version": "2.0.3", "integrity": "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/ini": {"version": "1.3.4", "integrity": "sha512-VUA7WAWNCWfm6/8f9kAb8Y6iGBWnmCfgFS5dTrv2C38LLm1KUmpY388mCVCJCsMKQomvOQ1oW8/edXdChd9ZXQ==", "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "integrity": "sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/is-typedarray": {"version": "1.0.0", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/isarray": {"version": "1.0.0", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/isstream": {"version": "0.1.2", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/jodid25519": {"version": "1.0.2", "integrity": "sha512-b2Zna/wGIyTzi0Gemg27JYUaRyTyBETw5GnqyVQMr71uojOYMrgkD2+Px3bG2ZFi7/zTUXJSDoGoBOhMixq7tg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"jsbn": "~0.1.0"}}, "node_modules/fsevents/node_modules/jsbn": {"version": "0.1.1", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/json-schema": {"version": "0.2.3", "integrity": "sha512-a3xHnILGMtk+hDOqNwHzF6e2fNbiMrXZvxKQiEv2MlgQP+pjIOzqAmKYD2mDpXYE/44M7g+n9p2bKkYWDUcXCQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/json-stable-stringify": {"version": "1.0.1", "integrity": "sha512-i/J297TW6xyj7sDFa7AmBPkQvLIxWr2kKPWI26tXydnZrzVAocNqn5DMNT1Mzk0vit1V5UkRM7C1KdVNp7Lmcg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"jsonify": "~0.0.0"}}, "node_modules/fsevents/node_modules/json-stringify-safe": {"version": "5.0.1", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/jsonify": {"version": "0.0.0", "integrity": "sha512-trvBk1ki43VZptdBI5rIlG4YOzyeH/WefQt5rj1grasPn4iiZWKet8nkgc4GlsAylaztn0qZfUYOiTsASJFdNA==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/jsprim": {"version": "1.4.0", "integrity": "sha512-OyKQuabgqUi2RUPauBrfZNoCb0KNoulf1DqQ07rUW2vzauzXAq/uUe7oDstV/2RavaxGn7NfcI/F2hrBk38Fbg==", "dev": true, "engines": ["node >=0.6.0"], "inBundle": true, "optional": true, "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.0.2", "json-schema": "0.2.3", "verror": "1.3.6"}}, "node_modules/fsevents/node_modules/jsprim/node_modules/assert-plus": {"version": "1.0.0", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/fsevents/node_modules/mime-db": {"version": "1.27.0", "integrity": "sha512-DNhC90PjVkQJpLVP+ct0lmKPQWAHFy+67X8IBOx+mda/I9vsrdJO/zoyEJdQdLsofi/l8GAG+IsfB0XCPLyLHg==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">= 0.6"}}, "node_modules/fsevents/node_modules/mime-types": {"version": "2.1.15", "integrity": "sha512-PjleM8evsL+OvsuE6EXom+8QAcSYALjmw+vYFqH8I+/+wNlewVgbM7/O1wcdCVL/ta8SC6l6BEK7A0/mZywpfg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"mime-db": "~1.27.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/fsevents/node_modules/minimatch": {"version": "3.0.4", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/minimist": {"version": "0.0.8", "integrity": "sha512-miQKw5Hv4NS1Psg2517mV4e4dYNaO3++hjAvLOAzKqZ61rH8NS1SK+vbfBWZ5PY/Me/bEWhUwqMghEW5Fb9T7Q==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/mkdirp": {"version": "0.5.1", "integrity": "sha512-SknJC52obPfGQPnjIkXbmA6+5H15E+fR+E4iR2oQ3zzCLbd7/ONua69R/Gw7AgkTLsRG+r5fzksYwWe1AgTyWA==", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "inBundle": true, "optional": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/fsevents/node_modules/ms": {"version": "2.0.0", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/node-pre-gyp": {"version": "0.6.39", "integrity": "sha512-OsJV74qxnvz/AMGgcfZoDaeDXKD3oY3QVIbBmwszTFkRisTSXbMQyn4UWzUMOtA5SVhrBZOTp0wcoSBgfMfMmQ==", "deprecated": "Please upgrade to @mapbox/node-pre-gyp: the non-scoped node-pre-gyp package is deprecated and only the @mapbox scoped package will recieve updates in the future", "dev": true, "inBundle": true, "optional": true, "dependencies": {"detect-libc": "^1.0.2", "hawk": "3.1.3", "mkdirp": "^0.5.1", "nopt": "^4.0.1", "npmlog": "^4.0.2", "rc": "^1.1.7", "request": "2.81.0", "rimraf": "^2.6.1", "semver": "^5.3.0", "tar": "^2.2.1", "tar-pack": "^3.4.0"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}}, "node_modules/fsevents/node_modules/nopt": {"version": "4.0.1", "integrity": "sha512-+5XZFpQZEY0cg5JaxLwGxDlKNKYxuXwGt8/Oi3UXm5/4ymrJve9d2CURituxv3rSrVCGZj4m1U1JlHTdcKt2Ng==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/fsevents/node_modules/npmlog": {"version": "4.1.0", "integrity": "sha512-ocolIkZYZt8UveuiDS0yAkkIjid1o7lPG8cYm05yNYzBn8ykQtaiPMEGp8fY9tKdDgm8okpdKzkvu1y9hUYugA==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/fsevents/node_modules/number-is-nan": {"version": "1.0.1", "integrity": "sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/oauth-sign": {"version": "0.8.2", "integrity": "sha512-VlF07iu3VV3+BTXj43Nmp6Irt/G7j/NgEctUS6IweH1RGhURjjCc2NWtzXFPXXWWfc7hgbXQdtiQu2LGp6MxUg==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/object-assign": {"version": "4.1.1", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/once": {"version": "1.4.0", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"wrappy": "1"}}, "node_modules/fsevents/node_modules/os-homedir": {"version": "1.0.2", "integrity": "sha512-B5JU3cabzk8c67mRRd3ECmROafjYMXbuzlwtqdM8IbS8ktlTix8aFGb2bAGKrSRIlnfKwovGUUr72JUPyOb6kQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/os-tmpdir": {"version": "1.0.2", "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/osenv": {"version": "0.1.4", "integrity": "sha512-W6FhbLxEWdiyX2/fCl2YBZUJOYWaCHJa+jJwUVMX0iFYJmwyd0uzKx4NxFdj3xo9C0pumQ6G/fvd1MbNhsqQbQ==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/fsevents/node_modules/path-is-absolute": {"version": "1.0.1", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/performance-now": {"version": "0.2.0", "integrity": "sha512-YHk5ez1hmMR5LOkb9iJkLKqoBlL7WD5M8ljC75ZfzXriuBIVNuecaXuU7e+hOwyqf24Wxhh7Vxgt7Hnw9288Tg==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/process-nextick-args": {"version": "1.0.7", "integrity": "sha512-yN0WQmuCX63LP/TMvAg31nvT6m4vDqJEiiv2CAZqWOGNWutc9DfDk1NPYYmKUFmaVM2UwDowH4u5AHWYP/jxKw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/punycode": {"version": "1.4.1", "integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/qs": {"version": "6.4.0", "integrity": "sha512-Qs6dfgR5OksK/PSxl1kGxiZgEQe8RqJMB9wZqVlKQfU+zzV+HY77pWJnoJENACKDQByWdpr8ZPIh1TBi4lpiSQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.6"}}, "node_modules/fsevents/node_modules/rc": {"version": "1.2.1", "integrity": "sha512-5kLVpOvFh6zdjGL2+UmCXd/nonPuxsRjM0LktPM6CtpFYOrZSd9rF4tveeMtql3HU6AsAovgqR8k9HQOSfXLMQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "index.js"}}, "node_modules/fsevents/node_modules/rc/node_modules/minimist": {"version": "1.2.0", "integrity": "sha512-7Wl+Jz+IGWuSdgsQEJ4JunV0si/iMhg42MnQQG6h1R6TNeVenp4U9x5CC5v/gYqz/fENLQITAWXidNtVL0NNbw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/readable-stream": {"version": "2.2.9", "integrity": "sha512-iuxqX7b7FYt08AriYECxUsK9KTXE3A/FenxIa3IPmvANHxaTP/wGIwwf+IidvvIDk/MsCp/oEV6A8CXo4SDcCg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"buffer-shims": "~1.0.0", "core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1"}}, "node_modules/fsevents/node_modules/request": {"version": "2.81.0", "integrity": "sha512-IZnsR7voF0miGSu29EXPRgPTuEsI/+aibNSBbN1pplrfartF5wDYGADz3iD9vmBVf2r00rckWZf8BtS5kk7Niw==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "inBundle": true, "optional": true, "dependencies": {"aws-sign2": "~0.6.0", "aws4": "^1.2.1", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~2.1.1", "har-validator": "~4.2.1", "hawk": "~3.1.3", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "performance-now": "^0.2.0", "qs": "~6.4.0", "safe-buffer": "^5.0.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "^0.6.0", "uuid": "^3.0.0"}, "engines": {"node": ">= 4"}}, "node_modules/fsevents/node_modules/rimraf": {"version": "2.6.1", "integrity": "sha512-5QIcndZ8am2WyseL6lln/utl51SwRBQs/at+zi1UnhsnPyZcAID+g0PZrKdb+kJn2fo/CwgyJweR8sP36Jer5g==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "inBundle": true, "optional": true, "dependencies": {"glob": "^7.0.5"}, "bin": {"rimraf": "bin.js"}}, "node_modules/fsevents/node_modules/safe-buffer": {"version": "5.0.1", "integrity": "sha512-cr7dZWLwOeaFBLTIuZeYdkfO7UzGIKhjYENJFAxUOMKWGaWDm2nJM2rzxNRm5Owu0DH3ApwNo6kx5idXZfb/Iw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/semver": {"version": "5.3.0", "integrity": "sha512-mfmm3/H9+67MCVix1h+IXTpDwL6710LyHuk7+cWC9T1mE0qz4iHhh6r4hU2wrIT9iTsAAC2XQRvfblL028cpLw==", "dev": true, "inBundle": true, "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/fsevents/node_modules/set-blocking": {"version": "2.0.0", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/signal-exit": {"version": "3.0.2", "integrity": "sha512-meQNNykwecVxdu1RlYMKpQx4+wefIYpmxi6gexo/KAbwquJrBUrBmKYJrE8KFkVQAAVWEnwNdu21PgrD77J3xA==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/sntp": {"version": "1.0.9", "integrity": "sha512-7bgVOAnPj3XjrKY577S+puCKGCRlUrcrEdsMeRXlg9Ghf5df/xNi6sONUa43WrHUd3TjJBF7O04jYoiY0FVa0A==", "deprecated": "This module moved to @hapi/sntp. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/fsevents/node_modules/sshpk": {"version": "1.13.0", "integrity": "sha512-4pjoCmB+Vta6odJ3nrT2zf223+xW6mOJfYtHGX7n5CZUAVm6GhuHLbKeai3c0XoWCu6ZNyPo3Nf71CaUbgck2g==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "dashdash": "^1.12.0", "getpass": "^0.1.1"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}, "optionalDependencies": {"bcrypt-pbkdf": "^1.0.0", "ecc-jsbn": "~0.1.1", "jodid25519": "^1.0.0", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}}, "node_modules/fsevents/node_modules/sshpk/node_modules/assert-plus": {"version": "1.0.0", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/fsevents/node_modules/string_decoder": {"version": "1.0.1", "integrity": "sha512-Ma/XSGC8lfDvw75eLjgg/a1nWDButtedmpbbNxH5Ruyr0IhqNXOKbG468VtPosrjhRgNOvgonmY54ZnGMdgJjw==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/fsevents/node_modules/string-width": {"version": "1.0.2", "integrity": "sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/stringstream": {"version": "0.0.5", "integrity": "sha512-QUQ1kThMjLRt4jA8lsn9lyIkE9bKafE7LDOL/nBBUY9Tfv2i3x1NAsVHG0uMCusFOWeeI6COhY/F20+avxRWSw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/strip-ansi": {"version": "3.0.1", "integrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/strip-json-comments": {"version": "2.0.1", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/tar": {"version": "2.2.1", "integrity": "sha512-2Tw2uNtZqQTSHTIMbKHKFeAPmKcljrNKqKiIN7pu3V/CxYqRgS8DLXvMkFRrbtXlg6mTOQcuTX7DMj18Xi0dtg==", "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"block-stream": "*", "fstream": "^1.0.2", "inherits": "2"}}, "node_modules/fsevents/node_modules/tar-pack": {"version": "3.4.0", "integrity": "sha512-mggElLHZCDZK1J8rHZqdqJ09zY9F8aW7CgbhHjXZKCfgVU6TqUPhM6okl3z8bRWfOoJBBRKoIOrwPbahCHV64w==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"debug": "^2.2.0", "fstream": "^1.0.10", "fstream-ignore": "^1.0.5", "once": "^1.3.3", "readable-stream": "^2.1.4", "rimraf": "^2.5.1", "tar": "^2.2.1", "uid-number": "^0.0.6"}}, "node_modules/fsevents/node_modules/tough-cookie": {"version": "2.3.2", "integrity": "sha512-42UXjmzk88F7URyg9wDV/dlQ7hXtl/SDV6xIMVdDq82cnDGQDyg8mI8xGBPOwpEfbhvrja6cJ8H1wr0xxykBKA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"punycode": "^1.4.1"}, "engines": {"node": ">=0.8"}}, "node_modules/fsevents/node_modules/tunnel-agent": {"version": "0.6.0", "integrity": "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/tweetnacl": {"version": "0.14.5", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/uid-number": {"version": "0.0.6", "integrity": "sha512-c461FXIljswCuscZn67xq9PpszkPT6RjheWFQTgCyabJrTUozElanb0YEqv2UGgk247YpcJkFBuSGNvBlpXM9w==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/util-deprecate": {"version": "1.0.2", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/uuid": {"version": "3.0.1", "integrity": "sha512-tyhM7iisckwwmyHVFcjTzISz/R1ss/bRudNgHFYsgeu7j4JbhRvjE+Hbcpr9y5xh+b+HxeFjuToDT4i9kQNrtA==", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "dev": true, "inBundle": true, "optional": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/fsevents/node_modules/verror": {"version": "1.3.6", "integrity": "sha512-i8GFYwImt5D5B8CPpi2jrDTy/faq4OEW+NkOTLSKcIdPfdYJvWv3VZddDKl0ByvBe6cJ2s5Mm2XDtv5c2pj/Eg==", "dev": true, "engines": ["node >=0.6.0"], "inBundle": true, "optional": true, "dependencies": {"extsprintf": "1.0.2"}}, "node_modules/fsevents/node_modules/wide-align": {"version": "1.1.2", "integrity": "sha512-ijDLlyQ7s6x1JgCLur53osjm/UXUYD9+0PbYKrBsYisYXzCxN+HC3mYDNy/dWdmf3AwqwU3CXwDCvsNgGK1S0w==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"string-width": "^1.0.2"}}, "node_modules/fsevents/node_modules/wrappy": {"version": "1.0.2", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/gaze": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/gaze/-/gaze-0.5.2.tgz", "integrity": "sha1-QLcJU30k0dRXZ9takIaJ3+aaxE8=", "dev": true, "dependencies": {"globule": "~0.1.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/generate-function": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.0.0.tgz", "integrity": "sha1-aFj+fAlpt9TpCTM3ZHrHn2DfvnQ=", "dev": true}, "node_modules/generate-object-property": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/generate-object-property/-/generate-object-property-1.2.0.tgz", "integrity": "sha1-nA4cQDCM6AT0eDYYuTf6iPmdUNA=", "dev": true, "dependencies": {"is-property": "^1.0.0"}}, "node_modules/get-stdin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz", "integrity": "sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "dev": true, "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/getpass/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/glob": {"version": "4.5.3", "resolved": "https://registry.npmjs.org/glob/-/glob-4.5.3.tgz", "integrity": "sha1-xstz0yJsHv7wTePFbQEvAzd+4V8=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "^2.0.1", "once": "^1.3.0"}, "engines": {"node": "*"}}, "node_modules/glob-base": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/glob-base/-/glob-base-0.3.0.tgz", "integrity": "sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=", "dev": true, "dependencies": {"glob-parent": "^2.0.0", "is-glob": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-parent": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-2.0.0.tgz", "integrity": "sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=", "dev": true, "dependencies": {"is-glob": "^2.0.0"}}, "node_modules/glob-stream": {"version": "3.1.18", "resolved": "https://registry.npmjs.org/glob-stream/-/glob-stream-3.1.18.tgz", "integrity": "sha1-kXCl8St5Awb9/lmPMT+PeVT9FDs=", "dev": true, "dependencies": {"glob": "^4.3.1", "glob2base": "^0.0.12", "minimatch": "^2.0.1", "ordered-read-streams": "^0.1.0", "through2": "^0.6.1", "unique-stream": "^1.0.0"}, "engines": {"node": ">= 0.9"}}, "node_modules/glob-stream/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/glob-stream/node_modules/through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "dependencies": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}, "node_modules/glob-watcher": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/glob-watcher/-/glob-watcher-0.0.6.tgz", "integrity": "sha1-uVtKjfdLOcgymLDAXJeLTZo7cQs=", "dev": true, "dependencies": {"gaze": "^0.5.1"}, "engines": {"node": ">= 0.9"}}, "node_modules/glob2base": {"version": "0.0.12", "resolved": "https://registry.npmjs.org/glob2base/-/glob2base-0.0.12.tgz", "integrity": "sha1-nUGbPijxLoOjYhZKJ3BVkiycDVY=", "dev": true, "dependencies": {"find-index": "^0.1.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/global-modules": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/global-modules/-/global-modules-0.2.3.tgz", "integrity": "sha1-6lo77ULG1s6ZWk+KEmm12uIjgo0=", "dev": true, "dependencies": {"global-prefix": "^0.1.4", "is-windows": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/global-prefix": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/global-prefix/-/global-prefix-0.1.5.tgz", "integrity": "sha1-jTvGuNo8qBEqFg2NSW/wRiv+948=", "dev": true, "dependencies": {"homedir-polyfill": "^1.0.0", "ini": "^1.3.4", "is-windows": "^0.2.0", "which": "^1.2.12"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globals": {"version": "9.18.0", "resolved": "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz", "integrity": "sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/globby": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/globby/-/globby-5.0.0.tgz", "integrity": "sha1-69hGZ8oNuzMLmbz8aOrCvFQ3Dg0=", "dev": true, "dependencies": {"array-union": "^1.0.1", "arrify": "^1.0.0", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/globby/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/globby/node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/globule": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/globule/-/globule-0.1.0.tgz", "integrity": "sha1-2cjt3h2nnRJaFRt5UzuXhnY0auU=", "dev": true, "dependencies": {"glob": "~3.1.21", "lodash": "~1.0.1", "minimatch": "~0.2.11"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/globule/node_modules/glob": {"version": "3.1.21", "resolved": "https://registry.npmjs.org/glob/-/glob-3.1.21.tgz", "integrity": "sha1-0p4KBV3qUTj00H7UDomC6DwgZs0=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"graceful-fs": "~1.2.0", "inherits": "1", "minimatch": "~0.2.11"}, "engines": {"node": "*"}}, "node_modules/globule/node_modules/graceful-fs": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.2.3.tgz", "integrity": "sha1-FaSAaldUfLLS2/J/QuiajDRRs2Q=", "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/globule/node_modules/inherits": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/inherits/-/inherits-1.0.2.tgz", "integrity": "sha1-ykMJ2t7mtUzAuNJH6NfHoJdb3Js=", "dev": true}, "node_modules/globule/node_modules/minimatch": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.14.tgz", "integrity": "sha1-x054BXT2PG+aCQ6Q775u9TpqdWo=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/glogg": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/glogg/-/glogg-1.0.0.tgz", "integrity": "sha1-f+DxmfV6yQbPUS/urY+Q7kooT8U=", "dev": true, "dependencies": {"sparkles": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/graceful-fs": {"version": "3.0.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.11.tgz", "integrity": "sha1-dhPHeKGv6mLyXGMKCG1/Osu92Bg=", "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js", "dev": true, "dependencies": {"natives": "^1.1.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/gulp": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/gulp/-/gulp-3.9.1.tgz", "integrity": "sha1-VxzkWSjdQK9lFPxAEYZgFsE4RbQ=", "dev": true, "dependencies": {"archy": "^1.0.0", "chalk": "^1.0.0", "deprecated": "^0.0.1", "gulp-util": "^3.0.0", "interpret": "^1.0.0", "liftoff": "^2.1.0", "minimist": "^1.1.0", "orchestrator": "^0.3.0", "pretty-hrtime": "^1.0.0", "semver": "^4.1.0", "tildify": "^1.0.0", "v8flags": "^2.0.2", "vinyl-fs": "^0.3.0"}, "bin": {"gulp": "bin/gulp.js"}, "engines": {"node": ">= 0.9"}}, "node_modules/gulp-bytediff": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/gulp-bytediff/-/gulp-bytediff-0.2.1.tgz", "integrity": "sha1-Al1bETWZhEWuZTAE9/oPUcHa7Ow=", "dev": true, "dependencies": {"filesize": "~3.1.0", "gulp-util": "~3.0.3", "map-stream": "~0.0.5"}}, "node_modules/gulp-jscs": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/gulp-jscs/-/gulp-jscs-1.6.0.tgz", "integrity": "sha1-sV7lJgH391pyXTQdHaFhsBJ9Z6A=", "deprecated": "JSCS is deprecated. Switch to ESLint.", "dev": true, "dependencies": {"gulp-util": "^3.0.4", "jscs": "^1.12.0", "object-assign": "^2.0.0", "through2": "^0.6.5", "tildify": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/gulp-jscs/node_modules/object-assign": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-2.1.1.tgz", "integrity": "sha1-Q8NuXVaf+OSBbE76i+AtJpZ8GKo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/gulp-jscs/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/gulp-jscs/node_modules/through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "dependencies": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}, "node_modules/gulp-jshint": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/gulp-jshint/-/gulp-jshint-1.12.0.tgz", "integrity": "sha1-I/vRuv3W+/5h6mRmenSAmpYdA94=", "dev": true, "dependencies": {"gulp-util": "^3.0.0", "jshint": "^2.7.0", "lodash": "^3.0.1", "minimatch": "^2.0.1", "rcloader": "0.1.2", "through2": "~0.6.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/gulp-jshint/node_modules/lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y=", "dev": true}, "node_modules/gulp-jshint/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/gulp-jshint/node_modules/through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "dependencies": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}, "node_modules/gulp-less": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/gulp-less/-/gulp-less-3.3.2.tgz", "integrity": "sha1-9mNq3MZhUKiQJxn6WZY/x/hipJo=", "dev": true, "dependencies": {"accord": "^0.27.3", "gulp-util": "^3.0.7", "less": "2.6.x || ^2.7.1", "object-assign": "^4.0.1", "through2": "^2.0.0", "vinyl-sourcemaps-apply": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/gulp-less/node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/gulp-load-plugins": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/gulp-load-plugins/-/gulp-load-plugins-0.7.1.tgz", "integrity": "sha1-Qq7x749MHMuoZP4olIvnre9uZh4=", "dev": true, "dependencies": {"findup-sync": "^0.1.2", "multimatch": "1.0.0"}}, "node_modules/gulp-load-plugins/node_modules/findup-sync": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-0.1.3.tgz", "integrity": "sha1-fz56l7gjksZTvwZYm9hRkOk8NoM=", "dev": true, "dependencies": {"glob": "~3.2.9", "lodash": "~2.4.1"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/gulp-load-plugins/node_modules/glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz", "integrity": "sha1-Spc/Y1uRkPcV0QmH1cAP0oFevj0=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inherits": "2", "minimatch": "0.3"}, "engines": {"node": "*"}}, "node_modules/gulp-load-plugins/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha1-+t2DS5aDBz2hebPq5tnA0VBT9z4=", "dev": true, "engines": ["node", "rhino"]}, "node_modules/gulp-load-plugins/node_modules/minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha1-J12O2qxPG7MyZHIInnlJyDlGmd0=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/gulp-load-utils": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/gulp-load-utils/-/gulp-load-utils-0.0.4.tgz", "integrity": "sha1-oFkTHUfyw19LByrTOl75sU2oBV0=", "deprecated": " ", "dev": true, "engines": {"node": ">= 0.9"}}, "node_modules/gulp-minify-css": {"version": "0.3.13", "resolved": "https://registry.npmjs.org/gulp-minify-css/-/gulp-minify-css-0.3.13.tgz", "integrity": "sha1-uoE8ZlQihoMFODzNGF8jnB41IJs=", "deprecated": "Please use gulp-clean-css", "dev": true, "dependencies": {"bufferstreams": "0.0.2", "clean-css": "~3.0.4", "gulp-util": "~3.0.1", "memory-cache": "0.0.5", "through2": "^0.6.1", "vinyl-sourcemaps-apply": "^0.1.4"}, "engines": {"node": ">= 0.10"}}, "node_modules/gulp-minify-css/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/gulp-minify-css/node_modules/source-map": {"version": "0.1.43", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/gulp-minify-css/node_modules/through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "dependencies": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}, "node_modules/gulp-minify-css/node_modules/vinyl-sourcemaps-apply": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/vinyl-sourcemaps-apply/-/vinyl-sourcemaps-apply-0.1.4.tgz", "integrity": "sha1-xfy9Q+LyOEI8LcmL3db3m3K8NFs=", "dev": true, "dependencies": {"source-map": "^0.1.39"}}, "node_modules/gulp-rename": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/gulp-rename/-/gulp-rename-1.2.2.tgz", "integrity": "sha1-OtRCh2PwXidk3sHGfYaNsnVoeBc=", "dev": true, "engines": {"node": ">=0.10.0", "npm": ">=1.2.10"}}, "node_modules/gulp-sourcemaps": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/gulp-sourcemaps/-/gulp-sourcemaps-1.12.0.tgz", "integrity": "sha1-eG+XyUoPloSSRl1wVY4EJCxnlZg=", "dev": true, "dependencies": {"@gulp-sourcemaps/map-sources": "1.X", "acorn": "4.X", "convert-source-map": "1.X", "css": "2.X", "debug-fabulous": "0.0.X", "detect-newline": "2.X", "graceful-fs": "4.X", "source-map": "0.X", "strip-bom": "2.X", "through2": "2.X", "vinyl": "1.X"}, "engines": {"node": ">=0.10.0"}}, "node_modules/gulp-sourcemaps/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/gulp-sourcemaps/node_modules/strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "dev": true, "dependencies": {"is-utf8": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/gulp-sourcemaps/node_modules/vinyl": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-1.2.0.tgz", "integrity": "sha1-XIgDbPVl5d8FVYv8kR+GVt8hiIQ=", "dev": true, "dependencies": {"clone": "^1.0.0", "clone-stats": "^0.0.1", "replace-ext": "0.0.1"}, "engines": {"node": ">= 0.9"}}, "node_modules/gulp-task-listing": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/gulp-task-listing/-/gulp-task-listing-0.3.0.tgz", "integrity": "sha1-JGXrAtGw2dl0nVnaVX4JcSbwCIc=", "dev": true, "dependencies": {"chalk": "*"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/gulp-uglify": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/gulp-uglify/-/gulp-uglify-1.5.4.tgz", "integrity": "sha1-UkeI2HZm0J+dDCH7IXf5ADmmWMk=", "dev": true, "dependencies": {"deap": "^1.0.0", "fancy-log": "^1.0.0", "gulp-util": "^3.0.0", "isobject": "^2.0.0", "through2": "^2.0.0", "uglify-js": "2.6.4", "uglify-save-license": "^0.4.1", "vinyl-sourcemaps-apply": "^0.2.0"}}, "node_modules/gulp-uglify/node_modules/uglify-js": {"version": "2.6.4", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.6.4.tgz", "integrity": "sha1-ZeovswWck5RpLxX+2HwrNsFrmt8=", "dev": true, "dependencies": {"async": "~0.2.6", "source-map": "~0.5.1", "uglify-to-browserify": "~1.0.0", "yargs": "~3.10.0"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/gulp-util": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/gulp-util/-/gulp-util-3.0.8.tgz", "integrity": "sha1-AFTh50RQLifATBh8PsxQXdVLu08=", "deprecated": "gulp-util is deprecated - replace it, following the guidelines at https://medium.com/gulpjs/gulp-util-ca3b1f9f9ac5", "dev": true, "dependencies": {"array-differ": "^1.0.0", "array-uniq": "^1.0.2", "beeper": "^1.0.0", "chalk": "^1.0.0", "dateformat": "^2.0.0", "fancy-log": "^1.1.0", "gulplog": "^1.0.0", "has-gulplog": "^0.1.0", "lodash._reescape": "^3.0.0", "lodash._reevaluate": "^3.0.0", "lodash._reinterpolate": "^3.0.0", "lodash.template": "^3.0.0", "minimist": "^1.1.0", "multipipe": "^0.1.2", "object-assign": "^3.0.0", "replace-ext": "0.0.1", "through2": "^2.0.0", "vinyl": "^0.5.0"}, "engines": {"node": ">=0.10"}}, "node_modules/gulplog": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/gulplog/-/gulplog-1.0.0.tgz", "integrity": "sha1-4oxNRdBey77YGDY86PnFkmIp/+U=", "dev": true, "dependencies": {"glogg": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/handlebars": {"version": "4.0.11", "resolved": "https://registry.npmjs.org/handlebars/-/handlebars-4.0.11.tgz", "integrity": "sha1-Ywo13+ApS8KB7a5v/F0yn8eYLcw=", "dev": true, "dependencies": {"async": "^1.4.0", "optimist": "^0.6.1", "source-map": "^0.4.4"}, "bin": {"handlebars": "bin/handlebars"}, "engines": {"node": ">=0.4.7"}, "optionalDependencies": {"uglify-js": "^2.6"}}, "node_modules/handlebars/node_modules/async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=", "dev": true}, "node_modules/handlebars/node_modules/source-map": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "integrity": "sha1-66T12pwNyZneaAMti092FzZSA2s=", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/har-schema": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-1.0.5.tgz", "integrity": "sha1-0mMTX0MwfALGAq/I/pWXDAFRNp4=", "dev": true, "optional": true, "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-4.2.1.tgz", "integrity": "sha1-M0gdDxu/9gDdID11gSpqX7oALio=", "deprecated": "this library is no longer supported", "dev": true, "optional": true, "dependencies": {"ajv": "^4.9.1", "har-schema": "^1.0.5"}, "engines": {"node": ">=4"}}, "node_modules/has-ansi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz", "integrity": "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/has-gulplog": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/has-gulplog/-/has-gulplog-0.1.0.tgz", "integrity": "sha1-ZBTIKRNpfaUVkDl9r7EvIpZ4Ec4=", "dev": true, "dependencies": {"sparkles": "^1.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/hasha": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/hasha/-/hasha-2.2.0.tgz", "integrity": "sha1-eNfL/B5tZjA/55g3NlmEUXsvbuE=", "dev": true, "dependencies": {"is-stream": "^1.0.1", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hawk": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/hawk/-/hawk-3.1.3.tgz", "integrity": "sha1-B4REvXwWQLD+VA0sm3PVlnjo4cQ=", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "dependencies": {"boom": "2.x.x", "cryptiles": "2.x.x", "hoek": "2.x.x", "sntp": "1.x.x"}, "engines": {"node": ">=0.10.32"}}, "node_modules/hoek": {"version": "2.16.3", "resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz", "integrity": "sha1-ILt0A9POo5jpHcRxCo/xuCdKJe0=", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "engines": {"node": ">=0.10.40"}}, "node_modules/homedir-polyfill": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/homedir-polyfill/-/homedir-polyfill-1.0.1.tgz", "integrity": "sha1-TCu8inWJmP7r9e1oWA921GdotLw=", "dev": true, "dependencies": {"parse-passwd": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hosted-git-info": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.5.0.tgz", "integrity": "sha512-pNgbURSuab90KbTqvRPsseaTxOJCZBD0a7t+haSN33piP9cCM4l0CqdzAif2hUqm716UovKB2ROmiabGAKVXyg==", "dev": true}, "node_modules/htmlparser2": {"version": "3.8.3", "resolved": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.8.3.tgz", "integrity": "sha1-mWwosZFRaovoZQGn15dX5ccMEGg=", "dev": true, "dependencies": {"domelementtype": "1", "domhandler": "2.3", "domutils": "1.5", "entities": "1.0", "readable-stream": "1.1"}}, "node_modules/http-errors": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.3.1.tgz", "integrity": "sha1-GX4izevUGYWF6GlO9nhhl7ke2UI=", "dev": true, "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-proxy": {"version": "0.10.4", "resolved": "https://registry.npmjs.org/http-proxy/-/http-proxy-0.10.4.tgz", "integrity": "sha1-FLoM6qIZf4n6MN6p57CeGc2Twi8=", "dev": true, "dependencies": {"colors": "0.x.x", "optimist": "0.6.x", "pkginfo": "0.3.x", "utile": "~0.2.1"}, "bin": {"node-http-proxy": "bin/node-http-proxy"}, "engines": {"node": ">= 0.6.6"}}, "node_modules/http-proxy/node_modules/colors": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/colors/-/colors-0.6.2.tgz", "integrity": "sha1-JCP+ZnisDF2uiFLl0OW+CMmXq8w=", "dev": true, "engines": {"node": ">=0.1.90"}}, "node_modules/http-proxy/node_modules/pkginfo": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.3.1.tgz", "integrity": "sha1-Wyn2qB9wcXFC4J52W76rl7T4HiE=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/http-signature": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.1.1.tgz", "integrity": "sha1-33LiZwZs0Kxn+3at+OE0qPvPkb8=", "dev": true, "dependencies": {"assert-plus": "^0.2.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/i": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/i/-/i-0.3.6.tgz", "integrity": "sha1-2WyScyB28HJxG2sQ/X1PZa2O4j0=", "dev": true, "engines": {"node": ">=0.4"}}, "node_modules/ibrik": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ibrik/-/ibrik-2.0.0.tgz", "integrity": "sha1-iaJDTypcgrkhZsPZfeO1Y27qLpw=", "dev": true, "dependencies": {"coffee-script": "~1.8.0", "esprima": "1.2.x", "estraverse": "~1.8.0", "fileset": "0.1.x", "istanbul": "~0.3.2", "lodash": "~2.4.1", "mkdirp": "~0.5.0", "optimist": "~0.6.1", "which": "~1.0.5"}, "bin": {"ibrik": "bin/ibrik"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ibrik/node_modules/estraverse": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-1.8.0.tgz", "integrity": "sha1-PxJk+2LIUA265eT3NwXNV21q9Cg=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/ibrik/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha1-+t2DS5aDBz2hebPq5tnA0VBT9z4=", "dev": true, "engines": ["node", "rhino"]}, "node_modules/ibrik/node_modules/which": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/which/-/which-1.0.9.tgz", "integrity": "sha1-RgwdoPgQED0DIam2M6+eV15kSG8=", "dev": true, "bin": {"which": "bin/which"}}, "node_modules/iconv-lite": {"version": "0.4.11", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.11.tgz", "integrity": "sha1-LstC/SlHRJIiCaLnxATayHk9it4=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/ignore": {"version": "3.3.7", "resolved": "https://registry.npmjs.org/ignore/-/ignore-3.3.7.tgz", "integrity": "sha512-YGG3ejvBNHRqu0559EOxxNFihD0AjpvHlC/pdGKd3X3ofe+CoJkYazwNJYTNebqpPKN+VVQbh4ZFn1DivMNuHA==", "dev": true}, "node_modules/image-size": {"version": "0.5.5", "resolved": "https://registry.npmjs.org/image-size/-/image-size-0.5.5.tgz", "integrity": "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=", "dev": true, "optional": true, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz", "integrity": "sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=", "dev": true, "dependencies": {"repeating": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/indx": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/indx/-/indx-0.2.3.tgz", "integrity": "sha1-Fdz1bunPZcAjTFE8J/vVgOcPvFA=", "dev": true}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}, "node_modules/ini": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/ini/-/ini-1.3.5.tgz", "integrity": "sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw==", "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "dev": true, "engines": {"node": "*"}}, "node_modules/inquirer": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/inquirer/-/inquirer-0.12.0.tgz", "integrity": "sha1-HvK/1jUE3wvHV4X/+MLEHfEvB34=", "dev": true, "dependencies": {"ansi-escapes": "^1.1.0", "ansi-regex": "^2.0.0", "chalk": "^1.0.0", "cli-cursor": "^1.0.1", "cli-width": "^2.0.0", "figures": "^1.3.5", "lodash": "^4.3.0", "readline2": "^1.0.1", "run-async": "^0.1.0", "rx-lite": "^3.1.2", "string-width": "^1.0.1", "strip-ansi": "^3.0.0", "through": "^2.3.6"}}, "node_modules/inquirer/node_modules/lodash": {"version": "4.17.4", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz", "integrity": "sha1-eCA6TRwyiuHYbcpkYONptX9AVa4=", "dev": true}, "node_modules/interpret": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.1.0.tgz", "integrity": "sha1-ftGxQQxqDg94z5XTuEQMY/eLhhQ=", "dev": true}, "node_modules/is-absolute": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/is-absolute/-/is-absolute-0.2.6.tgz", "integrity": "sha1-IN5p89uULvLYe5wto28XIjWxtes=", "dev": true, "dependencies": {"is-relative": "^0.2.1", "is-windows": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "node_modules/is-binary-path": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "dependencies": {"binary-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "node_modules/is-builtin-module": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-builtin-module/-/is-builtin-module-1.0.0.tgz", "integrity": "sha1-VAVy0096wxGfj3bDDLwbHgN6/74=", "dev": true, "dependencies": {"builtin-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-dotfile": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/is-dotfile/-/is-dotfile-1.0.3.tgz", "integrity": "sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-equal-shallow": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/is-equal-shallow/-/is-equal-shallow-0.1.3.tgz", "integrity": "sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=", "dev": true, "dependencies": {"is-primitive": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-finite": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.0.2.tgz", "integrity": "sha1-zGZ3aVYCvlUO8R6LSqYwU0K20Ko=", "dev": true, "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-2.0.1.tgz", "integrity": "sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=", "dev": true, "dependencies": {"is-extglob": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-my-json-valid": {"version": "2.16.1", "resolved": "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.16.1.tgz", "integrity": "sha512-ochPsqWS1WXj8ZnMIV0vnNXooaMhp7cyL4FMSIPKTtnV0Ha/T19G2b9kkhcNsabV9bxYkze7/aLZJb/bYuFduQ==", "deprecated": "catastrophic backtracking in regexes could potentially lead to REDOS attack, upgrade to 2.17.2 as soon as possible", "dev": true, "dependencies": {"generate-function": "^2.0.0", "generate-object-property": "^1.1.0", "jsonpointer": "^4.0.0", "xtend": "^4.0.0"}}, "node_modules/is-number": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=", "dev": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-cwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "integrity": "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-in-cwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-path-in-cwd/-/is-path-in-cwd-1.0.0.tgz", "integrity": "sha1-ZHdYK4IU1gI0YJRWcAO+ip6sBNw=", "dev": true, "dependencies": {"is-path-inside": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-inside": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-1.0.1.tgz", "integrity": "sha1-jvW33lBDej/cprToZe96pVy0gDY=", "dev": true, "dependencies": {"path-is-inside": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "dev": true, "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object/node_modules/isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-posix-bracket": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-posix-bracket/-/is-posix-bracket-0.1.1.tgz", "integrity": "sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-primitive": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-primitive/-/is-primitive-2.0.0.tgz", "integrity": "sha1-IHurkWOEmcB7Kt8kCkGochADRXU=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-property": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "integrity": "sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=", "dev": true}, "node_modules/is-relative": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-relative/-/is-relative-0.2.1.tgz", "integrity": "sha1-0n9MfVFtF1+2ENuEu+7yPDvJeqU=", "dev": true, "dependencies": {"is-unc-path": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-resolvable": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-resolvable/-/is-resolvable-1.0.0.tgz", "integrity": "sha1-jfV8YeouPFAUCNEA+wE8+NbgzGI=", "dev": true, "dependencies": {"tryit": "^1.0.1"}}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "dev": true}, "node_modules/is-unc-path": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/is-unc-path/-/is-unc-path-0.1.2.tgz", "integrity": "sha1-arBTpyVzwQJQ/0FqOBTDUXivObk=", "dev": true, "dependencies": {"unc-path-regex": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-utf8": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz", "integrity": "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=", "dev": true}, "node_modules/is-windows": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-0.2.0.tgz", "integrity": "sha1-3hqm1j6indJIc3tp8f+LgALSEIw=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "node_modules/isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/isobject/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "dev": true}, "node_modules/istanbul": {"version": "0.3.22", "resolved": "https://registry.npmjs.org/istanbul/-/istanbul-0.3.22.tgz", "integrity": "sha1-PhZNhQIf4ZyYXR8OfvDD4i0BLrY=", "deprecated": "This module is no longer maintained, try this instead:\n  npm i nyc\nVisit https://istanbul.js.org/integrations for other alternatives.", "dev": true, "dependencies": {"abbrev": "1.0.x", "async": "1.x", "escodegen": "1.7.x", "esprima": "2.5.x", "fileset": "0.2.x", "handlebars": "^4.0.1", "js-yaml": "3.x", "mkdirp": "0.5.x", "nopt": "3.x", "once": "1.x", "resolve": "1.1.x", "supports-color": "^3.1.0", "which": "^1.1.1", "wordwrap": "^1.0.0"}, "bin": {"istanbul": "lib/cli.js"}}, "node_modules/istanbul/node_modules/async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=", "dev": true}, "node_modules/istanbul/node_modules/esprima": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/esprima/-/esprima-2.5.0.tgz", "integrity": "sha1-84ekb9NEwbGjm6+MIL+0O20AWMw=", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/istanbul/node_modules/fileset": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/fileset/-/fileset-0.2.1.tgz", "integrity": "sha1-WI74lzxmI7KnbfRlEFaWuWqsgGc=", "dev": true, "dependencies": {"glob": "5.x", "minimatch": "2.x"}}, "node_modules/istanbul/node_modules/glob": {"version": "5.0.15", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "integrity": "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/istanbul/node_modules/resolve": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz", "integrity": "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=", "dev": true}, "node_modules/istanbul/node_modules/supports-color": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz", "integrity": "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=", "dev": true, "dependencies": {"has-flag": "^1.0.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/istanbul/node_modules/wordwrap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=", "dev": true}, "node_modules/jquery": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/jquery/-/jquery-2.2.4.tgz", "integrity": "sha512-lBHj60ezci2u1v2FqnZIraShGgEXq35qCzMv4lITyHGppTnA13rwR0MgwyNJh9TnDs3aXUvd1xjAotfraMHX/Q==", "deprecated": "This version is deprecated. Please upgrade to the latest version or find support at https://www.herodevs.com/support/jquery-nes.", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.10.0.tgz", "integrity": "sha512-O2v52ffjLa9VeM43J4XocZE//WT9N0IiwDa3KSHH7Tu8CtH+1qM8SIZvnsTh6v+4yFy5KUY3BHUVwjpfAWsjIA==", "dev": true, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/js-yaml/node_modules/esprima": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.0.tgz", "integrity": "sha512-oftTcaMu/EGrEIu904mWteKIv8vMuOgGYo7EhVJJN00R/EED9DCua/xxHRdYnKtcECzVg7xOWhflvJMnqcFZjw==", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "dev": true, "optional": true}, "node_modules/jscs": {"version": "1.13.1", "resolved": "https://registry.npmjs.org/jscs/-/jscs-1.13.1.tgz", "integrity": "sha1-fdRuGG8PzgcSzQMerMCkXvfc/rA=", "deprecated": "JSCS has merged with ESLint! See - https://medium.com/@markelog/jscs-end-of-the-line-bc9bf0b3fdb2", "dev": true, "dependencies": {"chalk": "~1.0.0", "cli-table": "~0.3.1", "commander": "~2.6.0", "esprima": "^1.2.5", "esprima-harmony-jscs": "1.1.0-bin", "estraverse": "^1.9.3", "exit": "~0.1.2", "glob": "^5.0.1", "lodash.assign": "~3.0.0", "minimatch": "~2.0.1", "pathval": "~0.1.1", "prompt": "~0.2.14", "strip-json-comments": "~1.0.2", "vow": "~0.4.8", "vow-fs": "~0.3.4", "xmlbuilder": "^2.6.1"}, "bin": {"jscs": "bin/jscs"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/jscs/node_modules/ansi-regex": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-1.1.1.tgz", "integrity": "sha1-QchHGUZGN15qGl0Qw8oFTvn8mA0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/jscs/node_modules/chalk": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.0.0.tgz", "integrity": "sha1-s89O0P9Tl8mcdbj2edsvUoMfltw=", "dev": true, "dependencies": {"ansi-styles": "^2.0.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^1.0.3", "strip-ansi": "^2.0.1", "supports-color": "^1.3.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscs/node_modules/glob": {"version": "5.0.15", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "integrity": "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/jscs/node_modules/has-ansi": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-1.0.3.tgz", "integrity": "sha1-wLWxYV2eOCsP9nFp2We0JeSMpTg=", "dev": true, "dependencies": {"ansi-regex": "^1.1.0", "get-stdin": "^4.0.1"}, "bin": {"has-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscs/node_modules/strip-ansi": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-2.0.1.tgz", "integrity": "sha1-32LBqpTtLxFOHQ8h/R1QSCt5pg4=", "dev": true, "dependencies": {"ansi-regex": "^1.0.0"}, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscs/node_modules/supports-color": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-1.3.1.tgz", "integrity": "sha1-FXWN8J2P87SswwdTn6vicJXhBC0=", "dev": true, "bin": {"supports-color": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/jshint": {"version": "2.9.5", "resolved": "https://registry.npmjs.org/jshint/-/jshint-2.9.5.tgz", "integrity": "sha1-HnJSkVzmgbQIJ+4UJIxG006apiw=", "dev": true, "dependencies": {"cli": "~1.0.0", "console-browserify": "1.1.x", "exit": "0.1.x", "htmlparser2": "3.8.x", "lodash": "3.7.x", "minimatch": "~3.0.2", "shelljs": "0.3.x", "strip-json-comments": "1.0.x"}, "bin": {"jshint": "bin/jshint"}}, "node_modules/jshint-stylish": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/jshint-stylish/-/jshint-stylish-1.0.2.tgz", "integrity": "sha1-6Z88w0CvsY4qdwL4eY10AMoxRGo=", "dev": true, "dependencies": {"chalk": "^1.0.0", "log-symbols": "^1.0.0", "string-length": "^1.0.0", "text-table": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jshint/node_modules/lodash": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.7.0.tgz", "integrity": "sha1-Nni9irmVBXwHreg27S7wh9qBHUU=", "dev": true}, "node_modules/jshint/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=", "dev": true}, "node_modules/json-stable-stringify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "dev": true, "dependencies": {"jsonify": "~0.0.0"}}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "dev": true}, "node_modules/jsonfile": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-2.4.0.tgz", "integrity": "sha1-NzaitCi4e72gzIO1P6PWM6NcKug=", "dev": true, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonfile/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "optional": true, "engines": {"node": ">=0.4.0"}}, "node_modules/jsonify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/jsonify/-/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=", "dev": true, "engines": {"node": "*"}}, "node_modules/jsonpointer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-4.0.1.tgz", "integrity": "sha1-T9kss04OnbPInIYi7PUfm5eMbLk=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "dev": true, "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "node_modules/jsprim/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/karma": {"version": "0.12.37", "resolved": "https://registry.npmjs.org/karma/-/karma-0.12.37.tgz", "integrity": "sha1-Gp9/3szWneLoNeBO26wuzT+mReQ=", "dev": true, "dependencies": {"chokidar": "^1.0.1", "colors": "^1.1.0", "connect": "^2.29.2", "di": "^0.0.1", "glob": "^5.0.6", "graceful-fs": "^3.0.6", "http-proxy": "^0.10", "lodash": "^3.8.0", "log4js": "^0.6.25", "mime": "^1.3.4", "minimatch": "^2.0.7", "optimist": "^0.6.1", "q": "^1.4.1", "rimraf": "^2.3.3", "socket.io": "0.9.16", "source-map": "^0.4.2", "useragent": "^2.1.6"}, "bin": {"karma": "bin/karma"}, "engines": {"node": ">=0.8 <=0.12 || >=1 <=2"}}, "node_modules/karma-coverage": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/karma-coverage/-/karma-coverage-0.2.7.tgz", "integrity": "sha1-92dAsnW78woKufQdjPVoQ6CZRXY=", "dev": true, "dependencies": {"dateformat": "~1.0.6", "ibrik": "~2.0.0", "istanbul": "~0.3.0", "minimatch": "~0.3.0"}, "peerDependencies": {"karma": ">=0.9"}}, "node_modules/karma-coverage/node_modules/dateformat": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/dateformat/-/dateformat-1.0.12.tgz", "integrity": "sha1-nxJLZ1lMk3/3BpMuSmQsyo27/uk=", "dev": true, "dependencies": {"get-stdin": "^4.0.1", "meow": "^3.3.0"}, "bin": {"dateformat": "bin/cli.js"}, "engines": {"node": "*"}}, "node_modules/karma-coverage/node_modules/minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha1-J12O2qxPG7MyZHIInnlJyDlGmd0=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/karma-phantomjs-launcher": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/karma-phantomjs-launcher/-/karma-phantomjs-launcher-0.1.4.tgz", "integrity": "sha1-TvluQyL/Y65dkY5RwlshNyMjjzA=", "deprecated": "PhantomJS development have stopped, use puppeteer or similar", "dev": true, "dependencies": {"phantomjs": "~1.9"}, "peerDependencies": {"karma": ">=0.9"}}, "node_modules/karma-qunit": {"version": "0.1.9", "resolved": "https://registry.npmjs.org/karma-qunit/-/karma-qunit-0.1.9.tgz", "integrity": "sha1-SXURbg/OvJDI0y8Z8SIuNbTqDQs=", "dev": true, "peerDependencies": {"qunitjs": "^1.14.0"}}, "node_modules/karma/node_modules/colors": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/colors/-/colors-1.1.2.tgz", "integrity": "sha1-FopHAXVran9RoSzgyXv6KMCE7WM=", "dev": true, "engines": {"node": ">=0.1.90"}}, "node_modules/karma/node_modules/glob": {"version": "5.0.15", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz", "integrity": "sha1-G8k2ueAvSmA/zCIuz3Yz0wuLk7E=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inflight": "^1.0.4", "inherits": "2", "minimatch": "2 || 3", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/karma/node_modules/lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y=", "dev": true}, "node_modules/karma/node_modules/source-map": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "integrity": "sha1-66T12pwNyZneaAMti092FzZSA2s=", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/kew": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/kew/-/kew-0.7.0.tgz", "integrity": "sha1-edk9LTM2PW/dKXCzNdkUGtWR15s=", "dev": true}, "node_modules/kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/klaw": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/klaw/-/klaw-1.3.1.tgz", "integrity": "sha1-QIhDO0azsbolnXh4XY6W9zugJDk=", "dev": true, "optionalDependencies": {"graceful-fs": "^4.1.9"}}, "node_modules/klaw/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "optional": true, "engines": {"node": ">=0.4.0"}}, "node_modules/lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha1-odePw6UEdMuAhF07O24dpJpEbo4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/lazy-debug-legacy": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/lazy-debug-legacy/-/lazy-debug-legacy-0.0.1.tgz", "integrity": "sha1-U3cWwHduTPeePtG2IfdljCkRsbE=", "dev": true, "peerDependencies": {"debug": "*"}}, "node_modules/less": {"version": "2.7.3", "resolved": "https://registry.npmjs.org/less/-/less-2.7.3.tgz", "integrity": "sha512-KPdIJKWcEAb02TuJtaLrhue0krtRLoRoo7x6BNJIBelO00t/CCdJQUnHW5V34OnHMWzIktSalJxRO+FvytQlCQ==", "dev": true, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=0.12"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "source-map": "^0.5.3"}}, "node_modules/less/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "optional": true, "engines": {"node": ">=0.4.0"}}, "node_modules/levn": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/levn/-/levn-0.2.5.tgz", "integrity": "sha1-uo0znQykphDjo/FFucr0iAcVUFQ=", "dev": true, "dependencies": {"prelude-ls": "~1.1.0", "type-check": "~0.3.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/liftoff": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/liftoff/-/liftoff-2.3.0.tgz", "integrity": "sha1-qY8v9nGD2Lp8+soQVIvX/wVQs4U=", "dev": true, "dependencies": {"extend": "^3.0.0", "findup-sync": "^0.4.2", "fined": "^1.0.1", "flagged-respawn": "^0.3.2", "lodash.isplainobject": "^4.0.4", "lodash.isstring": "^4.0.1", "lodash.mapvalues": "^4.4.0", "rechoir": "^0.6.2", "resolve": "^1.1.7"}, "engines": {"node": ">= 0.8"}}, "node_modules/load-json-file": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz", "integrity": "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/load-json-file/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/load-json-file/node_modules/strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "dev": true, "dependencies": {"is-utf8": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/lodash": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-1.0.2.tgz", "integrity": "sha1-j1dWDIO1n8JwvT1WG2kAQ0MOJVE=", "dev": true, "engines": ["node", "rhino"]}, "node_modules/lodash._baseassign": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash._baseassign/-/lodash._baseassign-3.2.0.tgz", "integrity": "sha1-jDigmVAPIVrQnlnxci/QxSv+Ck4=", "dev": true, "dependencies": {"lodash._basecopy": "^3.0.0", "lodash.keys": "^3.0.0"}}, "node_modules/lodash._basecopy": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._basecopy/-/lodash._basecopy-3.0.1.tgz", "integrity": "sha1-jaDmqHbPNEwK2KVIghEd08XHyjY=", "dev": true}, "node_modules/lodash._basetostring": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._basetostring/-/lodash._basetostring-3.0.1.tgz", "integrity": "sha1-0YYdh3+CSlL2aYMtyvPuFVZqB9U=", "dev": true}, "node_modules/lodash._basevalues": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._basevalues/-/lodash._basevalues-3.0.0.tgz", "integrity": "sha1-W3dXYoAr3j0yl1A+JjAIIP32Ybc=", "dev": true}, "node_modules/lodash._bindcallback": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._bindcallback/-/lodash._bindcallback-3.0.1.tgz", "integrity": "sha1-5THCdkTPi1epnhftlbNcdIeJOS4=", "dev": true}, "node_modules/lodash._createassigner": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/lodash._createassigner/-/lodash._createassigner-3.1.1.tgz", "integrity": "sha1-g4pbri/aymOsIt7o4Z+k5taXCxE=", "dev": true, "dependencies": {"lodash._bindcallback": "^3.0.0", "lodash._isiterateecall": "^3.0.0", "lodash.restparam": "^3.0.0"}}, "node_modules/lodash._getnative": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/lodash._getnative/-/lodash._getnative-3.9.1.tgz", "integrity": "sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=", "dev": true}, "node_modules/lodash._isiterateecall": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/lodash._isiterateecall/-/lodash._isiterateecall-3.0.9.tgz", "integrity": "sha1-UgOte6Ql+uhCRg5pbbnPPmqsBXw=", "dev": true}, "node_modules/lodash._reescape": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._reescape/-/lodash._reescape-3.0.0.tgz", "integrity": "sha1-Kx1vXf4HyKNVdT5fJ/rH8c3hYWo=", "dev": true}, "node_modules/lodash._reevaluate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._reevaluate/-/lodash._reevaluate-3.0.0.tgz", "integrity": "sha1-WLx0xAZklTrgsSTYBpltrKQx4u0=", "dev": true}, "node_modules/lodash._reinterpolate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz", "integrity": "sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=", "dev": true}, "node_modules/lodash._root": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._root/-/lodash._root-3.0.1.tgz", "integrity": "sha1-+6HEUkwZ7ppfgTa0YJ8BfPTe1pI=", "dev": true}, "node_modules/lodash.assign": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/lodash.assign/-/lodash.assign-3.0.0.tgz", "integrity": "sha1-93SdFYCkEgJzo3H1SmaxTJ1yJvo=", "dev": true, "dependencies": {"lodash._baseassign": "^3.0.0", "lodash._createassigner": "^3.0.0"}}, "node_modules/lodash.clone": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.clone/-/lodash.clone-4.5.0.tgz", "integrity": "sha1-GVhwRQ9aExkkeN9Lw9I9LeoZB7Y=", "deprecated": "This package is deprecated. Use structuredClone instead.", "dev": true}, "node_modules/lodash.clonedeep": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz", "integrity": "sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=", "dev": true}, "node_modules/lodash.defaults": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz", "integrity": "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=", "dev": true}, "node_modules/lodash.escape": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash.escape/-/lodash.escape-3.2.0.tgz", "integrity": "sha1-mV7g3BjBtIzJLv+ucaEKq1tIdpg=", "dev": true, "dependencies": {"lodash._root": "^3.0.0"}}, "node_modules/lodash.flatten": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/lodash.flatten/-/lodash.flatten-4.4.0.tgz", "integrity": "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=", "dev": true}, "node_modules/lodash.isarguments": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz", "integrity": "sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=", "dev": true}, "node_modules/lodash.isarray": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-3.0.4.tgz", "integrity": "sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=", "dev": true}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=", "dev": true}, "node_modules/lodash.isstring": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=", "dev": true}, "node_modules/lodash.keys": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-3.1.2.tgz", "integrity": "sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=", "dev": true, "dependencies": {"lodash._getnative": "^3.0.0", "lodash.isarguments": "^3.0.0", "lodash.isarray": "^3.0.0"}}, "node_modules/lodash.mapvalues": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz", "integrity": "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=", "dev": true}, "node_modules/lodash.merge": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.0.tgz", "integrity": "sha1-aYhLoUSsM/5plzemCG3v+t0PicU=", "dev": true}, "node_modules/lodash.partialright": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/lodash.partialright/-/lodash.partialright-4.2.1.tgz", "integrity": "sha1-ATDYDoM2MmTUAHTzKbij56ihzEs=", "dev": true}, "node_modules/lodash.pick": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/lodash.pick/-/lodash.pick-4.4.0.tgz", "integrity": "sha1-UvBWEP/53tQiYRRB7R/BI6AwAbM=", "deprecated": "This package is deprecated. Use destructuring assignment syntax instead.", "dev": true}, "node_modules/lodash.restparam": {"version": "3.6.1", "resolved": "https://registry.npmjs.org/lodash.restparam/-/lodash.restparam-3.6.1.tgz", "integrity": "sha1-k2pOMJ7zMKdkXtQUWYbIWuWyCAU=", "dev": true}, "node_modules/lodash.template": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/lodash.template/-/lodash.template-3.6.2.tgz", "integrity": "sha1-+M3sxhaaJVvpCYrosMU9N4kx0U8=", "deprecated": "This package is deprecated. Use https://socket.dev/npm/package/eta instead.", "dev": true, "dependencies": {"lodash._basecopy": "^3.0.0", "lodash._basetostring": "^3.0.0", "lodash._basevalues": "^3.0.0", "lodash._isiterateecall": "^3.0.0", "lodash._reinterpolate": "^3.0.0", "lodash.escape": "^3.0.0", "lodash.keys": "^3.0.0", "lodash.restparam": "^3.0.0", "lodash.templatesettings": "^3.0.0"}}, "node_modules/lodash.templatesettings": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-3.1.1.tgz", "integrity": "sha1-+zB4RHU7Zrnxr6VOJix0UwfbqOU=", "dev": true, "dependencies": {"lodash._reinterpolate": "^3.0.0", "lodash.escape": "^3.0.0"}}, "node_modules/lodash.uniq": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "integrity": "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=", "dev": true}, "node_modules/log-symbols": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-1.0.2.tgz", "integrity": "sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=", "dev": true, "dependencies": {"chalk": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/log4js": {"version": "0.6.38", "resolved": "https://registry.npmjs.org/log4js/-/log4js-0.6.38.tgz", "integrity": "sha1-LElBFmldb7JUgJQ9P8hy5mKlIv0=", "deprecated": "0.x is no longer supported. Please upgrade to 6.x or higher.", "dev": true, "dependencies": {"readable-stream": "~1.0.2", "semver": "~4.3.3"}, "engines": {"node": ">=0.8"}}, "node_modules/log4js/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/longest": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "integrity": "sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/loud-rejection": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz", "integrity": "sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=", "dev": true, "dependencies": {"currently-unhandled": "^0.4.1", "signal-exit": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "2.7.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.3.tgz", "integrity": "sha1-bUUk6LlV+V1PW1iFHOId1y+06VI=", "dev": true}, "node_modules/map-cache": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/map-obj": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz", "integrity": "sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/map-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/map-stream/-/map-stream-0.0.7.tgz", "integrity": "sha1-ih8HiW2CsQkmvTdEokIACfiJdKg=", "dev": true}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/memory-cache": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/memory-cache/-/memory-cache-0.0.5.tgz", "integrity": "sha1-2/maVtc2LEPsyvOfC6b5fzGgZ4Y=", "dev": true}, "node_modules/meow": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/meow/-/meow-3.7.0.tgz", "integrity": "sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=", "dev": true, "dependencies": {"camelcase-keys": "^2.0.0", "decamelize": "^1.1.2", "loud-rejection": "^1.0.0", "map-obj": "^1.0.1", "minimist": "^1.1.3", "normalize-package-data": "^2.3.4", "object-assign": "^4.0.1", "read-pkg-up": "^1.0.1", "redent": "^1.0.0", "trim-newlines": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/meow/node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/merge-stream": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-0.1.8.tgz", "integrity": "sha1-SKB7O0oSHXSj7b/c20sIrb8CQLE=", "dev": true, "dependencies": {"through2": "^0.6.1"}}, "node_modules/merge-stream/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/merge-stream/node_modules/through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "dependencies": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}, "node_modules/method-override": {"version": "2.3.10", "resolved": "https://registry.npmjs.org/method-override/-/method-override-2.3.10.tgz", "integrity": "sha1-49r41d7hDdLc59SuiNYrvud0drQ=", "dev": true, "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.2", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/method-override/node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "2.3.11", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=", "dev": true, "dependencies": {"arr-diff": "^2.0.0", "array-unique": "^0.2.1", "braces": "^1.8.2", "expand-brackets": "^0.1.4", "extglob": "^0.3.1", "filename-regex": "^2.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "normalize-path": "^2.0.1", "object.omit": "^2.0.0", "parse-glob": "^3.0.4", "regex-cache": "^0.4.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "dev": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.30.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.30.0.tgz", "integrity": "sha1-dMZD2i3Z1qRTmZY0ZbJtXKfXHwE=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.17", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.17.tgz", "integrity": "sha1-Cdejk/A+mVp5+K+Fe3Cp4KsWVXo=", "dev": true, "dependencies": {"mime-db": "~1.30.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz", "integrity": "sha1-jQh8OcazjAAbl/ynzm0OHoCvusc=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"brace-expansion": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}, "node_modules/mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mkdirp/node_modules/minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "node_modules/morgan": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.6.1.tgz", "integrity": "sha1-X9gYOYxoGcuiinzWZk8pL+HAu/I=", "dev": true, "dependencies": {"basic-auth": "~1.0.3", "debug": "~2.2.0", "depd": "~1.0.1", "on-finished": "~2.3.0", "on-headers": "~1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/morgan/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/morgan/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/multimatch": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/multimatch/-/multimatch-1.0.0.tgz", "integrity": "sha1-3N90mJb1FIDg4cSNq5y6Qe30ZKs=", "dev": true, "dependencies": {"array-differ": "^1.0.0", "array-union": "^1.0.0", "minimatch": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/multimatch/node_modules/minimatch": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-1.0.0.tgz", "integrity": "sha1-4N0hILSeG3JM6NcUxSCCKpQ4V20=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/multiparty": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/multiparty/-/multiparty-3.3.2.tgz", "integrity": "sha1-Nd5oBNwZZD5SSfPT473GyM4wHT8=", "dev": true, "dependencies": {"readable-stream": "~1.1.9", "stream-counter": "~0.2.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/multipipe": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/multipipe/-/multipipe-0.1.2.tgz", "integrity": "sha1-Ko8t33Du1WTf8tV/HhoTfZ8FB4s=", "dev": true, "dependencies": {"duplexer2": "0.0.2"}}, "node_modules/mute-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true}, "node_modules/nan": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.8.0.tgz", "integrity": "sha1-7XFfP+neArV6XmJS2QqWZ14fCFo=", "dev": true, "optional": true}, "node_modules/natives": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/natives/-/natives-1.1.1.tgz", "integrity": "sha512-8eRaxn8u/4wN8tGkhlc2cgwwvOLMLUMUn4IYTexMgWd+LyUDfeXVkk2ygQR0hvIHbJQXgHujia3ieUUDwNGkEA==", "deprecated": "This module relies on Node.js's internals and will break at some point. Do not use it, and update to graceful-fs@4.x.", "dev": true}, "node_modules/ncp": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/ncp/-/ncp-0.4.2.tgz", "integrity": "sha1-q8xsvT7C7Spyn/bnwfqPAXhKhXQ=", "dev": true, "bin": {"ncp": "bin/ncp"}}, "node_modules/negotiator": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.5.3.tgz", "integrity": "sha1-Jp1cR2gQ7JLtvntsLygxY4T5p+g=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/node-uuid": {"version": "1.4.8", "resolved": "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.8.tgz", "integrity": "sha1-sEDrCSOWivq/jTL7HxfxFn/auQc=", "deprecated": "Use uuid module instead", "dev": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/nopt": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "integrity": "sha1-xkZdvwirzU2zWTF/eaxopkayj/k=", "dev": true, "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/normalize-package-data": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.4.0.tgz", "integrity": "sha512-9jjUFbTPfEy3R/ad/2oNbKtW9Hgovl5O1FvFWKkKblNXoN/Oou6+9+KKohPK13Yc3/TyunyWhJp6gvRNR/PPAw==", "dev": true, "dependencies": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/oauth-sign": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha1-Rqarfwrq2N6unsBWV4C31O/rnUM=", "dev": true, "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-3.0.0.tgz", "integrity": "sha1-m+3VygiXlJvKR+f/QIBi1Un1h/I=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/object.defaults/-/object.defaults-1.1.0.tgz", "integrity": "sha1-On+GgzS0B96gbaFtiNXNKeQ1/s8=", "dev": true, "dependencies": {"array-each": "^1.0.1", "array-slice": "^1.0.0", "for-own": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults/node_modules/for-own": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/for-own/-/for-own-1.0.0.tgz", "integrity": "sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs=", "dev": true, "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.defaults/node_modules/isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/object.omit": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/object.omit/-/object.omit-2.0.1.tgz", "integrity": "sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=", "dev": true, "dependencies": {"for-own": "^0.1.4", "is-extendable": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.pick/node_modules/isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "dev": true, "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.1.tgz", "integrity": "sha1-ko9dD0cNSTQmUepnlLCFfBAGk/c=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/once/-/once-1.3.3.tgz", "integrity": "sha1-suJhVXzkwxTsgwTz+oJmPkKXyiA=", "dev": true, "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/onetime/-/onetime-1.1.0.tgz", "integrity": "sha1-ofeDj4MUxRbwXs78vEzP4EtO14k=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/optimist": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/optimist/-/optimist-0.6.1.tgz", "integrity": "sha1-2j6nRob6IaGaERwybpDrFaAZZoY=", "dev": true, "dependencies": {"minimist": "~0.0.1", "wordwrap": "~0.0.2"}}, "node_modules/optimist/node_modules/minimist": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz", "integrity": "sha1-3j+YVD2/lggr5IrRoMfNqDYwHc8=", "dev": true}, "node_modules/optionator": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.5.0.tgz", "integrity": "sha1-t1qJlaLUF98ltuTjhi9QqohlE2g=", "dev": true, "dependencies": {"deep-is": "~0.1.2", "fast-levenshtein": "~1.0.0", "levn": "~0.2.5", "prelude-ls": "~1.1.1", "type-check": "~0.3.1", "wordwrap": "~0.0.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/options": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/options/-/options-0.0.6.tgz", "integrity": "sha1-7CLTEoBrtT5zF3Pnza788cZDEo8=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/orchestrator": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/orchestrator/-/orchestrator-0.3.8.tgz", "integrity": "sha1-FOfp4nZPcxX7rBhOUGx6pt+UrX4=", "dev": true, "dependencies": {"end-of-stream": "~0.1.5", "sequencify": "~0.0.7", "stream-consume": "~0.1.0"}}, "node_modules/ordered-read-streams": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/ordered-read-streams/-/ordered-read-streams-0.1.0.tgz", "integrity": "sha1-/VZamvjrRHO6abbtijQ1LLVS8SY=", "dev": true}, "node_modules/os-homedir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/parse-filepath": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parse-filepath/-/parse-filepath-1.0.1.tgz", "integrity": "sha1-FZ1hVdQ5BNFsEO9piRHaHpGWm3M=", "dev": true, "dependencies": {"is-absolute": "^0.2.3", "map-cache": "^0.2.0", "path-root": "^0.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/parse-glob": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/parse-glob/-/parse-glob-3.0.4.tgz", "integrity": "sha1-ssN2z7EfNVE7rdFz7wu246OIORw=", "dev": true, "dependencies": {"glob-base": "^0.3.0", "is-dotfile": "^1.0.0", "is-extglob": "^1.0.0", "is-glob": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/parse-json": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "dev": true, "dependencies": {"error-ex": "^1.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/parse-passwd": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/parseurl": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.2.tgz", "integrity": "sha1-/CidTtiZMRlGDBViUyYs3I3mW/M=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz", "integrity": "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=", "dev": true, "dependencies": {"pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-is-inside": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "node_modules/path-parse": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.5.tgz", "integrity": "sha1-PBrfhx6pzWyUMbbqK9dKD/BVxME=", "dev": true}, "node_modules/path-root": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/path-root/-/path-root-0.1.1.tgz", "integrity": "sha1-mkpoFMrBwM1zNgqV8yCDyOpHRbc=", "dev": true, "dependencies": {"path-root-regex": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-root-regex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/path-root-regex/-/path-root-regex-0.1.2.tgz", "integrity": "sha1-v8zcjfWxLcUsi0PsONGNcsBLqW0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-type": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz", "integrity": "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/path-type/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/pathval": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/pathval/-/pathval-0.1.1.tgz", "integrity": "sha1-CPkRzcqczllCiA2ngXvAtyO2bYI=", "dev": true}, "node_modules/pause": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/pause/-/pause-0.1.0.tgz", "integrity": "sha1-68ikqGGf8LioGsFRPDQ0/0af23Q=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA=", "dev": true}, "node_modules/performance-now": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-0.2.0.tgz", "integrity": "sha1-M+8wxcd9TqIcWlOGnZG1bY8lVeU=", "dev": true, "optional": true}, "node_modules/phantomjs": {"version": "1.9.20", "resolved": "https://registry.npmjs.org/phantomjs/-/phantomjs-1.9.20.tgz", "integrity": "sha1-RCSsog4U0lXAsIia9va4lz2hDg0=", "dev": true, "hasInstallScript": true, "dependencies": {"extract-zip": "~1.5.0", "fs-extra": "~0.26.4", "hasha": "^2.2.0", "kew": "~0.7.0", "progress": "~1.1.8", "request": "~2.67.0", "request-progress": "~2.0.1", "which": "~1.2.2"}, "bin": {"phantomjs": "bin/phantomjs"}}, "node_modules/phantomjs/node_modules/async": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/async/-/async-2.6.0.tgz", "integrity": "sha512-xAfGg1/NTLBBKlHFmnd7PlmUW9KhVQIUuSrYem9xzFUZy13ScvtyGGejaae9iAVRiRq9+Cx7DPFaAAhCpyxyPw==", "dev": true, "dependencies": {"lodash": "^4.14.0"}}, "node_modules/phantomjs/node_modules/caseless": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz", "integrity": "sha1-cVuW6phBWTzDMGeSP17GDr2k99c=", "dev": true}, "node_modules/phantomjs/node_modules/commander": {"version": "2.12.2", "resolved": "https://registry.npmjs.org/commander/-/commander-2.12.2.tgz", "integrity": "sha512-BFnaq5ZOGcDN7FlrtBT4xxkgIToalIIxwjxLWVJ8bGTpe1LroqMiqQXdA7ygc7CRvaYS+9zfPGFnJqFSayx+AA==", "dev": true}, "node_modules/phantomjs/node_modules/form-data": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/form-data/-/form-data-1.0.1.tgz", "integrity": "sha1-rjFduaSQf6BlUCMEpm13M0de43w=", "dev": true, "dependencies": {"async": "^2.0.1", "combined-stream": "^1.0.5", "mime-types": "^2.1.11"}, "engines": {"node": ">= 0.10"}}, "node_modules/phantomjs/node_modules/har-validator": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz", "integrity": "sha1-zcvAgYgmWtEZtqWnyKtw7s+10n0=", "deprecated": "this library is no longer supported", "dev": true, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.4", "pinkie-promise": "^2.0.0"}, "bin": {"har-validator": "bin/har-validator"}, "engines": {"node": ">=0.10"}}, "node_modules/phantomjs/node_modules/lodash": {"version": "4.17.4", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz", "integrity": "sha1-eCA6TRwyiuHYbcpkYONptX9AVa4=", "dev": true}, "node_modules/phantomjs/node_modules/qs": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/qs/-/qs-5.2.1.tgz", "integrity": "sha1-gB/uAw4LlFDWOFrcSKTMVbRK7fw=", "dev": true, "engines": ">=0.10.40"}, "node_modules/phantomjs/node_modules/request": {"version": "2.67.0", "resolved": "https://registry.npmjs.org/request/-/request-2.67.0.tgz", "integrity": "sha1-ivdHgOK/EeoK6aqWXBHxGv0nJ0I=", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "dependencies": {"aws-sign2": "~0.6.0", "bl": "~1.0.0", "caseless": "~0.11.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~1.0.0-rc3", "har-validator": "~2.0.2", "hawk": "~3.1.0", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "node-uuid": "~1.4.7", "oauth-sign": "~0.8.0", "qs": "~5.2.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/phantomjs/node_modules/tough-cookie": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.2.tgz", "integrity": "sha1-yDoYMPTl7wuT7yo0iOck+N4Basc=", "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/phantomjs/node_modules/tunnel-agent": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "integrity": "sha1-Y3PbdpCf5XDgjXNYM2Xtgop07us=", "dev": true, "engines": {"node": "*"}}, "node_modules/phantomjs/node_modules/which": {"version": "1.2.14", "resolved": "https://registry.npmjs.org/which/-/which-1.2.14.tgz", "integrity": "sha1-mofEN48D6CfOyvGs31bHNsAcFOU=", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/pify": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pkginfo": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.4.1.tgz", "integrity": "sha1-tUGO8EOd5UJfxJlQQtztFPsqhP8=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/plato": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/plato/-/plato-1.7.0.tgz", "integrity": "sha1-mQCltJEKoZDeCKRbrmF1M0/WRqc=", "dev": true, "dependencies": {"eslint": "~3.0.1", "fs-extra": "~0.30.0", "glob": "~7.0.5", "jshint": "~2.9.2", "lodash": "~4.13.1", "posix-getopt": "~1.2.0", "typhonjs-escomplex": "0.0.9"}, "bin": {"plato": "bin/plato"}, "engines": {"node": ">= 4.4.5"}}, "node_modules/plato/node_modules/fs-extra": {"version": "0.30.0", "resolved": "http://registry.npmjs.org/fs-extra/-/fs-extra-0.30.0.tgz", "integrity": "sha1-8jP/zAjU2n1DLapEl3aYnbHfk/A=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^2.1.0", "klaw": "^1.0.0", "path-is-absolute": "^1.0.0", "rimraf": "^2.2.8"}}, "node_modules/plato/node_modules/glob": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/glob/-/glob-7.0.6.tgz", "integrity": "sha1-IRuvr0nlJbjNkyYNFKsTYVKz9Xo=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.2", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/plato/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/plato/node_modules/lodash": {"version": "4.13.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz", "integrity": "sha1-g+SxCRP0hJbU0W/sSlYK8u50S2g=", "dev": true}, "node_modules/plato/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/pluralize": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/pluralize/-/pluralize-1.2.1.tgz", "integrity": "sha1-0aIUg/0iu0HlihL6NCGCMUCJfEU=", "dev": true}, "node_modules/policyfile": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/policyfile/-/policyfile-0.0.4.tgz", "integrity": "sha1-1rgurZiueeviKOLa9ZAzEeyYLk0=", "dev": true, "engines": {"node": "*"}}, "node_modules/posix-getopt": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/posix-getopt/-/posix-getopt-1.2.0.tgz", "integrity": "sha1-Su7rfa3mb8qKk2XdqfawBXQctiE=", "dev": true, "engines": {"node": "*"}}, "node_modules/prelude-ls": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/preserve": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/preserve/-/preserve-0.2.0.tgz", "integrity": "sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pretty-hrtime": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/pretty-hrtime/-/pretty-hrtime-1.0.3.tgz", "integrity": "sha1-t+PqQkNaTJsnWdmeDyAesZWALuE=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/process-nextick-args": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "integrity": "sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M=", "dev": true}, "node_modules/progress": {"version": "1.1.8", "resolved": "https://registry.npmjs.org/progress/-/progress-1.1.8.tgz", "integrity": "sha1-4mDHj2Fhzdmw5WzD4Khd4Xx6V74=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/promise": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/promise/-/promise-7.3.1.tgz", "integrity": "sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==", "dev": true, "optional": true, "dependencies": {"asap": "~2.0.3"}}, "node_modules/prompt": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/prompt/-/prompt-0.2.14.tgz", "integrity": "sha1-V3VPZPVD/XsIRXB8gY7OYY8F/9w=", "dev": true, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.2.x", "winston": "0.8.x"}, "engines": {"node": ">= 0.6.6"}}, "node_modules/prr": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/prr/-/prr-0.0.0.tgz", "integrity": "sha1-GoS4WQgyVQFBGFPQCB7j+obikmo=", "dev": true, "optional": true}, "node_modules/punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true, "optional": true}, "node_modules/q": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/q/-/q-1.5.1.tgz", "integrity": "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=", "deprecated": "You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.\n\n(For a CapTP with native promises, see @endo/eventual-send and @endo/captp)", "dev": true, "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/qs": {"version": "6.4.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.4.0.tgz", "integrity": "sha1-E+JtKK1rD/qpExLNO/cI7TUecjM=", "dev": true, "optional": true, "engines": {"node": ">=0.6"}}, "node_modules/qunitjs": {"version": "1.14.0", "resolved": "https://registry.npmjs.org/qunitjs/-/qunitjs-1.14.0.tgz", "integrity": "sha1-x+5PZt4fDJd8YUgHqFDce796l/s=", "dev": true}, "node_modules/random-bytes": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/random-bytes/-/random-bytes-1.0.0.tgz", "integrity": "sha1-T2ih3Arli9P7lYSMMDJNt11kNgs=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/randomatic": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/randomatic/-/randomatic-1.1.7.tgz", "integrity": "sha512-D5JUjPyJbaJDkuAazpVnSfVkLlpeO3wDlPROTMLGKG1zMFNFRgrciKo1ltz/AzNTkqE0HzDx655QOL51N06how==", "dev": true, "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/randomatic/node_modules/is-number": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/randomatic/node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/randomatic/node_modules/kind-of": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/range-parser": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.3.tgz", "integrity": "sha1-aHKCNTXGkuLCoBA4Jq/YLC4P8XU=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.1.7", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.7.tgz", "integrity": "sha1-rf6s4uT7MJgFgBTQjActzFl1h3Q=", "dev": true, "dependencies": {"bytes": "2.4.0", "iconv-lite": "0.4.13", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/bytes": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.4.0.tgz", "integrity": "sha1-fZcZb51br39pNeJZhVSe3SpsIzk=", "dev": true}, "node_modules/raw-body/node_modules/iconv-lite": {"version": "0.4.13", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.13.tgz", "integrity": "sha1-H4irpKsLFQjoMSrMOTRfNumS4vI=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/rcfinder": {"version": "0.1.9", "resolved": "https://registry.npmjs.org/rcfinder/-/rcfinder-0.1.9.tgz", "integrity": "sha1-8+gPOH3fmugK4wpBADKWQuroERU=", "dev": true, "dependencies": {"lodash.clonedeep": "^4.3.2"}, "engines": {"node": ">=0.10"}}, "node_modules/rcloader": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/rcloader/-/rcloader-0.1.2.tgz", "integrity": "sha1-oJY6ZDfQnvjLktky0trUl7DRc2w=", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.", "dev": true, "dependencies": {"lodash": "~2.4.1", "rcfinder": "~0.1.6"}}, "node_modules/rcloader/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha1-+t2DS5aDBz2hebPq5tnA0VBT9z4=", "dev": true, "engines": ["node", "rhino"]}, "node_modules/read": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/read/-/read-1.0.7.tgz", "integrity": "sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ=", "dev": true, "dependencies": {"mute-stream": "~0.0.4"}, "engines": {"node": ">=0.8"}}, "node_modules/read-pkg": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz", "integrity": "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=", "dev": true, "dependencies": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz", "integrity": "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=", "dev": true, "dependencies": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/readdirp": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz", "integrity": "sha1-TtCtBg3zBzMAxIRANz9y0cxkLXg=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "minimatch": "^3.0.2", "readable-stream": "^2.0.2", "set-immediate-shim": "^1.0.1"}, "engines": {"node": ">=0.6"}}, "node_modules/readdirp/node_modules/graceful-fs": {"version": "4.1.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/readdirp/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/readdirp/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/readdirp/node_modules/readable-stream": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.3.tgz", "integrity": "sha512-m+qzzcn7KUxEmd1gMbchF+Y2eIUbieUaxkWtptyHywrX0rE8QEYqPC07Vuy4Wm32/xE16NcdBctb8S0Xe/5IeQ==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "safe-buffer": "~5.1.1", "string_decoder": "~1.0.3", "util-deprecate": "~1.0.1"}}, "node_modules/readdirp/node_modules/string_decoder": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.3.tgz", "integrity": "sha512-4AH6Z5fzNNBcH+6XDMfA/BTt87skxqJlO0lAh3Dker5zThcAxG6mKz+iGu308UKoPPQ8Dcqx/4JhujzltRa+hQ==", "dev": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/readline2": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/readline2/-/readline2-1.0.1.tgz", "integrity": "sha1-QQWWCP/BVHV7cV2ZidGZ/783LjU=", "dev": true, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "mute-stream": "0.0.5"}}, "node_modules/readline2/node_modules/mute-stream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.5.tgz", "integrity": "sha1-j7+rsKmKJT0xhDMfno3rc3L6xsA=", "dev": true}, "node_modules/rechoir": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=", "dev": true, "dependencies": {"resolve": "^1.1.6"}, "engines": {"node": ">= 0.10"}}, "node_modules/redent": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz", "integrity": "sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=", "dev": true, "dependencies": {"indent-string": "^2.1.0", "strip-indent": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/redis": {"version": "0.7.3", "resolved": "https://registry.npmjs.org/redis/-/redis-0.7.3.tgz", "integrity": "sha1-7le3pE0l7BWU5ENl2BZfp9HUgRo=", "dev": true, "optional": true, "engines": {"node": "*"}}, "node_modules/regex-cache": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/regex-cache/-/regex-cache-0.4.4.tgz", "integrity": "sha512-nVIZwtCjkC9YgvWkpM55B5rBhBYRZhAaJbgcFYXXsHnbZ9UZI9nnVWYZpBlCqv9ho2eZryPnWrZGsOdPwVWXWQ==", "dev": true, "dependencies": {"is-equal-shallow": "^0.1.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "node_modules/repeat-element": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.2.tgz", "integrity": "sha1-7wiaF40Ug7quTZPrmLT55OEdmQo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true, "engines": {"node": ">=0.10"}}, "node_modules/repeating": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz", "integrity": "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=", "dev": true, "dependencies": {"is-finite": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/replace-ext": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/replace-ext/-/replace-ext-0.0.1.tgz", "integrity": "sha1-KbvZIHinOfC8zitO5B6DeVNSKSQ=", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/request": {"version": "2.81.0", "resolved": "https://registry.npmjs.org/request/-/request-2.81.0.tgz", "integrity": "sha1-xpKJRqDgbF+Nb4qTM0af/aRimKA=", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "optional": true, "dependencies": {"aws-sign2": "~0.6.0", "aws4": "^1.2.1", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~2.1.1", "har-validator": "~4.2.1", "hawk": "~3.1.3", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "performance-now": "^0.2.0", "qs": "~6.4.0", "safe-buffer": "^5.0.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "^0.6.0", "uuid": "^3.0.0"}, "engines": {"node": ">= 4"}}, "node_modules/request-progress": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/request-progress/-/request-progress-2.0.1.tgz", "integrity": "sha1-XTa7V5YcZzqlt4jbyBQf3yO0Tgg=", "dev": true, "dependencies": {"throttleit": "^1.0.0"}}, "node_modules/request/node_modules/uuid": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.1.0.tgz", "integrity": "sha512-DIWtzUkw04M4k3bf1IcpS2tngXEL26YUD2M0tMDUpnUrz2hgzUBlD55a4FjdLGPvfHxS6uluGWvaVEqgBcVa+g==", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "dev": true, "optional": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/require-uncached": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/require-uncached/-/require-uncached-1.0.3.tgz", "integrity": "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=", "dev": true, "dependencies": {"caller-path": "^0.1.0", "resolve-from": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.5.0.tgz", "integrity": "sha512-hgoSGrc3pjzAPHNBg+KnFcK2HwlHTs/YrAGUr6qgTVUZmXv1UEXXl0bZNBKMA9fud6lRYFdPGz0xXxycPzmmiw==", "dev": true, "dependencies": {"path-parse": "^1.0.5"}}, "node_modules/resolve-dir": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/resolve-dir/-/resolve-dir-0.1.1.tgz", "integrity": "sha1-shklmlYC+sXFxJatiUpujMQwJh4=", "dev": true, "dependencies": {"expand-tilde": "^1.2.2", "global-modules": "^0.2.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-from": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz", "integrity": "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-url": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "deprecated": "https://github.com/lydell/resolve-url#deprecated", "dev": true}, "node_modules/response-time": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/response-time/-/response-time-2.3.2.tgz", "integrity": "sha1-/6cbq5UtYvfB1Jt0NDVfvGjf/Fo=", "dev": true, "dependencies": {"depd": "~1.1.0", "on-headers": "~1.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/response-time/node_modules/depd": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.1.tgz", "integrity": "sha1-V4O04cRZ8G+lyif5kfPQbnoxA1k=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/restore-cursor": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-1.0.1.tgz", "integrity": "sha1-NGYfRohjJ/7SmRR5FSJS35LapUE=", "dev": true, "dependencies": {"exit-hook": "^1.0.0", "onetime": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/revalidator": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.8.tgz", "integrity": "sha1-/s5hv6DBtSoga9axgZgYS91SOjs=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/right-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "integrity": "sha1-YTObci/mo1FWiSENJOFMlhSGE+8=", "dev": true, "dependencies": {"align-text": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.2.tgz", "integrity": "sha512-lreewLK/BlghmxtfH36YYVg1i8IAce4TI7oao75I1g245+6BctqTVQiBP3YUJ9C6DQOXJmkYR9X9fCLtCOJc5w==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "dependencies": {"glob": "^7.0.5"}, "bin": {"rimraf": "bin.js"}}, "node_modules/rimraf/node_modules/glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/rimraf/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/rndm": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/rndm/-/rndm-1.2.0.tgz", "integrity": "sha1-8z/pz7Urv9UgqhgyO8ZdsRCht2w=", "dev": true}, "node_modules/run-async": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/run-async/-/run-async-0.1.0.tgz", "integrity": "sha1-yK1KXhEGYeQCp9IbUw4AnyX444k=", "dev": true, "dependencies": {"once": "^1.3.0"}}, "node_modules/rx-lite": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/rx-lite/-/rx-lite-3.1.2.tgz", "integrity": "sha1-Gc5QLKVyZl87ZHsQk5+X/RYV8QI=", "dev": true}, "node_modules/safe-buffer": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "integrity": "sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==", "dev": true}, "node_modules/semver": {"version": "4.3.6", "resolved": "https://registry.npmjs.org/semver/-/semver-4.3.6.tgz", "integrity": "sha1-MAvG4OhjdPe6YQaLWx7NV/xlMto=", "dev": true, "bin": {"semver": "bin/semver"}}, "node_modules/send": {"version": "0.13.2", "resolved": "https://registry.npmjs.org/send/-/send-0.13.2.tgz", "integrity": "sha1-dl52B8gFVFK7pvCwUllTUJhgNt4=", "dev": true, "dependencies": {"debug": "~2.2.0", "depd": "~1.1.0", "destroy": "~1.0.4", "escape-html": "~1.0.3", "etag": "~1.7.0", "fresh": "0.3.0", "http-errors": "~1.3.1", "mime": "1.3.4", "ms": "0.7.1", "on-finished": "~2.3.0", "range-parser": "~1.0.3", "statuses": "~1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/send/node_modules/depd": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.1.tgz", "integrity": "sha1-V4O04cRZ8G+lyif5kfPQbnoxA1k=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/send/node_modules/mime": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/mime/-/mime-1.3.4.tgz", "integrity": "sha1-EV+eO2s9rylZmDyzjxSaLUDrXVM=", "dev": true, "bin": {"mime": "cli.js"}}, "node_modules/send/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/send/node_modules/statuses": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.2.1.tgz", "integrity": "sha1-3e1FzBglbVHtQK7BQkidXGECbSg=", "dev": true}, "node_modules/sequencify": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/sequencify/-/sequencify-0.0.7.tgz", "integrity": "sha1-kM/xnQLgcCf9dn9erT57ldHnOAw=", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/serve-favicon": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/serve-favicon/-/serve-favicon-2.3.2.tgz", "integrity": "sha1-3UGeJo3gEqtysxnTN/IQUBP5OB8=", "dev": true, "dependencies": {"etag": "~1.7.0", "fresh": "0.3.0", "ms": "0.7.2", "parseurl": "~1.3.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-favicon/node_modules/ms": {"version": "0.7.2", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "integrity": "sha1-riXPJRKziFodldfwN4aNhDESR2U=", "dev": true}, "node_modules/serve-index": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/serve-index/-/serve-index-1.7.3.tgz", "integrity": "sha1-egV/xu4o3GP2RWbl+lexEahq7NI=", "dev": true, "dependencies": {"accepts": "~1.2.13", "batch": "0.5.3", "debug": "~2.2.0", "escape-html": "~1.0.3", "http-errors": "~1.3.1", "mime-types": "~2.1.9", "parseurl": "~1.3.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/serve-index/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/serve-static": {"version": "1.10.3", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.3.tgz", "integrity": "sha1-zlpuzTEB/tXsCYJ9rCKpwpv7BTU=", "dev": true, "dependencies": {"escape-html": "~1.0.3", "parseurl": "~1.3.1", "send": "0.13.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-immediate-shim": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/set-immediate-shim/-/set-immediate-shim-1.0.1.tgz", "integrity": "sha1-SysbJ+uAip+NzEgaWOXlb1mfP2E=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/shelljs": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.3.0.tgz", "integrity": "sha1-NZbmMHp4FUT1kfN9phg2DzHbV7E=", "dev": true, "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/sigmund": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz", "integrity": "sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA=", "dev": true}, "node_modules/signal-exit": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true}, "node_modules/slice-ansi": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-0.0.4.tgz", "integrity": "sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/sntp": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz", "integrity": "sha1-ZUEYTMkK7qbG57NeJlkIJEPGYZg=", "deprecated": "This module moved to @hapi/sntp. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/socket.io": {"version": "0.9.16", "resolved": "https://registry.npmjs.org/socket.io/-/socket.io-0.9.16.tgz", "integrity": "sha1-O6sEROSbVfu8FXQk29Qao3WlGnY=", "dev": true, "dependencies": {"base64id": "0.1.0", "policyfile": "0.0.4", "socket.io-client": "0.9.16"}, "engines": {"node": ">= 0.4.0"}, "optionalDependencies": {"redis": "0.7.3"}}, "node_modules/socket.io-client": {"version": "0.9.16", "resolved": "https://registry.npmjs.org/socket.io-client/-/socket.io-client-0.9.16.tgz", "integrity": "sha1-TadRXF53MEHRtCOXBBW8xDDzX8Y=", "dev": true, "dependencies": {"active-x-obfuscator": "0.0.1", "uglify-js": "1.2.5", "ws": "0.4.x", "xmlhttprequest": "1.4.2"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/socket.io-client/node_modules/uglify-js": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-1.2.5.tgz", "integrity": "sha1-tULCx29477NLIAsgF3Y0Mw/3ArY=", "dev": true, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": "*"}}, "node_modules/source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.3.1.tgz", "integrity": "sha1-YQ9hIqRFuN1RU1oqcbeD38Ekh2E=", "deprecated": "See https://github.com/lydell/source-map-resolve#deprecated", "dev": true, "dependencies": {"atob": "~1.1.0", "resolve-url": "~0.2.1", "source-map-url": "~0.3.0", "urix": "~0.1.0"}}, "node_modules/source-map-url": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/source-map-url/-/source-map-url-0.3.0.tgz", "integrity": "sha1-fsrxO1e80J2opAxdJp2zN5nUqvk=", "deprecated": "See https://github.com/lydell/source-map-url#deprecated", "dev": true}, "node_modules/sparkles": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/sparkles/-/sparkles-1.0.0.tgz", "integrity": "sha1-Gsu/tZJDbRC76PeFt8xvgoFQEsM=", "dev": true, "engines": {"node": ">= 0.10"}}, "node_modules/spdx-correct": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-1.0.2.tgz", "integrity": "sha1-SzBz2TP/UfORLwOsVRlJikFQ20A=", "dev": true, "dependencies": {"spdx-license-ids": "^1.0.2"}}, "node_modules/spdx-expression-parse": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-1.0.4.tgz", "integrity": "sha1-m98vIOH0DtRH++JzJmGR/O1RYmw=", "dev": true}, "node_modules/spdx-license-ids": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-1.2.2.tgz", "integrity": "sha1-yd96NCRZSt5r0RkA1ZZpbcBrrFc=", "dev": true}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "node_modules/sshpk": {"version": "1.13.1", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.1.tgz", "integrity": "sha1-US322mKHFEMW3EwY/hzx2UBzm+M=", "dev": true, "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "dashdash": "^1.12.0", "getpass": "^0.1.1"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}, "optionalDependencies": {"bcrypt-pbkdf": "^1.0.0", "ecc-jsbn": "~0.1.1", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}}, "node_modules/sshpk/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/stack-trace": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz", "integrity": "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=", "dev": true, "engines": {"node": "*"}}, "node_modules/statuses": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.4.0.tgz", "integrity": "sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/stream-consume": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/stream-consume/-/stream-consume-0.1.0.tgz", "integrity": "sha1-pB6tGm1ggc63n2WwYZAbbY89HQ8=", "dev": true}, "node_modules/stream-counter": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/stream-counter/-/stream-counter-0.2.0.tgz", "integrity": "sha1-3tJmVWMZyLDiIoErnPOyb6fZR94=", "dev": true, "dependencies": {"readable-stream": "~1.1.8"}, "engines": {"node": ">=0.8.0"}}, "node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "node_modules/string-length": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/string-length/-/string-length-1.0.1.tgz", "integrity": "sha1-VpcPscOFWOnnC3KL894mmsRa36w=", "dev": true, "dependencies": {"strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/string-width": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stringstream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz", "integrity": "sha1-TkhM1N5aC7vuGORjB3EKioFiGHg=", "dev": true}, "node_modules/strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-bom": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-1.0.0.tgz", "integrity": "sha1-hbiGLzhEtabV7IRnqTWYFzo295Q=", "dev": true, "dependencies": {"first-chunk-stream": "^1.0.0", "is-utf8": "^0.2.0"}, "bin": {"strip-bom": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-indent": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz", "integrity": "sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=", "dev": true, "dependencies": {"get-stdin": "^4.0.1"}, "bin": {"strip-indent": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-json-comments": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.4.tgz", "integrity": "sha1-HhX7ysl9Pumb8tc7TGVrCCu6+5E=", "dev": true, "bin": {"strip-json-comments": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/table": {"version": "3.8.3", "resolved": "https://registry.npmjs.org/table/-/table-3.8.3.tgz", "integrity": "sha1-K7xULw/amGGnVdOUf+/Ys/UThV8=", "dev": true, "dependencies": {"ajv": "^4.7.0", "ajv-keywords": "^1.0.0", "chalk": "^1.1.1", "lodash": "^4.0.0", "slice-ansi": "0.0.4", "string-width": "^2.0.0"}}, "node_modules/table/node_modules/ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/table/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/table/node_modules/lodash": {"version": "4.17.4", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz", "integrity": "sha1-eCA6TRwyiuHYbcpkYONptX9AVa4=", "dev": true}, "node_modules/table/node_modules/string-width": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "dev": true, "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/table/node_modules/strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true}, "node_modules/throttleit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/throttleit/-/throttleit-1.0.0.tgz", "integrity": "sha1-nnhYNtr0Z0MUWlmEtiaNgoUorGw=", "dev": true}, "node_modules/through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=", "dev": true}, "node_modules/through2": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/through2/-/through2-2.0.3.tgz", "integrity": "sha1-AARWmzfHx0ujnEPzzteNGtlBQL4=", "dev": true, "dependencies": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "node_modules/through2/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/through2/node_modules/readable-stream": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.3.tgz", "integrity": "sha512-m+qzzcn7KUxEmd1gMbchF+Y2eIUbieUaxkWtptyHywrX0rE8QEYqPC07Vuy4Wm32/xE16NcdBctb8S0Xe/5IeQ==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "safe-buffer": "~5.1.1", "string_decoder": "~1.0.3", "util-deprecate": "~1.0.1"}}, "node_modules/through2/node_modules/string_decoder": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.3.tgz", "integrity": "sha512-4AH6Z5fzNNBcH+6XDMfA/BTt87skxqJlO0lAh3Dker5zThcAxG6mKz+iGu308UKoPPQ8Dcqx/4JhujzltRa+hQ==", "dev": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/tildify": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/tildify/-/tildify-1.2.0.tgz", "integrity": "sha1-3OwD9V3Km3qj5bBPIYF+tW5jWIo=", "dev": true, "dependencies": {"os-homedir": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/time-stamp": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/time-stamp/-/time-stamp-1.1.0.tgz", "integrity": "sha1-dkpaEa9QVhkhsTPztE5hhofg9cM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/tinycolor": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/tinycolor/-/tinycolor-0.0.1.tgz", "integrity": "sha1-MgtaUtg6u1l42Bo+iH1K77FaYWQ=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/tmp": {"version": "0.0.33", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "dev": true, "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/tough-cookie": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.3.tgz", "integrity": "sha1-C2GKVWW23qkL80JdBNVe3EdadWE=", "dev": true, "optional": true, "dependencies": {"punycode": "^1.4.1"}, "engines": {"node": ">=0.8"}}, "node_modules/trim-newlines": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz", "integrity": "sha1-WIeWa7WCpFA6QetST301ARgVphM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/tryit": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/tryit/-/tryit-1.0.3.tgz", "integrity": "sha1-OTvnMKlEb9Hq1tpZoBQwjzbCics=", "dev": true}, "node_modules/tsscmp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/tsscmp/-/tsscmp-1.0.5.tgz", "integrity": "sha1-fcSjOvcVgatDN9qR2FylQn69mpc=", "dev": true, "engines": {"node": ">=0.6.x"}}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dev": true, "optional": true, "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "dev": true, "optional": true}, "node_modules/type-check": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "dependencies": {"prelude-ls": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-is": {"version": "1.6.15", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.15.tgz", "integrity": "sha1-yrEPtJCeRByChC6v4a1kbIGARBA=", "dev": true, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.15"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "node_modules/typhonjs-ast-walker": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/typhonjs-ast-walker/-/typhonjs-ast-walker-0.1.1.tgz", "integrity": "sha1-gUVUptrSnhyyy2K8io6GwXTBaOM=", "dev": true}, "node_modules/typhonjs-escomplex": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/typhonjs-escomplex/-/typhonjs-escomplex-0.0.9.tgz", "integrity": "sha1-1Phd0oOOeiioVNnyVhbLxyo/Dg8=", "dev": true, "dependencies": {"babylon": "^6.0.0", "commander": "^2.0.0", "typhonjs-escomplex-module": "^0.0.9", "typhonjs-escomplex-project": "^0.0.9"}}, "node_modules/typhonjs-escomplex-commons": {"version": "0.0.14", "resolved": "https://registry.npmjs.org/typhonjs-escomplex-commons/-/typhonjs-escomplex-commons-0.0.14.tgz", "integrity": "sha1-V643xFegv6LSRHroJj4CKOK6wco=", "dev": true}, "node_modules/typhonjs-escomplex-module": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/typhonjs-escomplex-module/-/typhonjs-escomplex-module-0.0.9.tgz", "integrity": "sha1-31vDYLJg/zbi1pvFu0O3PvPzNlw=", "dev": true, "dependencies": {"escomplex-plugin-metrics-module": "^0.0.10", "escomplex-plugin-syntax-babylon": "^0.0.10", "typhonjs-ast-walker": "^0.1.0", "typhonjs-escomplex-commons": "^0.0.14", "typhonjs-plugin-manager": "^0.0.3"}}, "node_modules/typhonjs-escomplex-project": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/typhonjs-escomplex-project/-/typhonjs-escomplex-project-0.0.9.tgz", "integrity": "sha1-C6bwzDq6hiwjqXGpCa6rQR6+G/Q=", "dev": true, "dependencies": {"escomplex-plugin-metrics-project": "^0.0.10", "typhonjs-escomplex-commons": "^0.0.14", "typhonjs-escomplex-module": "^0.0.9", "typhonjs-plugin-manager": "^0.0.3"}}, "node_modules/typhonjs-plugin-manager": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/typhonjs-plugin-manager/-/typhonjs-plugin-manager-0.0.3.tgz", "integrity": "sha1-hN1eHQG0QRm95JPqZW3O+JJVq4Q=", "dev": true}, "node_modules/uglify-js": {"version": "2.8.29", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz", "integrity": "sha1-KcVzMUgFe7Th913zW3qcty5qWd0=", "dev": true, "dependencies": {"source-map": "~0.5.1", "yargs": "~3.10.0"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}, "optionalDependencies": {"uglify-to-browserify": "~1.0.0"}}, "node_modules/uglify-save-license": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/uglify-save-license/-/uglify-save-license-0.4.1.tgz", "integrity": "sha1-lXJsF8xv0XHDYX479NjYKqjEzOE=", "dev": true}, "node_modules/uglify-to-browserify": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "integrity": "sha1-bgkk1r2mta/jSeOabWMoUKD4grc=", "dev": true}, "node_modules/uid-safe": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/uid-safe/-/uid-safe-2.1.4.tgz", "integrity": "sha1-Otbzg2jG1MjHXsF2I/t5qh0HHYE=", "dev": true, "dependencies": {"random-bytes": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/unc-path-regex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/unc-path-regex/-/unc-path-regex-0.1.2.tgz", "integrity": "sha1-5z3T17DXxe2G+6xrCufYxqadUPo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/unique-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unique-stream/-/unique-stream-1.0.0.tgz", "integrity": "sha1-1ZpKdUJ0R9mqbJHnAmP40mpLEEs=", "dev": true}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/urix": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "deprecated": "Please see https://github.com/lydell/urix#deprecated", "dev": true}, "node_modules/user-home": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/user-home/-/user-home-1.1.1.tgz", "integrity": "sha1-K1viOjK2Onyd640PKNSFcko98ZA=", "dev": true, "bin": {"user-home": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/useragent": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/useragent/-/useragent-2.2.1.tgz", "integrity": "sha1-z1k+9PLRdYdei7ZY6pLhik/QbY4=", "dev": true, "dependencies": {"lru-cache": "2.2.x", "tmp": "0.0.x"}}, "node_modules/useragent/node_modules/lru-cache": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.2.4.tgz", "integrity": "sha1-bGWGGb7PFAMdDQtZSxYELOTcBj0=", "dev": true}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "node_modules/utile": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/utile/-/utile-0.2.1.tgz", "integrity": "sha1-kwyI6ZCY1iIINMNWy9mncFItkNc=", "dev": true, "dependencies": {"async": "~0.2.9", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.4.x", "rimraf": "2.x.x"}, "engines": {"node": ">= 0.6.4"}}, "node_modules/utils-merge": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.0.tgz", "integrity": "sha1-ApT7kiu5N1FTVBxPcJYjHyh8ivg=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/uuid/-/uuid-2.0.3.tgz", "integrity": "sha1-Z+LoY3lyFVMN/zGOW/nc6/1Hsho=", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "dev": true}, "node_modules/v8flags": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/v8flags/-/v8flags-2.1.1.tgz", "integrity": "sha1-qrGh+jDUX4jdMhFIh1rALAtV5bQ=", "dev": true, "dependencies": {"user-home": "^1.1.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/validate-npm-package-license": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz", "integrity": "sha1-KAS6vnEq0zeUWaz74kdGqywwP7w=", "dev": true, "dependencies": {"spdx-correct": "~1.0.0", "spdx-expression-parse": "~1.0.0"}}, "node_modules/vary": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/vary/-/vary-1.0.1.tgz", "integrity": "sha1-meSYFWaihhGN+yuBc1ffeZM3bRA=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "dev": true, "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/vhost": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/vhost/-/vhost-3.0.2.tgz", "integrity": "sha1-L7HezUxGaqiLD5NBrzPcGv8keNU=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/vinyl": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-0.5.3.tgz", "integrity": "sha1-sEVbOPxeDPMNQyUTLkYZcMIJHN4=", "dev": true, "dependencies": {"clone": "^1.0.0", "clone-stats": "^0.0.1", "replace-ext": "0.0.1"}, "engines": {"node": ">= 0.9"}}, "node_modules/vinyl-fs": {"version": "0.3.14", "resolved": "https://registry.npmjs.org/vinyl-fs/-/vinyl-fs-0.3.14.tgz", "integrity": "sha1-mmhRzhysHBzqX+hsCTHWIMLPqeY=", "dev": true, "dependencies": {"defaults": "^1.0.0", "glob-stream": "^3.1.5", "glob-watcher": "^0.0.6", "graceful-fs": "^3.0.0", "mkdirp": "^0.5.0", "strip-bom": "^1.0.0", "through2": "^0.6.1", "vinyl": "^0.4.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/vinyl-fs/node_modules/clone": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/clone/-/clone-0.2.0.tgz", "integrity": "sha1-xhJqkK1Pctv1rNskPMN3JP6T/B8=", "dev": true, "engines": {"node": "*"}}, "node_modules/vinyl-fs/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/vinyl-fs/node_modules/through2": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/through2/-/through2-0.6.5.tgz", "integrity": "sha1-QaucZ7KdVyCQcUEOHXp6lozTrUg=", "dev": true, "dependencies": {"readable-stream": ">=1.0.33-1 <1.1.0-0", "xtend": ">=4.0.0 <4.1.0-0"}}, "node_modules/vinyl-fs/node_modules/vinyl": {"version": "0.4.6", "resolved": "https://registry.npmjs.org/vinyl/-/vinyl-0.4.6.tgz", "integrity": "sha1-LzVsh6VQolVGHza76ypbqL94SEc=", "dev": true, "dependencies": {"clone": "^0.2.0", "clone-stats": "^0.0.1"}, "engines": {"node": ">= 0.9"}}, "node_modules/vinyl-sourcemaps-apply": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/vinyl-sourcemaps-apply/-/vinyl-sourcemaps-apply-0.2.1.tgz", "integrity": "sha1-q2VJ1h0XLCsbh75cUI0jnI74dwU=", "dev": true, "dependencies": {"source-map": "^0.5.1"}}, "node_modules/vow": {"version": "0.4.17", "resolved": "https://registry.npmjs.org/vow/-/vow-0.4.17.tgz", "integrity": "sha512-A3/9bWFqf6gT0jLR4/+bT+IPTe1mQf+tdsW6+WI5geP9smAp8Kbbu4R6QQCDKZN/8TSCqTlXVQm12QliB4rHfg==", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/vow-fs": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/vow-fs/-/vow-fs-0.3.6.tgz", "integrity": "sha1-LUxZviLivyYY3fWXq0uqkjvnIA0=", "dev": true, "dependencies": {"glob": "^7.0.5", "uuid": "^2.0.2", "vow": "^0.4.7", "vow-queue": "^0.4.1"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/vow-fs/node_modules/glob": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.2.tgz", "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/vow-fs/node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/vow-queue": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/vow-queue/-/vow-queue-0.4.3.tgz", "integrity": "sha512-/poAKDTFL3zYbeQg7cl4BGcfP4sGgXKrHnRFSKj97dteUFu8oyXMwIcdwu8NSx/RmPGIuYx1Bik/y5vU4H/VKw==", "dev": true, "dependencies": {"vow": "^0.4.17"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/when": {"version": "3.7.8", "resolved": "https://registry.npmjs.org/when/-/when-3.7.8.tgz", "integrity": "sha1-xxMLan6gRpPoQs3J56Hyqjmjn4I=", "dev": true}, "node_modules/which": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/which/-/which-1.3.0.tgz", "integrity": "sha512-xcJpopdamTuY5duC/KnTTNBraPK54YwpenP4lzxU8H91GudWpFv38u0CKjclE1Wi2EH2EDz5LRcHcKbCIzqGyg==", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/window-size": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "integrity": "sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/winston": {"version": "0.8.3", "resolved": "https://registry.npmjs.org/winston/-/winston-0.8.3.tgz", "integrity": "sha1-ZLar9M0Brcrv1QCTk7HY6L7BnbA=", "dev": true, "dependencies": {"async": "0.2.x", "colors": "0.6.x", "cycle": "1.0.x", "eyes": "0.1.x", "isstream": "0.1.x", "pkginfo": "0.3.x", "stack-trace": "0.0.x"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/winston/node_modules/colors": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/colors/-/colors-0.6.2.tgz", "integrity": "sha1-JCP+ZnisDF2uiFLl0OW+CMmXq8w=", "dev": true, "engines": {"node": ">=0.1.90"}}, "node_modules/winston/node_modules/pkginfo": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.3.1.tgz", "integrity": "sha1-Wyn2qB9wcXFC4J52W76rl7T4HiE=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/wordwrap": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "node_modules/write": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/write/-/write-0.2.1.tgz", "integrity": "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=", "dev": true, "dependencies": {"mkdirp": "^0.5.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ws": {"version": "0.4.32", "resolved": "https://registry.npmjs.org/ws/-/ws-0.4.32.tgz", "integrity": "sha1-eHphVEFPPJntg8V3IVOyD+sM7DI=", "dev": true, "hasInstallScript": true, "dependencies": {"commander": "~2.1.0", "nan": "~1.0.0", "options": ">=0.0.5", "tinycolor": "0.x"}, "bin": {"wscat": "bin/wscat"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ws/node_modules/commander": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.1.0.tgz", "integrity": "sha1-0SG7roYNmZKj1Re6lvVliOR8Z4E=", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/ws/node_modules/nan": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/nan/-/nan-1.0.0.tgz", "integrity": "sha1-riT4hQgY1mL8q1rPfzuVv6oszzg=", "dev": true}, "node_modules/xmlbuilder": {"version": "2.6.5", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-2.6.5.tgz", "integrity": "sha1-b/etYPty0idk8AehZLd/K/FABSY=", "dev": true, "dependencies": {"lodash": "^3.5.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/xmlbuilder/node_modules/lodash": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha1-W/Rejkm6QYnhfUgnid/RW9FAt7Y=", "dev": true}, "node_modules/xmlhttprequest": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/xmlhttprequest/-/xmlhttprequest-1.4.2.tgz", "integrity": "sha1-AUU6HZvtHo8XL2SVu/TIxCYyFQA=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/xtend": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68=", "dev": true, "engines": {"node": ">=0.4"}}, "node_modules/yargs": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "integrity": "sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=", "dev": true, "dependencies": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}}, "node_modules/yauzl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.1.tgz", "integrity": "sha1-lSj0QtqxsihOWLQ3m7GU4i4MQAU=", "dev": true, "dependencies": {"fd-slicer": "~1.0.1"}}, "node_modules/zeparser": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/zeparser/-/zeparser-0.0.5.tgz", "integrity": "sha1-A3JlYbwmjy5URPVMZlt/1KjAKeI=", "dev": true, "engines": {"node": "*"}}}}