﻿<style>
    #modalNhaThuocDanhSach label:hover {
        color: var(--escs-main-theme-color);
    }
</style>
<div id="modalNhaThuoc" class="modal-drag" style="width:400px; z-index:9999999;  margin-top: 5px !important; margin-left: -11px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn nhà thuốc</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_NhaThuoc" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalNhaThuocElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalNhaThuocDanhSach">

            </div>
        </div>
    </div>
    @*<div class="modal-drag-footer">
            <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonNhaThuoc">
                <i class="fas fa-save mr-2"></i> Chọn
            </button>
        </div>*@
</div>

<script type="text/html" id="modalNhaThuocDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) {
        if(item.mst != '' && item.mst != null)
        {
            item.mst = ' - MST:' + item.mst
        }
     %>
    <div class="custom-control custom-checkbox dsnt" id="dsnt_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="nha_thuoc_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalNhaThuocItem single_checked" onchange="onChonNhaThuoc(this);">
        <label class="custom-control-label" style="cursor:pointer;" for="nha_thuoc_<%- item.ma %>"><%- item.ten %><br /><%- item.mst %> - Địa chỉ: <%- item.dia_chi %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<div id="modalLHNV_tru_lui" class="modal-drag" style="width:500px; z-index:9999999;  margin-top: 5px !important; margin-left: -11px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn quyền lợi trừ lùi</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_LHNV_tru_lui" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modaLHNV_tru_lui_ElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:350px;" id="modalLHNV_tru_lui_DanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonLHNV_tru_lui">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalLHNV_tru_lui_DanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <% var pd = 0
        if (item.cap !=  1){
            pd = (item.cap-1) * 10;
        } %>
    <div class="custom-control custom-checkbox dsnt" id="dsnt_<%- item.lh_nv.replace(/\./g, '') %>" data-text="<%- item.ten.toLowerCase() %>" style="">
        <input type="checkbox" id="lhnv_<%- item.lh_nv %>" value="<%- item.lh_nv %>" class="custom-control-input modalLHNV_tru_lui_Item">
        <label class="custom-control-label" style="font-style: italic; cursor:pointer; padding-left: <%- pd %>px" for="lhnv_<%- item.lh_nv %>"> <%- item.ten_hien_thi %> (<%- item.lh_nv %>)</label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
<div id="modalLHNV_phu_thuoc" class="modal-drag" style="width:400px; z-index:9999999;  margin-top: 5px !important; margin-left: -11px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn quyền lợi phụ thuộc</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_LHNV_phu_thuoc" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modaLHNV_phu_thuoc_ElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLHNV_phu_thuoc_DanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonLHNV_phu_thuoc">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalLHNV_phu_thuoc_DanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsnt" id="dsnt_<%- item.lh_nv.replace(/\./g, '') %>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="phu_thuoc_<%- item.lh_nv %>" value="<%- item.lh_nv %>" class="custom-control-input modalLHNV_phu_thuoc_Item">
        <label class="custom-control-label" style="cursor:pointer;" for="phu_thuoc_<%- item.lh_nv %>">(<%- item.lh_nv %>) <%- item.ten_hien_thi %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>