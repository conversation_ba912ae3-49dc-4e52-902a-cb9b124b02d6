﻿<div id="modalDSKieuAD" class="modal-drag" style="width:350px; z-index:9999999;">
    <div class="modal-drag-header border-bottom px-2">
        <h5><span class="modal-drag-title px-2">Ch<PERSON><PERSON> kiểu áp dụng</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12 mt-2 d-flex scrollable" style="max-height:250px; flex-wrap: wrap" id="modalDSKieuADDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer border-top">
        <button type="button" class="btn btn-primary btn-sm wd-80" id="btnChonKieuAD">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalDSKieuADDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox col-12" data-text="">
        <input type="checkbox" id="kieu_ad_<%- item.kieu_ad %>" value="<%- item.kieu_ad %>" class="custom-control-input modalDSKieuADItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="kieu_ad_<%- item.kieu_ad %>"><%- item.kieu_ad_ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>