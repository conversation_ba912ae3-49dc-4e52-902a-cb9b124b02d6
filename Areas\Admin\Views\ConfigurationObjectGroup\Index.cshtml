﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục nhóm đối tượng";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON>h mục nhóm đối tượng</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Nhóm đối tượng</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" autocomplete="off" name="tim" id="tim" placeholder="Nhập thông tin mã/tên nhóm đối tượng" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThemMoi">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridView" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapThongTin" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTin" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin danh mục nhóm đối tượng</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã</label>
                                <input type="text" maxlength="30" name="ma" autocomplete="off" required class="form-control" placeholder="Mã đối tượng">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Thứ tự hiển thị</label>
                                <input type="text" min="0" maxlength="5" autocomplete="off" placeholder="Thứ tự hiển thị" name="stt" class="form-control decimal">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px;">
                        <div class="col-sm-8">
                            <div class="form-group">
                                <label class="_required">Tên</label>
                                <input type="text" maxlength="250" name="ten" autocomplete="off" required class="form-control" placeholder="Tên đối tượng">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="N">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>                    
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTin"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-90" id="btnXoaThongTin"><i class="fas fa-trash-alt"></i> Xóa</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ConfigurationObjectGroupService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ConfigurationObjectGroup.js" asp-append-version="true"></script>
}
