﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình loại đối tượng";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình loại đối tượng</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình loại đối tượng</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>

                        <div class="col-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" spellcheck="false" placeholder="Nhập mã/tên loại đối tượng" class="form-control">
                            </div>
                        </div>
                        <div class="col-2 ml-auto" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-48p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-49p" title="Thêm mới" id="btnNhap">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewPhanTrang" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalNhap" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nhập thông tin loại đối tượng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form name="frmSave" method="post" class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="_required">Đối tác</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;" required></select>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label class="_required">Mã loại đối tượng</label>
                            <input type="text" name="ma" autocomplete="off" class="form-control" required>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label class="_required">Tên loại đối tượng</label>
                            <input type="text" name="ten" autocomplete="off" class="form-control" required>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label>Thứ tự hiển thị</label>
                            <input type="number" name="stt" min="1" step="1" autocomplete="off" class="form-control">
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label class="_required">Trạng thái</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;" required>
                                <option>Chọn trạng thái</option>
                                <option value="D">Đang sử dụng</option>
                                <option value="K">Ngừng sử dụng</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-start">
                <button type="button" class="btn btn-outline-primary btn-sm wd-80 mr-auto" id="btnXoaThongTin">
                    <i class="fas fa-trash-alt mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80 " id="btnLuuThongTin">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 " id="btnLuuDongThongTin">
                    <i class="fas fa-save mr-2"></i>Lưu và đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80 " data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/LoaiDoiTuongService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/LoaiDoiTuong.js" asp-append-version="true"></script>
}