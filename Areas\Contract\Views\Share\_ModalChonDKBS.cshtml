﻿<div id="modalChonDKBS" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5><span class="modal-drag-title">Chọ<PERSON> đ<PERSON><PERSON><PERSON><PERSON><PERSON> bổ sung</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonDKBSElementSelect">
                
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonDKBSDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonDKBS">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalChonDKBSDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox" data-text="">
        <input type="checkbox" id="dkbs_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonDKBSItem">
        <label class="custom-control-label" style="cursor:pointer;" for="dkbs_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>