﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình đa ngôn ngữ";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình đa ngôn ngữ</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình đa ngôn ngữ</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <input type="hidden" name="ma_doi_tac" />
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="" class="form-control">
                            </div>
                        </div>
                        <div class="col-6 col-md-3 col-lg-2 ml-auto" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-48p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-49p" title="Thêm mới" id="btnNhap">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridView" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhap" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="min-width:65vw;">
        <div class="modal-content col-md-11" style="margin-left: 55px;">
            <form name="frmLuu" method="post">
                <div class="modal-header" style="padding: 10px 5px;">
                    <h4 class="modal-title">Nhập cấu hình</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body" style="padding: 10px 5px;">
                    <input type="hidden" name="ma_doi_tac" />
                    <div class="row" style="user-select:none;">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Mã thực thể</label>
                                <input type="text" class="form-control" name="ma_entity" required />
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Tên thực thể</label>
                                <input type="text" class="form-control" name="ten_entity" required />
                            </div>
                        </div>
                        <div class="col-12 mt-2">
                            <div class="table-responsive" style="max-height:420px;">
                                <table class="table table-sm table-bordered">
                                    <thead class="bg-primary text-center text-white sticky-top">
                                        <tr>
                                            <th scope="col" style="width:100px;">Khóa chính</th>
                                            <th scope="col" style="width:250px;">Mã thuộc tính</th>
                                            <th scope="col">Tên thuộc tính</th>
                                            <th scope="col" style="width:50px;"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="modalNhap_grid">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="padding: 10px 5px; display: block;">
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinConfig"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinConfig"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-primary btn-sm wd-150 float-right" onclick="_modalNhap_grid_newRow()"><i class="fa fa-plus"></i> Thêm thuộc tính</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/MultipleLanguageService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/MultipleLanguage.js" asp-append-version="true"></script>
}