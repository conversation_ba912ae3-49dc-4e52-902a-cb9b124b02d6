﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục hiệu suất xử lý";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<style>
    .rowSelected {
        background-color: #f4f4f4;
    }

    .rowSelected input {
        background-color: #f4f4f4 !important;
    }
</style>

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Hi<PERSON><PERSON> suất xử lý cán bộ</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Hi<PERSON><PERSON> suất xử lý</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập tên/mã cán bộ" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Chi nhánh</label>
                                <select class="select2 form-control custom-select" required name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required name="nv" style="width: 100%; height: 36px;">
                                    <option value="">Chọn nghiệp vụ</option>
                                    <option value="XE">Xe ô tô</option>
                                    <option value="XE_MAY">Xe máy</option>
                                    <option value="NG">Con người</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" display-format="date" value-format="number" name="ngay_ad" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThem">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewHieuSuatLamViec" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalThemHieuSuatXuLy" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:1100px">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình hiệu suất xử lý</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThemCauHinh" name="frmThemCauHinh" novalidate="novalidate" method="post">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required="" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã chi nhánh</label>
                                <select class="select2 form-control custom-select" required="" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác quản lý</label>
                                <select class="select2 form-control custom-select" required="" name="ma_doi_tac_ql" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required="" name="nv" style="width: 100%; height: 36px;">
                                    <option value="XE">Xe ô tô</option>
                                    <option value="XE_MAY">Xe máy</option>
                                    <option value="NG">Con người</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" autocomplete="off" required="" class="form-control datepicker tu_ngay" name="ngay_ad" display-format="date" value-format="number" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height: 350px">
                            <table id="tblCauHinhXe" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center uppercase">
                                        <th width="4%">STT</th>
                                        <th width="71%">Cán bộ xử lý</th>
                                        <th width="25%">Hệ số</th>
                                    </tr>
                                </thead>
                                <tbody id="cau_hinh_he_so"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display: block;">
                <button type="button" class="btn btn-outline-primary btn-sm wd-80 float-left" id="btnXoaCauHinh"><i class="fas fa-trash-alt"></i> Xóa</button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinh"><i class="fa fa-save"></i> Lưu</button>
            </div>
        </div>
    </div>
</div>

<partial name="_Template.cshtml" />

@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ProcessingFactorService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ProcessingFactor.js" asp-append-version="true"></script>
}