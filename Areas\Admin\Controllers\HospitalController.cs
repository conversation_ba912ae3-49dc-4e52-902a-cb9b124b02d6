﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class HospitalController : BaseController
    {
        private readonly IWebHostEnvironment _env;

        public HospitalController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> GetAll()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_CACHE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_NH, json);
            //var dataFile = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_BV_CACHE, json);
            //string jsonFileData = JsonConvert.SerializeObject(dataFile);
            //System.IO.File.WriteAllText(System.IO.Path.Combine(_env.WebRootPath, @"data/benh_vien.json"), jsonFileData);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Delete()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_BENH_VIEN_IMPORT_EXCEL, json);
            return Ok(data);
        }

        /// <summary>
        /// Lưu liên hệ cơ sở y tế
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> LuuLienHeCSYT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_LHE_NH, json);
            return Ok(data);
        }

        /// <summary>
        /// Xóa liên hệ cơ sở y tế
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> XoaLienHeCSYT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_LHE_X, json);
            return Ok(data);
        }

        /// <summary>
        /// Lấy danh sách liên hệ cơ sở y tế
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> layTTLienHeCSYT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_BENH_VIEN_LHE_TKIEM, json);
            return Ok(data);
        }
    }
}