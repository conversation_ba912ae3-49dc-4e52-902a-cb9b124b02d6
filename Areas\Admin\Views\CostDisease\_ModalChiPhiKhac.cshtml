﻿<div id="modalChiPhiKhac" class="modal-drag" style="width:365px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5><span class="modal-drag-title">Chọn chi phí kh<PERSON>c</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_ChiPhiKhac" type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChiPhiKhacElementSelect">
                <input type="hidden" id="modalChiPhiKhac_MaBenh">
            </div>
            <div class="col-12 mt-2 scrollable" style="height:250px;" id="modalChiPhiKhacDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonChiPhiKhac">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalChiPhiKhacDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <% if(item.ten_ct == null){ %>
    <div class="custom-control custom-checkbox dscpk" id="dscpk_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="chi_phi_khac_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChiPhiKhacItem">
        <label class="custom-control-label" style="cursor:pointer;" for="chi_phi_khac_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% }else{ %>
    <div class="custom-control custom-checkbox dscpk" id="dscpk_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="chi_phi_khac_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChiPhiKhacItem">
        <label class="custom-control-label" style="cursor:pointer;" for="chi_phi_khac_<%- item.ma %>"><%- item.ten_ct %> / <%- item.ten %></label>
    </div>
    <% } %>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>