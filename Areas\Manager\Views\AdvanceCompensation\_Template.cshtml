﻿<script type="text/html" id="tblTamUngBT_template">
    <% if(data.length > 0){
    _.forEach(data, function(item, index) { %>
    <tr class="row_item">
        <td class="text-center">
            <input type="hidden" data-field="ma_doi_tac" value="<%- item.ma_doi_tac %>" />
            <input type="hidden" data-field="ma_chi_nhanh" value="<%- item.ma_chi_nhanh %>" />
            <div class="custom-control custom-checkbox" style="padding-left: 1.9rem;">
                <input type="checkbox" <% if(item.checked){ %>checked<% } %> data-field="so_id" value="<%- item.so_id%>" onchange="onChon(this)" id="ttbt_<%- item.so_id%>" class="custom-control-input ttbt_item single_checked">
                <label class="custom-control-label" style="cursor:pointer;" for="ttbt_<%- item.so_id%>">&nbsp;</label>
            </div>
        </td>
        <td class="text-center"><%= item.nv_hthi %></td>
        <td class="text-center"><%= item.ngay_ht %></td>
        <td class="text-center"><%= item.trang_thai %></td>
        <td class="text-center"><%- item.so_hs %></td>
        <td class="text-center"><%- item.doi_tuong %></td>
        <td class="text-center"><%- item.nsd %></td>
        <td><%- item.ten_kh %></td>
        <td class="text-center"><%- item.ma_chi_nhanh %></td>
        <td class="text-center"><%- item.ma_chi_nhanh_ql %></td>
    </tr>
    <% })} %>

    <% if(data.length < 5){
    for(var i = 0; i < 5 - data.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 50px; text-align: center; height: 20px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblSoTienTamUngTheoLhnv_template">
    <% if (data.length > 0) {
    _.forEach(data, function(item,index) { %>
    <tr class="row_item">
        <td class="text-center"><%- index + 1%></td>
        <td class="text-left">
            <input type="hidden" data-name="lh_nv" data-field="lh_nv" data-val="<%- item.lh_nv %>" value="<%- item.lh_nv %>" />
            <%- item.ten_lh_nv %> (<%- item.lh_nv %>)
        </td>
        <td class="text-right">
            <input type="text" data-name="tien" autocomplete="off" data-field="tien" data-val="<%- item.tien %>" class="floating-input number" value="<%- ESUtil.formatMoney(item.tien) %>" />
        </td>
    </tr>
    <% })} %>

    <% if(data.length < 3){
    for(var i = 0; i < 3 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35.5px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>