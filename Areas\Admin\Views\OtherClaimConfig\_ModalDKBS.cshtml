﻿<style>
    .text {
        color: red;
    }
</style>
<div id="modalGiamTruDkbs" class="modal-drag" style="width:500px; z-index:9999999; margin-top: -360px !important; margin-left: 156px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn thông tin điều kho<PERSON>n b<PERSON> sung</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_dkbs" type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control item-dkbs">
                <input type="hidden" id="modalDkbsElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalDkbsDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonDkbs">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalDkbsDanhSachTemplate">
    <% if(ds_giam_tru_dkbs.length > 0){
    _.forEach(ds_giam_tru_dkbs, function(item,index) { %>
    <div class="custom-control custom-checkbox dkbs" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="dkbs_<%- item.ma %>" data-field="dkbs" value="<%- item.ma %>" class="custom-control-input item-dkbs modalDkbsItem">
        <label class="custom-control-label" style="cursor:pointer;" for="dkbs_<%- item.ma %>"><%- item.ten %> </label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>