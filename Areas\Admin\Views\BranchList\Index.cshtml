﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục mã đối tác chi nhánh";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Quản lý đơn vị</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Đơn vị</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin </label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin tìm kiếm" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="0">Ngừng sử dụng</option>
                                    <option value="1">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapThongTinMaDoiTac_chi_nhanh">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewMaDoiTac_chi_nhanh" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bs-example-modal-lg" id="modalNhapChiNhanh" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width:unset; width:60%;">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Thông tin chi nhánh đối tác <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body" style="padding-top:1px;">
                <div class="row">
                    <div class="col-md-12" id="navThongTinChiNhanh">
                        <ul class="nav nav-pills" role="tablist" style="background-color:#f8f9fa">
                            <li class="nav-item " style="font-weight:bold">
                                <a class="nav-link active" id="home-tab" data-toggle="tab" href="#tabThongTinChiNhanh" role="tab" aria-controls="home" aria-selected="true">
                                    <i class="fas fa-info-circle mr-2"></i> Thông tin chi nhánh
                                </a>
                            </li>
                            @*<li class="nav-item" style="font-weight:bold">
                                <a class="nav-link" id="btnChuKiSo" data-toggle="tab" href="#tabChuKiSo" role="tab" aria-controls="profile" aria-selected="false">
                                    <i class="fas fa-signature mr-2"></i> Chữ kí số
                                </a>
                            </li>*@
                            <li class="nav-item" style="font-weight:bold">
                                <a class="nav-link" id="profile-tab2" data-toggle="tab" href="#tabThongTinTaiKhoan" role="tab" aria-controls="profile" aria-selected="false">
                                    <i class="far fa-credit-card mr-2"></i> Tài khoản chi nhánh
                                </a>
                            </li>
                            <li class="nav-item" style="font-weight:bold">
                                <a class="nav-link" id="profile-tab3" data-toggle="tab" href="#tabPhanCongXuLy" role="tab" aria-controls="profile" aria-selected="false">
                                    <i class="far fa-folder-tree mr-2"></i> Phân công xử lý hồ sơ
                                </a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active" id="tabThongTinChiNhanh" role="tabpanel" aria-labelledby="home-tab">
                                <form name="frmLuuThongTinChiNhanh" method="post">
                                    <div class="row" style="margin-top:5px;">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label class="_required">Đối tác</label>
                                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="_required">Mã</label>
                                                <input type="text" maxlength="20" required name="ma" autocomplete="off" placeholder="Mã chi nhánh" class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-sm-5">
                                            <div class="form-group">
                                                <label class="_required">Tên</label>
                                                <input type="text" maxlength="250" name="ten" autocomplete="off" placeholder="Tên chi nhánh" required class="form-control">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top:5px;">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label class="_required">Tên tắt</label>
                                                <input type="text" maxlength="100" autocomplete="off" placeholder="Tên viết tắt" name="ten_tat" required class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="_required">Tên tiếng anh</label>
                                                <input type="text" maxlength="100" name="ten_e" autocomplete="off" placeholder="Tên tiếng anh" required class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-sm-5">
                                            <div class="form-group">
                                                <label class="_required">Địa chỉ</label>
                                                <div class="input-group">
                                                    <input type="text" maxlength="250" class="form-control" autocomplete="off" placeholder="Địa chỉ" required="" name="dchi">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4 d-none">
                                            <div class="form-group">
                                                <label class="_required">Số tài khoản</label>
                                                <div class="input-group">
                                                    <input type="text" maxlength="20" name="so_tk" autocomplete="off" placeholder="Số tài khoản" class="form-control text">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-8 d-none" style="margin-top: 10px">
                                            <div class="form-group">
                                                <label class="_required">Ngân hàng</label>
                                                <select class="select2 form-control custom-select" autocomplete="off" name="ngan_hang" style="width: 100%; height:36px;"></select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top:5px;">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label class="_required">Tỉnh/thành</label>
                                                <select class="select2 form-control custom-select" required name="tinh_thanh" style="width:100%"></select>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="">Phường/xã</label>
                                                <select class="select2 form-control custom-select" name="quan_huyen" style="width:100%"></select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top:5px;">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label class="_required">Mã số thuế</label>
                                                <input type="text" maxlength="20" name="mst" autocomplete="off" required="" class="form-control text" placeholder="Mã số thuế">
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="_required">Điện thoại</label>
                                                <input type="text" min="10" maxlength="15" name="d_thoai" required="" autocomplete="off" class="form-control text" placeholder="Số điện thoại">
                                            </div>
                                        </div>
                                        <div class="col-sm-5">
                                            <div class="form-group">
                                                <div class="form-group">
                                                    <label class="_required">Email</label>
                                                    <div class="input-group">
                                                        <input type="text" fn-validate="validateEmailControl" name="email" autocomplete="off" maxlength="50" required="" class="form-control email-inputmask" im-insert="true" placeholder="Email">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top:5px;">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label class="">Mã cấp trên</label>
                                                <select class="select2 form-control custom-select" name="ma_ct" style="width: 100%; height:36px;">
                                                    <option value="">Chọn mã cấp trên</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="_required">Loại</label>
                                                <select class="select2 form-control custom-select" required name="loai" style="width: 100%; height:36px;">
                                                    <option value="">Chọn loại</option>
                                                    <option value="CN">Chi nhánh</option>
                                                    <option value="KV">Khu vực</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-5">
                                            <div class="form-group">
                                                <label class="_required">Trạng thái</label>
                                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;">
                                                    <option value="">Chọn trạng thái</option>
                                                    <option value="0">Ngừng sử dụng</option>
                                                    <option value="1">Đang sử dụng</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" style="margin-top:5px;">
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label>Mã kế toán</label>
                                                <input type="text" maxlength="250" name="ma_ktoan" class="form-control text" placeholder="Mã kế toán">
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label>Nơi nhận hóa đơn</label>
                                                <input type="text" maxlength="250" name="noi_nhan_hdon" class="form-control text" placeholder="Nơi nhận hóa đơn GTGT">
                                            </div>
                                        </div>
                                        <div class="col-sm-5">
                                            <div class="form-group">
                                                <label>Địa chỉ nhận hóa đơn</label>
                                                <input type="text" maxlength="250" name="dchi_nhan_hdon" autocomplete="off" class="form-control text" placeholder="Địa chỉ nhận hóa đơn">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="">Đơn vị nhận hóa đơn</label>
                                                <select class="select2 form-control custom-select" name="ma_dvi_nhan_hdon" style="width: 100%; height:36px;">
                                                    <option value="">Chọn đơn vị nhận hóa đơn</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label class="">Xuất hóa đơn về đơn vị</label>
                                                <select class="select2 form-control custom-select" name="ma_dvi_xuat_hdon" style="width: 100%; height:36px;">
                                                    <option value="">Xuất hóa đơn về đơn vị</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer" style="display: block; margin-top: 15px; padding:10px 0 0 0;">
                                        @*<button type="button" class="btn btn-primary btn-sm wd-110 float-left text-nowrap" id="btnChuKiSo"><i class="fas fa-signature mr-1"></i> Chữ kí số</button>*@
                                        <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                                        <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinMaDoiTac_chi_nhanh"><i class="fa fa-save"></i> Lưu</button>
                                        @*<button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinMaDoiTac_chi_nhanh"><i class="fas fa-trash-alt"></i> Xóa</button>*@
                                    </div>
                                </form>
                            </div>
                            <div class="tab-pane" id="tabChuKiSo" role="tabpanel" aria-labelledby="home-tab">
                                <form class="row" name="frmNhapChuKiSo" method="post" novalidate="novalidate">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="">Người tạo</label>
                                            <div class="input-group">
                                                <input type="text" name="nsd_tao" class="form-control" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="">Ngày tạo</label>
                                            <div class="input-group">
                                                <input type="text" name="ngay_tao" class="form-control" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="">Người cập nhật</label>
                                            <div class="input-group">
                                                <input type="text" name="nsd_sua" class="form-control" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="">Ngày cập nhật</label>
                                            <div class="input-group">
                                                <input type="text" name="ngay_sua" class="form-control" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="_required">Tài khoản</label>
                                            <div class="input-group">
                                                <input type="text" name="ks_tk" class="form-control" required="">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="_required">Mật khẩu</label>
                                            <div class="input-group password" data-show-content="false">
                                                <input type="password" name="ks_mk" class="form-control input" required="">
                                                <div class="input-group-append">
                                                    <label class="input-group-text">
                                                        <a class="togglerPass" href="#">
                                                            <i class="fas fa-eye"></i>
                                                            <i class="fas fa-eye-slash d-none"></i>
                                                        </a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="_required">Token</label>
                                            <div class="input-group password" data-show-content="false">
                                                <input type="password" name="ks_token" class="form-control input" required="">
                                                <div class="input-group-append">
                                                    <label class="input-group-text">
                                                        <a class="togglerPass" href="#">
                                                            <i class="fas fa-eye"></i>
                                                            <i class="fas fa-eye-slash d-none"></i>
                                                        </a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex flex-nowrap">
                                            <button type="button" class="btn btn-danger btn-sm wd-150 mg-t-22 mr-auto tt_ks" id="btnKhoaChuKiSo">
                                                <i class="fas fa-lock mr-2"></i>Khóa tài khoản
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm wd-150 mg-t-22 mr-auto tt_ks" id="btnMoKhoaChuKiSo">
                                                <i class="fas fa-lock-open mr-2"></i>Mở khóa tài khoản
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 ml-auto mr-2" id="btnLuuChuKiSo">
                                                <i class="fas fa-save mr-2"></i>Lưu
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                                                <i class="fas fa-window-close mr-2"></i>Đóng
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="tab-pane" id="tabThongTinTaiKhoan" role="tabpanel" aria-labelledby="home-tab">
                                <form name="frmLuuThongNganHang" method="post">
                                    <div class="row" style="margin-top:5px;">
                                        <div class="col-sm-8">
                                            <div class="form-group">
                                                <label class="_required">Ngân hàng</label>
                                                <select class="select2 form-control custom-select" name="ngan_hang" style="width: 100%; height:36px;"></select>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <label class="_required">Số tài khoản</label>
                                                <div class="input-group">
                                                    <input type="text" maxlength="20" name="so_tk" autocomplete="off" placeholder="Số tài khoản" class="form-control text">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="table-responsive" style="max-height:35vh">
                                                <table class="table table-bordered fixed-header">
                                                    <thead class="font-weight-bold">
                                                        <tr class="text-center uppercase">
                                                            <th style="width:20px">STT</th>
                                                            <th>Ngân hàng</th>
                                                            <th style="width:150px">Số tài khoản</th>
                                                            <th style="width:40px"></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="bodyTableDanhSachTaiKhoanChiNhanh">
                                                        <tr>
                                                            <td colspan="4" class="text-center">Chưa có dữ liệu</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 mt-2 pt-2 text-right" style="border-top: 1px solid #e9ecef;">
                                            <button type="button" class="btn btn-primary btn-sm wd-80" id="btnLuuTaiKhoan"><i class="fa fa-save"></i> Lưu</button>
                                            <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="tab-pane" id="tabPhanCongXuLy" role="tabpanel" aria-labelledby="home-tab">
                                <div class="row">
                                    <div class="col-12 mt-2 d-none">
                                        <button type="button" class="btn btn-primary btn-sm wd-120" id="btnThemPhanCongXuLy"><i class="fas fa-plus"></i> Thêm cấu hình</button>
                                    </div>
                                    <div class="col-12 mt-2">
                                        <div class="table-responsive" style="max-height:35vh">
                                            <table class="table table-bordered fixed-header" style="width:130%">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th style="width:20px">STT</th>
                                                        <th>Đơn vị giám định</th>
                                                        <th>Đơn vị bồi thường</th>
                                                        <th>Đơn vị thanh toán</th>
                                                        <th style="width:150px">Ngày áp dụng</th>
                                                        <th style="width:40px"></th>
                                                    </tr>
                                                </thead>
                                                <tbody id="bodyTablePhanCongXuLy">
                                                    <tr>
                                                        <td colspan="6" class="text-center">Chưa có dữ liệu</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 mt-2 pt-2 text-right" style="border-top: 1px solid #e9ecef;">
                                        <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalPhanCongXuLy" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Cấu hình phân công xử lý</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body" style="padding-top:1px;">
                <div class="row">
                    <div class="col-12"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalTimKiemChiNhanhXuLy" class="modal-drag" style="width:350px; z-index: 9999999; margin-top: 5px !important; margin-left: 25px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn đơn vị</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" value="" onkeyup="onFocusTimKiemChiNhanhXuLy(this)" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control" id="timKiemChiNhanhXuLy">
                <input type="hidden" id="modalChonChiNhanhXuLyTimKiem_ma">
            </div>
            <div class="col-12 mt-2 scrollable streeHeight" style="max-height:300px;" id="treeChiNhanhXuLy"></div>
        </div>
    </div>
    <div class="modal-drag-footer" style="border-top: 1px solid #e9ecef;">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="ModalTimKiemChiNhanhXuLy_btnChonDonVi">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="bodyTableDanhSachTaiKhoanChiNhanhTemplate">
    <% if(ds_tai_khoan.length > 0){
    _.forEach(ds_tai_khoan, function(item,index) { %>
    <tr>
        <td class="text-center" style="width:20px"><%- item.stt %></td>
        <td><%- item.ten_ngan_hang %></td>
        <td style="width:100px"><%- item.so_tk %></td>
        <td style="width:40px">
            <a href="#" onclick="xoaTaiKhoan('<%- item.ngan_hang %>','<%- item.chi_nhanh %>','<%- item.so_tk %>')">
                <i class="fas fa-trash-alt" title="Xóa thông tin"></i>
            </a>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td colspan="4" class="text-center">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>
@section Styles{
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" asp-append-version="true" />
}
@section Scripts{
    <script src="~/libs/tree-js/vakata-jstree/dist/jstree.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BankListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/ModalChiNhanhXuLyService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/BranchList.js" asp-append-version="true"></script>
}

