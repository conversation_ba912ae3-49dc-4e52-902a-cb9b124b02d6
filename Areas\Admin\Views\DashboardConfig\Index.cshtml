﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình Dashboard";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình Dashboard</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình Dashboard</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        @* <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Người dùng</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="nsd_cai_dat" style="width: 100%; height:36px;"></select>
                            </div>
                        </div> *@
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập mã/tên" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-48p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-49p" title="Thêm mới" id="btnNhapConfig">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top: 3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewConfig" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalNhapConfig" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width:65%">
        <div class="modal-content">
            <form name="frmSaveConfig" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin Cấu hình Dashboard</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12 mb-4 d-none">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select required class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="table-responsive border" id="tblCauHinh" style="max-height: 275px;">
                                <table id="tbl_lan_bao_lanh_quyen_loi_ct" class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0;">
                                    <thead class="font-weight-bold card-title-bg-primary">
                                        <tr class="text-center uppercase">
                                            <th nowrap style="">Mã người sử dụng</th>
                                            <th nowrap style="">Tên người sử dụng</th>
                                            <th nowrap style="">Bồi thường xe ô tô</th>
                                            <th nowrap style="">Bồi thường xe máy</th>
                                            <th nowrap style="">Bồi thường sức khỏe</th>
                                            <th nowrap style=""></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tbDsCauHinh">
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="6">
                                                <a href="#" onclick="chonNguoiSuDung(this)">
                                                    <i class="fas fa-plus-square mr-2"></i>Thêm người sử dụng
                                                </a>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display: block;">
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinConfig"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinConfig"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<partial name="_Template" />
@section Scripts{
    <script src="~/js/app/Admin/services/DashboardConfigService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/DashboardConfig.js" asp-append-version="true"></script>
}