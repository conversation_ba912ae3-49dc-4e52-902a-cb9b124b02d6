﻿<div class="modal fade" id="modalCauHinhMauIn" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title" id="exampleModalLabel">Thông tin cấu hình</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body pt-2">
                <div id="modalCauHinhMauInNhap">
                    <form id="frmSaveCauHinhMauIn" name="frmSaveCauHinhMauIn" method="post">
                        <input type="hidden" name="ma_doi_tac" value="" />
                        <input type="hidden" name="bt" value="" />
                        <input type="hidden" name="ma_mau_in" value="" />
                        <input type="hidden" name="ma_chuc_danh" value="" />
                        <div class="row mt-1">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="ten_hthi_ky" class="_required">Tên hiển thị ký</label>
                                    <input type="text" class="form-control" id="ten_hthi_ky" required name="ten_hthi_ky" autocomplete="off" maxlength="100" placeholder="Tên hiển thị ký">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-1">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="stt" class="_required">Thứ tự hiển thị</label>
                                    <input type="text" class="form-control number" id="stt" required name="stt" autocomplete="off" maxlength="5" placeholder="Thứ tự hiển thị">
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="" class="_required">Loại</label>
                                    <select class="select2 form-control custom-select" required name="loai" style="width: 100%; height:36px;">
                                        <option value="NK">Người ký</option>
                                        <option value="NT">Người trình</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row border-top mt-2">
                            <div class="col-12" style="margin-top:10px">
                                <button type="button" class="btn btn-primary btn-sm wd-80 float-right" id="btnLuuCauHinhMauIn">
                                    <i class="fa fa-save mr-1"></i>Lưu
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm wd-80 float-left mr-2" id="btnXoaCauHinhMauIn"><i class="fas fa-trash-alt mr-1"></i>Xóa</button>
                                <button type="button" class="btn btn-primary btn-sm wd-140 float-left" id="btnXemDanhSachCauHinh"><i class="fa fa-eye mr-1"></i>Xem danh sách</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div id="modalCauHinhMauInLietKe">
                    <div class="row">
                        <div class="col-12 px-2">
                            <div id="gridViewDanhSachCauHinh" class="table-app">
                                <div class="table-responsive" style="height:185px; width: 100%;">
                                    <table class="table table-striped table-bordered fixed-header">
                                        <thead class="font-weight-bold">
                                            <tr class="text-center uppercase">
                                                <th style="width: 40px;">STT</th>
                                                <th>Tên hiển thị ký</th>
                                                <th>Loại</th>
                                                <th style="width: 40px;"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="tblDanhSachCauHinhMauIn">
                                        </tbody>
                                    </table>
                                </div>
                                <div id="tblDanhSachCauHinhMauIn_pagination"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row border-top mt-1">
                        <div class="col-12" style="margin-top:10px">
                            <button type="button" class="btn btn-primary btn-sm wd-80 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-1"></i> Đóng</button>
                            <button type="button" class="btn btn-primary btn-sm wd-90 float-right mr-2" id="btnManHinhThemMoi">
                                <i class="fas fa-plus mr-1"></i>Thêm mới
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblDanhSachCauHinhMauIn_template">
    <% if(data.length > 0){ %>
    <% _.forEach(data, function(item, index) { %>
    <tr style="cursor: pointer;">
        <td class="text-center"><%- item.stt %></td>
        <td class="text-left"><%- item.ten_hthi_ky %></td>
        <td class="text-center">
            <%if(item.loai=="NK"){%>
            <span>Người ký</span>
            <%}else{%>
            <span>Người trình</span>
            <%} %>
        </td>
        <td class="text-center">
            <a href="#" onclick="suaNoiDungCauHinh('<%- item.ma_doi_tac %>','<%- item.bt %>','<%- item.ma_mau_in %>','<%- item.ma_chuc_danh %>','<%- item.ten_hthi_ky %>','<%- item.stt %>','<%- item.loai %>')"><i class="fa fa-edit"></i></a>
        </td>
    </tr>
    <% })}else{ %>
    <tr style="cursor: pointer;">
        <td colspan="4" class="text-center">Chưa có dữ liệu</td>
    </tr>
    <% } %>

    <% if(data.length < 4){
    for(var i = 0; i < 4 - data.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="stt" title="" style="text-align: center; height: 20px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
