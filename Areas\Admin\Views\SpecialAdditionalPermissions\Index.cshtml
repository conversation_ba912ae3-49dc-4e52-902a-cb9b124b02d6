﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Quản lý điều khoản bổ sung đặc biệt";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Quản lý điều khoản bổ sung đặc biệt</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Quản lý điều khoản bổ sung đặc biệt</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form name="frmTimKiem" id="frmTimKiem" method="post" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Sản phẩm</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="san_pham" style="width: 100%; height:36px;">
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Điều khoản bổ sung</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_dkbs" style="width: 100%; height:36px;">
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Tìm kiếm</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Thông tin tìm kiếm" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top: 21px;">
                            <div class="form-group">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnAdd">
                                <i class="fa fa-plus"></i>
                            </button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div id="gridView" class="table-app" style="height: 65vh;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
 <partial name="_Modal.cshtml" />
@section Scripts {
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/HealthCare/services/HealthClaimCommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/PackageService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ProductHumanService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/SpecialAdditionalPermissionsService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/SpecialAdditionalPermissions.js" asp-append-version="true"></script>
}