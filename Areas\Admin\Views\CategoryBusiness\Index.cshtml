﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục cấu hình thông số nghiệp vụ <PERSON> đoạn kinh doanh";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON>h mục cấu hình GĐKD</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Danh mục cấu hình GĐKD</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin mã/tên/tên tắt" class="form-control">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThemMoi">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcelCateCommon">
                                <i class="fas fa-upload"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewGDKD" class="table-app" style="height: 65vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalDanhMucCauHinhGDKD" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:40%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin danh mục cấu hình GĐKD</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmDanhMucCauHinhGDKD" name="frmDanhMucCauHinhGDKD" method="post">
                    <div class="row">
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <label class="_required" for="ngay_ad">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" required autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã</label>
                                <input type="text" name="ma" required autocomplete="off" placeholder="Nhập mã" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-8">
                            <div class="form-group">
                                <lable class="_required">Tên</lable>
                                <input type="text" name="ten" required autocomplete="off" placeholder="Nhập tên" class="form-control">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <lable class="_required">Tên tắt</lable>
                                <input type="text" name="ten_tat" required autocomplete="off" placeholder="Nhập tên tắt" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-4">
                            <div class="form-group">
                                <lable class="_required">Đơn vị tính</lable>
                                <input type="text" name="ma_dvi_tinh" required autocomplete="off" placeholder="Nhập đơn vị tính" class="form-control">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <lable class="_required">Loại</lable>
                                <input type="text" name="loai" required autocomplete="off" placeholder="Nhập loại" class="form-control">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <lable class="_required">Nhập liệu</lable>
                                <select class="select2 form-control custom-select" required name="nhap_lieu" style="width:100%">
                                    <option value="">Chọn kiểu</option>
                                    <option value="C">Có nhập</option>
                                    <option value="K">Không nhập</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-4">
                            <div class="form-group">
                                <lable class="">Ghi chú</lable>
                                <input type="text" name="ghi_chu" autocomplete="off" placeholder="Nhập ghi chú" class="form-control">
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <lable class="_required">Cho phép chỉnh sửa</lable>
                                <select class="select2 form-control custom-select" required name="dieu_chinh" style="width:100%">
                                    <option value="">Chọn cho phép chỉnh sửa</option>
                                    <option value="C">Có</option>
                                    <option value="K">Không</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="form-group">
                                <lable class="">STT</lable>
                                <input type="text" name="stt" autocomplete="off" placeholder="Nhập STT" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-12">
                          
                            <div class="form-group">
                                <label >Công thức tính toán</label>
                                <textarea class="form-control" name="cong_thuc_tt" autocomplete="off"  rows="4" placeholder="Nhập công thức tính toán" tabindex="42" spellcheck="false"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="display: block; padding: 10px 5px;">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal" tabindex="17"><i class="fas fa-window-close"></i> Đóng</button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuGDKD" tabindex="18"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaGDKD" tabindex="19"><i class="fas fa-trash-alt"></i> Xóa</button>
            </div>
        </div>
    </div>
</div>
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section scripts {
   
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CategoryBusinessService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/CategoryBusiness.js" asp-append-version="true"></script>

}