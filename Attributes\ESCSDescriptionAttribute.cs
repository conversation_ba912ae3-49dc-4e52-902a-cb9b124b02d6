using System;

namespace ESCS.Attributes
{
    public class ESCSMethod
    {
        public const string GET = "GET";
        public const string POST = "POST";
        public const string DELETE = "DELETE";
    }

    public class ESCSDescriptionAttribute : Attribute
    {
        public string Method { get; set; }
        public string Description { get; set; }

        public ESCSDescriptionAttribute(string Method, string Description)
        {
            this.Method = Method;
            this.Description = Description;
        }
    }
}