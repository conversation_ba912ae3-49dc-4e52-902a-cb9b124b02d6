﻿var objDanhMuc = {};
var currentPage = 0;
var _service = new Service();
var _partnerListService = new PartnerListService();
var _generalDirectoryServiceDemo1 = new GeneralDirectoryServiceDemo1();

var _frmTimKiem = new FormService("frmTimKiem");
var _frmSaveCSYT = new FormService("frmSaveCSYT");
var _modalNhapCSYT = new ModalService("modalNhapCSYT");

const GRID_HO_SO_SO_DONG = 13;

var configColumn = [
    { field: "sott", title: "STT", width: "4%", hozAlign: "center", headerSort: false },
    { field: "thong_tin_chinh_sua", title: "Thông tin chỉnh sửa", width: "13%", hozAlign: "center", headerSort: false },
    { field: "ma", title: "M<PERSON>", width: "10%", align: "center", headerSort: false },
    { field: "ten", title: "Tên", width: "15%", headerSort: false },
    { field: "nhom_hthi", title: "Nhóm", width: "15%", hozAlign: "center", headerSort: false },
    { field: "trang_thai_hthi", title: "Trạng thái", width: "10%", hozAlign: "center", headerSort: false, formatter: "html" },
    { field: "ghi_chu", title: "Ghi chú", width: "25%", headerSort: false }
]

// Khởi tạo grid với cấu hình trên
var _gridViewCSYT = new GridViewService("gridViewCSYT", configColumn, getPaging, rowClick);

function getPaging(trang)
{
    // Lấy dữ liệu từ form tìm kiếm
    var objTimKiem = _frmTimKiem.getJsonData();

    currentPage = trang;
    objTimKiem.trang = trang;
    objTimKiem.so_dong = GRID_HO_SO_SO_DONG;

    _generalDirectoryServiceDemo1.timKiemPTrang(objTimKiem).then(res => {
        // Set dữ liệu cho grid
        _gridViewCSYT.setDataSource(res, trang, GRID_HO_SO_SO_DONG);
        // Thêm dòng trống nếu dữ liệu ít hơn số dòng quy định
        if (res.data_info.data != null && res.data_info.data != undefined && res.data_info.data.length <= GRID_HO_SO_SO_DONG) {
            _gridViewCSYT.addRowEmpty(GRID_HO_SO_SO_DONG - res.data_info.data.length);
        }
        else
        {
            _gridViewCSYT.addRowEmpty(GRID_HO_SO_SO_DONG);
        }
    });
}

function rowClick(data, row) {
    if (row !== undefined) {
        for (var i = 0; i < row.getTable().getRows().length; i++) {
            row.getTable().getRows()[i].deselect();
        }
    }
    if (data.ma_doi_tac === undefined || data.ma_doi_tac === null || data.ma_doi_tac === "") {
        return;
    }

    _generalDirectoryServiceDemo1.layThongTinChiTiet(data).then(res => {
        if (row !== undefined) {
            row.select();
        }
        var objDatact = res.data_info;
        _frmSaveCSYT.clearErrorMessage();
        _frmSaveCSYT.setData(objDatact); // Fill dữ liệu vào form
        _frmSaveCSYT.getControl("ma_doi_tac").readOnly();
        _frmSaveCSYT.getControl("ma").readOnly();
        $("#modal-user-log").html("(" + objDatact.nsd + " - " + objDatact.ngay + ")");
        _modalNhapCSYT.show();
    });
};

$(document).ready(function () {
    _service.all([_partnerListService.layDsDoiTac()]).then(arrRes => {
        ESCS_MA_DOI_TAC_DUY_NHAT = (arrRes[0].data_info != null && arrRes[0].data_info.length == 1) ? arrRes[0].data_info[0].ma : "";
        objDanhMuc.doi_tac = arrRes[0];
        // Set datasource cho dropdown đối tác trong form tìm kiếm
        _frmTimKiem.getControl("ma_doi_tac").setDataSource(arrRes[0].data_info, "ten", "ma", "Chọn đối tác", ESCS_MA_DOI_TAC_DUY_NHAT);
        _frmSaveCSYT.getControl("ma_doi_tac").setDataSource(objDanhMuc.doi_tac.data_info, "ten", "ma", "Chọn đối tác", ESCS_MA_DOI_TAC_DUY_NHAT);
    });
    getPaging(1); // Load trang 1 khi vào màn hình
});
