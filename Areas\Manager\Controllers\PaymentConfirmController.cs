﻿using ESCS.Attributes;
using ESCS.COMMON.Common;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Manager.Controllers
{
    [Area("Manager")]
    [SystemAuthen]
    public class PaymentConfirmController : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        public PaymentConfirmController(IWebHostEnvironment webHostEnvironment)
        {
            _webHostEnvironment = webHostEnvironment;
        }
        public IActionResult Index()
        {
            return View();
        }
        [AjaxOnly]
        public async Task<IActionResult> getPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> getDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_LKE_CT, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> getListMapping()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_MAPPING_EXCEL, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> saveThongTinImport()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_NH, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> xacNhanTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_DONG_Y, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> huyXacNhanTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_DONG_Y_HUY, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> xoaXacNhanTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_XOA, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> dsHoSoTuChoi()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_THANH_TOAN_CT_LOG_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> danhSachXNTTTonLke()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_TON_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> danhSachXNTTHuyLke()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_DA_XAC_NHAN_LKE, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> xacNhanTTThatBai()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_THAT_BAI, json);
            return Ok(data);
        }
        [HttpPost]
        [SystemAuthen]
        public async Task<ActionResult> UploadFileCTTT()
        {
            if (!Utilities.IsMultipartContentType(Request.ContentType))
                return BadRequest();
            IFormFileCollection files;
            var rq = Request.GetFormDataRequest(GetUser(), out files);
            var data = await Request.UploadFiles(StoredProcedure.PHT_BH_FILE_XNTT_LUU, (object)rq, files);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> traLaiXNTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XAC_NHAN_THANH_TOAN_TRA_LAI_NH, json);
            return Ok(data);
        }
    }
}