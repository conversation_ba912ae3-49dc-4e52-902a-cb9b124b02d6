﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class ProcessingFactorController : BaseController
    {
        private readonly IWebHostEnvironment _env;

        public ProcessingFactorController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NSD_HE_SO_XLY_LK, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetListNsd()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NSD_HE_SO_XLY_CACHE_NSD, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetListHeSoNsd()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NSD_HE_SO_XLY_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> HeSoNsdNhap()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NSD_HE_SO_XLY_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> deleteConfig()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NSD_HE_SO_XLY_X, json);
            return Ok(data);
        }
    }
}