﻿<div class="modal fade show" id="modalXemDGRR" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document" style="max-width: 75vw; margin-top:10px;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Đ<PERSON>h gi<PERSON> tổn thất</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body px-2 py-0">
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="card mb-0">
                            <div class="card-body p-0">
                                <div class="row w-100 m-0 p-0">
                                    <div class="row w-100 m-0 p-0">
                                        <div class="col-12 p-0 d-flex flex-nowrap bg-light" style="gap:0.5rem;">
                                            <div class="d-flex flex-column">
                                                <div class="flex-fill card border mb-0" style="min-width:250px;max-width:300px;height:0">
                                                    <div class="card-body p-2 d-flex flex-column" style="gap:0.5rem;">
                                                        <div class="flex-fill overflow-auto" style="height:0" id="modalVideoDanhSach">
                                                        </div>
                                                        <div class="text-center">
                                                            <button class="btn btn-sm btn-primary wd-110" id="modalVideoSuaTen" onclick="showNhapTen(this)" title="Sửa tên"><i class="fas fa-edit"></i> Sửa tên</button>
                                                            <button class="btn btn-sm btn-primary wd-110" id="modalVideoUpload" title="Upload video"><i class="fas fa-upload"></i> Upload</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-fill card border mb-0">
                                                <div class="card-body p-2">
                                                    <div class="videoContent">
                                                        <video autoplay muted playsinline style="width:100%" id="modalVideoView" src=""></video>
                                                        <form name="frmModalVideoUpload" method="post">
                                                            <input style="display:none" id="inputModalVideoUpload" accept="video/mp4,video/x-m4v,video/*" type="file" name="file" value="" />
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-block">
                    <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@* <script type="text/html" id="modalVideoDanhSachTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item, index) {
    if(index == 0){%>
    <a class="nav-link rounded videoLink active" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoHs('<%- item.bt %>')"><%- item.ten %></a>
    <%} else {%>
    <a class="nav-link rounded videoLink" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoHs('<%- item.bt %>')"><%- item.ten %></a>
    <%}})}%>
    </script> *@
