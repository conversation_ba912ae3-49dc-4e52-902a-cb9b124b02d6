﻿<div class="modal fade" id="modalDongBoAnhHoSo" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog" style="max-width:80vw;">
        <div class="modal-content">
            <div class="modal-header p-2">
                <h5 class="modal-title"><PERSON><PERSON><PERSON> bộ ảnh hồ sơ</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-0">
                <ul class="nav nav-pills px-2 pt-2" role="tablist" style="gap:.5rem;">
                    <li class="nav-item" role="presentation">
                        <button data-toggle="pill" data-target="#modalDongBoAnhHoSo_tab1" type="button" role="tab" style="font-weight:bold;" class="nav-link border-0 active"><PERSON><PERSON> <PERSON><PERSON><PERSON> hồ sơ liên kết</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button data-toggle="pill" data-target="#modalDongBoAnhHoSo_tab2" type="button" role="tab" style="font-weight:bold;" class="nav-link border-0">Tìm kiếm hồ sơ</button>
                    </li>
                </ul>
                <div class="tab-content">
                    <div id="modalDongBoAnhHoSo_tab1" role="tabpanel" class="tab-pane fade p-2 show active">
                        <div class="card m-0">
                            <div class="card-body p-2 border rounded overflow-hidden" style="height:45vh;">
                                <div id="tblDongBoAnhHoSo" class="h-100 overflow-auto scrollable"></div>
                            </div>
                        </div>
                    </div>
                    <div id="modalDongBoAnhHoSo_tab2" role="tabpanel" class="tab-pane fade p-2">
                        <div class="card m-0">
                            <div class="card-body p-2 border rounded overflow-hidden d-flex flex-column" style="height:45vh;gap:.75rem;">
                                <form name="frmDongBoAnhHoSo">
                                    <input type="hidden" name="so_id" />
                                    <input type="hidden" name="nv" value="XE" />
                                    <div class="row">
                                        <div class="col-2">
                                            <div class="form-group">
                                                <label for="ngay_d">Từ ngày</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_d" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                                    <div class="input-group-append">
                                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <div class="form-group">
                                                <label for="ngay_c">Đến ngày</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_c" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                                    <div class="input-group-append">
                                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <div class="form-group">
                                                <label>Nội dung tìm kiếm</label>
                                                <input type="text" name="tim" autocomplete="off" spellcheck="false" placeholder="Nhập thông tin hồ sơ" class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-2 ml-auto" style="padding-top: 21px;">
                                            <button type="button" name="btnSearch" class="btn btn-primary btn-sm wd-48p float-right" title="Tìm kiếm">
                                                <i class="fa fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                                <div id="tblDongBoAnhHoSoSearch" class="flex-fill overflow-auto scrollable" style="height:0;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-2">
                <button class="btn btn-primary btn-sm wd-80 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modalDanhSachHoSoGPLX" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:unset;width:90%;">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h4 class="modal-title">Danh sách hồ sơ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height:470px;">
                            <table class="table table-bordered fixed-header" style="width:130%;">
                                <thead class="font-weight-bold card-title-bg-primary">
                                    <tr class="text-center uppercase">
                                        <th>STT</th>
                                        <th>Ngày mở</th>
                                        <th>Số hồ sơ</th>
                                        <th>Tên lái xe</th>
                                        <th>Số GPLX</th>
                                        <th>Hiệu lực GPLX</th>
                                        <th>Trạng thái</th>
                                        <th>Biển số</th>
                                        <th>Số hợp đồng</th>
                                        <th>Tên khách hàng</th>
                                        <th>Hồ sơ giám định</th>
                                        <th>Hồ sơ bồi thường</th>
                                    </tr>
                                </thead>
                                <tbody id="tblDanhSachHoSoGPLX">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-1"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalInAnTaiLieuHSGT" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:50%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">In ấn tài liệu hồ sơ giấy tờ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-2">
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table class="table table-bordered fixed-header">
                                <thead class="font-weight-bold text-center uppercase">
                                    <tr>
                                        <th style="width:46px">
                                            <div class="custom-control custom-checkbox ml-2">
                                                <input type="checkbox" onchange="onChonInTatCa(this)" id="chonInAnTaiLieuHSGT" class="custom-control-input">
                                                <label class="custom-control-label" for="chonInAnTaiLieuHSGT">&nbsp;</label>
                                            </div>
                                        </th>
                                        <th>Tên tài liệu</th>
                                        <th style="width:80px">Loại file</th>
                                    </tr>
                                </thead>
                                <tbody id="tblInAnTaiLieuHSGT">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-2">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnInAnTaiLieuHSGT">
                    <i class="fa fa-print mr-2"></i>In ấn
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-70 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tblInAnTaiLieuHSGT_template">
    <% if(danh_sach.length > 0){%>
    <%_.forEach(danh_sach, function(item,index) { %>
    <tr>
         <td class="text-center">
            <div class="custom-control custom-checkbox" style="padding-left: 1.9rem;">
                <input type="checkbox" name="ds_file_in_an" value="<%- item.bt%>" id="file_pdf_<%- item.bt%>" class="custom-control-input file_pdf_item">
                <label class="custom-control-label" style="cursor:pointer;" for="file_pdf_<%- item.bt%>">&nbsp;</label>
            </div>
        </td>
        <td style="text-align: left">
            <a href="#"><%- item.ten_file %></a>
        </td>
        <td class="text-center"><%- item.extension %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td colspan="3" class="text-center">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="tblDanhSachHoSoGPLX_template">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr>
        <td class="text-center">
            <%- item.sott %>
        </td>
        <td class="text-center"><%- item.ngay_ht %></td>
        <td class="text-center"><%- item.so_hs %></td>
        <td class="text-center"><%- item.ten_lxe %></td>
        <td class="text-center"><%- item.so_gplx %></td>
        <td class="text-center"><%- item.thoi_han_gplx %></td>
        <td class="text-center"><%- item.trang_thai %></td>
        <td class="text-center"><%- item.doi_tuong %></td>
        <td class="text-center"><%- item.so_hd %></td>
        <td class="text-center"><%- item.ten_kh %></td>
        <td class="text-center"><a href="#" onclick="ShowInvestigationDisplay('<%- item.ma_doi_tac%>','<%- item.so_id %>','XEM_CTIET_HO_SO_GD', '<%- item.nghiep_vu %>')">HS giám định</a></td>
        <td class="text-center"><a href="#" onclick="ShowCompensationDisplay('<%- item.ma_doi_tac%>','<%- item.so_id %>','XEM_CTIET_HO_SO_BT', '<%- item.nghiep_vu %>')">HS bồi thường</a></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="12">Chưa có dữ liệu</td>
    </tr>
    <% } %>

    <% if(data.length < 11){
    for(var i = 0; i < 11 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
