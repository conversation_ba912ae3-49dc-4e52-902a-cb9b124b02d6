﻿<style>
    #modalNhomNguyenNhanDanhSach label:hover {
        color: var(--escs-main-theme-color);
    }
</style>
<div id="modalNhomNguyenNhan" class="modal-drag" style="width: 280px; z-index: 9999999; margin-top: 5px !important; margin-left: -10px !important;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn nhóm nguyên nhân</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_NhomNguyenNhan" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalNhomNguyenNhanElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:260px;" id="modalNhomNguyenNhanDanhSach">

            </div>
        </div>
    </div>
</div>

<script type="text/html" id="modalNhomNguyenNhanDanhSachTemplate">
    <% if(danh_sach_nnn.length > 0){
    _.forEach(danh_sach_nnn, function(item,index) { %>
    <% var ma = danh_sach_nnn[index].ma.trim().replace(/[^a-zA-Z0-9]/g, '') %>
    <div class="custom-control custom-checkbox dsnnn" id="dsnnn_<%- ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="nhom_nguyen_nhan_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input item-nhomnguyennhan modalNhomNguyenNhanItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="nhom_nguyen_nhan_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>