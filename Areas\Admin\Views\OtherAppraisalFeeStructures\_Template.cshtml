﻿<script type="text/html" id="tblCauHinhPhiGiamDinh_template">
    <%if(data.length>0){%>
    <% _.forEach(data, function(item,index) {%>
    <% if(item.loai=='H'){%>
    <tr class="CauTrucPhiGiamDinhParent" id="Parent_<%- item.ma %>" data-val="<%- item.ma %>" data-loai="Parent">
        <td class="text-center" style="font-weight:bold;background:#eef5f9;"><%- item.stt_hthi %></td>
        <td class="text-left" style="font-weight:bold;background:#eef5f9;">
            <input type="hidden" data-field="ma" value="<%- item.ma %>">
            <input type="hidden" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" data-field="stt" value="<%- item.stt %>" />
            <input type="hidden" data-field="stt_hthi" value="<%- item.stt_hthi %>" />
            <input type="hidden" data-field="ten" value="<%- item.ten %>" />
            <input type="hidden" data-field="loai" value="<%- item.loai %>" />
            <%- item.ten %>
        </td>
        <td class="text-right" style="background:#eef5f9;">
            <input type="text" class="floating-input number" data-field="don_gia" value="<%- ESUtil.formatMoneyNullable(item.don_gia) %>" />
        </td>
    </tr>
    <% }else{%>
    <tr class="CauTrucPhiGiamDinhItem" data-val="<%- item.ma_ct %>" data-loai="Child">
        <td class="text-center"><%- item.stt_hthi %></td>
        <td class="text-left">
            <input type="hidden" data-field="ma" value="<%- item.ma %>">
            <input type="hidden" data-field="ma_ct" value="<%- item.ma_ct %>" />
            <input type="hidden" data-field="stt" value="<%- item.stt %>" />
            <input type="hidden" data-field="stt_hthi" value="<%- item.stt_hthi %>" />
            <input type="hidden" data-field="ten" value="<%- item.ten %>" />
            <input type="hidden" data-field="loai" value="<%- item.loai %>" />


            <%- item.ten %>
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number" data-field="don_gia" value="<%- ESUtil.formatMoneyNullable(item.don_gia) %>" />
        </td>
    </tr>
    <%}%>

    <% }) }%>
</script>

