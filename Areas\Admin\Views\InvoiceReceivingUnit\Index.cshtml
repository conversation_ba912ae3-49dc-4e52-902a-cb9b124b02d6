﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình đơn vị nhận hóa đơn";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình đơn vị nhận hóa đơn</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình đơn vị nhận hóa đơn</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <input type="hidden" name="ma_doi_tac" />
                    <div class="row">
                        <div class="col-12 col-md-4 col-lg-3">
                            <div class="form-group">
                                <label class="">Chi nhánh</label>
                                <select class="select2 form-control" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-12 col-md-4 col-lg-3">
                            <div class="form-group">
                                <label class="">Chi nhánh nhận hóa đơn</label>
                                <select class="select2 form-control" name="ma_chi_nhanh_nhan_hdon" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-6 col-md-3 col-lg-2 ml-auto" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-48p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-49p" title="Thêm mới" id="btnNhap">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridView" class="table-app" ></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhap" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content col-md-11" style="margin-left: 55px;">
            <form name="frmLuu" method="post">
                <div class="modal-header" style="padding: 10px 5px;">
                    <h4 class="modal-title">Nhập cấu hình</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body" style="padding: 10px 5px;">
                    <input type="hidden" name="ma_doi_tac" />
                    <div class="row" style="user-select:none;">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="">Chi nhánh</label>
                                <select class="select2 form-control" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="mt-3">Danh sách đơn vị nhận hóa đơn</label>
                                <input type="text" class="form-control ds_chi_nhanh_nhan_hdon_filter" placeholder="Tìm kiếm" />
                            </div>
                            <div class="border rounded overflow-auto" style="height:200px" id="ds_chi_nhanh_nhan_hdon">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="padding: 10px 5px; display: block;">
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinConfig"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinConfig"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>


@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/InvoiceReceivingUnitService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/InvoiceReceivingUnit.js" asp-append-version="true"></script>
}