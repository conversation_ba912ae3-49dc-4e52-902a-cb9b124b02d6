﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<style>
    .table-scroll {
        position: relative;
        width: 100%;
        z-index: 1;
        margin: auto;
        overflow: auto;
    }

        .table-scroll table {
            width: 100%;
            min-width: 1280px;
            margin: auto;
            border-collapse: separate;
            border-spacing: 0;
        }

    .table-wrap {
        position: relative;
    }

    .table-scroll th,
    .table-scroll td {
        padding: 5px 10px;
        border: 1px solid #f8f8f8;
        background: #fff;
        vertical-align: top;
    }

    .table-scroll thead th {
        color: #fff;
        position: -webkit-sticky;
        position: sticky;
        top: 0;
    }

    #table-wrap th:first-child {
        position: -webkit-sticky;
        position: sticky;
        left: 0;
        z-index: 2;
        color: #fff;
    }

    #table-wrap th:nth-child(2) {
        position: -webkit-sticky;
        position: sticky;
        left: 100px;
        z-index: 2;
        color: #fff;
    }

    #table-wrap thead th:first-child {
        z-index: 5;
    }

    #table-wrap thead th:nth-child(2) {
        z-index: 5;
    }

    #tableNhapQLoiGoiHD > tr > td:first-child {
        position: sticky;
        left: 0;
        z-index: 50 !important;
        min-width: 100px;
        max-width: 100px;
        width: 100px;
    }

    #tableNhapQLoiGoiHD > tr > td:nth-child(2) {
        position: sticky;
        left: 100px;
        z-index: 50 !important;
    }

    table:has( #tableNhapQLoiGoiHD) > thead > tr > th:first-child {
        position: sticky;
        left: 0;
        z-index: 51 !important;
    }

    table:has( #tableNhapQLoiGoiHD) > thead > tr > th:nth-child(2) {
        position: sticky;
        left: 100px;
        z-index: 51 !important;
    }
</style>

<form name="frmThongTinGoiBH" method="post" class="flex-fill" style="height:0;">
    <div class="tab-content" style="height:100%;">
        <div style="height:100%;" class="tab-pane overflow-auto p-0 active" id="tabCauHinhQL" role="tabpanel" aria-labelledby="home-tab">
            <div class="container-fluid">
                <input type="hidden" name="so_id_goi" value="" />
                <input type="hidden" name="so_id_hd" value="" />
                <input type="hidden" name="ma_doi_tac" value="" />
                <input type="hidden" name="trang_thai" value="" />
                <div class="row mt-2">
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="_required">Sản phẩm</label>
                            <select class="select2 form-control custom-select" required name="ma_nhom" style="width: 100%; height:36px;"></select>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <label class="_required">Mã gói</label>
                            <input type="text" maxlength="80" autocomplete="off" required name="ma" class="form-control" placeholder="Mã gói">
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <label class="_required">Tên gói</label>
                            <input type="text" maxlength="100" autocomplete="off" name="ten" required class="form-control text" placeholder="Tên gói">
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="_required">Nhóm gói</label>
                            <select class="select2 form-control custom-select" required name="nhom_goi" style="width: 100%; height:36px;"></select>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="">Giới tính</label>
                            <select class="select2 form-control custom-select" name="gioi_tinh" style="width: 100%; height:36px;">
                                <option value=" ">Chọn giới tính</option>
                                <option value="NAM">Nam</option>
                                <option value="NU">Nữ</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <label class="_required">Từ tuổi</label>
                            <input type="text" maxlength="3" autocomplete="off" name="tuoi_tu" required class="form-control decimal" placeholder="Từ tuổi">
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <label class="_required">Đến tuổi</label>
                            <input type="text" maxlength="3" name="tuoi_toi" autocomplete="off" required class="form-control decimal" placeholder="Đến tuổi">
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <label for="ngay_d">Ngày áp dụng</label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                <div class="input-group-append">
                                    <span class="input-group-text" style="padding: 0.1em 0.4em !important;"><span class="ti-calendar"></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="_required">Loại hình</label>
                            <div class="input-group" style="cursor:pointer">
                                <input type="text" name="loai_hinh" style="cursor:pointer;" onclick="chonLoaiHinh(this);" class="form-control" autocomplete="off" placeholder="Click chọn loại hình bảo lãnh">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-1">
                    <div class="col-12">
                        <div class="d-flex flex-nowrap" style="gap:.5rem;">
                            <div class="flex-fill" style="width:0;">
                                <input type="text" placeholder="Nhập tên quyền lợi" class="form-control mb-2" id="timKiemQLGoi" value="" />
                                <div class="scrollable border rounded" id="treeQuyenLoiGoi" style="height:350px;"></div>
                            </div>
                            <div class="d-flex flex-column align-items-center justify-content-center">
                                <button class="btn btn-sm btn-block btn-primary" type="button" id="btnThemQuyenLoi" title="Thêm quyền lợi">
                                    <i class="fas fa-long-arrow-alt-left"></i>
                                </button>
                                <button class="btn btn-sm btn-block btn-primary" type="button" id="btnXoaQuyenLoi" title="Xóa quyền lợi">
                                    <i class="fas fa-long-arrow-alt-right"></i>
                                </button>
                            </div>
                            <div class="flex-fill" style="width:0;">
                                <input type="text" placeholder="Nhập tên quyền lợi" class="form-control mb-2" id="timKiemQLSP" value="" />
                                <div class="scrollable border rounded" id="treeQuyenLoiSP" style="height:350px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="height:100%;" class="tab-pane overflow-auto p-0" id="tabCauHienTienBH" role="tabpanel" aria-labelledby="home-tab">
            <div class="table-responsive table-scroll" id="table-scroll"  style="max-height:100%;">
                <table class="table table-bordered fixed-header main-table" id="main-table" style="width:130%;">
                    <thead class="font-weight-bold text-center uppercase text-nowrap">
                        <tr>
                            <th style="width:100px">Mã</th>
                            <th style="min-width:370px">Điều khoản</th>
                            <th style="width:50px">
                                <div class="custom-control custom-checkbox _required">
                                    <input onclick="chonMaNguyenTe(this)" type="checkbox" class="custom-control-input" id="chkMaNT-tableNhapQLoi" checked="">
                                    <label class="custom-control-label" for="chkMaNT-tableNhapQLoi">Nguyên tệ</label>
                                </div>
                            </th>
                            <th style="min-width: 170px">Quyền lợi trừ lùi</th>
                            <th style="width:120px">Số ngày(lần)/năm<span style="color:red">*</span></th>
                            <th style="width:120px" class="d-none">Số ngày/lần khám<span style="color:red">*</span></th>
                            <th style="width:150px">Q.Lợi/ngày(lần)<span style="color:red">*</span></th>
                            <th style="width:100px" class="d-none">Q.Lợi/lần khám<span style="color:red">*</span></th>
                            <th style="width:150px">Q.Lợi/năm<span style="color:red">*</span></th>
                            <th style="width:150px">Kiểu áp dụng<span style="color:red">*</span></th>
                            <th style="width: 100px; display: none">LHNV</th>
                            <th style="width:100px">Tỷ lệ đồng</th>
                            <th style="width:100px">T.Gian chờ</th>
                            <th style="width:100px">Phí BH</th>
                            <th style="width:100px">Ghi chú</th>
                        </tr>
                    </thead>
                    <tbody id="tableNhapQLoiGoiHD">
                    </tbody>
                </table>
            </div>
        </div>
        <div style="height:100%;" class="tab-pane overflow-auto p-0" id="tabQuyenLoiBoSung" role="tabpanel" aria-labelledby="home-tab">
            <div class="table-responsive" style="max-height:100%">
                <table class="table table-bordered fixed-header">
                    <thead class="font-weight-bold text-center uppercase text-nowrap">
                        <tr>
                            <th class="text-nowrap" style="width:20%">ĐIỀU KHOẢN BỔ SUNG</th>
                            <th class="text-nowrap" style="width:150px">
                                <div class="custom-control custom-checkbox _required">
                                    <input onclick="chonMaNguyenTe(this)" type="checkbox" class="custom-control-input" id="chkMaNT-tableDKBS" checked="">
                                    <label class="custom-control-label" for="chkMaNT-tableDKBS">Nguyên tệ</label>
                                </div>
                                @*<a class="_required" id="chkMaNT-tableDKBS" onclick="chonMaNguyenTe(this)" style="cursor:pointer; text-decoration: underline;">Nguyên tệ</a>*@
                            </th>
                            <th class="text-nowrap" style="width:18%">GHI CHÚ</th>
                            <th class="text-nowrap" style="width:8%">SỐ LẦN/NGÀY</th>
                            <th class="text-nowrap" style="width:8%">TIỀN LẦN/NGÀY</th>
                            <th class="text-nowrap" style="width:8%">TIỀN NĂM</th>
                            <th class="text-nowrap" style="width:10%">ĐỒNG BẢO HIỂM (%)</th>
                            <th class="text-nowrap" style="width:8%">SỐ NGÀY CHỜ</th>
                            <th class="text-nowrap" style="width:8%">PHÍ</th>
                            <th class="text-nowrap" style="width:8%">TỶ LỆ PHÍ (%)</th>
                            <th class="text-nowrap" style="width:6%; text-align:center"></th>
                        </tr>
                    </thead>
                    <tbody id="tableDKBSGoiHD"></tbody>
                    <tfoot>
                        <tr>
                            <td>
                                <a href="#" onclick="chonDKBS(this)">
                                    <i class="fas fa-plus-square mr-2"></i>Thêm điều khoản bổ sung
                                </a>
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <div style="height:100%;" class="tab-pane overflow-auto p-0" id="tabGhiChuKhac" role="tabpanel" aria-labelledby="home-tab">
            <div class="table-responsive" style="max-height:100%">
                <table class="table table-bordered fixed-header">
                    <thead class="font-weight-bold text-center uppercase text-nowrap">
                        <tr>
                            <th style="width:5%">STT</th>
                            <th style="width:90%">GHI CHÚ</th>
                            <th style="width:5%; text-align:center"></th>
                        </tr>
                    </thead>
                    <tbody id="tableGhiChuKhacGoiHD"></tbody>
                    <tfoot>
                        <tr>
                            <td colspan="2">
                                <a href="#" onclick="themGhiChu(this)">
                                    <i class="fas fa-plus-square mr-2"></i>Thêm ghi chú
                                </a>
                            </td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <div style="height:100%;" class="tab-pane overflow-auto p-0" id="tabTyLeDong" role="tabpanel" aria-labelledby="home-tab">
            <div class="container-fluid">
                <div class="row mt-2">
                    <div class="col-3">
                        <div class="form-group">
                            <label>Bệnh viện công - tỷ lệ đồng(%)</label>
                            <input type="text" id="bvc_tl_dong" autocomplete="off" maxlength="3" placeholder="Tỷ lệ đồng(%)" class="form-control decimal">
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <label>Bệnh viện công - thời gian chờ(ngày)</label>
                            <input type="text" id="bvc_tg_cho" autocomplete="off" maxlength="3" placeholder="Thời gian chờ(ngày)" class="form-control decimal">
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <label>Bệnh viện tư - tỷ lệ đồng(%)</label>
                            <input type="text" id="bvt_tl_dong" autocomplete="off" maxlength="3" placeholder="Tỷ lệ đồng(%)" class="form-control decimal">
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <label>Bệnh viện tư - thời gian chờ(ngày)</label>
                            <input type="text" id="bvt_tg_cho" autocomplete="off" maxlength="3" placeholder="Thời gian chờ(ngày)" class="form-control decimal">
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-sm-12">
                        <h5 class="m-0">Cấu hình mã bệnh</h5>
                        <div class="table-responsive" style="max-height: 400px">
                            <table id="tblCauHinhMaBenh" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold text-nowrap">
                                    <tr class="text-center uppercase">
                                        <th width="25%">Mã bệnh</th>
                                        <th width="40%">Quyền lợi</th>
                                        <th width="15%">Tỷ lệ đồng (%)</th>
                                        <th width="15%">Thời gian chờ (ngày)</th>
                                        <th style="width:5%"></th>
                                    </tr>
                                </thead>
                                <tbody id="modalThemMaBenhGoiHD"></tbody>
                                <tfoot>
                                    <tr>
                                        <td>
                                            <a href="#" onclick="chonCauHinhMaBenh(this)">
                                                <i class="fas fa-plus-square mr-2"></i>Thêm mã bệnh
                                            </a>
                                        </td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>