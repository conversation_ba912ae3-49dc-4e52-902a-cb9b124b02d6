﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<style>
    #navThongTinChiTiet .nav-link {
        background-color: #EDEFF0;
        color: var(--escs-main-theme-color);
    }

        #navThongTinChiTiet .nav-link.active {
            background-color: var(--escs-main-theme-color);
            color: #FFF;
        }

    .tab-scroll-inner {
        overflow-x: auto;
        -ms-overflow-style: none;
        scrollbar-width: none !important;
        cursor: grab;
    }

        .tab-scroll-inner::-webkit-scrollbar {
            display: none;
        }

        .tab-scroll-inner.dragging {
            cursor: grabbing;
            user-select: none;
        }
</style>

<div id="HealthGuaranteeModal" class="esmodal fade" tabindex="-1" data-keyboard="false" aria-hidden="true">
    <div class="esmodal-dialog">
        <div class="esmodal-content">
            <div class="esmodal-header py-1">
                <div id="titleUpdateContract">
                    <h4 class="esmodal-title">Hồ sơ: <span data-model="so_hs" class="mainTitle"></span> - <a href="#" onclick="xemToanBoThongTinHoSoBoiThuong()">Xem chi tiết hồ sơ</a></h4> @*<span class="timeGCN"></span> -*@
                </div>
                <div id="divThongBaoCanhBao">
                </div>
            </div>
            <div class="esmodal-body">
                <div class="row">
                    <div class="col-lg-3 common-tab pr-0 collapse show" id="sidebar_info">
                        <partial name="../_HealthCommonInfo" />
                    </div>
                    <div class="col info-tab" style="position: relative;">
                        <partial name="_HealthGuaranteeContent" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalYeuCauBoSungHoSo" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Hồ sơ giấy tờ, chứng từ bảo hiểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-2">
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table id="tableDsHoSoGiayTo" class="table table-bordered fixed-header" style="width: 130%;">
                                <thead class="font-weight-bold text-center uppercase">
                                    <tr>
                                        <th width="32%">Tên giấy tờ</th>
                                        <th width="8%">Ngày cung cấp</th>
                                        <th width="8%">Trạng thái</th>
                                        <th width="6%">Hợp lệ</th>
                                        <th width="8%">Loại</th>
                                        <th width="8%">YC bổ sung</th>
                                        <th width="8%">Ghi chú</th>
                                        <th width="12%">Người sử dụng</th>
                                        <th width="10%" class="d-none">Nguồn PS</th>
                                    </tr>
                                </thead>
                                <tbody id="bodyDsHoSoGiayTo">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-2">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnGuiEmailYCBSHS">
                    <i class="fa fa-envelope mr-2"></i>Yêu cầu bổ sung
                </button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnLichSuYeuCauBsGiayTo">
                    <i class="fa fa-history mr-2"></i>Lịch sử yêu cầu BSHS
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuYeuCauBoSungHoSo">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongYeuCauBoSungHoSo">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalMoHoSoBT" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:70vw">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thêm hồ sơ bảo lãnh viện phí</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="card border mb-0 p-2">
                <div class="card-body p-1" style="padding:0px;" id="navTabTimKiemNguoi">
                    <ul class="cd-breadcrumb triangle nav nav-tabs" role="tablist">
                        <li role="presentation" onclick="hienThiTabTKiemNguoi('tabThongTinCSYT')" class="navTabTimKiemNguoi ">
                            <a href="#Tab_tabThongTinCSYT" aria-controls="tabThongTinCSYT" role="tab" data-toggle="tab" aria-expanded="false" aria-selected="true">
                                <span class="fa fa-car mr-2"></span> Thông tin cơ sở y tế
                            </a>
                        </li>
                        <li role="presentation" onclick="hienThiTabTKiemNguoi('tabTimKiem')" class="navTabTimKiemNguoi tabTimKiem">
                            <a href="#Tab_tabTimKiem" aria-controls="tabTimKiem" role="tab" data-toggle="tab" aria-expanded="false" aria-selected="false">
                                <span class="fas fa-user-shield mr-2"></span> Thông tin người được bảo hiểm
                            </a>
                        </li>
                        <li role="presentation" onclick="hienThiTabTKiemNguoi('tabThongTinLienHe')" class="navTabTimKiemNguoi ">
                            <a href="#Tab_tabThongTinLienHe" aria-controls="tabThongTinLienHe" role="tab" data-toggle="tab" aria-expanded="false" aria-selected="false">
                                <span class="fas fa-id-card-alt mr-2"></span> Thông tin người thông báo
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane active" role="tabpanel" id="tabThongTinCSYT">
                            <form name="frmThemHoSo" novalidate="novalidate" method="post">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="nguon" class="_required">Cơ sở y tế</label>
                                            <div class="input-group" style="cursor:pointer">
                                                <input type="text" name="benh_vien" required style="cursor:pointer;z-index:999" id="frmThemHoSo_benh_vien" onclick="chonBenhVien(this);" class="form-control" autocomplete="off" placeholder="Click chọn bệnh viện">
                                                <div class="input-group-append">
                                                    @* <label class="input-group-text d-none">
                                                        <a href="javascript:void(0)" id="btnThemDanhMucBenhVien">
                                                            <i class="fas fa-plus-square" title="Thêm bệnh viện"></i>
                                                        </a>
                                                    </label> *@
                                                    <label class="input-group-text" for="">
                                                        <a href="#" onclick="xoaChon(this,'BV')">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label>Nhà thuốc</label>
                                            <div class="input-group" style="cursor:pointer">
                                                <input type="text" name="nha_thuoc" style="cursor:pointer;z-index:999" id="frmThemHoSo_nha_thuoc" onclick="chonNhaThuoc(this);" class="form-control" autocomplete="off" placeholder="Click chọn nhà thuốc">
                                                <div class="input-group-append">
                                                    @* <label class="input-group-text d-none">
                                                        <a href="javascript:void(0)" id="btnThemDanhMucNhaThuoc">
                                                            <i class="fas fa-plus-square" title="Thêm nhà thuốc"></i>
                                                        </a>
                                                    </label> *@
                                                    <label class="input-group-text" for="">
                                                        <a href="#" onclick="xoaChon(this,'NT')">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <div class="form-group">
                                                <label>Khoa</label>
                                                <input type="text" autocomplete="off" name="khoa" class="form-control" placeholder="Khoa">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <div class="form-group">
                                                <label>Số phòng</label>
                                                <input type="text" autocomplete="off" name="so_phong" class="form-control" placeholder="Số phòng">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <div class="form-group">
                                                <label>Loại phòng</label>
                                                <input type="text" autocomplete="off" name="loai_phong" class="form-control" placeholder="Loại phòng">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="m-0 pd-y-10">Thông tin liên hệ với cơ sở y tế</h5>
                                    </div>
                                </div>
                                <div class="row">
                                    <input type="hidden" name="bt" value="" />
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label class="">Họ và tên</label>
                                            <div class="input-group">
                                                <input type="text" autocomplete="off" class="form-control" maxlength="100" name="nguoi_tb" placeholder="VD: Nguyễn Văn A">
                                                <div class="input-group-append">
                                                    <label class="input-group-text">
                                                        <a href="#" onclick="timNguoiLienHeCSYT(this)"><i class="fas fa-search"></i></a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label class="">Điện thoại</label>
                                            <input type="text" autocomplete="off" fn-validate="validatePhoneControl" class="form-control phone" name="dthoai_nguoi_tb" placeholder="VD: 0972xxxxxx" im-insert="true">
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="">Email</label>
                                            <input type="text" autocomplete="off" class="form-control" maxlength="500" name="email_nguoi_tb" placeholder="VD: <EMAIL>;<EMAIL>"> @*fn-validate="validateEmailControl"*@
                                        </div>
                                    </div>
                                    <div class="col-2" style="padding-top:21px">
                                        <button type="button" class="btn btn-primary btn-sm" id="btnLuuNguoiLienHeCSYT">
                                            <i class="fa fa-save mr-1"></i> Lưu liên hệ
                                        </button>
                                        @*<button type="button" class="btn btn-primary btn-sm" id="btnImportNguoiLienHeCSYT">
                                        <i class="fas fa-upload mr-1"></i> Import
                                        </button>*@
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane" role="tabpanel" id="tabTimKiem">
                            <form name="frmTimKiemNDBH" novalidate="novalidate" method="post">
                                <div class="row mg-t-6">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label>Công ty bảo hiểm</label>
                                            <select class="select2 form-control custom-select" name="ma_doi_tac" style="width:100%">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label>Chi nhánh</label>
                                            <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width:100%">
                                                <option value="" data-select2-id="137">Chọn chi nhánh</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-3 d-none">
                                        <div class="form-group">
                                            <label>Từ hệ thống</label>
                                            <select class="select select2 form-control custom-select" name="tu_ht" style="width:100%">
                                                <option value="">Gọi từ hệ thống nào?</option>
                                                <option value="HTBT">Hệ thống bồi thường</option>
                                                <option value="CORE">Core bảo hiểm</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="form-group">
                                            <label class="_required">Ngày vào viện</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_vv" required display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="padding: 0.1em 0.5em !important;"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top:6px">
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label>Số GCN bảo hiểm</label>
                                            <input type="text" class="form-control" autocomplete="off" placeholder="Số giấy chứng nhận" name="gcn">
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label>Số hợp đồng</label>
                                            <input type="text" class="form-control" autocomplete="off" placeholder="Số hợp đồng" name="so_hd">
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label>Tên người được bảo hiểm</label>
                                            <input type="text" class="form-control" placeholder="Tên khách hàng" autocomplete="off" name="ten_kh">
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="form-group">
                                            <label for="">Ngày sinh</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker_null" autocomplete="off" name="ngay_sinh" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="padding: 0.1em 0.5em !important;"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mg-t-6" style="margin-top:6px">
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label class="">Số CMT/CCCD</label>
                                            <input type="text" autocomplete="off" placeholder="Số CMT/CCCD" class="form-control" name="nd_tim">
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label class="">Số điện thoại</label>
                                            <input type="text" autocomplete="off" placeholder="Số điện thoại" class="form-control" name="d_thoai">
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="">Nhóm sản phẩm</label>
                                            <select class="select2 form-control custom-select" name="lhnv" style="width:100%"></select>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnSearchNDBH">
                                            <i class="fa fa-search mr-2"></i>Tìm kiếm
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <div class="row">
                                <div class="col-12 mt-3">
                                    <div class="table-responsive" style="max-height: 250px;">
                                        <table id="tableDsGCNNguoi" class="table table-bordered fixed-header" style="width:270%">
                                            <thead class="font-weight-bold text-center uppercase">
                                                <tr>
                                                    <th style="width:2%">VIP</th>
                                                    <th style="width:3%">HĐ CŨ</th>
                                                    <th>TÊN NĐBH</th>
                                                    <th style="width:6%">CMND/CCCD</th>
                                                    <th style="width:5%">NGÀY SINH</th>
                                                    <th style="width:6%">TÊN GÓI BH</th>
                                                    <th style="width:8%">NGÀY HIỆU LỰC</th>
                                                    <th style="width:13%">TÊN NGƯỜI MUA</th>
                                                    <th style="width:7%">SỐ HĐ</th>
                                                    <th style="width:6%">ĐIỆN THOẠI</th>
                                                    <th style="width:6%">EMAIL</th>
                                                    <th style="width:13%">SẢN PHẨM</th>
                                                    <th style="width:6%">LOẠI HÌNH</th>
                                                    <th style="width:5%">SỐ GCN</th>
                                                    <th style="width:5%">ĐV CẤP ĐƠN</th>
                                                </tr>
                                            </thead>
                                            <tbody id="modalHealthSearchDsGCN">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                @*<div class="col-12">
                                <div id="gridViewTkiemNguoi" class="table-app scrollable" style="height: 29vh;"></div>
                                </div>*@
                            </div>
                        </div>
                        <div class="tab-pane" id="div_LSBT">
                            <h5>Lịch sử bồi thường</h5>
                            <div class="table-responsive" style="max-height: 66.5vh;" >
                                <table style="width:300%;" class="table table-bordered fixed-header">
                                    <thead class="text-center font-weight-bold">
                                        <tr>
                                            <th>Ngày mở</th>
                                            <th>Số hồ sơ</th>
                                            <th>Loại</th>
                                            <th>Ngày vv</th>
                                            <th>Ngày rv</th>
                                            <th>Hình thức điều trị</th>
                                            <th>Nguyên nhân</th>
                                            <th>Quyền lợi</th>
                                            <th>Cơ sở y tế</th>
                                            <th style="width: 25%;">Chẩn đoán</th>
                                            <th>Tiền yêu cầu</th>
                                            <th>Tiền duyệt</th>
                                            <th>Số ngày duyệt</th>
                                            <th>Trạng thái</th>
                                            <th>Lý do giảm trừ</th>
                                            <th>Ghi chú</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblLSTT">
                                    </tbody>
                                    <tfoot>
                                        <tr class="font-weight-bold text-center">
                                            <td colspan="10">Tổng cộng</td>
                                            <td class="text-right font-weight-bold text-danger" id="tongTienYeuCauLSTT"></td>
                                            <td class="text-right font-weight-bold text-danger" id="tongTienDuyetLSTT"></td>
                                            <td colspan="4"></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane" role="tabpanel" id="tabThongTinLienHe">
                            <form name="frmThongTinLienHe" novalidate="novalidate" method="post">
                                <input type="hidden" name="ma_doi_tac" value="">
                                <input type="hidden" name="ma_doi_tac_ql" value="">
                                <input type="hidden" name="ma_chi_nhanh_ql" value="">
                                <input type="hidden" name="so_id_hd_d" value="">
                                <input type="hidden" name="so_id_hd" value="">
                                <input type="hidden" name="so_id_dt" value="">
                                <input type="hidden" name="so_hd" value="">
                                <input type="hidden" name="so_id" value="">
                                <input type="hidden" name="so_hs" value="">
                                <div class="row">
                                    <div class="col-6">
                                        @*<div class="form-group">
                                        <label for="nguon" class="">Gói bảo hiểm</label>
                                        <input type="text" autocomplete="off" class="form-control" maxlength="100" name="goi_bh" readonly="readonly">
                                        </div>
                                        <button type="button" class="btn btn-primary btn-sm" id="btnThongTinGoiBH">
                                        Gói BH<i class="fas fa-chevron-right ml-2"></i>
                                        </button>*@

                                        <div class="form-group">
                                            <label for="nguon" class="">Gói bảo hiểm</label>
                                            <div class="input-group" style="cursor:pointer">
                                                <input type="text" autocomplete="off" class="form-control" name="goi_bh" value="Chưa xác đính" readonly="readonly">
                                                @* <div class="input-group-append">
                                                    <label class="input-group-text">
                                                        <a href="javascript:void(0)" id="btnThongTinGoiBH">
                                                            <i class="fas fa-edit" title="Sửa quyền lợi gói"></i>
                                                        </a>
                                                    </label>
                                                </div> *@
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label class="">Giờ thông báo</label>
                                            <div class="input-group bootstrap-timepicker timepicker">
                                                <input class="form-control input-small time" name="gio_tb" placeholder="HH:mm" type="text" autocomplete="off" tabindex="24" spellcheck="false">
                                                <div class="input-group-append">
                                                    <span class="input-group-text">
                                                        <span class="ti-calendar"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label class="_required">Ngày thông báo</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" required="" name="ngay_tb" display-format="date" value-format="number" placeholder="mm/dd/yyyy" autocomplete="off" tabindex="25" spellcheck="false">
                                                <div class="input-group-append">
                                                    <span class="input-group-text"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="m-0 pd-y-10">Thông tin người được bảo hiểm</h5>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="_required">Họ và tên</label>
                                            <input type="text" autocomplete="off" class="form-control" maxlength="100" name="ten" placeholder="VD: Nguyễn Văn A">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="_required">Ngày sinh</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" autocomplete="off" required display-format="date" value-format="number" name="ngay_sinh" placeholder="mm/dd/yyyy" readonly="readonly">
                                                <div class="input-group-append">
                                                    <span class="input-group-text"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="">Điện thoại</label>
                                            <input type="text" autocomplete="off" class="form-control phone" name="dien_thoai" placeholder="VD: 0972xxxxxx" im-insert="true">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label>Email</label>
                                            <input type="text" autocomplete="off" class="form-control email-inputmask" maxlength="100" name="email" placeholder="VD: <EMAIL>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="m-0 pd-y-10">Thông tin người liên hệ</h5>
                                    </div>
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox">
                                            <input class="custom-control-input" type="checkbox" id="chkThamGiaLienHe" value="option1">
                                            <label class="custom-control-label" for="chkThamGiaLienHe" style="font-size:12px"><b style="font-weight:bold">Người được bảo hiểm đồng thời là người liên hệ</b></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top:5px;">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="_required">Họ và tên</label>
                                            <input type="text" autocomplete="off" class="form-control" required="" maxlength="100" name="nguoi_lh" placeholder="VD: Nguyễn Văn A">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="_required">Mối quan hệ với người được bảo hiểm</label>
                                            <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="nguoi_lhla" required="">
                                                <option value="">Chọn mối quan hệ</option>
                                                <option value="BO">Bố</option>
                                                <option value="ME">Mẹ</option>
                                                <option value="BAN_THAN">Bản thân</option>
                                                <option value="KHAC">Khác (Anh/Chị/Em...)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="">Điện thoại</label>
                                            <input type="text" autocomplete="off" class="form-control phone" name="dthoai_nguoi_lh" placeholder="VD: 0972xxxxxx" im-insert="true">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label class="">Email</label>
                                            <input type="text" fn-validate="validateEmailControl" autocomplete="off" class="form-control email-inputmask" maxlength="100" name="email_nguoi_lh" placeholder="VD: <EMAIL>">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                @*<button type="button" class="btn btn-primary btn-sm" id="btnThongTinGoiBH">
                Gói BH<i class="fas fa-chevron-right ml-2"></i>
                </button>*@
                @* <button type="button" class="btn btn-primary btn-sm mr-auto" id="btnThemNg">
                    <i class="fas fa-plus-square mr-2" title="Thêm người/đối tượng vào hợp đồng"></i>Thêm người
                </button> *@
                <button type="button" class="btn btn-primary btn-sm" id="btnLSBT">
                    <i class="fas fa-history mr-2"></i>Lịch sử bồi thường hợp đồng
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="btnBack">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="btnTiepTheo">
                    Tiếp theo<i class="fas fa-chevron-right ml-2"></i>
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThayDoiDTBH" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:60vw">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Tìm kiếm người được bảo hiểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="card border mb-0 p-2">
                <form name="frmTimKiemDTBH" novalidate="novalidate" method="post">
                    <div class="row mg-t-6">
                        <div class="col-6">
                            <div class="form-group">
                                <label>Công ty bảo hiểm</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width:100%">
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Chi nhánh</label>
                                <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width:100%">
                                    <option value="" data-select2-id="137">Chọn chi nhánh</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:6px">
                        <div class="col-3">
                            <div class="form-group">
                                <label>Số GCN bảo hiểm</label>
                                <input type="text" class="form-control" autocomplete="off" placeholder="Số giấy chứng nhận" name="gcn">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Số hợp đồng</label>
                                <input type="text" class="form-control" autocomplete="off" placeholder="Số hợp đồng" name="so_hd">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Tên người được bảo hiểm</label>
                                <input type="text" class="form-control" placeholder="Tên khách hàng" autocomplete="off" name="ten_kh">
                            </div>
                        </div>
                    </div>
                    <div class="row mg-t-6" style="margin-top:6px">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Số CMT/CCCD/ Ngày sinh</label>
                                <input type="text" fn-validate="validateCMTControl" autocomplete="off" required="" placeholder="Số CMT/CCCD/ Ngày sinh" class="form-control" name="nd_tim">
                            </div>
                        </div>
                        <div class="col-3">
                            <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnSearchDTBH">
                                <i class="fa fa-search mr-2"></i>Tìm kiếm
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height:320px">
                            <table id="tbl_lan_bao_lanh_quyen_loi" class="table table-bordered fixed-header" style="width:170%">
                                <thead class="font-weight-bold card-title-bg-primary">
                                    <tr class="text-center uppercase">
                                        <th style="width:200px">Tên NĐBH</th>
                                        <th style="width:150px">Số GCN</th>
                                        <th style="width:115px">Số CCCD</th>
                                        <th style="width:115px">Điện thoại</th>
                                        <th style="width:150px">Email</th>
                                        <th style="width:200px">Tên người mua</th>
                                        <th style="width:150px">Số hợp đồng</th>
                                        <th>Đơn vị cấp đơn</th>
                                    </tr>
                                </thead>
                                <tbody id="tblDTBH">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm" id="btnLuuThayDoiDoiTuong">
                    <i class="fas fa-save mr-2"></i> Lưu thay đổi
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="DetailModal" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="titleUpdateContract">Thông tin chi tiết quyền lợi bảo lãnh</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-0">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body px-0">
                                    <div class="table-responsive">
                                        <table id="tbl_lan_bao_lanh_quyen_loi" class="table table-bordered fixed-header">
                                            <thead class="font-weight-bold card-title-bg-primary">
                                                <tr class="text-center uppercase">
                                                    <th>Tên loại chi phí</th>
                                                    <th>Số tiền</th>
                                                    <th>Ghi chú</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody id="detail_body">
                                                <tr class="text-center edit">
                                                    <td>Tiền khám</td>
                                                    <td class="text-right">
                                                        <input type="text" autocomplete="off" name="" class="decimal floating-input" value="200.000">
                                                    </td>
                                                    <td>
                                                        <input type="text" autocomplete="off" name="" class="floating-input" value="Khám chuyên gia">
                                                    </td>
                                                    <td class="text-center">
                                                        <i class="fa fa-trash text-danger remove_info"></i>
                                                    </td>
                                                </tr>
                                                <tr class="text-center edit">
                                                    <td>Tiền cận lâm sàng</td>
                                                    <td class="text-right">
                                                        <input type="text" autocomplete="off" name="" class="decimal floating-input" value="150.000">
                                                    </td>
                                                    <td>
                                                        <input type="text" autocomplete="off" name="" class="floating-input" value="Siêu âm họng">
                                                    </td>
                                                    <td class="text-center">
                                                        <i class="fa fa-trash text-danger remove_info"></i>
                                                    </td>
                                                </tr>
                                                <tr class="text-center edit">
                                                    <td>Tiền thuốc</td>
                                                    <td class="text-right">
                                                        <input type="text" autocomplete="off" name="" class="decimal floating-input" value="150.000">
                                                    </td>
                                                    <td>
                                                        <input type="text" autocomplete="off" name="" class="floating-input" value="Khám chuyên gia">
                                                    </td>
                                                    <td class="text-center">
                                                        <i class="fa fa-trash text-danger remove_info"></i>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr class="text-right">
                                                    <td colspan="4">
                                                        <a href="#" id="add_info">
                                                            <i class="fas fa-plus-square mr-2"></i>Thêm chi phí yêu cầu
                                                        </a>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalThemHMTT" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-header py-1">
                <h4 class="modal-title">Phân loại tài liệu</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" onclick="xoaSelectAnh()">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThemHMTT" name="frmThemHMTT" novalidate="novalidate" data-select2-id="frmThemHMTT" method="post">
                    <input type="hidden" name="index" value="">
                    <input type="hidden" name="bt" value="">
                    <input type="hidden" name="pm" value="BT">
                    <input type="hidden" name="hanh_dong" value="them_moi">
                    <div class="row">
                        <div class="col-12" id="frmThemHMTT_loai">
                            <div class="form-group">
                                <label class="_required">Nhóm tài liệu</label>
                                <select class="form-control select2" required="" name="loai" style="width:100%" disabled="disabled">
                                    <option value="TL">Giấy tờ, tài liệu</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Hạng mục tài liệu</label>
                                <div class="input-group">
                                    <select class="form-control select2" required="" name="hang_muc" style="width:100%"></select>
                                    <div class="input-group-append d-none">
                                        <label class="input-group-text">
                                            <a href="javascript:void(0)" id="btnThemHangMucTaiLieu">
                                                <i class="fas fa-plus-square" title="Thêm hạng mục tài liệu"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuHMTT">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-22" id="btnLuuDongHMTT">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" onclick="xoaSelectAnh()">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="popoverTraCuuBenh" class="popover popover-x popover-default" style="display:none; max-width:unset;width:650px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="btnClosePopover" class="close pull-right" data-dismiss="popover-x">&times;</span>Tra cứu bệnh
    </h3>
    <div class="popover-body popover-content">
        <div style="width:100%; margin-bottom:10px;">
            <div class="input-group">
                <input type="text" class="form-control" id="inputTimKiemBenhLy" placeholder="Tìm kiếm bệnh" value="" />
                <input type="hidden" id="inputTimKiemBenhLy_ma" />

                <div class="input-group-append">
                    <label class="input-group-text">
                        <a href="javascript:void(0)" onclick="getPagingBenhLy(1)">
                            <i class="fa fa-search"></i>
                        </a>
                    </label>
                </div>
            </div>
        </div>
        <div id="dsBenhLy" class="scrollable" style="max-height:450px">
        </div>
        <div id="dsBenhLy_pagination"></div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnTraCuuBenh">
            <i class="fa fa-save mr-2"></i>Lưu
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongTraCuuBenh">
            <i class="fas fa-window-close mr-2"></i>Đóng
        </button>
    </div>
</div>

<div id="modalThongTinCSYT" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin cơ sở y tế</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body py-2">
                <form name="frmThongTinCSYT" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="nguon" class="_required">Cơ sở y tế</label>
                                <div class="input-group" style="cursor:pointer">
                                    <input type="text" name="benh_vien" required style="cursor:pointer;z-index:999" id="frmThongTinCSYT_benh_vien" onclick="chonBenhVien(this);" class="form-control" autocomplete="off" placeholder="Click chọn bệnh viện">
                                    <div class="input-group-append">
                                        <label class="input-group-text" for="">
                                            <a href="#" onclick="xoaChon(this,'BV')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label>Nhà thuốc</label>
                                <div class="input-group" style="cursor:pointer">
                                    <input type="text" name="nha_thuoc" style="cursor:pointer;z-index:999" id="frmThemHoSo_nha_thuoc" onclick="chonNhaThuoc(this);" class="form-control" autocomplete="off" placeholder="Click chọn nhà thuốc">
                                    <div class="input-group-append">
                                        <label class="input-group-text" for="">
                                            <a href="#" onclick="xoaChon(this,'NT')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h5 class="m-0 pd-y-10">Thông tin liên hệ với cơ sở y tế</h5>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Họ và tên</label>
                                <input type="text" autocomplete="off" class="form-control" maxlength="100" name="nguoi_tb" placeholder="VD: Nguyễn Văn A">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="">Điện thoại</label>
                                <input type="text" autocomplete="off" class="form-control phone" max-length="15" name="dthoai_nguoi_tb" placeholder="VD: 0972xxxxxx" im-insert="true">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="">Email</label>
                                <input type="text" autocomplete="off" class="form-control" maxlength="500" name="email_nguoi_tb" placeholder="VD: <EMAIL>;<EMAIL>">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnThongTinCSYT">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110" id="btnLuuDongThongTinCSYT">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="popoverGhiChu" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="btnDongPopperGhiChu" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
        <span class="mr-2">
            <a href="#" onclick="chonGhiChu(this)" style="font-size: 12px; margin-left: 15px;">
                <i class="fa fa-bars mr-1"></i>
                Chọn ghi chú
            </a>
        </span>
        <span>
            <a href="#" id="btnThemGhiChu" style="font-size: 12px;">
                <i class="fas fa-plus-square mr-1"></i>
                Tạo ghi chú
            </a>
        </span>
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuChiPhi" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChu">
                    <textarea class="form-control" id="divGhiChu_NoiDung" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuGhiChu">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div id="popoverNguyenNhanGiamTru" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_nguyenNhanGiamTru" class="close pull-right" data-dismiss="popover-x">&times;</span>Lý do giảm trừ
    </h3>
    <div class="popover-body popover-content">
        <form name="frmNguyenNhanGiamTru" method="post">
            <div class="row">
                <div class="col-12" id="divNguyenNhan">
                    <textarea readonly class="form-control" id="divNguyenNhanGiamTru" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
</div>

<div id="modalHuyHoSo" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Lý do hủy hồ sơ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmLyDoHuyHoSo" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Lý do hủy hồ sơ</label>
                                <textarea class="form-control" required maxlength="500" name="ly_do" placeholder="Lý do"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuLyDoHuy">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modalTuChoiBL" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Lý do từ chối bảo lãnh</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmLyDoTuChoiBL" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Lý do từ chối bảo lãnh</label>
                                <textarea class="form-control" required maxlength="500" name="nd_tchoi" placeholder="Lý do"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuTuChoiBL">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modalChiTietChiPhi" class="modal fade" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:64%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin chi tiết chi phí</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="padding-top:5px;">
                <div class="row">
                    <div class="col-12">
                        <form name="frmChiPhiChiTiet" method="post">
                            <input type="hidden" name="ma_ct" />
                            <input type="hidden" name="tien_yc" />
                            <div class="table-responsive TABLE_CHI_PHI" id="CHI_PHI_KB" style="max-height:400px">
                                <table class="table table-bordered fixed-header">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>Tên chi phí khám</th>
                                            <th style="width:130px">Số tiền</th>
                                            <th style="width:130px">Giá tham khảo</th>
                                            <th style="width:130px">
                                                <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                                                    <input type="checkbox" onchange="onMacDinhChiPhi(this,'ALL_KB')" id="check_chi_phi_kb" value="1" checked="checked" class="custom-control-input">
                                                    <label class="custom-control-label" for="check_chi_phi_kb">Mặc định</label>
                                                </div>
                                            </th>
                                            <th style="width:40px"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblChiPhiKhamBenh">
                                    </tbody>
                                    <tfoot style="height:34.6px;">
                                        <tr class="text-left card-title-bg">
                                            <td>
                                                <a href="javascript:void(0)" onclick="chonChiPhiKhamBenh(this)" class="mr-3">
                                                    <i class="fas fa-plus-square mr-1"></i> Thêm chi phí khám bệnh
                                                </a>
                                                <a href="javascript:void(0)" onclick="showThemDMChiPhi(this,'KB')">
                                                    <i class="fas fa-plus-square mr-1"></i> Tạo mã chi phí khám bệnh
                                                </a>
                                            </td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiKhamBenh_TongTienKham">0</td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiKhamBenh_TongGiaThamKhao"></td>
                                            <td class="text-center">
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="table-responsive TABLE_CHI_PHI" id="CHI_PHI_TH" style="max-height:400px">
                                <table class="table table-bordered fixed-header">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>Tên thuốc</th>
                                            <th style="width:90px">Đvị tính</th>
                                            <th style="width:90px">Số lượng</th>
                                            <th style="width:100px">Đơn giá</th>
                                            <th style="width:100px">Thành tiền</th>
                                            <th style="width:100px">Giá tham khảo</th>
                                            <th style="width:130px">
                                                <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                                                    <input type="checkbox" onchange="onMacDinhChiPhi(this,'ALL_TH')" id="check_chi_phi_th" value="1" checked="checked" class="custom-control-input">
                                                    <label class="custom-control-label" for="check_chi_phi_th">Mặc định</label>
                                                </div>
                                            </th>
                                            <th style="width:40px"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblChiPhiThuoc">
                                    </tbody>
                                    <tfoot style="height:34.6px;">
                                        <tr class="text-left card-title-bg">
                                            <td colspan="4">
                                                <a href="javascript:void(0)" onclick="chonChiPhiThuoc(this)" class="mr-3">
                                                    <i class="fas fa-plus-square mr-1"></i> Thêm chi phí thuốc
                                                </a>
                                                <a href="javascript:void(0)" onclick="showThemDMChiPhi(this,'TH')">
                                                    <i class="fas fa-plus-square mr-1"></i> Tạo mã chi phí thuốc
                                                </a>
                                            </td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiThuoc_TongThanhTien">0</td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiThuoc_TongGiaThamKhao"></td>
                                            <td class="text-center">
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="table-responsive TABLE_CHI_PHI" id="CHI_PHI_KH" style="max-height:400px">
                                <table class="table table-bordered fixed-header">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>Tên chi phí</th>
                                            <th style="width:130px">Số tiền</th>
                                            <th style="width:200px">Ghi chú</th>
                                            <th style="width:130px">
                                                <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                                                    <input type="checkbox" onchange="onMacDinhChiPhi(this,'ALL_KH')" id="check_chi_phi_kh" value="1" checked="checked" class="custom-control-input">
                                                    <label class="custom-control-label" for="check_chi_phi_kh">Mặc định</label>
                                                </div>
                                            </th>
                                            <th style="width:40px"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblChiPhiKhac">
                                    </tbody>
                                    <tfoot style="height:34.6px;">
                                        <tr class="text-left card-title-bg">
                                            <td>
                                                <a href="javascript:void(0)" onclick="chonChiPhiKhac(this)" class="mr-3">
                                                    <i class="fas fa-plus-square mr-1"></i> Thêm chi phí khác
                                                </a>
                                                <a href="javascript:void(0)" onclick="showThemDMChiPhi(this,'KH')">
                                                    <i class="fas fa-plus-square mr-1"></i> Tạo mã chi phí khác
                                                </a>
                                            </td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiKhac_TongSoTien"></td>
                                            <td></td>
                                            <td class="text-center">
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuChiTietChiPhi">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-22" id="btnLuuDongChiTietChiPhi">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="popoverDMChiPhi" class="popover popover-x popover-default" style="display:none; width:450px; max-width:unset">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" onclick="dongDMChiPhi()" data-dismiss="popover-x">&times;</span>Thêm danh mục chi phí
    </h3>
    <div class="popover-body popover-content">
        <form name="frmDMChiPhi" method="post">
            <input type="hidden" name="ma_doi_tac" value="" />
            <input type="hidden" name="loai" value="" />
            <div class="row">
                <div class="col-6">
                    <div class="form-group">
                        <label class="">Mã cấp trên</label>
                        <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="ma_ct">
                        </select>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label class="_required">Mã</label>
                        <input type="text" name="ma" autocomplete="off" onclick="focusInput(this)" required class="form-control" value="" placeholder="Mã chi phí" />
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <label class="_required">Tên</label>
                        <input type="text" name="ten" autocomplete="off" onclick="focusInput(this)" required class="form-control" value="" placeholder="Tên chi phí" />
                    </div>
                </div>
            </div>
            <div class="row" id="cp_thuoc">
                <div class="col-6">
                    <div class="form-group">
                        <label>Đơn vị tính</label>
                        <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="dvi_tinh">
                        </select>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label>Giá</label>
                        <input type="text" name="gia" autocomplete="off" onclick="focusInput(this)" class="form-control number" value="" placeholder="Giá chi phí" />
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-15" id="btnLuuThongTinChiPhi">
            <i class="fa fa-save mr-2"></i>Lưu
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-15" id="btnLuuDongThongTinChiPhi" data-dismiss="modal">
            <i class="fa fa-hdd mr-2"></i>Lưu & đóng
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-15" data-dismiss="modal" onclick="dongDMChiPhi()">
            <i class="fas fa-window-close mr-2"></i>Đóng
        </button>
    </div>
</div>

<div id="popoverGhiChuLSTT" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChuLSTT" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuChiPhiLSTT" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChuLSTT">
                    <textarea readonly class="form-control" id="divGhiChu_NoiDungLSTT" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
</div>

<div id="popoverGhiChuQloi" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="btnDongPopperGhiChuQloi" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuQloi" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChuQloi">
                    <textarea class="form-control" id="divGhiChuQloi_NoiDung" rows="10" readonly></textarea>
                </div>
            </div>
        </form>
    </div>
</div>

<div id="popoverGhiChuBoSungHSGT" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 350px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="btnDongPopperGhiChuBoSungHSGT" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuBoSungHSGT" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChuBoSungHSGT">
                    <textarea class="form-control" id="divGhiChuBoSungHSGT_NoiDung" rows="8"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuGhiChuBoSungHSGT">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>
<div class="modal fade show" id="modalThemNguoiHD" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document" style="max-width:1300px">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h3>Thêm đối tượng bảo hiểm</h3>
                <button type="button" class="close" onclick="anHienModalAddNg()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form class="form" name="frmTTHD" method="post">
                    <div style="width:100%; padding:0 15px;">
                        <h5>Thông tin hợp đồng bảo hiểm</h5>
                        <div class="row">
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label class="_required">Số hợp đồng</label>
                                    <div class="input-group cursor-pointer">
                                        <input type="text" autocomplete="off" name="so_hd" onclick="traHD()" placeholder="Click chọn" required="" class="form-control" title="Nhập hợp đồng">
                                        @*<div class="input-group-append">
                                        <label class="input-group-text" for="">
                                        <a href="#" onclick="xoaChon(this,'ICD')">
                                        <i class="fas fa-times"></i>
                                        </a>
                                        </label>
                                        </div>*@
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4 TTHD">
                                <div class="form-group">
                                    <label class="">Đối tác</label>
                                    <select class="select2 form-control custom-select" name="ma_doi_tac_ql" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-sm-4 TTHD">
                                <div class="form-group">
                                    <label class="">Chi nhánh</label>
                                    <select class="select2 form-control custom-select" name="ma_chi_nhanh_ql" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-sm-2 TTHD">
                                <div class="form-group">
                                    <label class="">Khách hàng</label>
                                    <input type="text" autocomplete="off" class="form-control" readonly name="khach_hang">
                                </div>
                            </div>
                            <div class="col-sm-2 TTHD">
                                <div class="form-group">
                                    <label class="">Kiểu hợp đồng</label>
                                    <select class="select2 form-control custom-select" name="kieu_hd" style="width:100%" disabled>
                                        <option value="">Chọn kiểu hợp đồng</option>
                                        <option value="G">Gốc</option>
                                        <option value="B">Bổ sung</option>
                                        <option value="T">Tái tục</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-2 TTHD">
                                <div class="form-group">
                                    <label>Số hợp đồng gốc</label>
                                    <input type="text" autocomplete="off" class="form-control" placeholder="Số hợp đồng gốc" maxlength="50" readonly name="so_hd_goc">
                                </div>
                            </div>
                            <div class="col-sm-2 TTHD">
                                <div class="form-group">
                                    <label class="">Ngày cấp</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_cap" placeholder="mm/dd/yyyy" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2 TTHD">
                                <div class="form-group">
                                    <label class="">Ngày hiệu lực</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_hl" placeholder="mm/dd/yyyy" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2 TTHD">
                                <div class="form-group">
                                    <label class="">Ngày kết thúc</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_kt" placeholder="mm/dd/yyyy" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2 TTHD">
                                <div class="form-group">
                                    <label class="">Trạng thái</label>
                                    <select class="select2 form-control custom-select" name="trang_thai" style="width:100%" disabled>
                                        <option value="">Chọn trạng thái</option>
                                        <option value="D">Đã duyệt</option>
                                        <option value="C">Chưa duyệt</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
                <form class="form" name="frmNguoiDBH" method="post">
                    <div style="width:100%; padding:0 15px;">
                        <label class="mt-4"> <h5 class="m-0">Thông tin chi tiết người bảo hiểm</h5></label>
                        <input type="hidden" name="so_id" />
                        <input type="hidden" name="so_id_dt" />
                        <input type="hidden" name="ma_doi_tac" />
                        <input type="hidden" name="ma_chi_nhanh" />
                        <input type="hidden" name="phong" />
                        <input type="hidden" name="dkbs" />
                        <div class="row">
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label class="_required">Tên người được bảo hiểm</label>
                                    <input type="text" name="ten" autocomplete="off" required class="form-control" placeholder="VD: Nguyễn Văn A">
                                </div>
                            </div>

                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label class="_required">Ngày sinh</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" autocomplete="off" required display-format="date" value-format="number" name="ngay_sinh" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label class="_required">Giới tính</label>
                                    <select class="select2 form-control custom-select" required name="gioi_tinh" style="width: 100%; height:36px;">
                                        <option selected value="">Chọn giới tính</option>
                                        <option value="NAM">Nam</option>
                                        <option value="NU">Nữ</option>
                                        <option value="KHAC">Khác</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label class="">Số điện thoại</label>
                                    <input type="text" class="form-control phone" autocomplete="off" maxlength="11" name="d_thoai" im-insert="true" placeholder="VD: 0968xxxxxx">
                                </div>
                            </div>

                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label class="_required">Số CCCD</label>
                                    <input type="text" name="so_cmt" autocomplete="off" fn-validate="validateCMTControl" maxlength="12" required class="form-control" placeholder="Số căn cước công dân">
                                </div>
                            </div>

                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="text" name="email" autocomplete="off" fn-validate="validateEmailControl" maxlength="100" class="form-control" placeholder="Nhập địa chỉ email">
                                </div>
                            </div>

                            <div class="col-sm-5">
                                <div class="form-group">
                                    <label class="_required">Địa chỉ</label>
                                    <input type="text" name="dchi" autocomplete="off" maxlength="250" required class="form-control" placeholder="Nhập địa chỉ">
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="">Giấy chứng nhận</label>
                                    <input type="text" name="gcn" autocomplete="off" maxlength="50" class="form-control" placeholder="Nhập giấy chứng nhận">
                                </div>
                            </div>

                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label class="_required">Ngày cấp</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" autocomplete="off" required display-format="date" value-format="number" name="ngay_cap" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label class="_required">Giờ hiệu lực</label>
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input class="form-control input-small time" autocomplete="off" placeholder="HH:mm" required name="gio_hl" type="text" />
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <span class="ti-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label class="_required">Ngày hiệu lực</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" autocomplete="off" required display-format="date" value-format="number" name="ngay_hl" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label class="_required">Giờ kết thúc</label>
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input class="form-control input-small time" autocomplete="off" placeholder="HH:mm" required name="gio_kt" type="text" />
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <span class="ti-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label class="_required">Ngày kết thúc</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" autocomplete="off" required display-format="date" value-format="number" name="ngay_kt" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="_required">Sản phẩm</label>
                                    <select class="select2 form-control custom-select select2-hidden-accessible" required name="lhnv" style="width: 100%; height:36px;"></select>
                                </div>
                            </div>
                            <div class="col-5">
                                <div class="form-group">
                                    <label class="">Nhóm gói bảo hiểm</label>
                                    <select class="select2 form-control custom-select select2-hidden-accessible" name="nhom_goi" style="width: 100%; height:36px;"></select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Gói bảo hiểm</label>
                                    <div class="input-group" style="cursor:pointer">
                                        <input type="text" name="goi_bh" style="cursor:pointer;" required="" data-id-goi="" onclick="chonDsGoiBH(this);" class="form-control" autocomplete="off" placeholder="Click chọn gói bảo hiểm">                                                                                        <div class="input-group-append">
                                            <label class="input-group-text" for="">
                                                <a href="#" onclick="xemChiTietquyenLoiGoiBH()" data-dismiss="modal">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="">Thông tin người phụ thuộc</label>
                                    <input type="text" name="ten_phu_thuoc" autocomplete="off" maxlength="250" class="form-control" placeholder="Trường hợp NĐBH là người thân">
                                </div>
                            </div>
                            <div class="col-5">
                                <div class="form-group">
                                    <label class="">Đơn vị công tác</label>
                                    <input type="text" name="dvi_ctac" autocomplete="off" maxlength="250" class="form-control" placeholder="Nhập đơn vị công tác">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="">Chi nhánh công tác</label>
                                    <input type="text" name="cnhanh_ctac" autocomplete="off" maxlength="250" class="form-control" placeholder="Nhập chi nhánh">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="">Mã nhân viên</label>
                                    <input type="text" name="manv_ctac" autocomplete="off" maxlength="250" class="form-control" placeholder="Nhập mã nhân viên">
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="">Tiền lương</label>
                                    <input type="text" name="tien_luong" autocomplete="off" maxlength="250" class="form-control number" placeholder="Nhập tiền lương">
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="">Chức vụ</label>
                                    <input type="text" name="cvu_ctac" autocomplete="off" maxlength="250" class="form-control" placeholder="Nhập chức vụ">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="">Phòng ban công tác</label>
                                    <input type="text" name="pban_ctac" autocomplete="off" maxlength="250" class="form-control" placeholder="Nhập phòng ban">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3 mb-1">
                            <div class="col-md-12 text-right">
                                <button type="button" class="btn btn-primary btn-sm wd-80 mr-1" id="btnLuuNG">
                                    <i class="fa fa-save"></i>&nbsp;&nbsp;Lưu
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm wd-80 mr-1" id="btnMoiNG">
                                    <i class="fas fa-sync"></i>&nbsp;&nbsp;Mới
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm wd-80 " onclick="anHienModalAddNg()">
                                    <i class="fas fa-times"></i>&nbsp;&nbsp;Đóng
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div id="popoverTraCuuHD" class="popover popover-x popover-default" style="display:none; max-width:unset;width:650px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x">&times;</span>Tra cứu hợp đồng
    </h3>
    <div class="popover-body popover-content">
        <form name="frmTraCuuHD">
            <div style="width:100%; margin-bottom:10px;" class="row">
                <div class="col-3">
                    <div class="form-group">
                        <label for="ngay_d">Từ ngày</label>
                        <div class="input-group">
                            <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_d" placeholder="mm/dd/yyyy" tabindex="8" spellcheck="false">
                            <div class="input-group-append">
                                <span class="input-group-text px-2"><span class="ti-calendar"></span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="form-group">
                        <label for="ngay_c">Đến ngày</label>
                        <div class="input-group">
                            <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_c" placeholder="mm/dd/yyyy" tabindex="9" spellcheck="false">
                            <div class="input-group-append">
                                <span class="input-group-text px-2"><span class="ti-calendar"></span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="inputTimKiemHD" placeholder="Tìm kiếm hợp đồng" value="" />
                            <input type="hidden" id="inputTimKiemHD_ma" />

                            <div class="input-group-append">
                                <label class="input-group-text">
                                    <a href="javascript:void(0)" onclick="getPagingHD(1)">
                                        <i class="fa fa-search"></i>
                                    </a>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div id="dsHD" class="scrollable" style="max-height:450px">
        </div>
        <div id="dsHD_pagination"></div>
    </div>
</div>
<div id="modalGoiBH" class="modal-drag" style="width:400px; z-index:9999999; margin-top: 2px !important; margin-left: 77px !important;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn gói bảo hiểm</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_GoiBH" type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control item-goibh">
                <input type="hidden" id="modalGoiBHElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalGoiBHDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonGoiBH">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>
<div class="modal fade show" id="modalXemQuyenLoi" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document" style="max-width: 95vw; margin-top:10px;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Quyền lợi chi tiết gói bảo hiểm</h4>
                <button type="button" class="close" onclick="moThemNDBS()" aria-hidden="true">×</button>
            </div>
            <div class="modal-body px-2 py-0">
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="card border mb-2">
                            <div class="card mb-0">
                                <div class="card-body" style="padding:0px">
                                    <div class="row">
                                        <div class="col-12" id="navThongTinQuyenLoiDKBS">
                                            <ul class="nav nav-pills border-bottom" role="tablist" style="background-color:#f8f9fa">
                                                <li class="nav-item" style="font-weight:bold">
                                                    <a class="nav-link active" href="#tabThongTinQuyenLoiBaoHiem2" data-toggle="tab" role="tab" aria-controls="home" aria-selected="true">
                                                        <i class="far fa-file-search mr-2"></i> Quyền lợi chính
                                                    </a>
                                                </li>
                                                <li class="nav-item" style="font-weight:bold">
                                                    <a class="nav-link" href="#tabDieuKhoanBoSung2" data-toggle="tab" role="tab" aria-controls="profile" aria-selected="false">
                                                        <i class="fas fa-file-plus mr-2"></i> Điều khoản bổ sung
                                                    </a>
                                                </li>
                                            </ul>
                                            <div class="tab-content">
                                                <div class="tab-pane px-0 pt-2" id="tabThongTinQuyenLoiBaoHiem2" role="tabpanel">
                                                    <div class="table-responsive scrollable" style="max-height:500px;">
                                                        <table id="tb_quyenloi" class="table table-bordered fixed-header" style="width:130%">
                                                            <thead class="font-weight-bold text-center uppercase">
                                                                <tr>
                                                                    <th style="width: 350px">Điều khoản</th>
                                                                    <th style="width: 60px">Nguyên tệ</th>
                                                                    <th style="width:60px">Số lần/ngày</th>
                                                                    <th style="width:90px" class="d-none">Số ngày/lần khám</th>
                                                                    <th style="width:60px">Q.Lợi/ngày</th>
                                                                    <th style="width:60px" class="d-none">Q.Lợi/lần khám</th>
                                                                    <th style="width:80px">Q.Lợi/năm</th>
                                                                    <th style="width:80px">Kiểu AD</th>
                                                                    <th style="width:65px">Tỷ lệ đồng</th>
                                                                    <th style="width:60px">T.Gian chờ</th>
                                                                    <th style="width:65px">Phí BH</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="tableNhapQLoi">
                                                            </tbody>
                                                            <tfoot>
                                                                <tr class="text-left card-title-bg">
                                                                    <td class="font-weight-bold text-center" colspan="8">
                                                                        Tổng cộng
                                                                    </td>
                                                                    <td class="font-weight-bold text-right">
                                                                        <b style="font-weight:bold" id="tt_nguoi_phi_bh">0</b>
                                                                    </td>
                                                                </tr>
                                                            </tfoot>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div class="tab-pane px-0 pt-2" id="tabDieuKhoanBoSung2" role="tabpanel">
                                                    <div class="table-responsive" style="max-height:510px">
                                                        <table class="table table-bordered fixed-header" style="width:130%">
                                                            <thead class="font-weight-bold text-center uppercase">
                                                                <tr>
                                                                    <th style="width:20%">ĐIỀU KHOẢN BỔ SUNG</th>
                                                                    <th style="width:5%">NGUYÊN TỆ</th>
                                                                    <th style="width:17%">GHI CHÚ</th>
                                                                    <th style="width:7%">SỐ LẦN/NGÀY</th>
                                                                    <th style="width:7%">TIỀN/NGÀY</th>
                                                                    <th style="width:7%">TIỀN NĂM</th>
                                                                    <th style="width:10%">ĐỒNG BẢO HIỂM (%)</th>
                                                                    <th style="width:8%">SỐ NGÀY CHỜ</th>
                                                                    <th style="width:8%">PHÍ</th>
                                                                    <th style="width:8%">TỶ LỆ PHÍ (%)</th>
                                                                    <th style="width:6%; text-align:center"></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="tableDKBS"></tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" onclick="moThemNDBS()">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>
<div id="popoverNguyenNhan" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 450px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popNguyenNhan" class="close pull-right" data-dismiss="popover-x">&times;</span>
        <span class="mr-2">
            <a href="#" onclick="chonNguyenNhan(this)" style="font-size: 12px; margin-left: 15px;" id="btnChonNguyenNhan">
                <i class="fa fa-bars mr-1"></i>
                Chọn nguyên nhân giảm trừ
            </a>
        </span>
        <span>
            <a href="#" id="btnNhapNguyenNhan" style="font-size: 12px;">
                <i class="fas fa-plus-square mr-1"></i>
                Nhập nguyên nhân giảm trừ
            </a>
        </span>
    </h3>
    <div class="popover-body popover-content">
        <form name="frmNguyenNhan" method="post">
            <div class="row">
                <div class="col-12" id="divNguyenNhan">
                    <textarea class="form-control" id="divNguyenNhan_NoiDung" rows="10" placeholder="Nhập nguyên nhân giảm trừ"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuNguyenNhan">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div id="modalCapNhatUocTheoDiem" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width: 62%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cập nhật ước theo điểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmCapNhatUocTheoDiem" name="frmCapNhatUocTheoDiem" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:380px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>STT</th>
                                            <th>Ngày dự phòng</th>
                                            <th>Điểm dự phòng</th>
                                            <th style="width: 80px">Nghiệp vụ</th>
                                            <th style="width: 110px">Mã quyền lợi</th>
                                            <th style="width: 120px">Số tiền dự phòng</th>
                                            <th style="width: 120px">Tiền chênh lệch</th>
                                            <th>Trạng thái tích hợp</th>
                                            <th>Log</th>
                                            <th>Hành động</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblCapNhatUocTheoDiem"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0" style="display:block;">
                <button type="button" class="btn btn-primary btn-sm mg-t-22 d-none" id="btnDieuChinhBienDo"><i class="fas fa-window-alt mr-2"></i>Điều chỉnh biên độ</button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right d-none" id="btnThemDiemCapNhatUoc"><i class="fas fa-hdd mr-2"></i>Điều chỉnh tăng giảm dự phòng</button>
            </div>
        </div>
    </div>
</div>

<div id="popoverLogRq" class="popover popover-x popover-default" style="display:none; width:500px; max-width: 500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Xem log
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divLogRq">
                <label>Data Request</label>
                <textarea class="form-control" id="divLogRq_NoiDung" name="log_rq" readonly rows="6"></textarea>
            </div>
        </div>
        <div class="row">
            <div class="col-12" id="divLogRes">
                <label>Data Response</label>
                <textarea class="form-control" id="divLogRes_NoiDung" name="log_res" readonly rows="6"></textarea>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalBienDo" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 25%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Điều chỉnh biên độ ước</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="frmBienDo" name="frmBienDo" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:380px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th style="width: 200px">Điểm</th>
                                            <th style="width: 100px">Biên độ ước (%)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblBienDo"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer d-block pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right" id="btnLuuBienDo"><i class="fas fa-save mr-2"></i>Lưu</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalThemDiem" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 40%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Thêm điểm dự phòng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form name="frmThemDiem" method="post">
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Ngày dự phòng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" required name="ngay_dp" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" name="lh_nv" required style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Số tiền dự phòng</label>
                                <input type="text" name="tien" id="tien" required autocomplete="off" spellcheck="false" placeholder="Số tiền dự phòng" class="form-control number">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuThemDiem"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>
<div id="modalChonDVTT" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog modal-dialog-centered" style="width: 390px;">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-body ">
                <h2 class="text-center"><p class="font-weight-bold">Thông báo</p></h2>
                <h6><p class="m-0">Chọn đơn vị thanh toán để chuyển thanh toán hồ sơ này</p></h6>
                <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="don_vi_thanh_toan" id="don_vi_thanh_toan">
                </select>
                <div class="mt-4 text-center">
                    <button type="button" class="btn btn-primary " id="btnChuyenKeToan" style="font-size: 12px">
                        <i class="fas fa-save mr-2"></i>Tiếp theo
                    </button>
                    <button type="button" class="btn btn-danger " data-dismiss="modal" style="font-size: 12px">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Phương thức chi trả đồng bảo hiểm-->
<div id="modalPhuongAnChiTra" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width: 60%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Tính toán trách nhiệm đồng bảo hiểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="table-responsive" style="max-height:300px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th>STT</th>
                                <th>Nhà bảo hiểm</th>
                                <th>Vai trò</th>
                                <th>Tỷ lệ</th>
                                <th>Số tiền trách nhiệm</th>
                            </tr>
                        </thead>
                        <tbody id="tblPhuongAnChiTra">
                        </tbody>
                        <tfoot>
                            <tr class="text-left card-title-bg">
                                <th class="align-middle" colspan="4">Tổng cộng</th>
                                <th class="py-1">
                                    <div class="d-flex flex-nowrap justify-content-between" style="column-gap:.5rem;">
                                        <span>Số tiền bồi thường:</span>
                                        <span id="modalPhuongAnChiTraSoTienBT" class="text-danger"></span>
                                    </div>
                                    <div class="d-flex flex-nowrap justify-content-between" style="column-gap:.5rem;">
                                        <span>Số tiền thuộc trách nhiệm nhà bảo hiểm:</span>
                                        <span id="modalPhuongAnChiTraSoTienTrachNhiemPJICO" class="text-danger"></span>
                                    </div>
                                    <div class="d-flex flex-nowrap justify-content-between" style="column-gap:.5rem;">
                                        <span>Số tiền thuộc trách nhiệm nhà đồng:</span>
                                        <span id="modalPhuongAnChiTraSoTienTrachNhiemNhaDong" class="text-danger"></span>
                                    </div>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <form name="frmPhuongAnChiTra" id="frmPhuongAnChiTra" method="post">
                    <div class="row mt-2">
                        <div class="col-12">
                            <h5>Phương án chi trả</h5>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" name="phuong_an_chi_tra" id="phuong_an_chi_tra_full" value="100" class="custom-control-input single_checked">
                                <label class="custom-control-label" for="phuong_an_chi_tra_full" style="cursor:pointer; padding-top:2px">Nhà bảo hiểm chi trả 100%</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" name="phuong_an_chi_tra" id="phuong_an_chi_tra_trach_nhiem" value="MUC_TRACH_NHIEM" class="custom-control-input single_checked">
                                <label class="custom-control-label" for="phuong_an_chi_tra_trach_nhiem" style="cursor:pointer; padding-top:2px">Nhà bảo hiểm chi trả theo % trách nhiệm</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuPhuongAnChiTra"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalThongTinChiTiet" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 55%;">
        <div class="modal-content" id="navThongTinChiTiet">
            <div class="modal-header p-2">
                <div class="row w-100 m-0 p-0">
                    <div class="col-12 pl-1 pr-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="tab-scroll-wrapper flex-grow-1" style="overflow: hidden;">
                                <ul class="nav nav-pills font-weight-bold m-0 d-flex tab-scroll-inner" style="width: 770px; flex-wrap: nowrap; overflow-x: auto; white-space: nowrap;">
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabNguyenNhan" class="nav-link active" onclick="showStepThongTinChiTiet('tabNguyenNhan')">
                                            <i class="fas fa-hospital-user mr-2"></i>Nguyên nhân
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabTrieuChung" class="nav-link" onclick="showStepThongTinChiTiet('tabTrieuChung')">
                                            <i class="fas fa-hospital-user mr-2"></i>Triệu chứng
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabCanLamSang" class="nav-link" onclick="showStepThongTinChiTiet('tabCanLamSang')">
                                            <i class="fas fa-hospital-user mr-2"></i>Cận Lâm sàng
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabNguyenTac" class="nav-link" onclick="showStepThongTinChiTiet('tabNguyenTac')">
                                            <i class="fas fa-hospital-user mr-2"></i>Nguyên tắc điều trị
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabDtriCuThe" class="nav-link" onclick="showStepThongTinChiTiet('tabDtriCuThe')">
                                            <i class="fas fa-hospital-user mr-2"></i>Điều trị cụ thể
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabBienChung" class="nav-link" onclick="showStepThongTinChiTiet('tabBienChung')">
                                            <i class="fas fa-hospital-user mr-2"></i>Biến chứng
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabKhac" class="nav-link" onclick="showStepThongTinChiTiet('tabKhac')">
                                            <i class="fas fa-hospital-user mr-2"></i>Khác
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabNguonTaiLieu" class="nav-link" onclick="showStepThongTinChiTiet('tabNguonTaiLieu')">
                                            <i class="fas fa-hospital-user mr-2"></i>Nguồn tài liệu
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <button type="button" class="close ml-2" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-body p-2">
                <div class="row w-100 m-0 p-0">
                    <div class="col-12 p-1">
                        <div class="tab-content">
                            <div class="tab-pane p-0" id="tabNguyenNhan" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableNguyenNhan" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Nguyên nhân</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblNguyenNhan"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabTrieuChung" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableTrieuChung" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Triệu chứng</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblTrieuChung"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabCanLamSang" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableCanLamSang" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Thông tin cận lâm sàng</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblCanLamSang"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabNguyenTac" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableNguyenTacDtri" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Nguyên tắc điều trị</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblNguyenTacDtri"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabDtriCuThe" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableDtriCuThe" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Điều trị cụ thể</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblDtriCuThe"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabBienChung" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableBienChung" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Biến chứng</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblBienChung"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabKhac" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableKhac" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Thông tin khác</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblKhac"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabNguonTaiLieu" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableNguonTaiLieu" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="5%" class="">STT</th>
                                                        <th width="95%" class="">Nguồn tài liệu</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblNguonTaiLieu"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-1" style="display: block;">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>