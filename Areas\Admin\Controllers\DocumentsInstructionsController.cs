﻿using ESCS.Attributes;
using ESCS.Common;
using ESCS.COMMON.Common;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.COMMON.Http;
using ESCS.Controllers;
using ESCS.MODEL.ESCS;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class DocumentsInstructionsController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        public static string ValidateAndSanitizeImagePath(string imagePath, string baseDirectory)
        {
            if (string.IsNullOrWhiteSpace(imagePath))
                return null;

            var fileName = Path.GetFileName(imagePath);
            if (fileName.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
                throw new ArgumentException("Tên file chứa ký tự không hợp lệ!");

            var fullPath = Path.GetFullPath(Path.Combine(baseDirectory, fileName));

            if (!fullPath.StartsWith(Path.GetFullPath(baseDirectory)))
                throw new ArgumentException("Đường dẫn file không hợp lệ!");

            return fileName;
        }

        public static string GenerateSafeFileName(string originalName)
        {
            var safeName = Path.GetFileNameWithoutExtension(originalName)
                .Replace(" ", "_")
                .ToLower();
            safeName = Regex.Replace(safeName, @"[^a-z0-9_]", "");
            return safeName + Path.GetExtension(originalName).ToLower();
        }

        //[AjaxOnly]
        //public async Task<IActionResult> Save(ht_tai_lieu_huong_dan_upload model)
        //{
        //    var user = GetUser();
        //    model.ma_doi_tac_nsd = user.ma_doi_tac;
        //    model.ma_chi_nhanh_nsd = user.ma_chi_nhanh;
        //    model.nsd = user.nsd;
        //    model.pas = user.pas;
        //    if (model.file != null && model.file.Length > 0)
        //    {
        //        var defineInfo = Request.GetDefineInfo();
        //        var arr_ext = new[] { ".pdf", ".docx" };

        //        var ext = Path.GetExtension(model.file.FileName).ToLower();
        //        if (!arr_ext.Contains(ext.ToLower()))
        //            throw new Exception("Định dạng file không phù hợp");

        //        model.url_file = model.ma_doi_tac_nsd + "/TAI_LIEU_HUONG_DAN/" + model.file.FileName;

        //        List<file_uploads> file = new List<file_uploads>();
        //        using (var ms = new MemoryStream())
        //        {
        //            model.file.CopyTo(ms);
        //            var fileBytes = ms.ToArray();
        //            var fileBase64 = Convert.ToBase64String(fileBytes);
        //            file.Add(new file_uploads()
        //            {
        //                ma_doi_tac = model.ma_doi_tac_nsd,
        //                loai = "TAI_LIEU_HUONG_DAN",
        //                path = model.url_file,
        //                file_base64 = fileBase64
        //            });
        //            var byteArr = Convert.FromBase64String(file[0].file_base64);
        //            var resUpload = await EscsUtils.UploadFileToPath(file, defineInfo);
        //            if (resUpload.state_info.status == STATUS_NOTOK)
        //            {
        //                return Ok(resUpload);
        //            }
        //        }
        //    }

        //    var data = await Request.GetResponeNew(StoredProcedure.PHT_TAI_LIEU_HUONG_DAN_NH, JsonConvert.SerializeObject(model));
        //    return Ok(data);
        //}

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_TAI_LIEU_HUONG_DAN_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_TAI_LIEU_HUONG_DAN_LKE_CT, json);
            return Ok(data);
        }
        [AjaxOnly]
        public async Task<IActionResult> danhMucTaiLieu()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_TAI_LIEU_HUONG_DAN_DMUC, json);
            return Ok(data);
        }
    }
}