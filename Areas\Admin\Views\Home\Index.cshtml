﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Dashboard";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    @*<video controls style="width:400px"
           src="https://cloudapi.escs.vn/api/carclaim/video-mobile/OTVLa1JxWVlheFIwRkduWG5HeG9ZTThESmpBS2FZZWRMR0FKa3JBdUpDclhvSGhpYk5uQTVRQjF2eU9Va0E4V1d5Qy8vaE1hK3pMRHIxMkdhWXVpSHpqUkNaZllub3lwdk5GRFRZUWNJQTFBRUo3ZDV1Mk9HRXBnakJxS1l5a3crV21vMWlseGhhdGN4dzN1eG41TGZRRnlnOUwwdGRxMksxTzJCaDVoNVRJPQ">
    </video>*@
</div>
<partial name="_Modal.cshtml" />
@section scripts{
    
}