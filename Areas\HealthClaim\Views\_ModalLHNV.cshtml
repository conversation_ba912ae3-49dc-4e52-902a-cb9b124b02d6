﻿<style>
    .table-scroll {
        position: relative;
        width: 100%;
        z-index: 1;
        margin: auto;
        overflow: auto;
    }

        .table-scroll table {
            width: 100%;
            min-width: 1280px;
            margin: auto;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table-scroll th {
            padding: 5px 10px;
            border: 1px solid #f8f8f8;
            vertical-align: top;
        }

        .table-scroll td {
            padding: 5px 10px;
            border: 1px solid #f8f8f8;
            background: #fff;
            vertical-align: top;
        }

        .table-scroll thead th {
            color: #fff;
            position: sticky;
            top: 0;
        }

    .modalLHNVItem:hover td {
        background-color: #f0f0f0 !important;
    }

    .modalLHNVItem.active td {
        background-color: #f0f0f0 !important;
        color: red !important;
        font-weight: bold;
    }

    #modalLHNVDanhSach > tr > td:first-child {
        position: sticky;
        left: 0;
        z-index: 50 !important;
    }

    #modalLHNVDanhSach > tr > td:nth-child(2) {
        position: sticky;
        left: 100px;
        z-index: 50 !important;
    }

    table:has( #modalLHNVDanhSach) > thead > tr.head > th:first-child {
        position: sticky;
        left: 0;
        z-index: 51 !important;
        background-color: var(--escs-main-theme-color)
    }

    table:has( #modalLHNVDanhSach) > thead > tr.head > th:nth-child(2) {
        position: sticky;
        left: 100px;
        z-index: 51 !important;
        background-color: var(--escs-main-theme-color)
    }
</style>
<div class="modal fade show" id="modalLHNV" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document" style="max-width: 95vw; margin-top:10px;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin quyền lợi bảo hiểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body px-2 py-0">
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="card border mb-2">
                            <div class="card mb-0">
                                <div class="card-body" style="padding:0px">
                                    <div class="row">
                                        <div class="col-12" id="navThongTinQuyenLoiDKBS">
                                            <div class="table-responsive table-scroll" style="max-height:71vh" id="table-scroll">
                                                <table class="table table-bordered" style="border-collapse: separate; border-spacing: 0;width:130%">
                                                    <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 53 !important;">
                                                        <tr class="head">
                                                            <th rowspan="2" style="text-align:center; width:100px; min-width:100px; vertical-align:middle;">Mã quyền lợi</th>
                                                            <th rowspan="2" style="text-align:center; min-width:400px; max-width:450px ;vertical-align:middle;">Tên Quyền lợi bảo hiểm</th>
                                                            <th rowspan="2" style="text-align:center; vertical-align:middle;">Nguyên tệ</th>
                                                            <th colspan="5" style="text-align:center">Quyền lợi bảo hiểm gốc</th>
                                                            <th colspan="2" style="text-align:center">Quyền lợi đã sử dụng</th>
                                                            <th colspan="2" style="text-align:center">Quyền lợi còn lại</th>
                                                            <th colspan="3" style="text-align:center">Quyền lợi khả dụng</th>
                                                            <th colspan="2" style="text-align:center">Thông tin quyền lợi</th>
                                                        </tr>
                                                        <tr class="text-center uppercase">
                                                            <th style="min-width: 80px;" class="text-nowrap">Tỷ lệ đồng</th>
                                                            <th style="min-width: 100px;" class="text-nowrap">Số ngày chờ</th>
                                                            <th style="min-width: 100px;" class="text-nowrap">Số ngày(lần)/năm</th>
                                                            <th style="min-width: 120px;" class="d-none">Số ngày/lần khám</th>
                                                            <th style="min-width: 100px;" class="text-nowrap">Q.Lợi/ngày(lần)</th>
                                                            <th style="min-width: 110px;" class="d-none">Q.Lợi/lần khám</th>
                                                            <th style="min-width: 100px;" class="text-nowrap">Q.Lợi/năm</th>
                                                            <th style="min-width: 90px;" class="text-nowrap">Số lần(ngày)</th>
                                                            <th style="min-width: 90px;" class="text-nowrap">Q.Lợi/năm</th>
                                                            <th style="min-width: 90px;" class="text-nowrap">Số lần(ngày)</th>
                                                            <th style="min-width: 90px;" class="text-nowrap">Q.Lợi/năm</th>
                                                            <th style="min-width: 90px;" class="text-nowrap">Số lần(ngày)</th>
                                                            <th style="min-width: 90px;" class="text-nowrap">Tiền lần(ngày)</th>
                                                            <th style="min-width: 90px;" class="text-nowrap">Q.Lợi/năm</th>
                                                            <th style="min-width: 100px;" class="text-nowrap">Kiểu áp dụng</th>
                                                            <th style="min-width: 200px;" class="d-none">Q.Lợi phụ thuộc</th>
                                                            <th style="min-width: 200px;" class="text-nowrap">Q.Lợi trừ lùi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="modalLHNVDanhSach">
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="modalLHNVDanhSachTemplate">
        <% if(chi_chon_la == 1){  %>
        @* ap dung chi duoc chon la *@
        <% _.forEach(danh_sach, function(item, index) { %>
        <% var thu_tu_cha_con = item.cap %>

        <% if(item.check_ql_la != '1'){ %>
        <% if(thu_tu_cha_con == 0){ %>
        <tr class="modalLHNVItem" style="cursor:pointer;color:black">

            <td class="text-center"><%- item.lh_nv %></td>
            <td>
                <a>
                    <%= item.ten_hien_thi %>[<%= item.lh_nv %>]
                </a>
            </td>
            <td class="text-center"><%- item.nt_tien_bh %></td>
            <td class="text-center"><%- item.dong_bh %> % </td>
            <td class="text-center"><%- item.so_ngay_cho %></td>
            <td class="text-center"><%- item.so_lan_ngay %></td>
            <td class="text-center d-none"><%- item.ngay_lan_kham %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
            <td class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
            <td class="text-center"><%- item.so_lan_ngay_duyet %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
            <td class="text-center"><%- item.so_lan_ngay_con %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
            <td class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay_kha_dung) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
            <td class="text-center"><%- item.kieu_ad_ten%> </td>
            <td class="text-center d-none"><%- item.lhnv_phu_thuoc %></td>
            <td class="text-center"><%- item.lhnv_tru_lui %></td>
        </tr>
        <% } else{ %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr class="modalLHNVItem" style="cursor:pointer;color:black">
            <td class="text-center"><%- item.lh_nv %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>[<%= item.lh_nv %>]
                </a>
            </td>
            <td class="text-center"><%- item.nt_tien_bh %></td>
            <td class="text-center"><%- item.dong_bh %> % </td>
            <td class="text-center"><%- item.so_ngay_cho %></td>
            <td class="text-center"><%- item.so_lan_ngay %></td>
            <td class="text-center d-none"><%- item.ngay_lan_kham %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
            <td class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
            <td class="text-center"><%- item.so_lan_ngay_duyet %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
            <td class="text-center"><%- item.so_lan_ngay_con %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
            <td class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay_kha_dung) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
            <td class="text-center"><%- item.kieu_ad_ten%> </td>
            <td class="text-center d-none"><%- item.lhnv_phu_thuoc %></td>
            <td class="text-center"><%- item.lhnv_tru_lui %></td>
        </tr>
        <% } %>
        <% }else{ %>
        <% if(thu_tu_cha_con == 0){ %>
        <tr class="modalLHNVItem" data-val="<%= item.lh_nv %>" style="cursor:pointer;color:blue" onclick="onChonLHNV(this)">
            <td class="text-center"><%- item.lh_nv %></td>
            <td>
                <a>
                    <%= item.ten_hien_thi %>[<%= item.lh_nv %>]
                </a>
            </td>
            <td class="text-center"><%- item.nt_tien_bh %></td>
            <td class="text-center"><%- item.dong_bh %> % </td>
            <td class="text-center"><%- item.so_ngay_cho %></td>
            <td class="text-center"><%- item.so_lan_ngay %></td>
            <td class="text-center d-none"><%- item.ngay_lan_kham %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
            <td class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
            <td class="text-center"><%- item.so_lan_ngay_duyet %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
            <td class="text-center"><%- item.so_lan_ngay_con %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
            <td class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay_kha_dung) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
            <td class="text-center"><%- item.kieu_ad_ten%> </td>
            <td class="text-center"><%- item.lhnv_phu_thuoc %></td>
            <td class="text-center d-none"><%- item.lhnv_tru_lui %></td>
        </tr>
        <% } else{ %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr class="modalLHNVItem" data-val="<%= item.lh_nv %>" style="cursor:pointer;color:var(--escs-main-theme-color)" onclick="onChonLHNV(this)">
            <td class="text-center"><%- item.lh_nv %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>[<%= item.lh_nv %>]
                </a>
            </td>
            <td class="text-center"><%- item.nt_tien_bh %></td>
            <td class="text-center"><%- item.dong_bh %> % </td>
            <td class="text-center"><%- item.so_ngay_cho %></td>
            <td class="text-center"><%- item.so_lan_ngay %></td>
            <td class="text-center d-none"><%- item.ngay_lan_kham %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
            <td class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
            <td class="text-center"><%- item.so_lan_ngay_duyet %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
            <td class="text-center"><%- item.so_lan_ngay_con %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
            <td class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay_kha_dung) %></td>
            <td class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
            <td class="text-center"><%- item.kieu_ad_ten%> </td>
            <td class="text-center d-none"><%- item.lhnv_phu_thuoc %></td>
            <td class="text-center"><%- item.lhnv_tru_lui %></td>
        </tr>
        <% } %>
        <% } %>
        <% }) %>
        <% }else{ %>
        @* Khong ap dung chi duoc chon la *@
        <% _.forEach(danh_sach, function(item, index) { %>
        <% var thu_tu_cha_con = item.lh_nv.split('.').length - 1 %>

        <% if(thu_tu_cha_con == 0){ %>
        <tr class="modalLHNVItem" data-val="<%= item.lh_nv %>" style="cursor: pointer; color: blue" onclick="onChonLHNV(this)">
            <td class="text-center"><%- item.lh_nv %></td>
            <td style="font-weight: bold">
                <a>
                    <%= item.ten_hien_thi %>[<%= item.lh_nv %>]
                </a>
            </td>
            <td class="text-center"><%- item.nt_tien_bh %></td>
            <td style="font-weight: bold" class="text-center"><%- item.dong_bh %> % </td>
            <td style="font-weight: bold" class="text-center"><%- item.so_ngay_cho %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
            <td style="font-weight: bold" class="text-center d-none"><%- item.ngay_lan_kham %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
            <td style="font-weight: bold" class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_duyet %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_con %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay_kha_dung) %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.kieu_ad_ten%> </td>
            <td style="font-weight: bold" class="text-center d-none"><%- item.lhnv_phu_thuoc %></td>
            <td style="font-weight: bold" class="text-center"><%- item.lhnv_tru_lui %></td>
        </tr>
        <% } else{ %>
        <% var pd = thu_tu_cha_con * 15 %>
        <tr class="modalLHNVItem" data-val="<%= item.lh_nv %>" style="cursor:pointer;color:blue" onclick="onChonLHNV(this)">
            <td class="text-center"><%- item.lh_nv %></td>
            <td style="font-style: italic; padding-left: <%- pd %>px">
                <a>
                    <%= item.ten_hien_thi %>[<%= item.lh_nv %>]
                </a>
            </td>
            <td class="text-center"><%- item.nt_tien_bh %></td>
            <td style="font-weight: bold" class="text-center"><%- item.dong_bh %> % </td>
            <td style="font-weight: bold" class="text-center"><%- item.so_ngay_cho %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay %></td>
            <td style="font-weight: bold" class="text-center d-none"><%- item.ngay_lan_kham %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay) %></td>
            <td style="font-weight: bold" class="text-right d-none"><%- ESUtil.formatMoney(item.tien_lan_kham) %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_duyet %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_duyet) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_con %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_con) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.so_lan_ngay_kha_dung %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_lan_ngay_kha_dung) %></td>
            <td style="font-weight: bold" class="text-right"><%- ESUtil.formatMoney(item.tien_nam_kha_dung) %></td>
            <td style="font-weight: bold" class="text-center"><%- item.kieu_ad_ten%> </td>
            <td style="font-weight: bold" class="text-center d-none"><%- item.lhnv_phu_thuoc %></td>
            <td style="font-weight: bold" class="text-center"><%- item.lhnv_tru_lui %></td>
        </tr>
        <% } %>
        <% })} %>
</script>