﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class CategoryBusinessController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> đoạn kinh doanh liệt kê
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> getPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_KHAC_GDKD_LKE, json);
            return Ok(data);
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> đoạn kinh doanh liệt kê chi tiết
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> getDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_KHAC_GDKD_LKE_CT, json);
            return Ok(data);
        }

        /// <summary>
        /// Gián đoạn kinh doanh nhập
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_KHAC_GDKD_NH, json);
            return Ok(data);
        }

        /// <summary>
        /// Gián đoạn kinh doanh xoa
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> delete()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_KHAC_GDKD_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_KHAC_GDKD_IMPORT_EXCEL, json);
            return Ok(data);
        }
    }
}