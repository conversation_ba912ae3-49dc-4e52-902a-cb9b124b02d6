﻿<style>
    .card-title-bg-primary {
        background-color: var(--escs_theme_color);
        color: white;
    }

    #bodyDsBaoGiaGaraDoc tr td {
        cursor: all-scroll;
    }

    #bodyDsBaoGiaGaraDoc td:hover {
        background-color: #e8eef3;
    }

    #bodyDsBaoGiaGaraDoc td {
        padding: 0px;
    }

        #bodyDsBaoGiaGaraDoc td a {
            display: block;
            width: 100%;
            height: 34px;
            padding: 8px;
        }

    .ttbaogia_HBG {
        color: red !important;
    }

    #dsAnhBaoGiaGara input {
        color: white !important;
        position: absolute !important;
        z-index: 9;
        opacity: 1;
        width: 16px;
        height: 16px;
        margin-left: 3px !important;
    }

    #tableDuLieuOCRBaoGiaGara td {
        padding: 0.35rem;
        vertical-align: middle;
    }

    #tableDuLieuOCRBaoGiaGara .card-title-bg {
        background-color: #f8f9fa;
    }

    #tableLayBGCanhTranh tr th,
    #tableLayBGCanhTranh tr td {
        padding: 0.3rem !important;
    }

    #modalLayBGCanhTranh .list-item-lan {
        position: relative;
        cursor: pointer;
    }

        #modalLayBGCanhTranh .list-item-lan .highlight {
            font-weight: bold;
            color: var(--danger);
        }

        #modalLayBGCanhTranh .list-item-lan:hover {
            background-color: var(--escs-main-theme-color);
        }

        #modalLayBGCanhTranh .list-item-lan.active {
            background-color: var(--escs-main-theme-color) !important;
        }

            #modalLayBGCanhTranh .list-item-lan.active,
            #modalLayBGCanhTranh .list-item-lan.active td,
            #modalLayBGCanhTranh .list-item-lan.active td > h6 {
                color: var(--white) !important;
            }
</style>
<!-- Modal thông tin bao gia -->
<div id="CarCompensationQuotation" class="modal fade" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog" style="max-width: 98%; margin:10px auto;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="CarCompensationQuotationLabel">Đề xuất phương án khắc phục</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body py-1" style="height:590px">
                <div class="row">
                    <div class="col-6">
                        <h5>Danh sách các Gara báo giá</h5>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <form id="frmSelectedGara" name="frmSelectedGara" novalidate="novalidate" method="post">
                            <input type="hidden" name="bt_gara" value="" />
                            <input type="hidden" name="gara" value="" />
                        </form>
                        <div class="table-responsive" style="max-height:190px;">
                            <table id="tableDsGaraBaoGia" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold card-title-bg-primary">
                                    <tr class="text-center uppercase">
                                        <th style="width: 40px;"></th>
                                        <th style="width: 40px;">STT</th>
                                        <th>Tên cơ sở sửa chữa, báo giá</th>
                                        <th style="width: 145px;">Đối tượng</th>
                                        <th style="width: 145px;">Báo giá</th>
                                        <th style="width: 110px;">Ngày báo giá</th>
                                        <th style="width: 120px;">Ngày KT báo giá</th>
                                        <th style="width: 80px;">Tổng tiền</th>
                                        <th style="width: 90px;">Tổng duyệt</th>
                                        <th style="width: 80px;">Hành động</th>
                                        <th style="width: 100px;">BG cạnh tranh</th>
                                    </tr>
                                </thead>
                                <tbody id="garaBaoGia">
                                </tbody>
                                <tfoot>
                                    <tr class="card-title-bg" style="height:45px;">
                                        <td colspan="4">
                                            <a href="#" class="d-none btn btn-sm btn-primary mr-2" id="btnChuyenBaoGia">
                                                <i class="fas fa-share mr-1"></i> Chuyển báo giá sang gara
                                            </a>
                                            <a href="#" class="btn btn-sm btn-primary" id="btnOCRBaoGia">
                                                <i class="fas fa-qrcode mr-1"></i> OCR báo giá
                                            </a>
                                            <a href="#" class="mr-2 float-right" id="btnGaraBaoGiaSoSanh" style="padding-top:6px">
                                                <i class="fas fa-exchange-alt mr-1"></i>So sánh báo giá
                                            </a>
                                        </td>
                                        <td colspan="7" class="text-right" style="vertical-align: middle;">
                                            <div class="dropdown mr-2" style="width:110px; display:initial;">
                                                <a href="#" role="button" id="dropdownMenuLinkDownload" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fas fa-download mr-2"></i>Download mẫu báo giá
                                                </a>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuLinkDownload">
                                                    <a class="dropdown-item" id="btnDownloadMauBaoGia" href="javascript:void(0)" style="font-size:12px; padding: 0.3rem 0.5rem !important;">
                                                        Báo giá ngang
                                                    </a>
                                                    <a class="dropdown-item" id="btnDownloadMauBaoGiaDoc" href="javascript:void(0)" style="font-size:12px; padding: 0.3rem 0.5rem !important;">
                                                        Báo giá dọc
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="dropdown mr-2" style="width:110px; display:initial;">
                                                <a href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fas fa-upload mr-2"></i>Upload báo giá
                                                </a>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                                    <a class="dropdown-item" id="btnUploadMauBaoGia" href="javascript:void(0)" style="font-size:12px; padding: 0.3rem 0.5rem !important;">
                                                        <label for="file_upload_bao_gia">Mẫu báo giá ngang</label>
                                                    </a>
                                                    <a class="dropdown-item" id="btnUploadMauBaoGiaDoc" href="javascript:void(0)" style="font-size:12px; padding: 0.3rem 0.5rem !important;">
                                                        <label for="file_upload_bao_gia_doc">Mẫu báo giá dọc</label>
                                                    </a>
                                                </div>
                                            </div>
                                            <a href="#" id="btnDsGaraHopTac" class="mr-2" data-toggle="inline-popup" data-target="#dsGaraHopTac" data-backdrop="static" data-placement="bottom bottom-right">
                                                <i class="fa fa-bars mr-2"></i>Danh sách gara hợp tác
                                            </a>
                                            <a href="#" class="btnThemBaoGia mr-2" data-toggle="inline-popup" data-target="#themBaoGia" data-backdrop="static" data-placement="bottom bottom-right">
                                                <i class="fas fa-plus-square mr-2"></i>Thêm Gara báo giá
                                            </a>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top:10px">
                    <div class="col-3 pr-0">
                        <div>
                            <div class="input-group py-2" style="padding-top:0px !important">
                                <input type="text" id="inputTimKiemHangMucBaoGia" class="form-control autocomplete" autocomplete="off" placeholder="Tìm kiếm hạng mục">
                                <div class="input-group-append">
                                    <label class="input-group-text">
                                        <a href="#"><i class="fas fa-search" title="Tìm kiếm hang mục"></i></a>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 pl-0" style="text-align:right">
                        <span id="tinhToanBGHienThi" style="display:block; margin-top: 7px">
                            <span class="mr-2">Giá trị xe: <span class="tinhToanBGGiaTriXe" style="color:red; font-weight:bold;">0</span></span>
                            <span class="mr-2">Số tiền bảo hiểm: <span class="tinhToanBGSoTienBH" style="color:red; font-weight:bold;">0</span></span>
                            <span class="mr-2">Tỷ lệ BH: <span class="tinhToanBGTyLeBH" style="color:red; font-weight:bold;">0</span></span>
                            <span class="mr-2">Tỷ lệ tổn thất/STBH: <span class="tinhToanBGTyLeTonThat" style="color:red; font-weight:bold;">0</span></span>
                            <span class="mr-2" id="tinhToanBGBoiThuongToanBo">
                                <a href="#" id="btnBoiThuongToanBo"><i class="fas fa-car-crash mr-1"></i> Bồi thường toàn bộ</a>
                            </span>
                        </span>
                    </div>
                    <div class="col-3" style="padding-left:0px !important">
                        <div style="float:left; margin-top: 7px">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input checkbox" id="chk_tu_dong_gia_goi_y">
                                <label class="custom-control-label" for="chk_tu_dong_gia_goi_y">Tự động đề xuất giá</label>
                            </div>
                        </div>
                        <div style="float:right; margin-top: 7px">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input checkbox" id="chk_tu_dong_de_xuat">
                                <label class="custom-control-label" for="chk_tu_dong_de_xuat">Giá đề xuất bằng giá gara báo</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        @*fixed-header style="width:131%"*@
                        <div class="table-responsive" style="max-height:330px;">
                            <table id="tableDsHMTTBaoGia" class="table table-bordered" style="width:110%">
                                <thead class="font-weight-bold card-title-bg-primary" style="position:sticky;top:0">
                                    <tr class="text-center uppercase">
                                        <th rowspan="2" style="vertical-align: middle; width: 40px;">STT</th>
                                        <th rowspan="2" style="vertical-align: middle; width: 40px;"></th>
                                        <th rowspan="2" style="vertical-align: middle;">Hạng mục bồi thường</th>
                                        <th rowspan="2" style="vertical-align: middle;">Mức độ</th>
                                        <th style="width:75px; vertical-align: middle;" rowspan="2">Thay thế</th>
                                        <th rowspan="2" style="vertical-align: middle; width:60px">SL</th>
                                        <th style="width:80px; vertical-align: middle; display: none" rowspan="2">Giá đề xuất GĐV</th>
                                        <th style="width:343px" colspan="4">Tiền gara báo giá</th>
                                        <th style="width:227px" colspan="3">Tiền đề xuất duyệt</th>
                                        <th style="vertical-align: middle;" colspan="3">Giảm giá (%)  trước tính toán</th>
                                        <th style="width:60px; vertical-align: middle;" rowspan="2">Ghi chú</th>
                                    </tr>
                                    <tr class="text-center uppercase">
                                        <th style="width:90px;">Giá hợp tác</th>
                                        <th style="width:90px">Vật tư</th>
                                        <th style="width:90px">Nhân công</th>
                                        <th style="width:90px">Sơn</th>
                                        <th style="width:90px">Vật tư</th>
                                        <th style="width:90px">Nhân công</th>
                                        <th style="width:90px">Sơn</th>

                                        <th style="width:100px">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="chkGiamGiaBGVtu" checked="checked">
                                                <label class="custom-control-label" for="chkGiamGiaBGVtu">V.tư</label>
                                            </div>
                                        </th>
                                        <th style="width:105px">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="chkGiamGiaBGNhanCong" checked="checked">
                                                <label class="custom-control-label" for="chkGiamGiaBGNhanCong">Nhân công</label>
                                            </div>
                                        </th>
                                        <th style="width:100px">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="chkGiamGiaBGSon" checked="checked">
                                                <label class="custom-control-label" for="chkGiamGiaBGSon">Sơn</label>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="garaBaoGiaCT">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm" id="btnThemHangMucGaraBao" onclick="capNhatThongTinHangMuc(this, '', '', 'B','THEM_MOI', 'custom')">
                    <i class="fas fa-plus mr-1"></i>Chọn thêm hạng mục báo giá
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal" id="btnBaoGiaGara_close">
                    <i class="fas fa-window-close mr-1"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuBaoGiaGara">
                    <i class="fas fa-save mr-1"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnKetThucBaoGia">
                    <i class="fas fa-stop-circle mr-1"></i>Kết thúc báo giá
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right" id="btnHuyKetThucBaoGia">
                    <i class="fas fa-times mr-1"></i>Hủy kết thúc báo giá
                </button>
            </div>
        </div>
    </div>
</div>
<div id="popoverDKBS" class="popover popover-x popover-default" style="display:none; width:600px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Chọn điều khoản bổ sung
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divDKBS">
            </div>
        </div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="chonDKBS">
            <i class="far fa-check mr-2"></i>Chọn
        </button>
    </div>
</div>
<!-- Modal thông tin bao gia -->
<div id="modalBaoGiaDoc" class="modal fade" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="modal-dialog" style="max-width: 97%; margin:10px auto;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Báo giá gara</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height:500px;">
                            <table id="tableDsBaoGiaGaraDoc" class="table table-bordered" style="width:100%">
                                <thead class="font-weight-bold card-title-bg-primary">
                                    <tr class="text-center uppercase">
                                        <th rowspan="2" style="vertical-align:middle; width:250px;">Hạng mục</th>
                                        <th colspan="4">Vật tư</th>
                                        <th colspan="4">Nhân công</th>
                                        <th colspan="4">Sơn</th>
                                    </tr>
                                    <tr class="text-center uppercase">
                                        <th style="width: 250px;">Tên</th>
                                        <th>Số lượng</th>
                                        <th>Số tiền</th>
                                        <th style="width:55px">Tỷ lệ</th>
                                        <th style="width: 250px;">Tên</th>
                                        <th>Số tiền</th>
                                        <th style="width:55px">Tỷ lệ</th>
                                        <th style="width:55px">Tách</th>
                                        <th style="width: 250px;">Tên</th>
                                        <th>Số tiền</th>
                                        <th style="width:55px">Tỷ lệ</th>
                                        <th style="width:55px">Tách</th>
                                    </tr>
                                </thead>
                                <tbody id="bodyDsBaoGiaGaraDoc">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-1"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuBaoGiaGaraDoc">
                    <i class="fas fa-save mr-1"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalLayBGCanhTranh" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog" style="max-width: calc(100vw - 20rem);">
        <div class="modal-content flex-fill">
            <div class="modal-header p-2">
                <h5 class="modal-title">Báo giá cạnh tranh</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-2" style="height: 580px;">
                <div class="h-100 d-flex flex-nowrap" style="gap:.5rem;">
                    <div class="h-100 flex-grow-0 flex-shrink-0 text-nowrap" data-sidenav-mode="full">
                        <div class="card m-0 h-100 border" style="overflow-x:hidden; width: 270px">
                            <div class="card-header px-2 py-1 rounded">
                                <div class="d-flex flex-nowrap align-items-center" style="gap:.5rem;">
                                    <h6 class="m-0" data-sidenav-item="full">Danh sách báo giá</h6>
                                    <h6 class="m-0" data-sidenav-item="half">&nbsp;</h6>
                                </div>
                            </div>
                            <div class="card-body p-2 scrollable overflow-auto d-flex flex-column" style="gap:.5rem;" id="list-item-lan-container">
                            </div>
                        </div>
                    </div>
                    <div class="h-100 flex-fill" style="width:0px;">
                        <div class="card m-0 h-100 border">
                            <div class="card-body p-0 d-flex flex-column" style="gap:.5rem;">
                                <form class="row mx-n1" name="frmLayBGCanhTranh">
                                    <div class="col-12 p-1">
                                        <div class="table-responsive" style="height: 520px;">
                                            <table id="tableLayBGCanhTranh" class="table table-bordered table-sm mb-0 text-center bg-white" style="width:100%;">
                                                <thead class="bg-primary text-light align-middle text-nowrap sticky-top">
                                                    <tr class="text-center uppercase">
                                                        <th rowspan="2" style="vertical-align: middle; width: 40px;">STT</th>
                                                        <th rowspan="2" style="vertical-align: middle;">Hạng mục bồi thường</th>
                                                        <th rowspan="2" style="vertical-align: middle; width: 100px;">Mức độ</th>
                                                        <th style="width:75px; vertical-align: middle; width: 100px;" rowspan="2">Thay thế</th>
                                                        <th rowspan="2" style="vertical-align: middle; width:60px">SL</th>
                                                        <th style="width:80px; vertical-align: middle; display: none;" rowspan="2">Giá đề xuất GĐV</th>
                                                        <th style="width:343px" colspan="4">Tiền gara báo giá</th>
                                                    </tr>
                                                    <tr class="text-center uppercase">
                                                        <th style="width:105px">Tiền V.Tư</th>
                                                        <th style="width:90px">Nhân công</th>
                                                        <th style="width:90px">Tiền sơn</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblBGCanhTranh">
                                                </tbody>
                                                <tfoot>
                                                    <tr class="card-title-bg">
                                                        <td colspan="5" class="text-center font-weight-bold">
                                                            Tổng cộng
                                                        </td>
                                                        <td class="text-right font-weight-bold" id="modalChiTietTonThatNNTXTongTienTT"></td>
                                                        <td class="text-right font-weight-bold" id="modalChiTietTonThatNNTXTongTienThoaThuan"></td>
                                                        <td class="text-right font-weight-bold" id="modalChiTietTonThatNNTXTongTienThoaThuan"></td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="card-footer p-1 rounded d-block flex-nowrap justify-content-end" style="gap:.25rem">
                                <button class="btn btn-sm btn-primary" id="btnThemBGCanhTranh" onclick="showGaraBGCanhTranh(this)" type="button"><i class="fas fa-plus-square mr-2"></i>Thêm gara BG cạnh tranh</button>
                                <button class="btn btn-outline-primary btn-sm btnDongModal wd-80 float-right" type="button" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                                <button class="btn btn-sm btn-primary wd-80 float-right mr-2" id="btnXoaBGCanhTranh" type="button"><i class="fas fa-trash-alt mr-2"></i>Xóa</button>
                                <button class="btn btn-sm btn-primary wd-80 float-right mr-2" id="btnCapNhat" type="button"><i class="fas fa-save mr-2"></i>Lưu</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="popThemGaraBGCanhTranh" class="popover popover-x popover-default" style="display:none; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x">&times;</span>
        <span>Thêm gara báo giá cạnh tranh</span>
    </h3>
    <div class="popover-body popover-content">
        <form id="frmThemGaraBGCT" name="frmThemGaraBGCT" method="post">
            <input type="hidden" name="bt_gara" value="" />
            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <label class="_required">Chọn đối tượng tổn thất</label>
                        <div class="input-group">
                            <select class="select2 form-control custom-select" required name="so_id_doi_tuong" style="width:100%">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <label class="_required">Chọn Gara</label>
                        <div class="input-group">
                            <select class="select2 form-control custom-select" required name="gara" style="width:100%">
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuGaraBGCT">
            <i class="fas fa-save mr-2"></i>Lưu
        </button>
    </div>
</div>

<div class="modal fade" id="modalLichSuBaoGiaHangMuc" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="max-width: 60vw;margin-top: 20px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLichSuBaoGiaHangMuc_title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form name="frmTimKiemLichSuBaoGiaHangMuc" method="post" class="row">
                    <input type="hidden" name="hang_muc" />
                    <div class="col-2">
                        <div class="form-group">
                            <label class="">Hãng xe</label>
                            <select class="select2 form-control custom-select" name="hang_xe" style="width: 100%; height:36px;">
                                <option value="">Chọn hãng xe</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label class="">Hiệu xe</label>
                            <select class="select2 form-control custom-select" name="hieu_xe" style="width: 100%; height:36px;">
                                <option value="">Chọn hiệu xe</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label class="">Năm sx</label>
                            <select class="select2 form-control custom-select" name="nam_sx" style="width: 100%; height:36px;">
                                <option value="">Chọn năm sản xuất</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label>Thay thế/sửa chữa</label>
                            <select class="select2 form-control custom-select" name="thay_the_sc" style="width:100%; height:36px;">
                                <option value="">Chọn</option>
                                <option value="T">Thay thế</option>
                                <option value="S">Sửa chữa</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label>Chính hãng/Không chính hãng</label>
                            <select class="select2 form-control custom-select" name="chinh_hang" style="width:100%; height:36px;">
                                <option value="">Chọn</option>
                                <option value="C">Chính hãng</option>
                                <option value="K">Không chính hãng</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label>Số hồ sơ</label>
                            <input type="text" autocomplete="off" class="form-control" name="so_hs" />
                        </div>
                    </div>
                    <div class="col" style="padding-top:21px;">
                        <button type="button" class="btn btn-primary btn-sm wd-40p" onclick="getPagingLichSuBaoGiaHangMuc(1);">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </form>
                <div class="row" style="margin-top:3px;">
                    <div class="col-12">
                        <div id="gridViewLichSuBaoGiaHangMuc" class="table-app"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-1"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>