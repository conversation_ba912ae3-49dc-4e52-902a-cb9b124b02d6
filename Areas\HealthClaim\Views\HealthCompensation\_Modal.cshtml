﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<style>
    .img-container-tab-1 {
        width: 411.35px !important;
        left: 0px !important;
    }

    #navThongTinChiTiet .nav-link {
        background-color: #EDEFF0;
        color: var(--escs-main-theme-color);
    }

    #navThongTinChiTiet .nav-link.active {
        background-color: var(--escs-main-theme-color);
        color: #FFF;
    }

    .tab-scroll-inner {
        overflow-x: auto;
        -ms-overflow-style: none;
        scrollbar-width: none !important;
        cursor: grab;
    }

        .tab-scroll-inner::-webkit-scrollbar {
            display: none;
        }

        .tab-scroll-inner.dragging {
            cursor: grabbing;
            user-select: none;
        }

    .text-danger-custom {
        float: right !important;
        margin-bottom: 0 !important;
        margin-top: 5px !important;
    }

    .lable-custom {
        margin-bottom: 0 !important;
        margin-top: 5px !important;
    }
</style>

<div id="HealthCompensationModal" class="esmodal fade" tabindex="-1" data-keyboard="false" aria-hidden="true">
    <div class="esmodal-dialog">
        <div class="esmodal-content">
            <div class="esmodal-header py-1">
                <div id="titleUpdateContract">
                    <h4 class="esmodal-title">Hồ sơ: <span data-model="so_hs" class="mainTitle"></span> - <a href="#" onclick="xemToanBoThongTinHoSoBoiThuong()">Xem chi tiết hồ sơ</a></h4> @*<span class="timeGCN"></span> -*@
                </div>
                <div id="divThongBaoCanhBao">
                </div>
            </div>
            <div class="esmodal-body">
                <div class="row">
                    <div class="col-lg-3 common-tab pr-0 collapse show" id="sidebar_info">
                        <partial name="_HealthCommonInfo" />
                    </div>
                    <div class="col info-tab">
                        <partial name="_HealthCompensationContent" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalThemHMTT" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-header py-1">
                <h4 class="modal-title">Phân loại tài liệu</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" onclick="xoaSelectAnh()">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThemHMTT" name="frmThemHMTT" novalidate="novalidate" data-select2-id="frmThemHMTT" method="post">
                    <input type="hidden" name="index" value="">
                    <input type="hidden" name="bt" value="">
                    <input type="hidden" name="pm" value="BT">
                    <input type="hidden" name="hanh_dong" value="them_moi">
                    <div class="row">
                        <div class="col-12" id="frmThemHMTT_loai">
                            <div class="form-group">
                                <label class="_required">Nhóm tài liệu</label>
                                <select class="form-control select2" required="" name="loai" style="width:100%" disabled="disabled">
                                    <option value="TL">Giấy tờ, tài liệu</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Hạng mục tài liệu</label>
                                <div class="input-group">
                                    <select class="form-control select2" required="" name="hang_muc" style="width:100%"></select>
                                    <div class="input-group-append d-none">
                                        <label class="input-group-text">
                                            <a href="javascript:void(0)" id="btnThemHangMucTaiLieu">
                                                <i class="fas fa-plus-square" title="Thêm hạng mục tài liệu"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuHMTT">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-22" id="btnLuuDongHMTT">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" onclick="xoaSelectAnh()">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalYeuCauBoSungHoSo" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Hồ sơ giấy tờ, chứng từ bảo hiểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-2">
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table id="tableDsHoSoGiayTo" class="table table-bordered fixed-header" style="width: 130%;">
                                <thead class="font-weight-bold text-center uppercase">
                                    <tr>
                                        <th width="32%">Tên giấy tờ</th>
                                        <th width="8%">Ngày cung cấp</th>
                                        <th width="8%">Trạng thái</th>
                                        <th width="6%">Hợp lệ</th>
                                        <th width="8%">Loại</th>
                                        <th width="8%">YC bổ sung</th>
                                        <th width="8%">Ghi chú</th>
                                        <th width="12%">Người sử dụng</th>
                                        <th width="10%" class="d-none">Nguồn PS</th>
                                    </tr>
                                </thead>
                                <tbody id="bodyDsHoSoGiayTo">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-2">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnGuiEmailYCBSHS">
                    <i class="fa fa-envelope mr-2"></i>Yêu cầu bổ sung
                </button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnLichSuYeuCauBsGiayTo">
                    <i class="fa fa-history  mr-2"></i>Lịch sử yêu cầu BSHS
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuYeuCauBoSungHoSo">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongYeuCauBoSungHoSo">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemLanBaoLanh" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div id="modalDialogThongTin" class="modal-dialog" style="max-width: 90%; margin:5px auto;">
        <div class="modal-content" style="max-height: 95vh">
            <div class="modal-header py-1">
                <h5 class="modal-title" id="HealthGuaranteeAddLabel">
                    Thêm quyền lợi tính toán <span style="position:absolute; right: 5%" class="canh_bao_ngay_nam_vien"><i style="color:red" class="fas fa-exclamation-triangle mr-2"></i>Số ngày nằm viện chưa đủ 24h</span>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-0" style="max-height: 81vh; overflow: auto;">
                <div class="container-fluid">
                    <form name="frmThemLanBaoLanh" method="post">
                        <input type="hidden" name="ma_doi_tac" />
                        <input type="hidden" name="so_id" />
                        <input type="hidden" name="lan" />
                        <input type="hidden" name="id_qloi" />
                        <h5 class="pt-2" style="color: var(--escs-main-theme-color);">Thông tin lần tính toán</h5>
                        <div class="row">
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="">Ngày tạo lần tính toán</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" disabled="disabled" name="ngay_ht" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="">Giờ khám/vào viện</label>
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input class="form-control input-small time" placeholder="HH:mm" name="gio_vv" type="text" readonly />
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <span class="ti-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-3">
                                <div class="form-group">
                                    <label class="_required">Ngày khám/vào viện</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" required="" name="ngay_vv" display-format="date" readonly value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="">Giờ kết thúc/ra viện</label>
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input class="form-control input-small time" placeholder="HH:mm" name="gio_rv" type="text" readonly />
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <span class="ti-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="_required">Ngày kết thúc/ra viện</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" required name="ngay_rv" display-format="date" readonly value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h5 class="pt-2" style="color: var(--escs-main-theme-color);">Thông tin quyền lợi tính toán</h5>
                        <div class="row" style="background-color:#D1E6AC">
                            <div class="col-2 pb-1">
                                <label class="lable-custom">Số ngày(lần)/năm: </label>
                                <span class="text-danger-custom" id="so_lan_ngay"></span>
                            </div>
                            <div class="col-2">
                                <label class="lable-custom">Q.Lợi/ngày(lần): </label>
                                <span class="text-danger-custom" id="tien_lan_ngay"></span>
                            </div>
                            <div class="col-2">
                                <label class="lable-custom">Q.Lợi/năm: </label>
                                <span class="text-danger-custom" id="tien_nam"></span>
                            </div>
                            <div class="col-2">
                                <label class="lable-custom">Số ngày(lần) còn: </label>
                                <span class="text-danger-custom" id="so_lan_ngay_con"></span>
                            </div>
                            <div class="col-2">
                                <label class="lable-custom">Tỷ lệ đồng chi trả của KH: </label>
                                <span class="text-danger-custom" id="dong_bh"></span>
                            </div>
                            <div class="col-2">
                                <label class="lable-custom">Q.Lợi/năm còn: </label>
                                <span class="text-danger-custom" id="tien_nam_con"></span>
                            </div>
                            
                        </div>
                        <div class="row" style="background-color:#D1E6AC">
                            <div class="col-2">
                                <label class="lable-custom">Thời gian chờ: </label>
                                <span class="text-danger-custom" id="so_ngay_cho"></span>
                            </div>
                            <div class="col-2">
                                <label class="lable-custom">Thời gian đã chờ: </label>
                                <span class="text-danger-custom" id="tgian_da_cho"></span>
                            </div>
                            
                            <div class="col-2">
                                <label class="lable-custom">Tỷ lệ thời gian đã chờ: </label>
                                <span class="text-danger-custom" id="tle_tgian_da_cho"></span>
                            </div>
                            <div class="col-2 pb-1" style="font-weight: bold;">
                                <label class="lable-custom">Số ngày(lần) khả dụng: </label>
                                <span class="text-danger text-danger-custom" id="so_lan_ngay_kha_dung"></span>
                            </div>
                            <div class="col-2" style="font-weight: bold;">
                                <label class="lable-custom">Tiền ngày(lần) khả dụng: </label>
                                <span class="text-danger text-danger-custom" id="tien_lan_ngay_kha_dung"></span>
                            </div>
                            <div class="col-2" style="font-weight: bold;">
                                <label class="lable-custom">Q.Lợi/năm khả dụng: </label>
                                <span class="text-danger text-danger-custom" id="tien_nam_kha_dung"></span>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="_required">Nhóm nguyên nhân</label>
                                    <select class="select2 form-control custom-select" required="" name="nhom_nguyen_nhan">
                                    </select>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="_required">Hình thức điều trị</label>
                                    <select class="select2 form-control custom-select" required="" name="hinh_thuc">
                                    </select>
                                </div>
                            </div>
                            <div class="col-8" id="QLBH">
                                <div class="form-group">
                                    <label for="" class="_required">Quyền lợi bảo hiểm (<span class="dynamic-label nt_tien_bh">VND</span>)</label>
                                    <div class="input-group">
                                        <input type="text" name="lh_nv" style="cursor:pointer;" required="" data-val="" onclick="chonDsLHNV(this);" class="form-control" autocomplete="off" placeholder="Click chọn quyền lợi bảo hiểm">
                                        <div class="input-group-append">
                                            <label class="input-group-text" for="lh_nv">
                                                <a href="#" id="btnXemChiTietQuyenLoiBaoHiem" title="Xem chi tiết quyền lợi bảo hiểm trên Giấy chứng nhận">
                                                    <i class="far fa-file-search"></i>
                                                </a>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row d-none" id="QLLHNV">
                            <div class="col-6 ">
                                <div class="form-group">
                                    <label class="">Quyền lợi trừ lùi</label>
                                    <input type="text" autocomplete="off" class="form-control text" name="lhnv_tru_lui" onclick="chonLHNV_tru_lui(this);" placeholder="Chọn quyền lợi trừ lùi ...." data-val="">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="">Quyền lợi phụ thuộc</label>
                                    <input type="text" autocomplete="off" class="form-control text" name="lhnv_phu_thuoc" onclick="chonLHNV_phu_thuoc(this);" placeholder="Chọn quyền lợi phụ thuộc ...." data-val="">
                                </div>
                            </div>
                        </div>
                        <div class="row tai-nan">
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="_required">Ngày tai nạn</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" readonly="readonly" required class="form-control datepicker_null" name="ngay_xr" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-10">
                                <div class="form-group">
                                    <label class="_required">Nơi xảy ra tai nạn</label>
                                    <input type="text" autocomplete="off" required class="form-control text" name="noi_xr" placeholder="Nhập nơi xảy ra tai nạn ....">
                                </div>
                            </div>
                        </div>
                        <div class="row tai-nan">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Nguyên nhân tai nạn</label>
                                    <input type="text" autocomplete="off" required class="form-control text" name="nguyen_nhan_tnan" placeholder="Nhập nguyên nhân tai nạn ...">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Hậu quả tai nạn</label>
                                    <input type="text" autocomplete="off" required class="form-control text" name="hau_qua_ct" placeholder="Nhập hậu quả tai nạn ...">
                                </div>
                            </div>
                        </div>
                        <div class="row  d-none">
                            <div class="col-2">
                                <div class="form-group">
                                    <label>Số ngày/lần còn lại</label>
                                    <div class="input-group">
                                        <input type="text" name="so_lan_ngay_con" class="form-control number" disabled="disabled" title="Số ngày hoặc số lần khám quyền lợi còn lại">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label>Giới hạn tiền/lần/ngày</label>
                                    <div class="input-group">
                                        <input type="text" name="tien_lan_ngay" class="form-control number" disabled="disabled" title="Số tiền giới hạn/lần khám/ngày nằm viện">
                                        <div class="input-group-append" style="width: 45px;">
                                            <span class="input-group-text dynamic-label nt_tien_bh" style="width: 100%;padding-left: 8px;">
                                                VND
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label>Tổng tiền còn lại</label>
                                    <div class="input-group">
                                        <input type="text" name="tien_nam_con" class="form-control number" disabled title="Tổng số tiền giới hạn còn lại">
                                        <div class="input-group-append" style="width: 45px;">
                                            <span class="input-group-text dynamic-label nt_tien_bh" style="width: 100%;padding-left: 8px;">
                                                VND
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label>Tỷ lệ đồng chi trả</label>
                                    <div class="input-group">
                                        <input type="text" name="dong_bh" class="form-control number" disabled title="Tỷ lệ đồng chi trả">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label>Thời gian chờ (ngày)</label>
                                    <div class="input-group">
                                        <input type="text" name="so_ngay_cho" class="form-control number" disabled title="Thời gian chờ áp dụng (ngày)">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 d-none">
                                <div class="form-group">
                                    <label>Tỷ lệ chi trả</label>
                                    <div class="input-group">
                                        <input type="text" name="ty_le_chi_tra" class="form-control number" disabled title="Tỷ lệ chi trả" value="100">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label>Số tiền còn lại tạm tính</label>
                                    <div class="input-group">
                                        <input type="text" name="tien_con_lai_tam_tinh" class="form-control number" disabled title="Số tiền còn lại tạm tính">
                                        <div class="input-group-append">
                                            <span class="input-group-text tien_tam_tinh">
                                                <a href="javascript:void(0);"><i class="fas fa-sync-alt"></i></a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-2 d-none">
                                <div class="form-group">
                                    <label class="">Mã nhóm bệnh</label>
                                    <select class="select2 form-control custom-select" name="ma_nhom_benh">
                                    </select>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="_required">Mã bệnh ICD</label>
                                    <div class="input-group cursor-pointer">
                                        <input type="text" name="ma_benh" style="pointer-events: auto !important" readonly placeholder="Click chọn" required class="form-control" onclick="traCuuBenh()" />
                                        <div class="input-group-append">
                                            <span class="input-group-text ma_benh_icd">
                                                <a href="javascript:void(0);" onclick="openFileBenhICD(this)">
                                                    <i class="far fa-file-alt"></i>
                                                </a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="_required">Chẩn đoán</label>
                                    <div class="input-group" style="cursor:pointer">
                                        <input type="text" name="chan_doan" autocomplete="off" placeholder="Chẩn đoán" required="" class="form-control" title="Chẩn đoán">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="_required">Kiểu áp dụng thời gian chờ</label>
                                    <select class="select2 form-control custom-select" required name="kieu_ad_tgian_cho">
                                        <option value="">Chọn cách tính thời gian chờ</option>
                                        <option value="TG_CHO">Chặn thời gian chờ</option>
                                        <option value="TL_TG_CHO">Bồi thường theo tỷ lệ thời gian chờ</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-2 d-none">
                                <div class="form-group">
                                    <label>Số tiền yêu cầu</label>
                                    <div class="input-group">
                                        <input type="text" name="tien_yc" readonly="readonly" class="form-control number" title="Số tiền yêu cầu">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="" id="titleSoNgayYc">Số ngày/lần y.cầu</label>
                                    <div class="input-group">
                                        <input type="text" name="so_ngay_yc" autocomplete="off" placeholder="Số lần/ngày y.cầu" required="" class="form-control decimal" onkeyup="handleInput(this)">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="" id="titleSoNgayD">Số ngày/lần duyệt</label>
                                    <div class="input-group">
                                        <input type="text" name="so_ngay_duyet" autocomplete="off" placeholder="Số lần/ngày duyệt" required="" class="form-control decimal" onkeyup="handleInput(this)">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label class="_required">Nguyên tệ số tiền YC</label>
                                    <div class="input-group">
                                        <select class="select2 form-control custom-select" required="" name="nt_tien_yc"></select>
                                        <div class="input-group-append">
                                            <span class="input-group-text nt_ty_gia_hs">
                                                <a href="javascript:void(0);"><i class="fas fa-balance-scale mr-1"></i></a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-2 divTienLuong d-none">
                                <div class="form-group">
                                    <label class="_required">Tiền lương</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" name="tien_luong" id="tien_luong" required class="form-control text-right money-nullable" placeholder="0" title="Số tiền lương" value="0" onchange="tinhNgayLuong()">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 divTienLuong d-none">
                                <div class="form-group">
                                    <label class="_required">Số ngày làm việc/tháng</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" name="so_ngay_lv" id="so_ngay_lv" required class="form-control text-right decimal" placeholder="0" title="Số ngày làm việc/tháng" onchange="tinhNgayLuong()">
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 divTienLuong d-none">
                                <div class="form-group">
                                    <label>Số tiền lương/ngày công</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" name="tien_ngay_luong" id="tien_ngay_luong" readonly class="form-control text-right money-nullable" placeholder="0" title=" tiền lương trên ngày công">
                                    </div>
                                </div>
                            </div>
                        </div>

                        @*<div class="row mt-2">
                        <div class="col-8">
                        <h5 class="pt-2" style="color: var(--escs-main-theme-color);">Thông tin quyền lợi yêu cầu tính toán lần <span id="lan_tinh_toan"></span> <span id="thong_bao_quyen_loi" class="text-danger"></span></h5>
                        </div>
                        <div class="col-4">
                        <a href="#" id="btnXemTaiLieuTab3" class="float-right btn btn-sm btn-primary"><i class="fas fa-file-pdf mr-2"></i>Xem tài liệu, hình ảnh, hồ sơ giấy tờ</a>
                        </div>
                        </div>*@

                        @*huynq*@
                        <div class="row mb-3 align-items-center">
                            <div class="col-6 py-2" id="navThongTinQuyenLoiLan">
                                <ul class="nav nav-pills font-weight-bold" role="tablist">
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link active" data-val="XE" data-toggle="tab" href="#tabThongTinQuyenLoiLanChiPhi" role="tab">
                                            Thông tin chi phí yêu cầu bảo hiểm
                                        </a>
                                    </li>
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" data-toggle="tab" href="#tabThongTinQuyenLoiLanTruLui" role="tab">
                                            Thông tin quyền lợi trừ lùi
                                        </a>
                                    </li>
                                    <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" data-val="XE_MAY" data-toggle="tab" href="#tabThongTinQuyenLoiLanGhiChu" role="tab">
                                            Ghi chú
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <a href="#" id="btnXemTaiLieuTab3" class="float-right btn btn-primary btn-sm"><i class="fas fa-file-pdf mr-2"></i>Xem tài liệu, hình ảnh, hồ sơ giấy tờ</a>
                            </div>
                            <div class="col-12">
                                <div class="tab-content">
                                    <div id="tabThongTinQuyenLoiLanChiPhi" class="tab-pane fade show active data-scroll p-0" role="tabpanel">
                                        <div class="table-responsive scrollable" style="max-height:215px" id="tblChiPhiCTiet">
                                            <table id="tbl_lan_bao_lanh_quyen_loi_ct" class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0;">
                                                <thead class="font-weight-bold card-title-bg-primary">
                                                    <tr class="text-center uppercase">
                                                        <th nowrap>Tên loại chi phí</th>
                                                        <th nowrap style="width:110px">Tiền yêu cầu</th>
                                                        <th nowrap style="width:112px">Tiền GT vượt QL</th>
                                                        <th nowrap style="width:112px">Tiền giảm trừ</th>
                                                        <th nowrap style="width:100px">Tiền GT theo % TG chờ</th>
                                                        <th nowrap style="width:100px">Tiền GT theo % đồng</th>
                                                        <th nowrap style="width:110px">Tiền duyệt</th>
                                                        <th nowrap width="200">
                                                            Nguyên nhân giảm trừ
                                                        </th>
                                                        <th nowrap style="width:75px">Ghi chú</th>
                                                        <th style="width:30px"></th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tbDsKhoanChiTab3">
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td colspan="1" class="py-0">
                                                            <div class="d-flex justify-content-between flex-nowrap">
                                                                <a href="#" onclick="chonLoaiChiPhi(this)">
                                                                    <i class="fas fa-plus-square mr-2"></i>Thêm chi phí
                                                                </a>
                                                                <span> Nguyên tệ tiền yêu cầu <span class="font-weight-bold dynamic-label nt_tien_yc">VND</span></span>
                                                            </div>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_yc" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam_vuot_qloi" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam_tgian_cho" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam_dong_chi_tra" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_duyet" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right" colspan="3" rowspan="3">
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="1" class="text-right py-0">
                                                            Quy đổi sang nguyên tệ số tiền bảo hiểm <span class="font-weight-bold dynamic-label nt_tien_bh">VND</span>
                                                        </td>
                                                        <td class="text-right py-0 text-danger">
                                                            <span id="tong_chphi_tien_yc_bh" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0 text-danger">
                                                            <span id="tong_chphi_tien_giam_vuot_qloi_bh" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0 text-danger">
                                                            <span id="tong_chphi_tien_giam_bh" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0 text-danger">
                                                            <span id="tong_chphi_tien_giam_tgian_cho_bh" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0 text-danger">
                                                            <span id="tong_chphi_tien_giam_dong_chi_tra_bh" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0 text-danger">
                                                            <span id="tong_chphi_tien_duyet_bh" class="font-weight-bold">0</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="1" class="text-right py-0">
                                                            Quy đổi sang <span class="font-weight-bold dynamic-label">VND</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_yc_vnd" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam_vuot_qloi_vnd" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam_vnd" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam_tgian_cho_vnd" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_giam_dong_chi_tra_vnd" class="font-weight-bold">0</span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_chphi_tien_duyet_vnd" class="font-weight-bold">0</span>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                    <div id="tabThongTinQuyenLoiLanTruLui" class="tab-pane fade data-scroll p-0" role="tabpanel">
                                        <div class="table-responsive scrollable" style="max-height:215px" id="tblQuyenLoiTruLui">
                                            <table id="tbl_lan_bao_lanh_quyen_loi_tru_lui" class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0;">
                                                <thead class="font-weight-bold card-title-bg-primary">
                                                    <tr class="text-center uppercase">
                                                        <th nowrap style="width:800px">Quyền lợi trừ lùi</th>
                                                        <th nowrap style="width:50px">Số ngày trừ lùi</th>
                                                        <th nowrap style="width:112px">Tiền trừ lùi</th>
                                                        <th nowrap style="width:112px">Tiền trừ lùi(N.tệ tiền BH)</th>
                                                        <th nowrap style="width:112px">Tiền trừ lùi(VND)</th>
                                                        <th nowrap style="width:75px">Ghi chú</th>
                                                        <th style="width:30px"></th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tbQloiTruLui">
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td colspan="2" class="py-0">
                                                            <div class="d-flex justify-content-between flex-nowrap">
                                                                <a href="#" onclick="chonLHNV_tru_lui(this)">
                                                                    <i class="fas fa-plus-square mr-2"></i>Thêm quyền lợi trừ lùi
                                                                </a>
                                                                <span class="font-weight-bold">Tổng cộng:</span>
                                                            </div>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_tru_lui_tien_yc" class="font-weight-bold"></span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_tru_lui_tien_bh" class="font-weight-bold"></span>
                                                        </td>
                                                        <td class="text-right py-0">
                                                            <span id="tong_tru_lui_tien_vnd" class="font-weight-bold"></span>
                                                        </td>
                                                        <td></td>
                                                        <td></td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                    <div id="tabThongTinQuyenLoiLanGhiChu" class="tab-pane fade data-scroll p-0" role="tabpanel">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="form-group">
                                                    <label class=""><a href="#" id="btnXemNhomGhiChu" onclick="chonNhomGhiChu(this)">Ghi chú nội bộ <i style="font-size:10px">(Click để chọn)</i></a></label>
                                                    <div class="input-group">
                                                        <textarea rows="8" autocomplete="off" placeholder="Nội dung ghi chú" name="ghi_chu" class="form-control"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-group">
                                                    <label class="">Ghi chú khác</label>
                                                    <div class="input-group">
                                                        <textarea rows="8" autocomplete="off" placeholder="Nội dung ghi chú" name="ghi_chu_khac" class="form-control"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                @*<button type="button" class="btn btn-primary btn-sm wd-150 ml-2" id="btnThemQuyenLoiModal">
                <i class="fas fa-plus mr-2"></i> Thêm mới quyền lợi
                </button>*@
                <button class="btn btn-primary btn-sm wd-85 mg-t-22  float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm float-right wd-110" id="btnLuuDongLanBaoLanh">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm ml-2 float-right" id="btnLuuLanBaoLanh">
                    <i class="fas fa-save mr-2"></i>Tính toán và lưu
                </button>
            </div>
        </div>
    </div>
    <div class="modal-dialog" id="modalDialogTaiLieu" style="width: 24%; margin:5px 0px;float:left;display:none">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Hình ảnh tài liệu hồ sơ giấy tờ</h5>
                <button type="button" class="close" id="btnCloseTaiLieu" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-0" style="height:88.5vh">
                <div class="modal-hien-thi">
                    <div class="card mb-0">
                        <div id="div_hinh_anh" class="card-body p-0" style="height:56vh; text-align:center">
                            <div id="img-container-tab-1" style="height:56vh"></div>
                            <div id="tai-lieu-tab1" style="height:56vh; overflow:hidden">
                                <div class="tab-content" id="tai_lieu_content"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-tai-lieu">
                    <div id="accordion">
                        <div class="card mb-1">
                            <div class="card-header p-2" style="border:none" id="headingOne">
                                <h5 class="mb-0">
                                    <button class="btn p-0" style="color:var(--escs-main-theme-color); font-size:12px" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                        <i class="fas fa-file-pdf mr-2"></i> Hồ sơ giấy tờ
                                    </button>
                                </h5>
                            </div>
                            <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" data-parent="#accordion">
                                <div class="card-body p-1">
                                    <div style="width:100%; vertical-align:middle;height:150px" class="scrollable">
                                        <ul style="padding-left:15px; margin:unset" id="dsTaiLieuHoSo"></ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-1">
                            <div class="card-header p-2" style="border:none" id="headingTwo">
                                <h5 class="mb-0">
                                    <button class="btn collapsed p-0" style="color:var(--escs-main-theme-color); font-size:12px" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                        <i class="fas fa-image mr-2"></i> Tài liệu hình ảnh
                                    </button>
                                </h5>
                            </div>
                            <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
                                <div class="card-body p-1">
                                    <div style="width:100%; vertical-align:middle;height:150px" class="scrollable">
                                        <div style="width:100%" id="dsHinhAnhHoSo" class="list-pictures-tab-1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="popoverGhiChu" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChu" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
        <span class="mr-2">
            <a href="#" onclick="chonGhiChu(this)" style="font-size: 12px; margin-left: 15px;">
                <i class="fa fa-bars mr-1"></i>
                Chọn ghi chú
            </a>
        </span>
        <span>
            <a href="#" id="btnThemGhiChu" style="font-size: 12px;">
                <i class="fas fa-plus-square mr-1"></i>
                Tạo ghi chú
            </a>
        </span>
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuChiPhi" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChu">
                    <textarea class="form-control" id="divGhiChu_NoiDung" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuGhiChu">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div id="popoverGhiChuLSTT" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChuLSTT" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuChiPhiLSTT" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChuLSTT">
                    <textarea readonly class="form-control" id="divGhiChu_NoiDungLSTT" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
</div>

<div id="popoverNguyenNhanGiamTru" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_nguyenNhanGiamTru" class="close pull-right" data-dismiss="popover-x">&times;</span>Lý do giảm trừ
    </h3>
    <div class="popover-body popover-content">
        <form name="frmNguyenNhanGiamTru" method="post">
            <div class="row">
                <div class="col-12" id="divNguyenNhan">
                    <textarea readonly class="form-control" id="divNguyenNhanGiamTru" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
</div>

<div id="popoverTraCuuBenh" class="popover popover-x popover-default" style="display:none; max-width:unset;width:650px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right dongpopoverTraCuuBenh" data-dismiss="popover-x">&times;</span>Tra cứu bệnh
    </h3>
    <div class="popover-body popover-content">
        <div style="width:100%; margin-bottom:10px;">
            <div class="input-group">
                <input type="text" class="form-control" id="inputTimKiemBenhLy" placeholder="Tìm kiếm bệnh" value="" />
                <input type="hidden" id="inputTimKiemBenhLy_ma" />

                <div class="input-group-append">
                    <label class="input-group-text">
                        <a href="javascript:void(0)" onclick="getPagingBenhLy(1)">
                            <i class="fa fa-search"></i>
                        </a>
                    </label>
                </div>
            </div>
        </div>
        <div id="dsBenhLy" class="scrollable" style="max-height:450px">
        </div>
        <div id="dsBenhLy_pagination"></div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnTraCuuBenh">
            <i class="fa fa-save mr-2"></i>Lưu
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongTraCuuBenh">
            <i class="fas fa-window-close mr-2"></i>Đóng
        </button>
    </div>
</div>

<div id="ModalTCBT" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Lý do từ chối bồi thường</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmLyDoTuChoiBT" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Lý do từ chối</label>
                                <textarea class="form-control" maxlength="500" name="ly_do" placeholder="Lý do"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuLyDo">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modalChiTietChiPhi" class="modal fade" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:64%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin chi tiết chi phí</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="padding-top:5px;">
                <div class="row">
                    <div class="col-12">
                        <form name="frmChiPhiChiTiet" method="post">
                            <input type="hidden" name="ma_ct" />
                            <input type="hidden" name="tien_yc" />
                            <div class="table-responsive TABLE_CHI_PHI" id="CHI_PHI_KB" style="max-height:400px">
                                <table class="table table-bordered fixed-header">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>Tên chi phí khám</th>
                                            <th style="width:130px">Số tiền</th>
                                            <th style="width:130px">Giá tham khảo</th>
                                            <th style="width:130px">
                                                <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                                                    <input type="checkbox" onchange="onMacDinhChiPhi(this,'ALL_KB')" id="check_chi_phi_kb" value="1" checked="checked" class="custom-control-input">
                                                    <label class="custom-control-label" for="check_chi_phi_kb">Mặc định</label>
                                                </div>
                                            </th>
                                            <th style="width:40px"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblChiPhiKhamBenh">
                                    </tbody>
                                    <tfoot style="height:34.6px;">
                                        <tr class="text-left card-title-bg">
                                            <td>
                                                <a href="javascript:void(0)" onclick="chonChiPhiKhamBenh(this)" class="mr-3">
                                                    <i class="fas fa-plus-square mr-1"></i> Thêm chi phí khám bệnh
                                                </a>
                                                <a href="javascript:void(0)" onclick="showThemDMChiPhi(this,'KB')">
                                                    <i class="fas fa-plus-square mr-1"></i> Tạo mã chi phí khám bệnh
                                                </a>
                                            </td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiKhamBenh_TongTienKham">0</td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiKhamBenh_TongGiaThamKhao"></td>
                                            <td class="text-center">
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="table-responsive TABLE_CHI_PHI" id="CHI_PHI_TH" style="max-height:400px">
                                <table class="table table-bordered fixed-header">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>Tên thuốc</th>
                                            <th style="width:90px">Đvị tính</th>
                                            <th style="width:90px">Số lượng</th>
                                            <th style="width:100px">Đơn giá</th>
                                            <th style="width:100px">Thành tiền</th>
                                            <th style="width:100px">Giá tham khảo</th>
                                            <th style="width:130px">
                                                <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                                                    <input type="checkbox" onchange="onMacDinhChiPhi(this,'ALL_TH')" id="check_chi_phi_th" value="1" checked="checked" class="custom-control-input">
                                                    <label class="custom-control-label" for="check_chi_phi_th">Mặc định</label>
                                                </div>
                                            </th>
                                            <th style="width:40px"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblChiPhiThuoc">
                                    </tbody>
                                    <tfoot style="height:34.6px;">
                                        <tr class="text-left card-title-bg">
                                            <td colspan="4">
                                                <a href="javascript:void(0)" onclick="chonChiPhiThuoc(this)" class="mr-3">
                                                    <i class="fas fa-plus-square mr-1"></i> Thêm chi phí thuốc
                                                </a>
                                                <a href="javascript:void(0)" onclick="showThemDMChiPhi(this,'TH')">
                                                    <i class="fas fa-plus-square mr-1"></i> Tạo mã chi phí thuốc
                                                </a>
                                            </td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiThuoc_TongThanhTien">0</td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiThuoc_TongGiaThamKhao"></td>
                                            <td class="text-center">
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="table-responsive TABLE_CHI_PHI" id="CHI_PHI_KH" style="max-height:400px">
                                <table class="table table-bordered fixed-header">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>Tên chi phí</th>
                                            <th style="width:130px">Số tiền</th>
                                            <th style="width:200px">Ghi chú</th>
                                            <th style="width:130px">
                                                <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                                                    <input type="checkbox" onchange="onMacDinhChiPhi(this,'ALL_KH')" id="check_chi_phi_kh" value="1" checked="checked" class="custom-control-input">
                                                    <label class="custom-control-label" for="check_chi_phi_kh">Mặc định</label>
                                                </div>
                                            </th>
                                            <th style="width:40px"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblChiPhiKhac">
                                    </tbody>
                                    <tfoot style="height:34.6px;">
                                        <tr class="text-left card-title-bg">
                                            <td>
                                                <a href="javascript:void(0)" onclick="chonChiPhiKhac(this)" class="mr-3">
                                                    <i class="fas fa-plus-square mr-1"></i> Thêm chi phí khác
                                                </a>
                                                <a href="javascript:void(0)" onclick="showThemDMChiPhi(this,'KH')">
                                                    <i class="fas fa-plus-square mr-1"></i> Tạo mã chi phí khác
                                                </a>
                                            </td>
                                            <td class="font-weight-bold" style="text-align:right" id="tblChiPhiKhac_TongSoTien"></td>
                                            <td></td>
                                            <td class="text-center">
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuChiTietChiPhi">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-22" id="btnLuuDongChiTietChiPhi">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="popoverDMChiPhi" class="popover popover-x popover-default" style="display:none; width:450px; max-width:unset">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" onclick="dongDMChiPhi()" data-dismiss="popover-x">&times;</span>Thêm danh mục chi phí
    </h3>
    <div class="popover-body popover-content">
        <form name="frmDMChiPhi" method="post">
            <input type="hidden" name="ma_doi_tac" value="" />
            <input type="hidden" name="loai" value="" />
            <div class="row">
                <div class="col-6">
                    <div class="form-group">
                        <label class="">Mã cấp trên</label>
                        <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="ma_ct">
                        </select>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label class="_required">Mã</label>
                        <input type="text" name="ma" autocomplete="off" onclick="focusInput(this)" required class="form-control" value="" placeholder="Mã chi phí" />
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <label class="_required">Tên</label>
                        <input type="text" name="ten" autocomplete="off" onclick="focusInput(this)" required class="form-control" value="" placeholder="Tên chi phí" />
                    </div>
                </div>
            </div>
            <div class="row" id="cp_thuoc">
                <div class="col-6">
                    <div class="form-group">
                        <label>Đơn vị tính</label>
                        <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="dvi_tinh">
                        </select>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label>Giá</label>
                        <input type="text" name="gia" autocomplete="off" onclick="focusInput(this)" class="form-control number" value="" placeholder="Giá chi phí" />
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-15" id="btnLuuThongTinChiPhi">
            <i class="fa fa-save mr-2"></i>Lưu
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-15" id="btnLuuDongThongTinChiPhi" data-dismiss="modal">
            <i class="fas fa-hdd mr-2"></i>Lưu & đóng
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-15" data-dismiss="modal" onclick="dongDMChiPhi()">
            <i class="fas fa-window-close mr-2"></i>Đóng
        </button>
    </div>
</div>

<div id="popoverGhiChuBoSungHSGT" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 350px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="btnDongPopperGhiChuBoSungHSGT" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuBoSungHSGT" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChuBoSungHSGT">
                    <textarea class="form-control" id="divGhiChuBoSungHSGT_NoiDung" rows="8"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuGhiChuBoSungHSGT">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>

<div id="modalHuyHoSoTinhToan" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Lý do hủy hồ sơ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmLyDoHuyHoSo" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Lý do hủy hồ sơ</label>
                                <textarea class="form-control" required maxlength="500" name="ly_do" placeholder="Lý do"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuLyDoHuy">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalXemNoiDungKhachHangXN" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title">Khách hàng xác nhận</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="frmmodalXemNoiDungKhachHangXN" name="frmmodalXemNoiDungKhachHangXN" method="post">
                    <div class="row">
                        <div class="col-12">
                            <label>Nội dung khách hàng xác nhận</label>
                            <div class="form-group">
                                <textarea rows="4" name="noi_dung" autocomplete="off" class="form-control" disabled></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <label class="_required">Nội dung cán bộ phản hồi</label>
                            <div class="form-group">
                                <textarea rows="4" name="noi_dung_phan_hoi" autocomplete="off" class="form-control" placeholder="Nhập nội dung phản hồi" required></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer py-2">
                <div class="col-12 px-0">
                    <button type="button" class="btn btn-primary btn-sm ml-2" id="btnGuiEmailPhanHoi">
                        <i class="fas fa-envelope mr-2"></i>Gửi email phản hồi
                    </button>
                    <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


<div id="popoverNguyenNhan" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 450px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popNguyenNhan" class="close pull-right" data-dismiss="popover-x">&times;</span>
        <span class="mr-2">
            <a href="#" onclick="chonNguyenNhan(this)" style="font-size: 12px; margin-left: 15px;" id="btnChonNguyenNhan">
                <i class="fa fa-bars mr-1"></i>
                Chọn nguyên nhân giảm trừ
            </a>
        </span>
        <span>
            <a href="#" id="btnNhapNguyenNhan" style="font-size: 12px;">
                <i class="fas fa-plus-square mr-1"></i>
                Nhập nguyên nhân giảm trừ
            </a>
        </span>
    </h3>
    <div class="popover-body popover-content">
        <form name="frmNguyenNhan" method="post">
            <div class="row">
                <div class="col-12" id="divNguyenNhan">
                    <textarea class="form-control" id="divNguyenNhan_NoiDung" rows="10" placeholder="Nhập nguyên nhân giảm trừ"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuNguyenNhan">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>
<div id="modalChonDVTT" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog modal-dialog-centered" style="width: 390px;">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-body ">
                <h2 class="text-center"><p class="font-weight-bold">Thông báo</p></h2>
                <h6><p class="m-0">Chọn đơn vị thanh toán để chuyển thanh toán hồ sơ này</p></h6>
                <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="don_vi_thanh_toan" id="don_vi_thanh_toan">
                </select>
                <div class="mt-4 text-center">
                    <button type="button" class="btn btn-primary " id="btnChuyenKeToan" style="font-size: 12px">
                        <i class="fas fa-save mr-2"></i>Tiếp theo
                    </button>
                    <button type="button" class="btn btn-danger " data-dismiss="modal" style="font-size: 12px">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalCapNhatUocTheoDiem" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-lg" style="max-width: 62%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cập nhật ước theo điểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmCapNhatUocTheoDiem" name="frmCapNhatUocTheoDiem" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:380px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>STT</th>
                                            <th>Ngày dự phòng</th>
                                            <th>Điểm dự phòng</th>
                                            <th style="width: 80px">Nghiệp vụ</th>
                                            <th style="width: 110px">Mã quyền lợi</th>
                                            <th style="width: 120px">Số tiền dự phòng</th>
                                            <th style="width: 120px">Tiền chênh lệch</th>
                                            <th>Trạng thái tích hợp</th>
                                            <th>Log</th>
                                            <th>Hành động</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblCapNhatUocTheoDiem"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0" style="display:block;">
                <button type="button" class="btn btn-primary btn-sm mg-t-22 d-none" id="btnDieuChinhBienDo"><i class="fas fa-window-alt mr-2"></i>Điều chỉnh biên độ</button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right d-none" id="btnThemDiemCapNhatUoc"><i class="fas fa-hdd mr-2"></i>Điều chỉnh tăng giảm dự phòng</button>
            </div>
        </div>
    </div>
</div>

<div id="popoverLogRq" class="popover popover-x popover-default" style="display:none; width:500px; max-width: 500px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x" onclick="dongPopover(this)">&times;</span>Xem log
    </h3>
    <div class="popover-body popover-content">
        <div class="row">
            <div class="col-12" id="divLogRq">
                <label>Data Request</label>
                <textarea class="form-control" id="divLogRq_NoiDung" name="log_rq" readonly rows="6"></textarea>
            </div>
        </div>
        <div class="row">
            <div class="col-12" id="divLogRes">
                <label>Data Response</label>
                <textarea class="form-control" id="divLogRes_NoiDung" name="log_res" readonly rows="6"></textarea>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalBienDo" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 25%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Điều chỉnh biên độ ước</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="frmBienDo" name="frmBienDo" method="post">
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:380px">
                                <table class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th style="width: 200px">Điểm</th>
                                            <th style="width: 100px">Biên độ ước (%)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tblBienDo"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer d-block pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right" id="btnLuuBienDo"><i class="fas fa-save mr-2"></i>Lưu</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="modalThemDiem" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 40%;">
        <div class="modal-content">
            <div class="modal-header" style="padding: 0.5rem 1rem;">
                <h5 class="modal-title">Thêm điểm dự phòng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <form name="frmThemDiem" method="post">
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Ngày dự phòng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" required name="ngay_dp" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" name="lh_nv" required style="width:100%"></select>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="_required">Số tiền dự phòng</label>
                                <input type="text" name="tien" id="tien" required autocomplete="off" spellcheck="false" placeholder="Số tiền dự phòng" class="form-control number">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuThemDiem"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<!--Phương thức chi trả đồng bảo hiểm-->
<div id="modalPhuongAnChiTra" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width: 60%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Tính toán trách nhiệm đồng bảo hiểm</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="table-responsive" style="max-height:300px">
                    <table class="table table-bordered fixed-header">
                        <thead class="font-weight-bold">
                            <tr class="text-center uppercase">
                                <th>STT</th>
                                <th>Nhà bảo hiểm</th>
                                <th>Vai trò</th>
                                <th>Tỷ lệ</th>
                                <th>Số tiền trách nhiệm</th>
                            </tr>
                        </thead>
                        <tbody id="tblPhuongAnChiTra">
                        </tbody>
                        <tfoot>
                            <tr class="text-left card-title-bg">
                                <th class="align-middle" colspan="4">Tổng cộng</th>
                                <th class="py-1">
                                    <div class="d-flex flex-nowrap justify-content-between" style="column-gap:.5rem;">
                                        <span>Số tiền bồi thường:</span>
                                        <span id="modalPhuongAnChiTraSoTienBT" class="text-danger"></span>
                                    </div>
                                    <div class="d-flex flex-nowrap justify-content-between" style="column-gap:.5rem;">
                                        <span>Số tiền thuộc trách nhiệm nhà bảo hiểm:</span>
                                        <span id="modalPhuongAnChiTraSoTienTrachNhiemPJICO" class="text-danger"></span>
                                    </div>
                                    <div class="d-flex flex-nowrap justify-content-between" style="column-gap:.5rem;">
                                        <span>Số tiền thuộc trách nhiệm nhà đồng:</span>
                                        <span id="modalPhuongAnChiTraSoTienTrachNhiemNhaDong" class="text-danger"></span>
                                    </div>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <form name="frmPhuongAnChiTra" id="frmPhuongAnChiTra" method="post">
                    <div class="row mt-2">
                        <div class="col-12">
                            <h5>Phương án chi trả</h5>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" name="phuong_an_chi_tra" id="phuong_an_chi_tra_full" value="100" class="custom-control-input single_checked">
                                <label class="custom-control-label" for="phuong_an_chi_tra_full" style="cursor:pointer; padding-top:2px">Nhà bảo hiểm chi trả 100%</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" name="phuong_an_chi_tra" id="phuong_an_chi_tra_trach_nhiem" value="MUC_TRACH_NHIEM" class="custom-control-input single_checked">
                                <label class="custom-control-label" for="phuong_an_chi_tra_trach_nhiem" style="cursor:pointer; padding-top:2px">Nhà bảo hiểm chi trả theo % trách nhiệm</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnLuuPhuongAnChiTra"><i class="fa fa-save"></i> Lưu</button>
                <button type="button" class="btn btn-primary btn-sm wd-85" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
            </div>
        </div>
    </div>
</div>

<div id="modalChonNhomKHVip" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog modal-dialog-centered" style="width: 390px;">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-body ">
                <h2 class="text-center font-weight-bold">Thông báo</h2>
                <h6 class="m-0">Chọn kiểu khách hàng vip</h6>
                <select class="select2 form-control select2-hidden-accessible" style="width:100%" name="nhom_kh_vip" id="nhom_kh_vip">
                    <option value="">Không</option>
                    <option value="VIP">VIP</option>
                    <option value="SVIP">SVIP</option>
                </select>
                <div class="mt-4 text-center">
                    <button type="button" class="btn btn-primary " id="btnLuuKHVip" style="font-size: 12px">
                        <i class="fas fa-save mr-2"></i>Lưu
                    </button>
                    <button type="button" class="btn btn-danger " data-dismiss="modal" style="font-size: 12px">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalThongTinChiTiet" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 55%;">
        <div class="modal-content" id="navThongTinChiTiet">
            <div class="modal-header p-2">
                <div class="row w-100 m-0 p-0">
                    <div class="col-12 pl-1 pr-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="tab-scroll-wrapper flex-grow-1" style="overflow: hidden;">
                                <ul class="nav nav-pills font-weight-bold m-0 d-flex tab-scroll-inner" style="width: 770px; flex-wrap: nowrap; overflow-x: auto; white-space: nowrap;">
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabNguyenNhan" class="nav-link active" onclick="showStepThongTinChiTiet('tabNguyenNhan')">
                                            <i class="fas fa-hospital-user mr-2"></i>Nguyên nhân
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabTrieuChung" class="nav-link" onclick="showStepThongTinChiTiet('tabTrieuChung')">
                                            <i class="fas fa-hospital-user mr-2"></i>Triệu chứng
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabCanLamSang" class="nav-link" onclick="showStepThongTinChiTiet('tabCanLamSang')">
                                            <i class="fas fa-hospital-user mr-2"></i>Cận Lâm sàng
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabNguyenTac" class="nav-link" onclick="showStepThongTinChiTiet('tabNguyenTac')">
                                            <i class="fas fa-hospital-user mr-2"></i>Nguyên tắc điều trị
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabDtriCuThe" class="nav-link" onclick="showStepThongTinChiTiet('tabDtriCuThe')">
                                            <i class="fas fa-hospital-user mr-2"></i>Điều trị cụ thể
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabBienChung" class="nav-link" onclick="showStepThongTinChiTiet('tabBienChung')">
                                            <i class="fas fa-hospital-user mr-2"></i>Biến chứng
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabKhac" class="nav-link" onclick="showStepThongTinChiTiet('tabKhac')">
                                            <i class="fas fa-hospital-user mr-2"></i>Khác
                                        </div>
                                    </li>
                                    <li class="nav-item mr-2 flex-shrink-0">
                                        <div id="tabNguonTaiLieu" class="nav-link" onclick="showStepThongTinChiTiet('tabNguonTaiLieu')">
                                            <i class="fas fa-hospital-user mr-2"></i>Nguồn tài liệu
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <button type="button" class="close ml-2" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-body p-2">
                <div class="row w-100 m-0 p-0">
                    <div class="col-12 p-1">
                        <div class="tab-content">
                            <div class="tab-pane p-0" id="tabNguyenNhan" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableNguyenNhan" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Nguyên nhân</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblNguyenNhan"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabTrieuChung" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableTrieuChung" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Triệu chứng</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblTrieuChung"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabCanLamSang" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableCanLamSang" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Thông tin cận lâm sàng</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblCanLamSang"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabNguyenTac" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableNguyenTacDtri" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Nguyên tắc điều trị</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblNguyenTacDtri"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabDtriCuThe" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableDtriCuThe" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Điều trị cụ thể</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblDtriCuThe"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabBienChung" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableBienChung" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Biến chứng</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblBienChung"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabKhac" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableKhac" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="10%" class="">Mã bệnh</th>
                                                        <th width="90%" class="">Thông tin khác</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblKhac"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane p-0" id="tabNguonTaiLieu" role="tabpanel">
                                <div class="row" style="margin-top:3px;">
                                    <div class="col col-sm-12">
                                        <div class="table-responsive" style="height: 315px">
                                            <table id="tableNguonTaiLieu" class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th width="5%" class="">STT</th>
                                                        <th width="95%" class="">Nguồn tài liệu</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblNguonTaiLieu"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-1" style="display: block;">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>
<div id="ModalTraHSTT" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Lý do trả hồ sơ </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmTraHSTT" method="post">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Lý do trả hồ sơ về bộ phận tiếp nhận</label>
                                <textarea class="form-control" required maxlength="500" name="ly_do" rows="5" placeholder="Nhập lý do..."></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2" id="btnLuuTraHSTT">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal"><i class="fas fa-window-close"></i>&nbsp;&nbsp;Đóng</button>
            </div>
        </div>
    </div>
</div>