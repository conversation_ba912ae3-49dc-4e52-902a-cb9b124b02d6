﻿using ESCS.Attributes;
using ESCS.COMMON.Common;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.COMMON.Response;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class PortalAccountController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PPORTAL_NSD_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Save()
        {
            var rq = Request.GetDataRequest(GetUser());
            if (rq.mat_khau != null && !string.IsNullOrEmpty(rq.mat_khau.ToString()))
            {
                string pas = rq.mat_khau.ToString();
                BaseResponse<string> resError = new BaseResponse<string>();
                resError.state_info.status = STATUS_NOTOK;
                resError.state_info.message_code = "500";
                resError.state_info.message_body = @"Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt ([@#!$^&*%./\|])";
                if (pas.Length < 8)
                    return Ok(resError);
                var regexItem1 = new Regex("[a-z]");
                var regexItem2 = new Regex("[A-Z]");
                var regexItem3 = new Regex("[0-9]");
                var regexItem4 = new Regex(@"[[@#!$^&*%./\|\]]");
                if (!regexItem1.IsMatch(pas) || !regexItem2.IsMatch(pas) || !regexItem3.IsMatch(pas) || !regexItem4.IsMatch(pas))
                    return Ok(resError);

                rq.mat_khau = Utilities.Sha256Hash(rq.mat_khau.ToString());
            }
            else
            {
                rq.mat_khau = "";
            }
            var data = await Request.GetRespone(StoredProcedure.PORTAL_NSD_NH, (object)rq);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PPORTAL_NSD_LKE_CT, json);
            return Ok(data);
        }
    }
}