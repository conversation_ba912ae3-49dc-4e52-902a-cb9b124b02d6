﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh sách các loại bệnh";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12 col-md-12 pd-3">
            <div class="card">
                <div class="card-body" style="padding-top:5px">
                    <form name="FrmSearch" method="post">
                        <div class="row row-cols-6">
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Tìm kiếm thông tin</label>
                                    <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập tìm kiếm theo tên" class="form-control">
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Tìm kiếm theo mã bệnh</label>
                                    <input type="text" name="ma" id="ma" autocomplete="off" placeholder="Nhập vào mã bệnh" class="form-control">
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <label>Tìm kiếm theo mã bộ y tế</label>
                                    <input type="text" name="ma_byt" id="ma_byt" autocomplete="off" placeholder="Nhập vào mã bộ y tế" class="form-control">
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label>Đối tác</label>
                                    <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%">
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3" style="padding-top: 21px;">
                                <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiemBenh">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThemMoi">
                                    <i class="fa fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcelDiseasList">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                    <i class="fas fa-upload"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                    <div class="row mt-2">
                        <div class="col-12">
                            <div id="gridViewDanhSachBenh" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalChiTietBenh" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Thông tin loại bệnh</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body" style="padding-top: 10px;">
                <form name="frmChiTietBenh" method="post">
                    <div class="row">
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%">
                                </select>
                            </div>
                        </div>
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã cấp trên</label>
                                <select class="select2 form-control custom-select" name="ma_ct" style="width: 100%">
                                </select>
                            </div>
                        </div>
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã bệnh</label>
                                <input type="text" name="ma" required autocomplete="off" placeholder="Mã bệnh" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-sm-8">
                            <div class="form-group">
                                <label class="_required">Tên tiếng việt</label>
                                <input type="text" name="ten_v" required autocomplete="off" placeholder="Tên tiếng việt" class="form-control">
                            </div>
                        </div>
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên tiếng anh</label>
                                <input type="text" name="ten_e" required autocomplete="off" placeholder="Tên tiếng anh" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã bộ y tế</label>
                                <input type="text" name="ma_byt" required autocomplete="off" placeholder="Mã bộ y tế" class="form-control">
                            </div>
                        </div>
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Bệnh đặc biệt</label>
                                <select class="select2 form-control custom-select" name="benh_db" style="width: 100%">
                                    <option value="">Chọn bệnh đặc biệt</option>
                                    <option value="C">Có</option>
                                    <option value="K">Không</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Bệnh có sẵn</label>
                                <select class="select2 form-control custom-select" name="benh_cs" style="width: 100%">
                                    <option value="">Chọn bệnh đặc biệt</option>
                                    <option value="C">Có</option>
                                    <option value="K">Không</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Bệnh bẩm sinh</label>
                                <select class="select2 form-control custom-select" name="benh_bs" style="width: 100%">
                                    <option value="">Chọn bệnh đặc biệt</option>
                                    <option value="C">Có</option>
                                    <option value="K">Không</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label class="_required">Bệnh tình dục</label>
                                <select class="select2 form-control custom-select" name="benh_td" style="width: 100%">
                                    <option value="">Chọn bệnh đặc biệt</option>
                                    <option value="C">Có</option>
                                    <option value="K">Không</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-sm-12">
                            <div class="form-group">
                                <label class="">Mã nhóm bệnh</label>
                                <div class="input-group">
                                    <textarea type="text" name="ma_nhom_benh" id="maNhomBenh" autocomplete="off" placeholder="Mã nhóm bệnh" class="form-control input-group cursor-pointer" onclick="chonMaNhomBenh(this)" style="pointer-events: unset !important" readonly rows="3"></textarea>
                                    <div class="input-group-append">
                                        <label class="input-group-text">
                                            <a href="javascript:void(0)" id="btnCauHinhNhomBenh">
                                                <i class="fas fa-plus" title="Thêm nhóm bệnh"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuMaBenh">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal">
                    <i class="fas fa-window-close"></i> Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalCauHinhNhomBenh" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header pt-1 pb-1">
                <h4 class="modal-title">Cấu hình nhóm bệnh</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body" style="padding-top: 10px;">
                <form name="frmCauHinhNhomBenh" method="post">
                    <div class="row">
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm</label>
                                <input type="text" name="tim" required autocomplete="off" placeholder="Mã nhóm bệnh/tên nhóm bệnh" class="form-control">
                            </div>
                        </div>
                        <div class="col col-sm-4">
                            <div class="form-group">
                                <label>Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="K">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-sm-4" style="margin-top: 22px">
                            <button type="button" class="btn btn-primary btn-sm wd-90" id="btnTimKiemCauHinh">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-90" id="btnThemCauHinh">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table id="tableCauHinhNhomBenh" class="table table-bordered fixed-header" style="width: 100%;">
                                <thead class="font-weight-bold text-center uppercase">
                                    <tr>
                                        <th width="5%">STT</th>
                                        <th width="10%">Mã nhóm bệnh</th>
                                        <th width="30%">Tên nhóm bệnh</th>
                                        <th width="10%">STT hiển thị</th>
                                        <th width="15%">Trạng thái</th>
                                        <th width="5%"></th>
                                    </tr>
                                </thead>
                                <tbody id="tblCauHinhNhomBenh">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer pt-0 pb-0">
                <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal">
                    <i class="fas fa-window-close"></i> Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalLuuCauHinhNhomBenh" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="width: 35%">
        <div class="modal-content">
            <div class="modal-header pt-1 pb-1">
                <h4 class="modal-title">Cấu hình nhóm bệnh</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body" style="padding-top: 10px;">
                <form name="frmLuuCauHinhNhomBenh" method="post">
                    <div class="row">
                        <div class="col col-sm-6">
                            <div class="form-group">
                                <label class="_required">Mã nhóm bệnh</label>
                                <input type="text" name="ma" required autocomplete="off" placeholder="Mã nhóm bệnh" class="form-control">
                            </div>
                        </div>
                        <div class="col col-sm-6">
                            <div class="form-group">
                                <label class="_required">Tên nhóm bệnh</label>
                                <input type="text" name="ten" required autocomplete="off" placeholder="Tên nhóm bệnh" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-sm-6">
                            <div class="form-group">
                                <label>Thứ tự hiển thị</label>
                                <input type="text" name="stt" autocomplete="off" placeholder="Thứ tự hiển thị" class="form-control number">
                            </div>
                        </div>
                        <div class="col col-sm-6">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="K">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0" style="display:block;">
                <button type="button" class="btn btn-outline-primary btn-sm wd-90" id="btnXoaCauHinh">
                    <i class="fas fa-trash-alt"></i> Xoá
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close"></i> Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinh">
                    <i class="fas fa-save"></i> Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalMaNhomBenh" class="modal-drag" style="width:300px; z-index:9999999;">
    <div class="modal-drag-header p-2">
        <h5><span class="modal-drag-title">Chọn mã nhóm bệnh</span> <span data-dismiss="modal-drag" id="closeMaNhomBenh"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_MaNhomBenh" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonMaNhomBenhElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:200px;" id="modalChonMaNhomBenhDanhSach"></div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn-outline-primary btn-sm wd-85" id="btnBoChonMaNhomBenh">
            <i class="fas fa-times mr-1"></i> Bỏ chọn
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnChonMaNhomBenh">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<partial name="_Template.cshtml" />
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />

@section scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/libs/bootstrap-tabdrop/bootstrap-tabdrop.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DiseasesListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/DiseasesList.js" asp-append-version="true"></script>
}