﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<style>
    #modalThemGoiHD .tabulator-row.tabulator-selected {
        background-color: unset !important;
    }
</style>

<div class="modal fade" id="modalGoiHD" tabindex="-1" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" style="max-width:96vw;margin-top:10px;margin-bottom: 10px;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Danh sách gói bảo hiểm của hợp đồng</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a">
                <div class="d-flex flex-nowrap align-items-stretch" style="gap:.75rem;height:83vh;">
                    <div class="card mb-0 border flex-grow-1 flex-shrink-0" style="width:300px;max-width:300px;" id="sidebar_info">
                        <div class="card-header bg-white text-center pb-1">
                            <h5 class="mb-0">Danh sách gói</h5>
                        </div>
                        <div class="card-body p-2 d-flex flex-column">
                            <div class="card border flex-fill mb-2" style="height:0;">
                                <div class="card-body py-0 px-1 overflow-auto">
                                    <table class="table table-sm" style="font-weight:bold;">
                                        <thead class="text-center bg-primary text-white">
                                            <tr>
                                                <th>STT</th>
                                                <th>Tên gói</th>
                                                <th>Ngày AD</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-center" id="danhsachGoiHD" style="cursor:pointer;">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="d-flex flex-nowrap" style="gap:.25rem">
                                <button type="button" class="btn btn-sm btn-primary flex-fill" style="width:0;" title="Thêm mới" onclick="_modalThemGoiHD.show();" @*onclick="themGoiHD();"*@>
                                    <i class="fas fa-plus mr-2"></i>Thêm
                                </button>
                                <button type="button" class="btn btn-sm btn-primary flex-fill" style="width:0;" title="Xóa gói khỏi danh sách" onclick="xoaGoiHD();">
                                    <i class="fas fa-trash-alt mr-2"></i>Xóa
                                </button>
                                <button type="button" class="btn btn-sm btn-primary flex-fill d-none" style="width:0;" title="Import gói bảo hiểm" onclick="EXCEL.open('xlsGoiBH')">
                                    <i class="fas fa-upload mr-2"></i>Import
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card mb-0 border flex-grow-1 flex-shrink-1">
                        <div class="card-header bg-white text-center p-0">
                            <ul class="nav nav-pills pt-1 px-2" role="tablist" style="background-color:#ffffff" id="modalGoiHDNav">
                                <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                                    <a class="nav-link" data-toggle="collapse" href="#sidebar_info" role="button" id="main_collapse">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                                    <a class="nav-link active" onclick="hthiTab('tabCauHinhQL')" id="home-tab" data-toggle="tab" href="#tabCauHinhQL" role="tab" aria-controls="home" aria-selected="true">
                                        <i class="fas fa-info-circle mr-2"></i> Thông tin gói BH
                                    </a>
                                </li>
                                <li class="nav-item mr-2" style=" background-color: #f8f9fa; border-radius: 5px;font-weight:bold">
                                    <a class="nav-link" id="profile-tab" onclick="hthiTab('tabCauHienTienBH')" data-toggle="tab" href="#tabCauHienTienBH" role="tab" aria-controls="profile" aria-selected="false">
                                        <i class="fas fa-funnel-dollar mr-2"></i> Cấu hình quyền lợi BH chính
                                    </a>
                                </li>
                                <li class="nav-item mr-2" style=" background-color: #f8f9fa; border-radius: 5px;font-weight:bold">
                                    <a class="nav-link" id="dkbs-tab" onclick="hthiTab('tabQuyenLoiBoSung')" data-toggle="tab" href="#tabQuyenLoiBoSung" role="tab" aria-controls="profile" aria-selected="false">
                                        <i class="fas fa-list-alt mr-2"></i> Cấu hình điều khoản bổ sung
                                    </a>
                                </li>
                                <li class="nav-item mr-2" style=" background-color: #f8f9fa; border-radius: 5px;font-weight:bold">
                                    <a class="nav-link" id="dkbs-tab" onclick="hthiTab('tabGhiChuKhac')" data-toggle="tab" href="#tabGhiChuKhac" role="tab" aria-controls="profile" aria-selected="false">
                                        <i class="fas fa-list-alt mr-2"></i> Các ghi chú khác
                                    </a>
                                </li>
                                <li class="nav-item r-2" style=" background-color: #f8f9fa; border-radius: 5px;font-weight:bold">
                                    <a class="nav-link" id="tabLeTyLeDong" onclick="hthiTab('tabTyLeDong')" data-toggle="tab" href="#tabTyLeDong" role="tab" aria-controls="profile" aria-selected="false">
                                        <span class="mdi mdi-av-timer">
                                            Tỷ lệ đồng - Thời gian chờ
                                        </span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body p-2 d-flex flex-column">
                            <div class="card border flex-fill mb-2 overflow-hidden" style="height:0;">
                                <div class="card-body p-0 overflow-hidden d-flex flex-column">
                                    <partial name="_CaiDatGoiHD_Content.cshtml" />
                                </div>
                            </div>
                            <div class="d-flex flex-nowrap justify-content-end" style="gap:.25rem">
                                <button type="button" data-tab="GoiHD-tabCauHinhQL" class="btn btn-sm btn-primary" id="btnApDung">
                                    <i class="fas fa-edit mr-1"></i> Áp dụng với NDBH
                                </button>
                                <button type="button" data-tab="GoiHD-tabCauHinhQL" class="btn btn-sm btn-primary" id="btnExportExcelGoiHD">
                                    <i class="fas fa-download mr-2"></i> Export
                                </button>
                                <button type="button" data-tab="GoiHD-tabCauHinhQL" class="btn btn-sm btn-primary" id="btnImportExcelGoiHD" onclick="EXCEL.open('xlsDK')" title="Import quyền lợi của gói bảo hiểm" >
                                    <i class="fas fa-upload mr-2"></i> Import
                                </button>
                                <button type="button" data-tab="GoiHD-tabCauHinhQL" class="btn btn-sm btn-primary" id="btnLuuGoiHD">
                                    <i class="fa fa-save mr-2"></i> Lưu
                                </button>
                                <button type="button" data-tab="GoiHD-tabCauHinhQL" class="btn btn-sm btn-primary" id="btnCopyGoiHD">
                                    <i class="fa fa-clone mr-2"></i> Copy
                                </button>

                                <button type="button" data-tab="GoiHD-tabCauHienTienBH" class="btn btn-sm btn-primary" id="btnLuuTienQLoiGoiHD">
                                    <i class="fa fa-save mr-2"></i> Lưu
                                </button>

                                <button type="button" data-tab="GoiHD-tabQuyenLoiBoSung" class="btn btn-sm btn-primary" id="btnLuuDKBSGoiHD">
                                    <i class="fa fa-save mr-2"></i> Lưu
                                </button>

                                <button type="button" data-tab="GoiHD-tabGhiChuKhac" class="btn btn-sm btn-primary" id="btnLuuGhiChuKhacGoiHD">
                                    <i class="fa fa-save mr-2"></i> Lưu
                                </button>

                                <button type="button" data-tab="GoiHD-tabTyLeDong" class="btn btn-sm btn-primary" id="btnLuuTyLeDongGoiHD">
                                    <i class="fa fa-save mr-2"></i> Lưu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @*<div class="modal-footer justify-content-start">
                <button type="button" class="btn btn-sm btn-primary ml-auto" data-dismiss="modal">
                <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                </div>*@
        </div>
    </div>
</div>

<div class="modal fade" id="modalThemGoiHD" tabindex="-1" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" style="max-width:60vw;margin-top:10px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm gói bảo hiểm</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12" id="navTimKiemGoiHD">
                        <ul class="nav nav-pills font-weight-bold mb-3" role="tablist">
                            <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                <a class="nav-link active" data-val="KHO_GOI" data-toggle="tab" href="#tabTimKiemGoiHDHT" role="tab">
                                    Tìm kiếm gói bảo hiểm từ nguồn hệ thống
                                </a>
                            </li>
                            <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                <a class="nav-link" data-val="HOP_DONG" data-toggle="tab" href="#tabTimKiemGoiHDKhac" role="tab">
                                    Tìm kiếm gói bảo hiểm từ nguồn hợp đồng khác
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-12">
                        <div class="tab-content">
                            <div id="tabTimKiemGoiHDHT" class="tab-pane fade show active data-scroll p-0" role="tabpanel">
                                <form name="frmTimKiemGoiHDHT" class="row mb-3">                                   
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="so_hs">Tuổi từ</label>
                                            <input type="text" class="form-control decimal" maxlength="3" autocomplete="off" name="tuoi_tu" placeholder="Tuổi từ">
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="so_hs">Tuổi tới</label>
                                            <input type="text" class="form-control decimal" maxlength="3" autocomplete="off" name="tuoi_toi" placeholder="Tuổi tới">
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="ngay_d">Áp dụng từ</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad_tu" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="ngay_d">Áp dụng đến</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad_toi" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="so_hs">Thông tin tìm kiếm</label>
                                            <input type="text" class="form-control" autocomplete="off" name="tim" placeholder="Nhập mã/tên gói bảo hiểm">
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group">
                                            <label for="ma_doi_tac">Giới tính</label>
                                            <select class="select2 form-control custom-select" name="gioi_tinh" style="width: 100%; height:36px;">
                                                <option value="">Chọn</option>
                                                <option value="NAM">Nam</option>
                                                <option value="NU">Nữ</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col" style="margin-top:1.3rem">
                                        <button type="button" class="btn btn-primary btn-sm" title="Tìm kiếm" onclick="getPagingHDHT(1);">
                                            <i class="fa fa-search mr-2"></i>Tìm kiếm
                                        </button>
                                    </div>
                                </form>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="table-responsive">
                                            <div id="gridViewTimKiemGoiHDHT" class="table-app" style="height:320px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="tabTimKiemGoiHDKhac" class="tab-pane fade data-scroll p-0" role="tabpanel">
                                <form name="frmTimKiemGoiHDKhac" class="row mb-3">
                                    <div class="col-8">
                                        <div class="form-group">
                                            <label for="so_hd">Thông tin tìm kiếm</label>
                                            <input type="text" class="form-control" autocomplete="off" name="tim" placeholder="Nhập số hợp đồng">
                                        </div>
                                    </div>
                                    <div class="col" style="margin-top:1.3rem">
                                        <button type="button" class="btn btn-primary btn-sm" title="Tìm kiếm" onclick="getPagingHDKhac(1);">
                                            <i class="fa fa-search mr-2"></i>Tìm kiếm
                                        </button>
                                    </div>
                                </form>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="table-responsive">
                                            <div id="gridViewTimKiemGoiHDKhac" class="table-app" style="height:320px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-sm btn-primary" onclick="nhapGoiDS();">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-sm btn-primary" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalLoaiHinh" class="modal-drag" style="width:350px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn loại hình bảo lãnh</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_LoaiHinh" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalLoaiHinhElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLoaiHinhDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer" style="height: 32px; border-top: 1px solid #eaeaea; margin:0px 0px 10px 0px;">
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnChonLoaiHinh">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="danhsachGoiHD_template">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr onclick="chonGoiHD(this, '<%- item.ma_doi_tac %>', <%- item.so_id_hd %>, <%- item.so_id_goi %>);" data-so_id_goi="<%- item.so_id_goi %>">
        <td><%- index+1 %></td>
        <td class=" text-left"><%- item.ten %></td>
        <td><%- item.ngay_ad_hthi??'null' %></td>
    </tr>
    <% })} %>
</script>

<script type="text/html" id="tableNhapQLoiGoiHD_template">
    <% if(dk.length > 0){ %>
    <% _.forEach(dk, function(item,index) { %>
    <% if(item.lh_nv_ct == null){ %>
    <tr class="item_lhnv" data-lhnv="<%- item.lh_nv %>" data-lhnv-ct="<%- item.lh_nv_ct %>">
        <td class="text-center"><%- item.lh_nv %></td>
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd %>px">
            <input type="text" data-ten="<%- item.lh_nv %>" id="<%- item.lh_nv %>" class="floating-input" value="<%- item.ten %>" />
        </td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd %>px">
            <input type="text" data-ten="<%- item.lh_nv %>" class="floating-input" value="<%- item.ten %>" />
        </td>
        <%
        }
        %>
        <td class="text-center d-none">
            <input type="hidden" data-lhnv-ct="<%- item.lh_nv %>" value="<%- item.lh_nv_ct %>" />
            <input type="hidden" data-bt="<%- item.lh_nv %>" value="<%- item.bt %>" />
            <input type="hidden" data-tl-phi="<%- item.lh_nv %>" value="<%- item.tl_phi %>" />
            <input type="hidden" data-so-ngay-gia-han="<%- item.lh_nv %>" value="<%- item.so_ngay_gia_han %>" />
            <input type="hidden" data-quyen-loi="<%- item.lh_nv %>" value="<%- item.quyen_loi %>" />
            <input type="hidden" data-loai="<%- item.lh_nv %>" value="<%- item.loai %>" />
            <input type="hidden" data-loaiq="<%- item.lh_nv %>" value="<%- item.loaiq %>" />
            <%- item.sott %>
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-ma-nt="<%- item.lh_nv %>" data-val="<%- item.ma_nt??'VND' %>" onclick="chonMaNguyenTe(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ma_nt??'VND' %>" style="text-align:center; cursor:pointer" />
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-lhnv-tru-lui="<%- item.lh_nv %>" data-val="<%- item.lhnv_tru_lui %>" onclick="chonDSLHNV(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.lhnv_tru_lui %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-right">
            <input type="text" data-so-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_lan_ngay) %>" />
        </td>

        <td class="text-right  d-none">
            <input type="text" data-ngay-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.ngay_lan_kham) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-tien-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_lan_ngay) %>" />
        </td>
        <td class="text-right  d-none">
            <input type="text" data-tien-lan-kham="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_lan_kham) %>" />
        </td>
        @*<td class="text-right">
            <input type="text" data-tl-tien-nam="<%- item.lh_nv %>" maxlength="16" class="decimal floating-input" value="<%- item.tl_tien_nam %>" />
        </td>*@
        <td class="text-right">
            <input type="text" data-tien-nam="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_nam) %>" />
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-kieu-ad="<%- item.lh_nv %>" data-val="<%- item.kieu_ad %>" onclick="chonDSKieuAD(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.kieu_ad_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td style="display: none" class="text-center"><%- item.lh_nv %></td>
        <td class="text-right">
            <input type="text" data-dong-bh="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.dong_bh) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-so-ngay-cho="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- item.so_ngay_cho %>" />
        </td>
        <td class="text-right">
            <input type="text" data-phi="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.phi) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-ghi-chu="<%- item.lh_nv %>" maxlength="200" class="floating-input" value="<%- item.ghi_chu %>" />
        </td>
    </tr>
    <% }else if(item.lh_nv_ct.toString().indexOf('.') != -1){ %>
    <tr class="item_lhnv" data-lhnv="<%- item.lh_nv %>" data-lhnv-ct="<%- item.lh_nv_ct %>">
        <td class="text-center"><%- item.lh_nv %></td>
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd %>px">
            <input type="text" data-ten="<%- item.lh_nv %>" class="floating-input" value="<%- item.ten %>" />
        </td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd%>px; font-style: italic;">
            <input type="text" data-ten="<%- item.lh_nv %>" class="floating-input" value="<%- item.ten %>" />
        </td>
        <%
        }
        %>
        <td class="text-center d-none">
            <input type="hidden" data-lhnv-ct="<%- item.lh_nv %>" value="<%- item.lh_nv_ct %>" />
            <input type="hidden" data-bt="<%- item.lh_nv %>" value="<%- item.bt %>" />
            <input type="hidden" data-tl-phi="<%- item.lh_nv %>" value="<%- item.tl_phi %>" />
            <input type="hidden" data-so-ngay-gia-han="<%- item.lh_nv %>" value="<%- item.so_ngay_gia_han %>" />
            <input type="hidden" data-quyen-loi="<%- item.lh_nv %>" value="<%- item.quyen_loi %>" />
            <input type="hidden" data-loai="<%- item.lh_nv %>" value="<%- item.loai %>" />
            <input type="hidden" data-loaiq="<%- item.lh_nv %>" value="<%- item.loaiq %>" />
            <%- item.sott %>
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-ma-nt="<%- item.lh_nv %>" data-val="<%- item.ma_nt??'VND' %>" onclick="chonMaNguyenTe(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ma_nt??'VND' %>" style="text-align:center; cursor:pointer" />
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-lhnv-tru-lui="<%- item.lh_nv %>" data-val="<%- item.lhnv_tru_lui %>" onclick="chonDSLHNV(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.lhnv_tru_lui %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-right" style="font-style: italic;">
            <input type="text" data-so-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_lan_ngay) %>" />
        </td>
        <td class="text-right  d-none" style="font-style: italic;">
            <input type="text" data-ngay-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input " value="<%- ESUtil.formatMoney(item.ngay_lan_kham) %>" />
        </td>
        <td class="text-right" style="font-style: italic;">
            <input type="text" data-tien-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_lan_ngay) %>" />
        </td>
        <td class="text-right  d-none" style="font-style: italic;">
            <input type="text" data-tien-lan-kham="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_lan_kham) %>" />
        </td>
        @*<td class="text-right" style="font-style: italic;">
            <input type="text" data-tl-tien-nam="<%- item.lh_nv %>" maxlength="16" class="decimal floating-input" value="<%- item.tl_tien_nam %>" />
        </td>*@
        <td class="text-right" style="font-style: italic;">
            <input type="text" data-tien-nam="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_nam) %>" />
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-kieu-ad="<%- item.lh_nv %>" data-val="<%- item.kieu_ad %>" onclick="chonDSKieuAD(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.kieu_ad_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center" style="font-style: italic;display: none"><%- item.lh_nv %></td>
        <td class="text-right" style="font-style: italic;">
            <input type="text" data-dong-bh="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.dong_bh) %>" />
        </td>
        <td class="text-right" style="font-style: italic;">
            <input type="text" data-so-ngay-cho="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_ngay_cho) %>" />
        </td>
        <td class="text-right" style="font-style: italic;">
            <input type="text" data-phi="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.phi) %>" />
        </td>
        <td class="text-right" style="font-style: italic;">
            <input type="text" data-ghi-chu="<%- item.lh_nv %>" maxlength="200" class="floating-input" value="<%- item.ghi_chu %>" />
        </td>
    </tr>
    <% }else{ %>
    <tr class="item_lhnv" data-lhnv="<%- item.lh_nv %>" data-lhnv-ct="<%- item.lh_nv_ct %>">
        <td class="text-center"><%- item.lh_nv %></td>
        <%if(item.pd==15){
        %>
        <td style="padding-left: <%- item.pd %>px">
            <input type="text" data-ten="<%- item.lh_nv %>" class="floating-input" value="<%- item.ten %>" />
        </td>
        <%
        }
        else
        {
        %>
        <td style="padding-left: <%- item.pd %>px">
            <input type="text" data-ten="<%- item.lh_nv %>" class="floating-input" value="<%- item.ten %>" />
        </td>
        <%
        }
        %>
        
        <td class="text-center d-none">
            <input type="hidden" data-lhnv-ct="<%- item.lh_nv %>" value="<%- item.lh_nv_ct %>" />
            <input type="hidden" data-bt="<%- item.lh_nv %>" value="<%- item.bt %>" />
            <input type="hidden" data-tl-phi="<%- item.lh_nv %>" value="<%- item.tl_phi %>" />
            <input type="hidden" data-so-ngay-gia-han="<%- item.lh_nv %>" value="<%- item.so_ngay_gia_han %>" />
            <input type="hidden" data-quyen-loi="<%- item.lh_nv %>" value="<%- item.quyen_loi %>" />
            <input type="hidden" data-loai="<%- item.lh_nv %>" value="<%- item.loai %>" />
            <input type="hidden" data-loaiq="<%- item.lh_nv %>" value="<%- item.loaiq %>" />
            <%- item.sott %>
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-ma-nt="<%- item.lh_nv %>" data-val="<%- item.ma_nt??'VND' %>" onclick="chonMaNguyenTe(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ma_nt??'VND' %>" style="text-align:center; cursor:pointer" />
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-lhnv-tru-lui="<%- item.lh_nv %>" data-val="<%- item.lhnv_tru_lui %>" onclick="chonDSLHNV(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.lhnv_tru_lui %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-right">
            <input type="text" data-so-lan-ngay="<%- item.lh_nv %>" onchange="changeSoLanNgayBac2(this)" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_lan_ngay) %>" />
        </td>
        <td class="text-right  d-none">
            <input type="text" data-ngay-lan-kham="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.ngay_lan_kham) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-tien-lan-ngay="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_lan_ngay) %>" />
        </td>
        <td class="text-right  d-none" style="font-style: italic;">
            <input type="text" data-tien-lan-kham="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_lan_kham) %>" />
        </td>
        @*<td class="text-right">
            <input type="text" data-tl-tien-nam="<%- item.lh_nv %>" maxlength="16" class="decimal floating-input" value="<%- item.tl_tien_nam %>" />
        </td>*@
        <td class="text-right">
            <input type="text" data-tien-nam="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.tien_nam) %>" />
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-kieu-ad="<%- item.lh_nv %>" data-val="<%- item.kieu_ad %>" onclick="chonDSKieuAD(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.kieu_ad_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td style="display: none" class="text-center"><%- item.lh_nv %></td>
        <td class="text-right">
            <input type="text" data-dong-bh="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.dong_bh) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-so-ngay-cho="<%- item.lh_nv %>" maxlength="16" class="number floating-input" value="<%- ESUtil.formatMoney(item.so_ngay_cho) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-phi="<%- item.lh_nv %>" maxlength="16" class="money-nullable floating-input" value="<%- ESUtil.formatMoneyNullable(item.phi) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-ghi-chu="<%- item.lh_nv %>" maxlength="200" class="floating-input" value="<%- item.ghi_chu %>" />
        </td>
    </tr>
    <% } %>
    <% })} %>
    <% if(dk.length < 12){
    for(var i = 0; i < 12 - dk.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tableDKBSGoiHDTemplate">
    <%if(ds_dkbs.length > 0){
    _.forEach(ds_dkbs, function(item,index) { %>
    <tr class="dkbs">
        <td>
            <input type="hidden" data-name="ma" data-field="ma" value="<%- item.ma %>" />
            <a href="#" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td>
            <input type="text" class="floating-input combobox" data-name="ma_nt" data-field="ma_nt" data-val="<%- item.ma_nt??'VND' %>" onclick="chonMaNguyenTe(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ma_nt??'VND' %>" style="text-align:center; cursor:pointer" />
        </td>
        <td>
            <input type="text" data-name="ghi_chu" autocomplete="off" maxlength="500" class="floating-input" data-field="ghi_chu" value="<%- item.ghi_chu %>" />
        </td>
        <td>
            <input style="text-align:center" type="text" data-name="so_lan_ngay" autocomplete="off" maxlength="18" class="floating-input decimal" data-field="so_lan_ngay" value="<%- item.so_lan_ngay %>" />
        </td>
        <td>
            <input type="text" data-name="tien_lan_ngay" autocomplete="off" maxlength="18" class="number floating-input" data-field="tien_lan_ngay" value="<%- item.tien_lan_ngay %>" />
        </td>
        <td>
            <input type="text" data-name="tien_nam" autocomplete="off" maxlength="18" class="number floating-input" data-field="tien_nam" value="<%- item.tien_nam %>" />
        </td>
        <td>
            <input type="text" data-name="dong_bh" autocomplete="off" maxlength="3" class="floating-input number" data-field="dong_bh" value="<%- item.dong_bh %>" />
        </td>
        <td>
            <input style="text-align:center" type="text" data-name="so_ngay_cho" autocomplete="off" maxlength="18" class="floating-input decimal" data-field="so_ngay_cho" value="<%- item.so_ngay_cho %>" />
        </td>
        <td>
            <input type="text" data-name="phi" autocomplete="off" maxlength="18" class="number floating-input" data-field="phi" value="<%- item.phi %>" />
        </td>
        <td>
            <input type="text" data-name="tl_phi" autocomplete="off" maxlength="3" class="number floating-input" data-field="tl_phi" value="<%- item.tl_phi %>" />
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" data-val="<%- item.ma %>" onclick="xoaDKBS(this)"></i>
        </td>
    </tr>
    <% })} %>
    <% if(ds_dkbs.length < 11){
    for(var i = 0; i < 11 - ds_dkbs.length; i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalThemMaBenhGoiHD_template">
    <%if(ds_ma_benh.length > 0){
    _.forEach(ds_ma_benh, function(item,index) { %>
    <tr class="ma_benh">
        <td>
            <input type="hidden" data-name="ma_benh" data-field="ma_benh" value="<%- item.ma_benh %>" />
            <a href="#" data-field="ma_benh" data-val="<%- item.ma_benh %>"><%- item.ten_v %></a>
        </td>
        <td>
            <input type="hidden" data-name="lh_nv_ten" value="<%- item.lh_nv_ten %>" />
            <% if(item.lh_nv == '' || item.lh_nv == null || item.lh_nv == undefined){ %>
            <a href="#" data-field="lh_nv" data-val="<%- item.lh_nv %>" onclick="chonCauHinhQuyenLoi(this)">Chọn danh sách quyền lợi</a>
            <% }else{ %>
            <a href="#" data-field="lh_nv" data-val="<%- item.lh_nv %>" onclick="chonCauHinhQuyenLoi(this)"><%- item.lh_nv_ten %></a>
            <% } %>
        </td>
        <td>
            <input type="text" data-name="tl_dong" maxlength="3" autocomplete="off" class="floating-input number" value="<%- item.tl_dong %>" />
        </td>
        <td>
            <input type="text" data-name="tg_cho" maxlength="3" autocomplete="off" class="floating-input number" value="<%- item.tg_cho %>" />
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" data-val="<%- item.ma_benh %>" onclick="xoaMaBenh(this)"></i>
        </td>
    </tr>
    <% })} %>

    <% if(ds_ma_benh.length < 8){
    for(var i = 0; i < 8 - ds_ma_benh.length; i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalLoaiHinhDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dslh" id="dslh_<%- item.ma %>" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="loai_hinh_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonLoaiHinhItem">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_hinh_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="tableGhiChuKhacGoiHDTemplate">
    <%if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="ghiChuKhac">
        <td>
            <a href="#" data-field="ma" data-val="<%- item.ma %>"><%- item.ma %></a>
        </td>
        <td>
            <input type="text" data-name="ten" autocomplete="off" maxlength="500" class="floating-input" data-field="ten" value="<%- item.ten %>" />
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" data-val="<%- item.ma %>" onclick="xoaGhiChuKhac(this)"></i>
        </td>
    </tr>
    <% })} %>
    <% if(data.length < 11){
    for(var i = 0; i < 11 - data.length; i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>