﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh sách ứng dụng mobile";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<style>
    #gridView tr[data-row] {
        cursor: pointer;
    }
</style>

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" autocomplete="off">
                    <div class="row">
                        <div class="col-2">
                            <div class="form-group">
                                <label for="ma_app">Mã ứng dụng</label>
                                <input type="text" name="ma_app" id="ma_app" placeholder="Mã ứng dụng" class="form-control">
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label for="ten_app">Tên ứng dụng</label>
                                <input type="text" name="ten_app" id="ten_app" placeholder="Tên ứng dụng" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width:100%" >
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="K">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm" style="width: 65px" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm" style="width: 65px" id="btnThemMoi">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:.5rem">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div class="table-app" style="height: 70vh;">
                                <div class="table-responsive h-100 border">
                                    <table class="table table-bordered text-center">
                                        <thead class="bg-primary text-white">
                                            <tr>
                                                <th scope="col">STT</th>
                                                <th scope="col">Đối tác</th>

                                                <th scope="col">Mã ứng dụng</th>
                                                <th scope="col">Tên ứng dụng</th>
                                                <th scope="col">Trạng thái</th>
                                                <th scope="col">Codepush</th>

                                                <th scope="col">Mã Pb Android</th>
                                                <th scope="col">Tên Pb Android</th>
                                                <th scope="col">Pb push Android</th>

                                                <th scope="col">Mã Pb IOS</th>
                                                <th scope="col">Tên Pb IOS</th>
                                                <th scope="col">Pb push IOS</th>
                                            </tr>
                                        </thead>
                                        <tbody id="gridView"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalNhap" tabindex="-1">
    <div class="modal-dialog" style="max-width:1100px;">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title" id="exampleModalLabel">Nhập thông tin ứng dụng mobile</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form method="post" name="frmNhap" autocomplete="off">
                    <div class="row" style="row-gap: .75rem;">
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Mã ứng dụng</label>
                                <input type="text" name="ma_app" placeholder="Mã ứng dụng" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Tên ứng dụng</label>
                                <input type="text" name="ten_app" placeholder="Tên ứng dụng" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width:100%" required>
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="K">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label class="_required">Codepush</label>
                                <select class="select2 form-control custom-select" name="use_codepush" style="width:100%" required>
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="K">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Tên phiên bản Android</label>
                                <input type="text" name="version_name_android" placeholder="Tên phiên bản" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Mã phiên bản Android</label>
                                <input type="text" name="version_android" placeholder="Mã phiên bản" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Phiên bản push Android</label>
                                <input type="text" name="version_push_android" placeholder="Mã phiên bản" class="form-control">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Tên phiên bản IOS</label>
                                <input type="text" name="version_name_ios" placeholder="Tên phiên bản" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Mã phiên bản IOS</label>
                                <input type="text" name="version_ios" placeholder="Mã phiên bản" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Phiên bản push IOS</label>
                                <input type="text" name="version_push_ios" placeholder="Mã phiên bản" class="form-control">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer py-1">
                <button id="btnDelete" type="button" class="btn btn-sm btn-outline-primary mr-auto">
                    <i class="mr-2 fas fa-trash-alt"></i>Xóa
                </button>

                <button type="button" class="btn btn-sm btn-outline-primary" data-dismiss="modal">
                    <i class="mr-2 fas fa-window-close"></i>Đóng
                </button>
                <button id="btnSave" type="button" class="btn btn-sm btn-primary">
                    <i class="mr-2 fas fa-save"></i>Lưu
                </button>
                <button id="btnSaveClose" type="button" class="btn btn-sm btn-primary">
                    <i class="mr-2 fas fa-save"></i>Lưu và đóng
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="gridView_template">
    <% _.forEach(danh_sach, function(item,index) { %>

        <tr data-ma_doi_tac="<%- item.ma_doi_tac %>"
            data-ma_app="<%- item.ma_app %>"
            data-ten_app="<%- item.ten_app %>"
            data-trang_thai="<%- item.trang_thai %>"
            data-use_codepush="<%- item.use_codepush %>"
            data-version_android="<%- item.version_android %>"
            data-version_name_android="<%- item.version_name_android %>"
            data-version_push_android="<%- item.version_push_android %>"
            data-version_ios="<%- item.version_ios %>"
            data-version_name_ios="<%- item.version_name_ios %>"
            data-version_push_ios="<%- item.version_push_ios %>"
            data-row>
            <td scope="col"><%- item.sott %></td>
            <td><%- item.ma_doi_tac %></td>

            <td><%- item.ma_app %></td>
            <td><%- item.ten_app %></td>
            <td><%- item.trang_thai_ten %></td>
            <td><%- item.use_codepush_ten %></td>

            <td><%- item.version_android %></td>
            <td><%- item.version_name_android %></td>
            <td><%- item.version_push_android %></td>

            <td><%- item.version_ios %></td>
            <td><%- item.version_name_ios %></td>
            <td><%- item.version_push_ios %></td>
        </tr>

    <% }) %>
    <% for(let i=0; i < 14 - danh_sach.length; i++) { %>
        <tr>
            <td scope="col">&nbsp;</td>
            <td></td>

            <td></td>
            <td></td>
            <td></td>
            <td></td>

            <td></td>
            <td></td>
            <td></td>

            <td></td>
            <td></td>
            <td></td>
        </tr>
    <% } %>
</script>

@* <div class="modal fade bs-example-modal-lg" id="modalNhapNganHang" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinNganHang" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin ngân hàng <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã ngân hàng</label>
                                <div class="input-group">
                                    <input type="text" maxlength="20" class="form-control" required="" autocomplete="off" name="ma_ngan_hang" placeholder="Mã ngân hàng">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên ngân hàng</label>
                                <input type="text" maxlength="250" name="ten_ngan_hang" autocomplete="off" required class="form-control" placeholder="Tên ngân hàng">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 5px;">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Mã chi nhánh</label>
                                <input type="text" maxlength="20" name="ma_chi_nhanh" autocomplete="off" class="form-control" placeholder="Mã chi nhánh">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên chi nhánh</label>
                                <input type="text" maxlength="250" name="ten_chi_nhanh" autocomplete="off" required class="form-control" placeholder="Tên chi nhánh">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="0">Ngừng sử dụng</option>
                                    <option value="1">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btnAction btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinNganHang"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btnAction btn btn-primary btn-sm wd-80" id="btnCopyThongTinNganHang"><i class="fas fa-copy"></i> Copy</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 ml-auto" id="btnLuuThongTinNganHang"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div> *@

@section Scripts {
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/MobileAppListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/MobileAppList.js" asp-append-version="true"></script>
}

