﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Contract.Controllers
{
    [Area("Contract")]
    [SystemAuthen]
    [ESCSDescription(ESCSMethod.GET, "Hợp đồng bảo hiểm sức khỏe con người")]
    public class HealthCareController : BaseController
    {
        [ESCSDescription(ESCSMethod.GET, "Màn hình tìm kiếm/xem thông tin chung")]
        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_LKE, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "<PERSON><PERSON><PERSON> thông tin khách hàng")]
        [AjaxOnly]
        public async Task<IActionResult> Save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KH_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> TimKiemKhachHang()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KH_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> TichHopKhachHang()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KH_CONVERT, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.GET, "Liệt kê danh sách hồ sơ khách hàng")]
        [AjaxOnly]
        public async Task<IActionResult> layChiTietHopDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_LKE, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.GET, "Liệt kê danh sách hồ sơ khách hàng")]
        [AjaxOnly]
        public async Task<IActionResult> getListLHNV()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PKH_BH_NG_NHLV, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.GET, "Lấy danh sách gói bảo hiểm")]
        [AjaxOnly]
        public async Task<IActionResult> getDsGoiBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PKH_BH_NG_GOI_BH, json);
            return Ok(data);
        }

        // lưu hợp đồng
        [AjaxOnly]
        public async Task<IActionResult> hd_nh()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_HD_NH, json);
            return Ok(data);
        }

        // Lưu khách hàng
        [AjaxOnly]
        public async Task<IActionResult> luuThongTinGCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaThongTinGCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_XOA, json);
            return Ok(data);
        }

        // Lấy quyền lợi gói bảo hiểm đồng tái
        [AjaxOnly]
        public async Task<IActionResult> LayQuyenLoiGoiBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_QL_LKE, json);
            return Ok(data);
        }

        // Lấy quyền lợi điều khoản bổ sung của gcn
        [AjaxOnly]
        public async Task<IActionResult> LayDKBSGCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_DS_DKBS_LKE, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Lưu thông tin Email CC")]
        [AjaxOnly]
        public async Task<IActionResult> SaveEmail_CC()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_EMAIL_CC_NH, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.GET, "Xem thông tin chi tiết Email CC")]
        [AjaxOnly]
        public async Task<IActionResult> GetDetailEmail_CC()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_EMAIL_CC_LKE_CT, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.DELETE, "Xóa thông tin Email CC")]
        [AjaxOnly]
        public async Task<IActionResult> DeleteEmailCC()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_EMAIL_CC_LKE_X, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.GET, "Lấy danh sách các nhà bảo hiểm")]
        [AjaxOnly]
        public async Task<IActionResult> getListNhaBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_NHA_BH_TATCA, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.GET, "Lấy danh sách đồng tái")]
        [AjaxOnly]
        public async Task<IActionResult> getListDongtai()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_DONG_TAI_LKE, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Lưu danh sách đồng tái")]
        [AjaxOnly]
        public async Task<IActionResult> LuuThongTinDongTai()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_DONG_TAI_SAVE, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Lấy chi tiết đồng tái")]
        [AjaxOnly]
        public async Task<IActionResult> getDetailDongTai()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DONG_TAI_CT, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.DELETE, "Xóa đồng tái")]
        [AjaxOnly]
        public async Task<IActionResult> XoaDongTai()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DONG_TAI_DELETE, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Lưu thông tin import excel")]
        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_BH_HD_NG_GCN_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Lưu phí phát sinh TPA")]
        [AjaxOnly]
        public async Task<IActionResult> TongPhiTpaNhap()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_PHI_TPA_NH, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Lấy tổng phí TPA")]
        [AjaxOnly]
        public async Task<IActionResult> GetTongPhiTpa()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_PHI_TPA_LK, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Xóa phí phát sinh TPA")]
        [AjaxOnly]
        public async Task<IActionResult> xoaTongPhiTpa()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_PHI_TPA_X, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Xóa phí phát sinh TPA")]
        [AjaxOnly]
        public async Task<IActionResult> GetLisMappingDoiTuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_DS_MAPPING_EXCEL, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Xóa phí phát sinh TPA")]
        [AjaxOnly]
        public async Task<IActionResult> UpdateMappingDoiTuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_DS_MAPPING_NH, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Sửa thông tin giấy chứng nhận")]
        [AjaxOnly]
        public async Task<IActionResult> LuuSuaGCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_UPDATE, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Hủy duyệt hợp đồng")]
        [AjaxOnly]
        public async Task<IActionResult> huyDuyetHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            //var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_HUY_DUYET, json);
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_HUY_DUYET, json, "/api/health/contract/unapprove");
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> getPagingKyThanhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KY_THANH_TOAN_LKE_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> getDetailKyThanhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KY_THANH_TOAN_LKE_LKE_CT, json);
            return Ok(data);
        }

        [ESCSDescription(ESCSMethod.POST, "Nhập thông tin kỳ thanh toán")]
        [AjaxOnly]
        public async Task<IActionResult> nhapThongTinKyThanhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KY_THANH_TOAN_NH_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaThongTinKyThanhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KY_THANH_TOAN_XOA, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDanhSachNDBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_DS_LKE, json);
            return Ok(data);
        }

        //huynq
        [AjaxOnly]
        public async Task<IActionResult> layChiTietTyGiaHopDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_NT_TIEN_BH_LKE, json);
            return Ok(data);
        }

        //huynq
        [AjaxOnly]
        public async Task<IActionResult> luuChiTietTyGiaHopDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_NT_TIEN_BH_NH, json);
            return Ok(data);
        }

        //huynq
        [AjaxOnly]
        public async Task<IActionResult> layDanhSachGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layChiTietGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDKBSGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_DKBS_LK, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> nhapGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> nhapChiTietGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_TIEN_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> nhapDKBSGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_DKBS_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> nhapGhiChuGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_GHI_CHU_KHAC_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> nhapBVGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_CAU_HINH_BV_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> timKiemGoiHDHT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_NG_NGUOI_GOI_BH_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> timKiemGoiHDKhac()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_TIM_KIEM_GOI_HD_KHAC, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> copyGoiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_NGUON_KHAC_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDMChung()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_MA_DANH_MUC_CACHE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuDM()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DANH_MUC_PJICO_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemCTDM()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DANH_MUC_PJICO_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> getPagingDM()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DANH_MUC_PJICO_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDSDM()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DANH_MUC_PJICO_CACHE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaDM()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DANH_MUC_PJICO_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuPTKT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_PHUONG_THUC_KHAI_THAC_PJICO_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemPTKT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_PHUONG_THUC_KHAI_THAC_PJICO_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> getPagingPTKT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_PHUONG_THUC_KHAI_THAC_PJICO_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaPTKT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_PHUONG_THUC_KHAI_THAC_PJICO_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDSPTKT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_PHUONG_THUC_KHAI_THAC_PJICO_CACHE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuDL()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_DAI_LY_PJICO_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemCTDL()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_DAI_LY_PJICO_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> getPagingDL()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_DAI_LY_PJICO_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDSDL()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_DAI_LY_PJICO_CACHE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaDL()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_DAI_LY_PJICO_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> laySTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_STT_MAX, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> TimKiemNgPT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BH_HD_NGUOI_DS_TKIEM, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcelDK()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_CT_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcelHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> MappingDataExcelGCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_SDBS_NGUOI_DS_MAPPING_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> copyHDGoc()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_HD_COPY_HD_GOC, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcelKyTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_KY_TT_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> checkNDBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_CHECK_NDBH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel_DSCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_BH_HD_NG_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel_GoiBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel_QloiHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_QLOI_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel_LSBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_HS_LSBT_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel_GoiBH_PJICO()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_PJICO_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel_GCN_PJICO()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_BH_HD_NG_GCN_PJICO_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> duyetHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            //var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_DUYET, json);
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_DUYET, json, "/api/health/contract/approve");
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveDataExcel_GoiBH_CT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NGUOI_GOI_BH_CT_PJICO_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> saveListDL()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_DAI_LY_PJICO_IMPORT_EXCEL, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> checkNDBH_Edit()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_CHECK_THAY_DOI_NDBH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> convertHD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_HT_PJICO_CONVERT_DATA_IMP, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> convertHSBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_BH_HSBT_IMPORT_EXCEL_CONVERT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> checkConvert()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_HT_PJICO_CHECK_CONVERT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> capNhatUocDuPhongLke()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_DP_DIEM_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemMauInXacMinhPhi()
        {
            var json = Request.GetDataRequestNew(GetUser());
            //var urlApi = "api/partner/form/fee-verify-v2";
            var urlApi = "api/partner/form/fee-verify";
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NSD_KTRA, json, urlApi);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> updateQuyenLoiGCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_DK_UPDATE, json);
            return Ok(data);
        }

        /// <summary>
        /// Xem tình trạng thanh toán phí
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> xemTinhTrangTTPhiCore()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_KY_THANH_TOAN_LKE, json, "/api/partner/get-payment-v2");
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> updateGoiBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_NG_GCN_DS_UPDATE_GOI_BH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> capNhatLaiUocDuPhongNH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_DP_DIEM_TICH_HOP_LAI_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> quaTrinhXuLy()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_QTXL_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layPhiDaChuyenFTS()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HD_PHI_CHUYEN_FTS_LAY_PHI_DA_CHUYEN, json);
            return Ok(data);
        }

        public async Task<IActionResult> DSNhomSuKienBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NHOM_SU_KIEN_TKIEM, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDSSuKienTheoVuTT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_SU_KIEN_BAO_HIEM_TKIEM, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> TKiemSuKienBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_SU_KIEN_BAO_HIEM_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuSuKienBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_SU_KIEN_BAO_HIEM_NH, json);
            return Ok(data);
        }
    }
}