﻿<script type="text/html" id="tblThongTinEmailCC_template">
    <% if(data.length > 0){ %>
    <% _.forEach(data, function(item,index) { %>
    <tr class="ttpChiTietItem" data-stt="<%- item.stt %>">
        <td style="width: 40px; text-align: center;">
             <input type="hidden" class="floating-input" data-field="bt" value="<%- item.bt %>" /> 
           <%- index + 1 %>
        </td>
        <td>
            <input  type="text" data-field="ten" class="floating-input" value="<%- item.ten %>" />
        </td>
        <td>
            <input  type="text" data-field="email" class="floating-input" value="<%- item.email%>" />
        </td>
        <td>
            <input  type="text" data-field="email_cc" class="floating-input" value="<%- item.email_cc %>" />
        </td>
        <td>
            <input  type="text" data-field="email_bcc" class="floating-input" value="<%- item.email_bcc %>"/>
        </td>
        <td class="text-center text-nowrap align-middle">
            <a href="#" class="ml-3" onclick="xoaDong(this)"><i class="fas fa-trash-alt" title="Xóa dòng dữ liệu"></i></a>
        </td>
    </tr>
    <% }) %>
    <% }%>

    <% if(data.length < 5){
    for(var i = 0; i < 5 - data.length;i++ ){
    %>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

