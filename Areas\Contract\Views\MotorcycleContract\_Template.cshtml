﻿<script type="text/html" id="danhDachGCN_template">
    <% if(gcn.length > 0){
    _.forEach(gcn, function(item,index) { %>
    <% if(item.bien_xe != null){ %>
    <tr data-search="<%- item.bien_xe.toLowerCase() %>" style="cursor:pointer" class="item-gcn" onclick="xemChiTietGCN('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>')" id="item-gcn-<%- item.ma_doi_tac %><%- item.so_id %><%- item.so_id_dt %>">
        <td style="font-weight:bold; padding: 8px 0 8px 0"><%- item.bien_xe %></td>
        <td style="float:right; padding: 8px 0 8px 0"><%- item.hieu_luc_gcn %></td>
    </tr>
    <% }else{ %>
    <tr data-search="<%- item.so_khung.toLowerCase() %>" style="cursor:pointer" class="item-gcn" onclick="xemChiTietGCN('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>')" id="item-gcn-<%- item.ma_doi_tac %><%- item.so_id %><%- item.so_id_dt %>">
        <td style="font-weight:bold"><%- item.so_khung %></td>
        <td style="float:right"><%- item.hieu_luc_gcn %></td>
    </tr>
    <% } %>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="2">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="danhSachNV_template">
    <% if(lhnv.length > 0){
    _.forEach(lhnv, function(item,index) { %>
    <tr row-val="<%- item.ma %>" row-vcx="<%- item.vcx %>">
        <td class="text-center"><%- index+1 %></td>
        <td><%- item.ten %></td>
        <td><input type="text" name="" maxlength="18" class="number floating-input" col-tien-bh="<%- item.ma %>" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.tien) %>" /></td>

        <% if(item.vcx == 'VCX'){ %>
        <%if(item.ktru=="K"){%>
        <td class="text-center"><input type="checkbox" col-khau-tru="<%- item.ma %>" name="ktru" onclick="onCheckKhauTru(this, '<%- item.ma %>')" /></td>
        <td><input type="text" name="" maxlength="18" col-mien-thuong="<%- item.ma %>" disabled="disabled" class="number floating-input" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.mien_thuong) %>" /></td>
        <% } else { %>
        <td class="text-center"><input type="checkbox" checked="checked" col-khau-tru="<%- item.ma %>" name="ktru" onclick="onCheckKhauTru(this, '<%- item.ma %>')" /></td>
        <td><input type="text" name="" maxlength="18" col-mien-thuong="<%- item.ma %>" class="number floating-input" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.mien_thuong) %>" /></td>
        <% } %>
        <% } else{ %>
        <td class="text-center"><input type="checkbox" col-khau-tru="<%- item.ma %>" name="ktru" style="display: none" /></td>
        <td><input type="text" name="" maxlength="18" col-mien-thuong="<%- item.ma %>" disabled="disabled" class="number floating-input" value="<%- item.mien_thuong %>" style="display: none" /></td>
        <% } %>
        <td><input type="text" name="" maxlength="18" col-phi-bh="<%- item.ma %>" class="number floating-input" onchange="hthiTongTien()" value="<%- ESUtil.formatMoney(item.phi) %>" /></td>
    </tr>
    <% })}%>
</script>

<script type="text/html" id="danhSachDKBS_template">
    <% if(dkbs.length > 0){
    _.forEach(dkbs, function(item,index) { %>
    <tr row-val="<%- item.ma %>">
        <td class="text-center"><%- index+1 %></td>
        <td class="text-center"><%- item.ma %></td>
        <td><%- item.ten %></td>
        <%if(!item.chon){
        %>
        <td class="text-center"><input type="checkbox" col-dkbs="<%- item.ma %>" /></td>
        <%
        }
        else
        {
        %>
        <td class="text-center"><input type="checkbox" checked="checked" col-dkbs="<%- item.ma %>" /></td>
        <%
        }
        %>
        <td></td>
    </tr>
    <% })} %>
</script>

<script type="text/html" id="SDBS_template">
    <% var stt = 1 %>
    <% if(dk.length > 0){
    _.forEach(dk, function(item,index) { %>
    <tr>
        <td class="text-center" style=" text-transform: uppercase;">
            <%- item.so_hd %>
        </td>
        <td class="text-center">
            <%- item.kieu_hd_ten %>
        </td>
        <td class="text-center">
            <%- item.ngay_cap_text %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="4">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="DongTai_template">
    <% var stt = 1 %>
    <% if(dk.length > 0){
    _.forEach(dk, function(item,index) { %>
    <tr onclick="Xem_chi_tiet_dong_tai('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.so_id_dt %>', '<%- item.don_vi_dong_tai %>', '<%- item.loai_dong %>')">
        <td class="text-center"><%- stt %></td>
        <% stt++ %>
        <td class="text-center"><b style="font-weight: bold"><%- item.ten_don_vi_dong_tai_hthi %></b></td>
        <td class="text-center" style=" text-transform: uppercase;">
            <%- item.kieu %>
        </td>
        <td class="text-center">
            <% if(item.bien_xe_dt != null){ %>
            <%- item.bien_xe_dt %>
            <% }else{ %>
            <%- item.so_khung_dt %>
            <% } %>

        </td>
        <td class="text-center">
            <%- item.ten_lhnv %>
        </td>
        <td class="text-center">
            <%- item.tl_dong %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_cd %>%
        </td>
        <td class="text-center">
            <%- item.tl_tai_tt %>%
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Không có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="lstImage_template">
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <div class="pt-2" id="nhom_anh_<%- index %>">
        <p class="m-0 font-weight-bold"><a href="#" onclick="onToggleImg('<%- index %>')"><%- item.nhom %></a></p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTiet(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img<%- image.bt %>" class="nhom_anh_ton_that_<%- index %>" value="<%- image.bt %>" name="ds_anh_xe">
            <p class="fileNameImage"><%- image.ten_file %></p>
            <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>"  data-cnhanh="<%- image.ma_chi_nhanh %>"  src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>"  data-cnhanh="<%- image.ma_chi_nhanh %>"  src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>"  data-cnhanh="<%- image.ma_chi_nhanh %>"  src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>"  data-cnhanh="<%- image.ma_chi_nhanh %>"  src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% })} %>
</script>
