﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình ngày nghỉ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <!-- Row -->
    <div class="row">
        <!-- Column -->
        <div class="col-lg-12 col-md-12 pb-15">
            <div class="card">
                <div class="card-body" style="padding-top:5px;">
                    <form name="frmTimKiem" id="frmTimKiem" method="post">
                        <div class="row">
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="ngay_d">Ngày tìm kiếm</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" class="form-control datepicker" name="ngay_d" display-format="date" value-format="number" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="ngay_c">&nbsp;</label>
                                    <div class="input-group">
                                        <input type="text" autocomplete="off" class="form-control datepicker" display-format="date" value-format="number" name="ngay_c" placeholder="mm/dd/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group" style="float:left">
                                    <label for="">&nbsp;</label>
                                    <div class="input-group">
                                        <button type="button" id="btnTimKiem" class="btn btn-primary btn-sm wd-80 mr-2">
                                            <i class="fa fa-search"></i>
                                        </button>
                                        <button type="button" id="btnThem" class="btn btn-primary btn-sm wd-80">
                                            <i class="fa fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div id="gridViewNgayNghi" class="table-app"></div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalNgayNghi" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin ngày nghỉ <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThongTinNgayNghi" name="frmThongTinNgayNghi" novalidate="novalidate" method="post">
                    <input type="hidden" name="bt" />
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Ngày bắt đầu</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" display-format="date" value-format="number" required name="tu_ngay" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="_required">Ngày kết thúc</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" display-format="date" value-format="number" required name="den_ngay" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="_required">Tên</label>
                                <input class="form-control" name="ten" required autocomplete="off" placeholder="Tên" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="">Ghi chú</label>
                                <textarea class="form-control" name="ghi_chu" rows="3" placeholder="Ghi chú"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer d-block">
                <button type="button" class="btn btn-outline-primary btn-sm wd-80 ml-2" id="btnXoaNgayNghi">
                    <i class="fa fa-trash mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 mg-t-22 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80 ml-2 float-right" id="btnLuuNgayNghi">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

@section scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ConfigureHolidaysService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ConfigureHolidays.js" asp-append-version="true"></script>
}