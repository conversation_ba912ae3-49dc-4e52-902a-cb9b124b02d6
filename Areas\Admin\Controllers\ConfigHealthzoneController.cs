﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class ConfigHealthzoneController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// Phân công xử lý theo ca con người
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_PHAN_CONG_XLY_NH, json);
            return Ok(data);
        }

        /// <summary>
        /// Tìm kiếm phân trang phân công xưr lý công việc theo ca
        /// </summary>
        /// <returns></returns>
        [AjaxOnly]
        public async Task<IActionResult> getPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_PHAN_CONG_XLY_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemCT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_NG_PHAN_CONG_XLY_LKE_CT, json);
            return Ok(data);
        }
    }
}