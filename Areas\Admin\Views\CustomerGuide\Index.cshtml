﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình báo cáo giám định";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON><PERSON><PERSON> biện pháp hạn chế tổn thất & hướng dẫn khách hàng</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active"><PERSON><PERSON><PERSON> bi<PERSON>n pháp hạn chế tổn thất & hướng dẫn khách hàng</li>
        </ol>
    </div>
</div>

<div class="container-fluid" >
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label for="nv">Ngiệp vụ</label>
                                <select class="select2 form-control custom-select" name="nv" style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Nhóm nguyên nhân</label>
                                <select class="select2 form-control custom-select" name="nhom_nguyen_nhan" style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Công việc</label>
                                <select class="select2 form-control custom-select" name="loai" style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập mã/tên" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapThongTinCode">
                                <i class="fa fa-plus"></i>
                            </button>
                            @* <button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcelCateCommon">
                                <i class="fas fa-upload"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                <i class="fas fa-download"></i>
                            </button> *@
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top: 3px;">
                    <div class="col-md-12">
                        <div class="table-responsive" style="margin-top: 3px">
                            <div id="gridViewCode" class="table-app" style="height: 65vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapCode" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmSaveCode" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Các biện pháp hạn chế tổn thất & hướng dẫn khách hàng<span id="modal-user-log" style="font-size: 14px; margin-left: 10px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="pm" value="bt" />
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required name="nv" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Nhóm nguyên nhân</label>
                                <select class="select2 form-control custom-select" name="nhom_nguyen_nhan" required style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã</label>
                                <input type="text" maxlength="250" placeholder="Mã" autocomplete="off" name="ma" required class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-8">
                            <div class="form-group">
                                <label class="_required">Tên</label>
                                <input type="text" maxlength="250" placeholder="Tên công việc hướng dẫn khách hàng và biện pháp hạn chế" autocomplete="off" name="ten" required class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Công việc</label>
                                <select class="select2 form-control custom-select" required name="loai" style="width: 100%; height: 36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Số thứ tự</label>
                                <input type="text" min="0" maxlength="5" autocomplete="off" placeholder="Thứ tự hiển thị" name="stt" class="form-control decimal">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display: block; padding: 10px 5px;">
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80 d-none" id="btnDeleteCode"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnSaveCode"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />

@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CustomerGuideService.js" asp-append-version="true"></script>
    <script src="~/js/app/OtherClaim/services/PropertyClaimService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/CustomerGuide.js" asp-append-version="true"></script>
 }