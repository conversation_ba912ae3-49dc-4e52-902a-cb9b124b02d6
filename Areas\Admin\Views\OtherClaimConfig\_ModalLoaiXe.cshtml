﻿<style>
    .text {
        color: red;
    }
</style>
<div id="modalLoaiXe" class="modal-drag" style="width:700px; z-index:9999999; margin-top: -400px !important; margin-left: 385px !important;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn thông tin loại xe</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_LoaiXe" type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control item-loaixe">
                <input type="hidden" id="modalLoaiXeElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLoaiXeDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonLoaiXe">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalLoaiXeDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox loaixe" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="loai_xe_<%- item.ma %>" data-field="loai_xe" value="<%- item.ma %>" class="custom-control-input item-loaixe modalLoaiXeItem">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_xe_<%- item.ma %>"><%- item.ten %> </label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
