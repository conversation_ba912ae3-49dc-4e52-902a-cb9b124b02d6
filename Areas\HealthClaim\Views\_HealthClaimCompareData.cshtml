﻿<style>
    .img-container-ocr {
        width: 100% !important;
        left: 0px !important;
    }

    #dsHinhAnhHoSoOCR img:hover {
        border: 2px solid var(--escs-main-theme-color);
    }

    #dsHinhAnhHangMucGiayToOCR img:hover {
        border: 2px solid var(--escs-main-theme-color);
    }

    #modalHealthClaimCompareData th {
        padding: 0.4rem !important;
    }

    #modalHealthClaimCompareData td {
        padding: 0.4rem !important;
    }

    #tblDanhSachHinhAnhHoSoGiayToOCR input {
        color: white !important;
        position: absolute !important;
        z-index: 9;
        opacity: 1;
        width: 15px;
        height: 15px;
        margin-left: 3px !important;
    }

    #tblDanhSachThongTinHoSoGiayToOCR input {
        color: white !important;
        position: absolute !important;
        z-index: 9;
        opacity: 1;
        width: 15px;
        height: 15px;
        margin-left: 3px !important;
    }

    .table-so-sanh tbody tr td {
        padding: 0.4rem;
    }
</style>

<div class="modal fade bs-example-modal-lg" id="modalHealthClaimCompareData" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 100%; margin: 10px auto;">
        <div class="modal-content" id="navHealthClaimCompareData">
            <div class="modal-header p-2">
                <div class="row w-100 m-0 p-0">
                    <div class="col-12 px-1">
                        <div class="d-flex justify-content-between">
                            <ul class="nav nav-pills font-weight-bold p-0" role="tablist">
                                <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                    <a href="#" id="tabXemThongTinOCR" class="nav-link active" aria-controls="tabThongTinOCR" role="tab" data-toggle="tab" aria-expanded="false" aria-selected="false" style="width: 238px;" onclick="showThongTinOCR('tabThongTinOCR')">
                                        <i class="far fa-book mr-2 mr-2"></i>Thông tin OCR
                                    </a>
                                </li>
                                <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                    <a href="#" id="tabXemSoSanhThongTinOCR" class="nav-link" aria-controls="tabSoSanhThongTinOCR" role="tab" data-toggle="tab" aria-expanded="false" aria-selected="false" style="width: 223px;" onclick="showThongTinOCR('tabSoSanhThongTinOCR')">
                                        <i class="fas fa-camera-retro mr-2"></i>So sánh thông tin
                                    </a>
                                </li>
                            </ul>
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-body p-2" style="height: 83.5vh">
                <div class="row w-100 m-0 p-0">
                    <div class="col-12 px-1">
                        <div class="tab-content">
                            <div class="tab-pane active px-0 py-1" id="tabThongTinOCR" role="tabpanel">
                                <div class="row" style="margin:0; padding:0">
                                    <div class="col-8 p-1">
                                        <div class="card mb-0 modal-main-content">
                                            <div class="card-body px-0" style="padding-top:0 !important">
                                                <div class="rounded px-2" style="height: 78.5vh; overflow-x: hidden;">
                                                    <div class="card mb-0">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <div id="headingGYCBH" class="text-left p-1 card-title-bg font-weight-bold accordion_ocr" style="cursor:pointer" data-toggle="collapse" data-target="#collapseGYCBH">
                                                                    <span class="title">Giấy yêu cầu bảo hiểm</span>
                                                                </div>
                                                                <form action="/" method="post" name="frmOcrGiayYCBH" novalidate="novalidate">
                                                                    <div id="collapseGYCBH" class="table-responsive collapse show" aria-labelledby="headingGYCBH" data-parent="#tabThongTinOCR">
                                                                        <table id="tableDsHoSoGiayTo" class="table table-bordered fixed-header">
                                                                            <thead class="font-weight-bold text-center uppercase">
                                                                                <tr class="text-center font-weight-bold">
                                                                                    <th style="width: 3%;">
                                                                                        <div class="custom-control custom-checkbox ml-2">
                                                                                            <input type="checkbox" onchange="onChonTatCaGiayYCBH(this)" id="ocr_chon_tat_ca_giay_ycbh" class="custom-control-input">
                                                                                            <label class="custom-control-label" for="ocr_chon_tat_ca_giay_ycbh">&nbsp;</label>
                                                                                        </div>
                                                                                    </th>
                                                                                    <th style="width: 20%;">Tiêu chí</th>
                                                                                    <th style="width: 38%;">Thông tin OCR</th>
                                                                                    <th style="width: 38%;">Thông tin điều chỉnh</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody id="modalDataOCRGiayYCBH">
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </form>
                                                            </div>

                                                            <div class="col-12 mt-3">
                                                                <div id="headingGRV" class="text-left p-1 card-title-bg font-weight-bold accordion" style="cursor:pointer" data-toggle="collapse" data-target="#collapseGRV">
                                                                    <span class="title">Giấy ra viện</span>
                                                                </div>
                                                                <form action="/" method="post" name="frmOcrGiayRaVien" novalidate="novalidate">
                                                                    <div id="collapseGRV" class="table-responsive collapse" aria-labelledby="headingGRV" data-parent="#tabThongTinOCR">
                                                                        <table id="tableDsHoSoGiayTo" class="table table-bordered fixed-header">
                                                                            <thead class="font-weight-bold text-center uppercase">
                                                                                <tr class="text-center font-weight-bold">
                                                                                    <th style="width: 3%;">
                                                                                        <div class="custom-control custom-checkbox ml-2">
                                                                                            <input type="checkbox" onchange="onChonTatCaGiayRaVien(this)" id="ocr_chon_tat_ca_giay_ra_vien" class="custom-control-input">
                                                                                            <label class="custom-control-label" for="ocr_chon_tat_ca_giay_ra_vien">&nbsp;</label>
                                                                                        </div>
                                                                                    </th>
                                                                                    <th style="width: 20%;">Tiêu chí</th>
                                                                                    <th style="width: 38%;">Thông tin OCR</th>
                                                                                    <th style="width: 38%;">Thông tin điều chỉnh</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody id="modalDataOCRGiayRaVien">
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </form>
                                                            </div>

                                                            <div class="col-12 mt-3">
                                                                <div id="headingBangKe" class="text-left p-1 card-title-bg font-weight-bold accordion" style="cursor:pointer" data-toggle="collapse" data-target="#collapseBangKe">
                                                                    <span class="title">Bảng kê chi tiết</span>
                                                                </div>
                                                                <form action="/" method="post" name="frmOcrBangKe" novalidate="novalidate">
                                                                    <div id="collapseBangKe" class="table-responsive collapse" aria-labelledby="headingBangKe" data-parent="#tabThongTinOCR">
                                                                        <table id="tableDsHoSoGiayTo" class="table table-bordered fixed-header">
                                                                            <thead class="font-weight-bold text-center uppercase">
                                                                                <tr class="text-center font-weight-bold">
                                                                                    <th style="width: 3%;">
                                                                                        <div class="custom-control custom-checkbox ml-2">
                                                                                            <input type="checkbox" onchange="onChonTatCaBangKe(this)" id="ocr_chon_tat_ca_bang_ke" class="custom-control-input">
                                                                                            <label class="custom-control-label" for="ocr_chon_tat_ca_bang_ke">&nbsp;</label>
                                                                                        </div>
                                                                                    </th>
                                                                                    <th style="width: 20%;">Tiêu chí</th>
                                                                                    <th style="width: 38%;">Thông tin OCR</th>
                                                                                    <th style="width: 38%;">Thông tin điều chỉnh</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody id="modalDataOCRBangKe">
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </form>
                                                            </div>

                                                            <div class="col-12 mt-3">
                                                                <div id="headingPhieuKham" class="text-left p-1 card-title-bg font-weight-bold accordion" style="cursor:pointer" data-toggle="collapse" data-target="#collapsePhieuKham">
                                                                    <span class="title">Phiếu khám</span>
                                                                </div>
                                                                <form action="/" method="post" name="frmOcrPhieuKham" novalidate="novalidate">
                                                                    <div id="collapsePhieuKham" class="table-responsive collapse" aria-labelledby="headingPhieuKham" data-parent="#tabThongTinOCR">
                                                                        <table id="tableDsHoSoGiayTo" class="table table-bordered fixed-header">
                                                                            <thead class="font-weight-bold text-center uppercase">
                                                                                <tr class="text-center font-weight-bold">
                                                                                    <th style="width: 3%;">
                                                                                        <div class="custom-control custom-checkbox ml-2">
                                                                                            <input type="checkbox" onchange="onChonTatCaPhieuKham(this)" id="ocr_chon_tat_ca_phieu_kham" class="custom-control-input">
                                                                                            <label class="custom-control-label" for="ocr_chon_tat_ca_phieu_kham">&nbsp;</label>
                                                                                        </div>
                                                                                    </th>
                                                                                    <th style="width: 20%;">Tiêu chí</th>
                                                                                    <th style="width: 38%;">Thông tin OCR</th>
                                                                                    <th style="width: 38%;">Thông tin điều chỉnh</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody id="modalDataOCRPhieuKham">
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 p-1">
                                        <div class="card m-0">
                                            <div class="card-body p-0">
                                                <div class="border rounded">
                                                    <div class="tab-header">
                                                        <div class="text-center p-1 card-title-bg font-weight-bold">
                                                            <span class="title-ocr">Danh sách hình ảnh</span>
                                                        </div>
                                                    </div>
                                                    <div class="tab-content" style="border: unset;">
                                                        <div id="img-container-ocr" style="height:56vh"></div>
                                                        <div class="p-0" role="tabpanel" id="tabThongTinHinhAnhOCR" style="overflow:hidden">
                                                            <div class="card-body p-0">
                                                                <div style="width:100%; vertical-align:middle;height:150px" class="scrollable">
                                                                    <div style="width:100%" id="dsAnhTonThatOCR" class="list-pictures-ocr">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane px-0 py-1" id="tabSoSanhThongTinOCR" role="tabpanel">
                                <div class="row" style="margin:0; padding:0">
                                    <div class="col-12 p-1">
                                        <div class="card mb-0 modal-main-content">
                                            <div class="card-body px-0" style="padding-top:0 !important">
                                                <div class="rounded px-2" style="height: 78.5vh; overflow-x: hidden;">
                                                    <div class="card mb-0">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <div class="text-left p-1 card-title-bg font-weight-bold">
                                                                    <span class="title-ocr">Thông tin liên hệ</span>
                                                                </div>
                                                                <div id="tblDoiChieuThongTinOCRThongTinLienHe"></div>
                                                            </div>
                                                            <div class="col-12">
                                                                <div class="text-left p-1 card-title-bg font-weight-bold">
                                                                    <span class="title-ocr">Thông tin hợp đồng</span>
                                                                </div>
                                                                <div id="tblDoiChieuThongTinOCRThongTinHopDong"></div>
                                                            </div>
                                                            <div class="col-12">
                                                                <div class="text-left p-1 card-title-bg font-weight-bold">
                                                                    <span class="title-ocr">Thông tin lần khám</span>
                                                                </div>
                                                                <div id="tblDoiChieuThongTinOCRThongTinLanKham"></div>
                                                            </div>
                                                            <div class="col-12">
                                                                <div class="text-left p-1 card-title-bg font-weight-bold">
                                                                    <span class="title-ocr">Thông tin thanh toán thụ hưởng</span>
                                                                </div>
                                                                <div id="tblDoiChieuThongTinOCRThongTinThanhToanThuHuong"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-1" style="display: block;">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" id="btnApDungOCR">
                    <i class="fa fa-check mr-2"></i>Áp dụng
                </button>
                <button type="button" class="btn btn-primary btn-sm mg-t-22 float-right" id="btnLuuThongTinOCR">
                    <i class="fa fa-check mr-2"></i>Lưu thông tin hồ sơ
                </button>
            </div>
        </div>
    </div>
</div>

@*Modal chọn loại chi phí*@
<div id="modalChonLoaiChiPhiOCR" class="modal-drag" style="width:350px; z-index:9999999;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn loại chi phí</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputTimKiemLoaiChiPhi" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonLoaiChiPhiElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:300px;" id="modalChonLoaiChiPhiOCRDanhSach">

            </div>
        </div>
    </div>
</div>
@*Template chọn loại chi phí*@
<script type="text/html" id="modalChonLoaiChiPhiDanhSachOCRTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox chi_phi_ocr" id="chi_phi_ocr_<%- item.ma_day_du %>" data-text="<%- item.ten_day_du.toLowerCase() %>">
        <input type="checkbox" id="loai_chi_phi_ocr_<%- item.ma_day_du %>" data-loai="<%- item.loai%>" value="<%- item.ma %>" onchange="onChonLoaiChiPhiOCR(this)" class="custom-control-input single-checked modalChonLoaiChiPhiOCRItem">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_chi_phi_ocr_<%- item.ma_day_du %>"><%- item.ten_day_du %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

@*Danh sách tài liệu hồ sơ hình ảnh*@
<script type="text/html" id="tblDanhSachHinhAnhHoSoGiayToOCR_template">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <div style="display: inline-block;width:100%;">
        <p style="font-weight: bold;" class="p-2 mb-0">
            <a href="#" onclick="onToggleImageOCR('<%- index %>')"><%- ESUtil.rutGonText(85, item.nhom) %></a>
        </p>
        <ul class="docs-pictures clearfix">
            <% _.forEach(item.children, function(image,index1) { %>
            <li class="p-1" style="width: 108px !important;">
                <input type="checkbox" onclick="onChonAnhOCR('<%- image.so_id%>','<%- image.ma_file%>','<%- image.bt%>')" id="img_<%- image.so_id %>_<%- image.ma_file %>_<%- image.bt %>" class="nhom_anh_ocr_<%- index %> mt-1 images-ocr" value="<%- image.bt%>" data-ma-file="<%- image.ma_file %>" name="ocr_image">
                <% if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
                <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
                <% } else if(_.includes([".xml"], image.extension)){%>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
                <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
                <img onclick="openModalXemHinhAnhHoaDonCTiet('<%- image.ten_file %>','<%- image.bt %>','<%- image.extension %>')" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
                <% } %>
            </li>
            <% }) %>
        </ul>
    </div>
    <% })} %>
</script>
@*Thông tin chung OCR*@
<script type="text/html" id="modalCompareDataOCRGiayToTemplate">
    <% if(data!==undefined && data!==null && data.length > 0){%>
    <%_.forEach(data, function(item,index) {%>
    <tr class="row_item">
        <%if(item.nd_so_sanh != false){%>
        <td><%- item.noi_dung %></td>
        <%if(item.loai == 'BENH_VIEN'){%>
        <td class="text-center">
            <input type="hidden" class="combobox" data-field="loai" data-val="<%- item.loai%>" />
            <%if(item.benh_vien != null && item.benh_vien != "" && item.benh_vien != undefined){%>
            <input type="text" autocomplete="off" class="floating-input combobox text-center text-primary cursor-pointer" onclick="chonBenhVienOCR(this)" data-field="gia_tri" data-val="<%- item.benh_vien%>" value="<%- item.nd_ocr %>">
            <%}else{%>
            <input type="text" autocomplete="off" class="floating-input combobox text-center text-danger cursor-pointer" onclick="chonBenhVienOCR(this)" data-field="gia_tri" data-val="<%- item.benh_vien%>" value="<%- item.nd_ocr %>">
            <%}%>
        </td>
        <%}else{%>
        <td class="text-center">
            <input type="hidden" class="combobox" data-field="loai" data-val="<%- item.loai%>" />
            <input type="text" autocomplete="off" class="floating-input text-center combobox cursor-pointer" onchange="suaNoiDungOCR(this)" data-field="gia_tri" data-val="<%- item.nd_ocr %>" value="<%- item.nd_ocr %>" />
        </td>
        <%}%>
        <td class="text-center"><% if(item.so_sanh) { %> <i class="fas fa-check text-success"></i> <%} else { %><i class="fas fa-times text-danger"></i> <%}%></td>
        <td class="text-center"><%- item.nd_goc %></td>
        <td class="text-right">
            <div class="custom-control custom-checkbox ml-2">
                <input type="checkbox" onchange="onChonDuLieuOCR(this, '<%- item.loai %>')" id="ocr_<%- index + 1%>" class="custom-control-input ocr_item">
                <label class="custom-control-label" for="ocr_<%- index + 1%>">&nbsp;</label>
            </div>
        </td>
        <%}%>
    </tr>
    <% })}else{%>
    <tr class="text-center">
        <td colspan="5">Chưa có dữ liệu</td>
    </tr>
    <%}%>
</script>
<script type="text/html" id="modalCompareDataOCRBangKeChiTietTemplate">
    <% if(data!==undefined && data!==null && data.length > 0){%>
    <%_.forEach(data, function(item,index) {%>
    <tr class="row_item">
        <td class="text-left">
            <input class="floating-input form-control" type="text" data-field="ten_dich_vu" value="<%- item.ten_dich_vu %>" />
        </td>
        <td class="text-center"><%- ESUtil.formatMoney(item.don_gia) %></td>
        <td class="text-center"><%- item.so_luong %></td>
        <td class="text-center"><%- ESUtil.formatMoney(item.thanh_tien) %></td>
    </tr>
    <% })}else{%>
    <tr class="text-center">
        <td colspan="4">Chưa có dữ liệu</td>
    </tr>
    <%}%>
</script>

@*Thông tin OCR hóa đơn*@
<script type="text/html" id="modalCompareDataOCRHoaDonChiTietTemplate">
    <% if(data!==undefined && data!==null && data.length > 0){%>
    <%_.forEach(data, function(item,index) {%>
    <tr class="row_item">
        <td class="text-left">
            <input class="floating-input form-control" type="text" data-field="ten_dich_vu" value="<%- item.ten_dich_vu %>" />
            <input class="floating-input form-control" type="hidden" data-field="dvi_tinh" value="<%- item.dvi_tinh %>" />
            <input class="floating-input form-control number" type="hidden" data-field="so_luong" value="<%- item.so_luong %>" />
            <input class="floating-input form-control number" type="hidden" data-field="don_gia" value="<%- item.don_gia %>" />
            <input class="floating-input form-control number" type="hidden" data-field="thanh_tien" value="<%- item.thanh_tien %>" />
            <input class="floating-input form-control" type="hidden" data-field="ten_chi_phi" value="<%- item.ten_chi_phi %>" />
            <input class="floating-input form-control" type="hidden" data-field="loai_chi_phi" value="<%- item.loai_chi_phi %>" />
        </td>
        <td class="text-center"><%- item.dvi_tinh_ten %></td>
        <td class="text-center">
            <input type="text" autocomplete="off" class="floating-input text-center number combobox cursor-pointer" onchange="suaNoiDungOCR(this)" data-field="so_luong" data-val="<%- item.so_luong %>" value="<%- item.so_luong %>" />
        </td>
        <td class="text-center"><%- ESUtil.formatMoney(item.don_gia) %></td>
        <td class="text-center">
            <% if(item.thanh_tien == 0){%>
                <input type="text" autocomplete="off" class="floating-input text-center number cursor-pointer" onchange="suaNoiDungOCR(this)" data-field="don_gia" data-val="<%- item.don_gia %>" value="<%- ESUtil.formatMoney(item.don_gia) %>" />
            <%}else{%>
                <input type="text" autocomplete="off" class="floating-input text-center number cursor-pointer" onchange="suaNoiDungOCR(this)" data-field="thanh_tien" data-val="<%- item.thanh_tien %>" value="<%- ESUtil.formatMoney(item.thanh_tien) %>" />
            <%}%>
        </td>
        <td class="text-center">
            <%if(item.ma_chi_phi == "" || item.ma_chi_phi == null){%>
            <a class="cursor-pointer" onclick="capNhatLoaiChiPhi(this)" href="#" data-field="ma_chi_phi" data-val="<%- item.ma_chi_phi%>" title="<%- item.ten_chi_phi %>">Chọn loại chi phí</a>
            <%}else{%>
            <a class="cursor-pointer" onclick="capNhatLoaiChiPhi(this)" href="#" data-field="ma_chi_phi" data-val="<%- item.ma_chi_phi%>" title="<%- item.ten_chi_phi %>"><%- ESUtil.rutGonText(50, item.ten_chi_phi) %></a>
            <%}%>
        </td>
    </tr>
    <% })}else{%>
    <tr class="text-center">
        <td colspan="6">Chưa có dữ liệu</td>
    </tr>
    <%}%>
</script>


