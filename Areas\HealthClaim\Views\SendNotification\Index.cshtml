﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Gửi thông báo ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<input type="hidden" id="notify_info" value="@TempData[ESCS.COMMON.Contants.ESCSConstants.NOTIFY_INFO]" />
@*<input type="hidden" id="notify_info" value="@ViewBag.ho_so" />*@
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12 col-md-12 pd-3">
            <div class="card">
                <div class="card-body" style="padding-top: 5px;">
                    <form name="FrmSearch" method="post">
                        <div class="row row-cols-6">
                            <div class="col col-1">
                                <div class="form-group">
                                    <label for="ngay_d">Ngày tìm kiếm</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" name="ngay_d" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label for="ngay_c">&nbsp;</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control datepicker" name="ngay_c" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                        <div class="input-group-append">
                                            <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 d-none">
                                <div class="form-group">
                                    <label>Đối tác</label>
                                    <select class="select2 form-control custom-select" name="ma_doi_tac" style="width:100%">
                                    </select>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="cap_do">Cấp độ</label>
                                    <select class="select2 form-control custom-select" name="cap_do" style="width:100%">
                                        <option value="" selected>Chọn cấp độ</option>
                                        <option value="1">Cấp 1</option>
                                        <option value="2">Cấp 2</option>
                                        <option value="3">Cấp 3</option>
                                        <option value="4">Cấp 4</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="csyt">Cơ sở ý tế</label>
                                    <input type="text" class="form-control" autocomplete="off" name="csyt" required placeholder="Chọn csyt " onclick="chonBV(this)">
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="trang_thai_hs_goc">Trạng thái thông báo</label>
                                    <select class="select2 form-control custom-select" name="trang_thai_hs_goc" style="width:100%">
                                        <option value="" selected>Chọn trạng thái</option>
                                        <option value="C">Chưa gửi</option>
                                        <option value="D">Đã gửi</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-2" style="margin-top:23px">
                                <button type="button" class="btn btn-primary btn-sm wd-65" id="btnTimKiemTB" title="Tìm kiếm thông báo">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-65" id="btnThemMoiTB" title="Thêm mới thông bá">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                    <div class="row" style="margin-top: 3px;">
                        <div class="col-12">
                            <div id="gridViewTBQT" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<partial name="_Modal.cshtml" />
@section scripts{
    <script src="~/js/common/ModalService.js" asp-append-version="true"></script>
    <script src="~/js/app/healthcare/services/sendnotificationservice.js" asp-append-version="true"></script>
    <script src="~/js/app/healthcare/sendnotification.js" asp-append-version="true"></script>
}