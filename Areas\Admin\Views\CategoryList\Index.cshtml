﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Quản lý người dùng";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<style>
    .card_active {
        background-color: #9abcea40;
    }

    .cau_hinh_xe:hover {
        background-color: #eef5f9;
    }

    .bg-color {
        background-color: #ececec;
        border: 1px solid #607d8b;
        /*#2d88ff*/
    }

    .rowSelected {
        background-color: #f4f4f4;
    }

        .rowSelected input {
            background-color: #f4f4f4 !important;
        }
</style>

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Danh sách cấu hình</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Danh sách cấu hình</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <div class="row mt-3">
                    <div class="col-sm-4">
                        <div class="cau_hinh_nguoi card mb-3 cursor-pointer bg-color" id="ch_dm_dai_ly" onclick="themDL()">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Quản lý danh mục Đại lý</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="ch_sla_nguoi card mb-3 cursor-pointer bg-color" id="ch_dm_chuong_trinh_bh" onclick="themDM('HD_CHUONG_TRINH_BH');">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-shield-virus"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Quản lý danh mục Chương trình bảo hiểm</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="ch_duyet_tu_dong_nguoi card mb-3 cursor-pointer bg-color" id="ch_dm_pt_khai_thac" onclick="themPTKT();">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fab fa-usps"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Quản lý danh mục Phương thức khai thác</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="ch_duyet_tu_dong_nguoi card mb-3 cursor-pointer bg-color" id="ch_dm_dvi_boi_thuong" onclick="themDM('HD_DON_VI_TPA');">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="far fa-file-alt"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Quản lý danh mục Đơn vị bồi thường</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="ch_duyet_tu_dong_nguoi card mb-3 cursor-pointer bg-color" id="ch_dm_loai_sdbs" onclick="themDM('HD_LOAI_SDBS');">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Quản lý danh mục Loại SDBS</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="ch_duyet_tu_dong_nguoi card mb-3 cursor-pointer bg-color" id="ch_dm_nv" onclick="themNV();">
                            <div class="card-body p-3">
                                <div class="d-flex flex-row">
                                    <div class="round round-lg text-white d-inline-block text-center rounded-circle bg-info">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="ml-2 align-self-center wd-60p">
                                        <h6>Quản lý danh sách nhân viên</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modalThemDaiLy" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:60%">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-header py-1">
                <h4 class="modal-title">Danh mục đại lý</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div id="modalTimDL">
                    <form id="frmTimDL" name="frmTimDL" novalidate="novalidate" data-select2-id="frmTimDL" method="post">
                        <div class="row">
                            <div class="col-5">
                                <div class="form-group">
                                    <select class="form-control select2" name="ma_doi_tac" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-4">
                                <input type="text" name="tim" autocomplete="off" class="form-control " placeholder="Mhập nội dung tìm kiếm">
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-primary btn-sm wd-30p" id="btnTimDL" tabindex="135">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-30p" id="btnThemDL" tabindex="136">
                                    <i class="fa fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-30p" onclick="EXCEL.open('xlsDL')" tabindex="136">
                                    <i class="fa fa-upload"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <div id="gridViewDL">
                            </div>
                        </div>
                    </form>
                </div>
                <div id="modalThemDL">
                    <form id="frmThemDL" name="frmThemDL" novalidate="novalidate" data-select2-id="frmThemDL" method="post">
                        <div class="row">
                            <input type="hidden" name="ma_dlc" autocomplete="off" class="form-control">
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Đối tác:</label>
                                    <select class="form-control select2" required="" name="ma_doi_tac" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Loại đại lý:</label>
                                    <select class="form-control select2" required="" name="loai" style="width:100%">
                                        <option value="">Chọn loại đại lý</option>
                                        <option value="CN">Cá nhân</option>
                                        <option value="TC">Tổ chức</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-4 d-none">
                                <div class="form-group">
                                    <label class=" ">Nghiệp vụ:</label>
                                    <select class="form-control select2" name="nv" style="width:100%">
                                        <option value="">Chọn ngiệp vụ</option>
                                        <option value="NGUOI">Con người</option>
                                        <option value="XE">Xe cơ giới</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required ">Phương thức khai thác:</label>
                                    <input type="text" name="pt_kt" style="cursor:pointer;" onclick="chonPhuongThuc(this);" required class="form-control" autocomplete="off" data-val="" placeholder="Click chọn phương thức khai thác">
                                </div>
                            </div>

                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Mã</label>
                                    <input type="text" name="ma" autocomplete="off" maxlength="18" required class="form-control " placeholder="Mã đại lý">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="">Tên cấp trên</label>
                                    <input type="text" name="ma_ct" data-val="" style="cursor:pointer;background-color: #e9ecef;" class="form-control" autocomplete="off" placeholder="Click chọn mã cấp trên" id="btnTimKiemDaiLyService">

                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Trạng thái:</label>
                                    <select class="form-control select2" required="" name="trang_thai" style="width:100%">
                                        <option value="">Chọn trạng thái</option>
                                        <option value="1">Đang hoạt động</option>
                                        <option value="0">Ngừng hoạt động</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-4">
                                <div class="form-group">
                                    <label class="">SDT </label>
                                    <input type="text" name="sdt" autocomplete="off" maxlength="10" class="form-control " placeholder="Nhập sdt">
                                </div>
                            </div>
                            <div class="col-8">
                                <div class="form-group">
                                    <label class="_required">Tên </label>
                                    <input type="text" name="ten" autocomplete="off" maxlength="500" required class="form-control " placeholder="Tên đại lý">
                                </div>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 mr-auto" id="btnXoaDL">
                    <i class="fas fa-save mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuDL">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn-outline-primary btn-sm wd-85 mg-t-22" id="btnBackDL" onclick="$('#modalTimDL').show(), $('#modalThemDL').hide(), $('#btnLuuDL').hide(),$('#btnBackDL').hide(),$('#btnXoaDL').hide()">
                    <i class="fas fa-chevron-left"></i> Quay lại
                </button>
            </div>
        </div>
    </div>
</div>
<div id="modalThemDanhMuc" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:60%">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="title">Thêm danh mục</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div id="modalBDTim">
                    <form id="frmTimDM" name="frmTimDM" novalidate="novalidate" data-select2-id="frmTimDM" method="post">
                        <div class="row">
                            <input type="hidden" name="nhom" />
                            <div class="col-3">
                                <div class="form-group">
                                    <select class="form-control select2" name="ma_doi_tac" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <select class="form-control select2" name="nhom_ten" style="width:100%" disabled>
                                        <option value="HD_PHUONG_THUC_KHAI_THAC">Phương thức khai thác</option>
                                        <option value="HD_CHUONG_TRINH_BH">Chương trình BH</option>
                                        <option value="HD_DON_VI_TPA">Đơn vị bồi thường</option>
                                        <option value="HD_LOAI_SDBS">Loại SDBS</option>
                                        <option value="HD_LOAI_THUE">Loại thuế</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-3">
                                <input type="text" name="tim" autocomplete="off" class="form-control " placeholder="Nhập nội dung tìm kiếm">
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnDMTim" tabindex="135">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnThemDM" tabindex="136">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <div id="gridViewDM">
                            </div>
                        </div>
                    </form>
                </div>
                <div id="modalBDThem">
                    <form id="frmThemDM" name="frmThemDM" novalidate="novalidate" data-select2-id="frmThemDM" method="post">
                        <div class="row">
                            <input type="hidden" name="nhom" />
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Đối tác:</label>
                                    <select class="form-control select2" required="" name="ma_doi_tac" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-4 d-none">
                                <div class="form-group">
                                    <label class="">Nghiệp vụ:</label>
                                    <select class="form-control select2" name="nv" style="width:100%">
                                        <option value="">Chọn ngiệp vụ</option>
                                        <option value="NGUOI">Con người</option>
                                        <option value="XE">Xe cơ giới</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Nhóm</label>
                                    <select class="form-control select2" required="" name="nhom_ten" style="width:100%" disabled>
                                        <option value="HD_PHUONG_THUC_KHAI_THAC">Phương thức khai thác</option>
                                        <option value="HD_CHUONG_TRINH_BH">Chương trình BH</option>
                                        <option value="HD_DON_VI_TPA">Đơn vị bồi thường</option>
                                        <option value="HD_LOAI_SDBS">Loại SDBS</option>
                                        <option value="HD_LOAI_THUE">Loại thuế</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Mã</label>
                                    <input type="text" name="ma" autocomplete="off" maxlength="18" required class="form-control " placeholder="Mã danh mục">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Tên </label>
                                    <input type="text" name="ten" autocomplete="off" maxlength="50" required class="form-control " placeholder="Tên danh mục">
                                </div>
                            </div>
                            
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="">SDT </label>
                                    <input type="text" name="sdt" autocomplete="off" maxlength="10" class="form-control " placeholder="Nhập sdt">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Trạng thái:</label>
                                    <select class="form-control select2" required="" name="trang_thai" style="width:100%">
                                        <option value="">Chọn trạng thái</option>
                                        <option value="1">Đang hoạt động</option>
                                        <option value="0">Ngừng hoạt động</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 mr-auto" id="btnXoaDM">
                    <i class="fas fa-save mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuDM">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn-outline-primary btn-sm wd-85 mg-t-22" id="btnBack" onclick="$('#modalBDTim').show(), $('#modalBDThem').hide(), $('#btnLuuDM').hide(),$('#btnBack').hide(),$('#btnXoaDM').hide()">
                    <i class="fas fa-chevron-left"></i> Quay lại
                </button>
            </div>
        </div>
    </div>
</div>
<div id="modalThemPhuongThucKhaiThac" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:60%">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-header py-1">
                <h4 class="modal-title" >Danh mục phương thức khai thác</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div id="modalTimPTKT">
                    <form id="frmTimPTKT" name="frmTimPTKT" novalidate="novalidate"  method="post">
                        <div class="row">
                            <input type="hidden" name="nhom" />
                            <div class="col-3">
                                <div class="form-group">
                                    <select class="form-control select2" name="ma_doi_tac" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-3">
                                <input type="text" name="tim" autocomplete="off" class="form-control " placeholder="Nhập nội dung tìm kiếm">
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimPTKT" tabindex="135">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnThemPTKT" tabindex="136">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <div id="gridViewPTKT">
                            </div>
                        </div>
                    </form>
                </div>
                <div id="modalThemPTKT">
                    <form id="frmThemPTKT" name="frmThemPTKT" novalidate="novalidate" method="post">
                        <div class="row">
                            <input type="hidden" name="nhom" />
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="_required">Đối tác:</label>
                                    <select class="form-control select2" required="" name="ma_doi_tac" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-4 d-none">
                                <div class="form-group">
                                    <label class="">Nghiệp vụ:</label>
                                    <select class="form-control select2" name="nv" style="width:100%">
                                        <option value="">Chọn ngiệp vụ</option>
                                        <option value="NGUOI">Con người</option>
                                        <option value="XE">Xe cơ giới</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="">Áp dụng đại lý</label>
                                    <select class="form-control select2" name="ad_dai_ly" style="width:100%" >
                                        <option value="">Không áp dụng đại lý</option>
                                        <option value="C">Áp dụng đại lý</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Mã</label>
                                    <input type="text" name="ma" autocomplete="off" maxlength="18" required class="form-control " placeholder="Mã danh mục">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Tên </label>
                                    <input type="text" name="ten" autocomplete="off" maxlength="50" required class="form-control " placeholder="Tên danh mục">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Trạng thái:</label>
                                    <select class="form-control select2" required="" name="trang_thai" style="width:100%">
                                        <option value="">Chọn trạng thái</option>
                                        <option value="1">Đang hoạt động</option>
                                        <option value="0">Ngừng hoạt động</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 mr-auto" id="btnXoaPTKT">
                    <i class="fas fa-save mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuPTKT">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn-outline-primary btn-sm wd-85 mg-t-22" id="btnBackPTKT" onclick="">
                    <i class="fas fa-chevron-left"></i> Quay lại
                </button>
            </div>
        </div>
    </div>
</div>
<div id="modalQLNV" class="modal face" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:unset; width:70%">
        <div class="modal-content" data-select2-id="132">
            <div class="modal-header py-1">
                <h4 class="modal-title" id="title">Danh sách nhân viên</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body py-1">
                <div id="modalTimNV">
                    <form id="frmTimNV" name="frmTimNV" novalidate="novalidate" data-select2-id="frmTimNV" method="post">
                        <div class="row">
                            <div class="col-3">
                                <label class="">Đối tác</label>
                                <div class="form-group">
                                    <select class="form-control select2" name="ma_doi_tac"></select>
                                </div>
                            </div>
                            <div class="col-3">
                                <label class="">Phòng ban</label>
                                <div class="form-group">
                                    <select class="form-control select2" name="phong_ban" ></select>
                                </div>
                            </div>
                            <div class="col-4">
                                <label class="">Nhập thông tin tìm kiếm</label>
                                <div class="form-group">
                                    <input type="text" name="tim" autocomplete="off" class="form-control " placeholder="Mhập nội dung tìm kiếm">
                                </div>
                            </div>
                            <div class="col-2" style="padding-top: 27px;">
                                <button type="button" class="btn btn-primary btn-sm wd-30p" id="btnTimNV" tabindex="135">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-30p" id="btnThemNV" tabindex="136">
                                    <i class="fa fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-30p" onclick="EXCEL.open('xlsNV')" tabindex="136">
                                    <i class="fa fa-upload"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive mt-2">
                            <div id="gridViewNV">
                            </div>
                        </div>
                    </form>
                </div>
                <div id="modalThemNV">
                    <form id="frmThemNV" name="frmThemNV" novalidate="novalidate" data-select2-id="frmThemNV" method="post">
                        <div class="row">
                            <input type="hidden" name="ma_dlc" autocomplete="off" class="form-control">
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Đối tác:</label>
                                    <select class="form-control select2" required="" name="ma_doi_tac" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Chi nhánh:</label>
                                    <select class="form-control select2" required="" name="ma_chi_nhanh" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Phòng ban:</label>
                                    <select class="form-control select2" required="" name="phong_ban" style="width:100%"></select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Mã nhân viên</label>
                                    <input type="text" name="ma_nv" autocomplete="off" maxlength="18" required class="form-control " placeholder="Mã nhân viên">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required ">Tên nhân viên:</label>
                                    <input type="text" name="ten_nv" autocomplete="off" maxlength="18" required class="form-control " placeholder="Nhập tên nhân viên">
                                </div>
                            </div>

                           @* <div class="col-4">
                                <div class="form-group">
                                    <label class="">Tài khoản ESCS</label>
                                    <input type="text" name="tk_escs" autocomplete="off" maxlength="18" required class="form-control " placeholder="Mã nhân viên">
                                </div>
                            </div>*@
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="">Số CCCD</label>
                                    <input type="text" name="cccd" autocomplete="off" maxlength="18"  class="form-control " placeholder="Số cccd">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="">Số điện thoại</label>
                                    <input type="text" name="sdt" autocomplete="off" maxlength="18"  class="form-control " placeholder="Số điện thoại">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="">Email</label>
                                    <input type="text" name="email" autocomplete="off" maxlength="30" class="form-control " placeholder="Email">
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="_required">Trạng thái:</label>
                                    <select class="form-control select2" required="" name="trang_thai" style="width:100%">
                                        <option value="">Chọn trạng thái</option>
                                        <option value="1" >Đang hoạt động</option>
                                        <option value="0">Ngừng hoạt động</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 mr-auto" id="btnXoaNV">
                    <i class="fas fa-save mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnLuuNV">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn-outline-primary btn-sm wd-85 mg-t-22" id="btnBackNV" onclick="$('#modalTimNV').show(), $('#modalThemNV').hide(), $('#btnLuuNV').hide(),$('#btnBackNV').hide(),$('#btnXoaNV').hide()">
                    <i class="fas fa-chevron-left"></i> Quay lại
                </button>
            </div>
        </div>
    </div>
</div>
<div id="modalTimKiemDaiLy" class="modal-drag" style="width: 350px; z-index: 9999999; margin-top: 5px !important;">
    <div class="modal-drag-header border-bottom">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn đại lý</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" value="" onkeyup="onFocusTimKiemDaiLy(this)" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control focus-assist" id="timKiemDaiLy">
            </div>
            <div class="col-12 mt-2 scrollable streeHeight" style="max-height:300px;" id="treeDaiLy"></div>
        </div>
    </div>
    <div class="modal-drag-footer" style="border-top: 1px solid #e9ecef;">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="ModalTimKiemDaiLyApprove_btnChonDonVi">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>
<div id="modalPhuongThuc" class="modal-drag" style="width: 400px; z-index: 9999999; margin-top: 5px !important; margin-left: -10px !important;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn phương thức khai thác</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_PhuongThuc" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalPhuongThucElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:260px;" id="modalPhuongThucDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonPhuongThuc">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>
<script type="text/html" id="modalPhuongThucDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten %>">
        <input type="checkbox" name="chon_nguoi_pt" id="pt_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalPhuongThucItem " data-val="<%- item.ten%>">
        <label class="custom-control-label" style="cursor:pointer;" for="pt_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />

@section Styles{
    <link href="~/css/app/CarClaim/CarClaimCommon.css" rel="stylesheet" />
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" />
}
@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/libs/tree-js/vakata-jstree/dist/jstree.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/services/partnerlistservice.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/common/gridviewservice.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/HealthService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DepartmentListService.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/services/categorylistservice.js" asp-append-version="true"></script>
    <script src="~/js/app/admin/categorylist.js" asp-append-version="true"></script>
}
