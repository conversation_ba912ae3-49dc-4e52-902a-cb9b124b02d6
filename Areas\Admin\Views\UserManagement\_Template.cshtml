﻿<script type="text/html" id="dsTinhThanhTemplate">
    <div class="custom-control custom-checkbox">
        <input type="checkbox" id="tinh_thanh_tat_ca" onchange="chonTatCaTinhThanh(this)" class="custom-control-input">
        <label class="custom-control-label" for="tinh_thanh_tat_ca"><b style="font-weight:bold">Chọn tất cả tỉnh thành</b></label>
    </div>
    <% _.forEach(ds_tinh_thanh, function(tinh_thanh, index) { %>
    <div class="custom-control custom-checkbox divItemTinhThanh" data-text="<%- tinh_thanh.ten_tinh.toUpperCase() %>">
        <% if(tinh_thanh.chon !== undefined && tinh_thanh.chon) {%>
        <input type="checkbox" id="tinh_thanh_<%- tinh_thanh.ma_tinh %>" onchange="onChangeTinhThanh()" value="<%- tinh_thanh.ma_tinh %>" class="custom-control-input dbgd_tinh_thanh" checked="checked">
        <%}else{%>
        <input type="checkbox" id="tinh_thanh_<%- tinh_thanh.ma_tinh %>" onchange="onChangeTinhThanh()" value="<%- tinh_thanh.ma_tinh %>" class="custom-control-input dbgd_tinh_thanh">
        <%}%>
        <label class="custom-control-label" for="tinh_thanh_<%- tinh_thanh.ma_tinh %>"><%- tinh_thanh.ten_tinh %></label>
    </div>
    <%})%>

</script>

<script type="text/html" id="dsQuanHuyenTemplate">
    <div class="custom-control custom-checkbox">
        <input type="checkbox" id="quan_huyen_tat_ca" onchange="chonTatCaQuanHuyen(this)" class="custom-control-input">
        <label class="custom-control-label" for="quan_huyen_tat_ca"><b style="font-weight:bold">Chọn tất cả phường xã</b></label>
    </div>
    <% _.forEach(ds_quan_huyen, function(quan_huyen, index) { %>
    <div class="custom-control custom-checkbox divItemQuanHuyen" data-text="<%- quan_huyen.ten_quan.toUpperCase() %> - <%- quan_huyen.ten_tinh.toUpperCase() %>">
        <% if(quan_huyen.chon !== undefined && quan_huyen.chon) {%>
        <input type="checkbox" id="quan_huyen_<%- quan_huyen.ma_quan %>_<%- quan_huyen.ma_tinh %>" onchange="onChangeQuanHuyen()" value="<%- quan_huyen.ma_tinh %>.<%- quan_huyen.ma_quan %>" class="custom-control-input dbgd_quan_huyen" checked="checked">
        <%}else{%>
        <input type="checkbox" id="quan_huyen_<%- quan_huyen.ma_quan %>_<%- quan_huyen.ma_tinh %>" onchange="onChangeQuanHuyen()" value="<%- quan_huyen.ma_tinh %>.<%- quan_huyen.ma_quan %>" class="custom-control-input dbgd_quan_huyen">
        <%}%>
        <label class="custom-control-label" for="quan_huyen_<%- quan_huyen.ma_quan %>_<%- quan_huyen.ma_tinh %>"><%- quan_huyen.ten_quan %> - <%- quan_huyen.ten_tinh %></label>
    </div>
    <%})%>
</script>

<script type="text/html" id="templateDsChucNang">
    <% _.forEach(ds_quyen, function(item, index) {
    %>
    <tr>
        <td style="width:20px" class="text-center"><%- index+1 %></td>
        <td>
            <%- item.ten_chuc_nang %>
            <input type="hidden" name="quyen[][nhom_chuc_nang]" value="<%- item.nhom_chuc_nang %>" />
        </td>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="onNhapChange(this)" name="quyen[<%- index %>][nhap]" value="1" id="nhap_<%- item.nhom_chuc_nang %>" <% if(item.nhap){%> checked="checked" <%}%>  class="custom-control-input nhap-item">
                <label class="custom-control-label" for="nhap_<%- item.nhom_chuc_nang %>"><b style="font-weight:bold">&nbsp;</b></label>
            </div>
        </td>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="onXemChange(this)" name="quyen[<%- index %>][xem]" value="1" id="xem_<%- item.nhom_chuc_nang %>" <% if(item.xem){%> checked="checked" <%}%> class="custom-control-input xem-item">
                <label class="custom-control-label" for="xem_<%- item.nhom_chuc_nang %>"><b style="font-weight:bold">&nbsp;</b></label>
            </div>
        </td>
    </tr>
    <%
    }) %>
</script>

<script type="text/html" id="templateDonViQuanLy">
    <% if(quanly.length > 0){%>
    <% _.forEach(quanly, function(item, indexDtac) {
    if(item.chon)
    {
    %>
    <tr data-search="<%- ESUtil.xoaKhoangTrangText(item.ten_tat) %>" class="phanQuyenDonViQuanLy">
        <td class="text-center">
            <input type="hidden" class="floating-input" data-field="ma_doi_tac" data-val="<%- item.ma_doi_tac %>" value="<%- item.ma_doi_tac %>" />
            <input type="hidden" class="floating-input" data-field="ma" data-val="<%- item.ma %>" value="<%- item.ma %>" />
            <input type="hidden" class="floating-input" data-field="ten_tat" data-val="<%- item.ten_tat %>" value="<%- item.ten_tat %>" />
            <input type="hidden" class="floating-input" data-field="ten_dtac" data-val="<%- item.ten_dtac %>" value="<%- item.ten_dtac %>" />
            <div class="custom-control custom-checkbox ml-2">
                <input type="checkbox" data-field="chon" checked="checked" data-val="<%- item.chon %>" data-doi-tac="<%- item.ma_doi_tac %>" data-chi-nhanh="<%- item.ma %>" onchange="onChangeCNhanh(this)" id="cnhanh_<%- indexDtac  %>" class="custom-control-input input-dvi-qly checkbox">
                <label class="custom-control-label" for="cnhanh_<%- indexDtac  %>">@* <b style="font-weight:bold">&nbsp;</b> *@</label>
            </div>
        </td>
        <td><%- item.ten_tat  %></td>
        <td><%- item.ten_dtac  %></td>
    </tr>
    <%
    }
    else
    {
    %>
    <tr data-search="<%- ESUtil.xoaKhoangTrangText(item.ten_tat) %>" class="phanQuyenDonViQuanLy">
        <td class="text-center">
            <input type="hidden" class="floating-input" data-field="ma_doi_tac" data-val="<%- item.ma_doi_tac %>" value="<%- item.ma_doi_tac %>" />
            <input type="hidden" class="floating-input" data-field="ma" data-val="<%- item.ma %>" value="<%- item.ma %>" />
            <input type="hidden" class="floating-input" data-field="ten_tat" data-val="<%- item.ten_tat %>" value="<%- item.ten_tat %>" />
            <input type="hidden" class="floating-input" data-field="ten_dtac" data-val="<%- item.ten_dtac %>" value="<%- item.ten_dtac %>" />
            <div class="custom-control custom-checkbox">
                <input type="checkbox" data-field="chon" data-val="<%- item.chon %>" data-doi-tac="<%- item.ma_doi_tac %>" data-chi-nhanh="<%- item.ma %>" onchange="onChangeCNhanh(this)" id="cnhanh_<%- indexDtac  %>" class="custom-control-input input-dvi-qly checkbox">
                <label class="custom-control-label" for="cnhanh_<%- indexDtac  %>"><b style="font-weight:bold">&nbsp;</b></label>
            </div>
        </td>
        <td><%- item.ten_tat  %></td>
        <td><%- item.ten_dtac  %></td>
    </tr>
    <%
    }
    })}%>
    <% if(quanly.length < 9){
    for(var i = 0; i < 9 - quanly.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapGiamDinh">
    <% if(giam_dinh.length > 0){
    _.forEach(giam_dinh, function(item,index) { %>
    <tr class="text-center">
        <td><%- index+1 %></td>
        <td>
            <select class="form-control select2" id="gd_lh_nv_<%- index%>" style="width:100%">
                <option value="">Chọn loại hình nghiệp vụ</option>
                <% _.forEach(ds_lhnv, function(item_lhnv,index_lhnv) { %>
                <option value="<%- item_lhnv.ma %>" <%- ESUtil.selected(item.lh_nv, item_lhnv.ma) %>><%- item_lhnv.ten %></option>
                <%})%>
            </select>
        </td>
        <td>
            <select class="form-control select2" id="gd_thay_the_sc_<%- index%>" style="width:100%">
                <option value="">Chọn phương án</option>
                <option value="T" <%- ESUtil.selected(item.thay_the_sc, 'T') %>>Thay thế</option>
                <option value="S" <%- ESUtil.selected(item.thay_the_sc, 'S') %>>Sửa chữa</option>
            </select>
        </td>
        <td>
            <input class="form-control number" value="<%- ESUtil.formatMoney(item.tien) %>" id="gd_tien_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <a href="#"><i class="fas fa-trash-alt" title="Xóa phân cấp" onclick="xoaPhanCapGiamDinh('<%- index %>')"></i></a>
        </td>
    </tr>
    <% })}%>

    <% if(giam_dinh.length < 4){
    for(var i = 0; i < 4 - giam_dinh.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapBoiThuong">
    <% if(boi_thuong.length > 0){
    _.forEach(boi_thuong, function(item,index) { %>
    <tr class="text-center">
        <td><%- index+1 %></td>
        <td style="width:35%">
            <select class="form-control select2" id="bt_lh_nv_<%- index%>" style="width:100%">
                <option value="">Chọn loại hình nghiệp vụ</option>
                <% _.forEach(ds_lhnv, function(item_lhnv,index_lhnv) { %>
                <option value="<%- item_lhnv.ma %>" <%- ESUtil.selected(item.lh_nv, item_lhnv.ma) %>><%- item_lhnv.ten %></option>
                <%})%>
            </select>
        </td>
        <td>
            <input class="form-control number" value="<%- ESUtil.formatMoney(item.tien_phuong_an) %>" id="bt_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number" value="<%- ESUtil.formatMoney(item.tien_bao_lanh) %>" id="bt_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number" value="<%- ESUtil.formatMoney(item.tien_boi_thuong) %>" id="bt_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <a href="#"><i class="fas fa-trash-alt" title="Xóa phân cấp" onclick="xoaPhanCapBoiThuong('<%- index %>')"></i></a>
        </td>
    </tr>
    <% })}%>

    <% if(boi_thuong.length < 10){
    for(var i = 0; i < 10 - boi_thuong.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templateItemHangMuc">
    <% _.forEach(hang_muc, function(item, index) {
    %>
    <li data-val="<%- item.ma %>" class="item_hang_muc" data-text="<%- item.ten.toUpperCase() %>" onclick="chonHangMuc('<%- item.ma %>','<%- item.ten %>')"><%- item.ten %></li>
    <%
    })%>
</script>

<script type="text/html" id="templatePhanCapGiamDinhTNClone">
    <% if(ds_lhnv_tn.length > 0){ %>
    <% var arrLHNVGD = [] %>
    <% _.forEach(ds_lhnv_tn, function(item,index) { %>
    <tr class="text-center gd_tn">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% var tien_uoc_bcgd = 0 %>
        <% if(giam_dinh.length > 0){ %>
        <% var tien_t = 0; tien_s = 0 %>
        <% _.forEach(giam_dinh, function(item1, index1){ %>
        <% if(item1.lh_nv == item.ma && item1.thay_the_sc == 'T'){ %>
        <% tien_t = item1.tien %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% } %>
        <% if(item1.lh_nv == item.ma && item1.thay_the_sc == 'S'){ %>
        <% tien_s = item1.tien %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% } %>
        <% if(item1.lh_nv == item.ma && item1.thay_the_sc == ' '){ %>
        <% tien_s = item1.tien %>
        <% tien_t = item1.tien %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% } %>
        <% }) %>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney(tien_t) %>" id="gd_tien_t_<%- index %>" name="tien_t" />
        </td>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney(tien_s) %>" id="gd_tien_s_<%- index %>" name="tien_s" />
        </td>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney() %>" id="gd_tien_t_<%- index %>" name="tien_t" />
        </td>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney() %>" id="gd_tien_s_<%- index %>" name="tien_s" />
        </td>
        <% } %>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney(tien_uoc_bcgd) %>" id="tien_uoc_bcgd_<%- index %>" name="tien_uoc_bcgd" />
        </td>
    </tr>
    <% })}%>
</script>

<script type="text/html" id="templatePhanCapGiamDinhBBClone">
    <% if(ds_lhnv_bb.length > 0){ %>
    <% var arrLHNVGD = [] %>
    <% _.forEach(ds_lhnv_bb, function(item,index) { %>
    <tr class="text-center gd_bb">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% var tien_uoc_bcgd = 0 %>
        <% if(giam_dinh.length > 0){ %>
        <% var tien = 0 %>
        <% _.forEach(giam_dinh, function(item1, index1){ %>
        <% if(item1.lh_nv == item.ma){ %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% tien = item1.tien %>
        <% } %>
        <% }) %>
        <td>
            <input class="form-control number floating-input" maxlength="15" data-field="gd_tien" value="<%- ESUtil.formatMoney(tien) %>" id="gd_tien_<%- index %>" name="tien" />
        </td>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" maxlength="15" data-field="gd_tien" value="<%- ESUtil.formatMoney() %>" id="gd_tien_<%- index %>" name="tien" />
        </td>
        <% } %>
        <td>
            <input class="form-control number floating-input" maxlength="15" data-field="tien_uoc_bcgd" value="<%- ESUtil.formatMoney(tien_uoc_bcgd) %>" id="tien_uoc_bcgd_<%- index %>" name="tien_uoc_bcgd" />
        </td>
    </tr>
    <% })}%>

    <% if(ds_lhnv_bb.length < 6){
    for(var i = 0; i < 6 - ds_lhnv_bb.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapBoiThuongClone">
    <% if(ds_lhnv.length > 0){ %>
    <% var arrLHNV = [] %>
    <% var arrLHNVBT = [] %>
    <% _.forEach(ds_lhnv, function(item,index) { %>
    <tr class="text-center btx">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="bt_lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% if(boi_thuong.length > 0){ %>
        <% _.forEach(boi_thuong, function(item1, index1){ %>
        <% arrLHNVBT.push(item1.lh_nv) %>
        <% if(item1.lh_nv == item.ma){ %>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_phuong_an" value="<%- ESUtil.formatMoney(item1.tien_phuong_an) %>" id="bt_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_bao_lanh" value="<%- ESUtil.formatMoney(item1.tien_bao_lanh) %>" id="bt_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_boi_thuong" value="<%- ESUtil.formatMoney(item1.tien_boi_thuong) %>" id="bt_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_tu_choi" value="<%- ESUtil.formatMoney(item1.tien_tu_choi) %>" id="bt_tien_tu_choi_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control floating-input text-center" type="number" min="0" max="100" data-field="bt_ty_le_phan_tram" value="<%- item1.ty_le_phan_tram??100 %>" id="bt_ty_le_phan_tram_<%- index%>" />
        </td>
        <% } %>
        <% }) %>
        <% if(arrLHNVBT.includes(item.ma) == false){ %>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="bt_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="bt_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="bt_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_tu_choi" value="<%- ESUtil.formatMoney() %>" id="bt_tien_tu_choi_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control floating-input text-center" type="number" min="0" max="100" data-field="bt_ty_le_phan_tram" value="<%- 100 %>" id="bt_ty_le_phan_tram_<%- index%>" />
        </td>
        <% } %>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="bt_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="bt_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="bt_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_tu_choi" value="<%- ESUtil.formatMoney() %>" id="bt_tien_tu_choi_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control floating-input text-center" type="number" min="0" max="100" data-field="bt_ty_le_phan_tram" value="<%- 100 %>" id="bt_ty_le_phan_tram_<%- index%>" />
        </td>
        <% } %>
    </tr>
    <% }) %>
    <% }%>

    <% if(ds_lhnv.length < 8){
    for(var i = 0; i < 8 - ds_lhnv.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapBoiThuongNguoiClone">
    <% if(sp_con_nguoi.length > 0){ %>
    <% var arrLHNV = [] %>
    <% var arrLHNVBT = [] %>
    <% _.forEach(sp_con_nguoi, function(item,index) { %>
    <tr class="text-center bt_ng">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="btn_lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% if(boi_thuong_nguoi.length > 0){ %>
        <% _.forEach(boi_thuong_nguoi, function(item1, index1){ %>
        <% arrLHNVBT.push(item1.lh_nv) %>
        <% if(item1.lh_nv == item.ma){ %>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an" value="<%- ESUtil.formatMoney(item1.tien_phuong_an) %>" id="btn_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_bao_lanh" value="<%- ESUtil.formatMoney(item1.tien_bao_lanh) %>" id="btn_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong" value="<%- ESUtil.formatMoney(item1.tien_boi_thuong) %>" id="btn_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_xac_nhan_uoc" value="<%- ESUtil.formatMoney(item1.tien_xac_nhan_uoc) %>" id="btn_tien_xac_nhan_uoc_<%- index %>" placeholder="Số tiền" />
        </td>
        <% } %>
        <% }) %>
        <% if(arrLHNVBT.includes(item.ma) == false){ %>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="btn_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="btn_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="btn_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
          <td>
            <input class="form-control number floating-input" data-field="tien_xac_nhan_uoc" value="<%- ESUtil.formatMoney() %>" id="btn_tien_xac_nhan_uoc_<%- index %>" placeholder="Số tiền" />
        </td>
        <% } %>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="btn_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="btn_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="btn_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
         <td>
            <input class="form-control number floating-input" data-field="tien_xac_nhan_uoc" value="<%- ESUtil.formatMoney() %>" id="btn_tien_xac_nhan_uoc_<%- index %>" placeholder="Số tiền" />
        </td>
        <% } %>
    </tr>
    <% }) %>
    <% }%>
</script>

<script type="text/html" id="templatePhanCapBoiThuongKyThuatClone">
    <% if(sp_ky_thuat.length > 0){ %>
    <% _.forEach(sp_ky_thuat, function(item,index) { %>
    <tr class="text-center bt_kt">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_kt<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_du_phong_kt" value="<%- ESUtil.formatMoney(item.tien_du_phong_bt) %>" id="tien_du_phong_bt_kt_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_cpk_kt" value="<%- ESUtil.formatMoney(item.tien_cpk) %>" id="tien_cpk_kt_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an_kt" value="<%- ESUtil.formatMoney(item.tien_phuong_an) %>" id="tien_phuong_an_kt_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong_kt" value="<%- ESUtil.formatMoney(item.tien_boi_thuong) %>" id="tien_boi_thuong_kt_<%- index%>" placeholder="Số tiền" />
        </td>
    </tr>
    <% }) %>
    <% }%>
</script>
<script type="text/html" id="templatePhanCapBoiThuongTaiSanClone">
    <% if(sp_tai_san.length > 0){ %>
    <% _.forEach(sp_tai_san, function(item,index) { %>
    <tr class="text-center bt_ts">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_ts<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_du_phong_ts" value="<%- ESUtil.formatMoney(item.tien_du_phong_bt) %>" id="tien_du_phong_bt_ts_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_cpk_ts" value="<%- ESUtil.formatMoney(item.tien_cpk) %>" id="tien_cpk_ts_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an_ts" value="<%- ESUtil.formatMoney(item.tien_phuong_an) %>" id="tien_phuong_an_ts_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong_ts" value="<%- ESUtil.formatMoney(item.tien_boi_thuong) %>" id="tien_boi_thuong_ts_<%- index%>" placeholder="Số tiền" />
        </td>
    </tr>
    <% }) %>
    <% }%>
</script>
<script type="text/html" id="templatePhanCapBoiThuongTrachNhiemClone">
    <% if(sp_trach_nhiem.length > 0){ %>
    <% _.forEach(sp_trach_nhiem, function(item,index) { %>
    <tr class="text-center bt_tn">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_tn<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_du_phong_tn" value="<%- ESUtil.formatMoney(item.tien_du_phong_bt) %>" id="tien_du_phong_bt_tn_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_cpk_tn" value="<%- ESUtil.formatMoney(item.tien_cpk) %>" id="tien_cpk_tn_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an_tn" value="<%- ESUtil.formatMoney(item.tien_phuong_an) %>" id="tien_phuong_an_tn_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong_tn" value="<%- ESUtil.formatMoney(item.tien_boi_thuong) %>" id="tien_boi_thuong_tn_<%- index%>" placeholder="Số tiền" />
        </td>
    </tr>
    <% }) %>
    <% }%>
</script>
<script type="text/html" id="templatePhanCapBoiThuongHonHopClone">
    <% if(sp_hon_hop.length > 0){ %>
    <% _.forEach(sp_hon_hop, function(item,index) { %>
    <tr class="text-center bt_hh">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_hh<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_du_phong_hh" value="<%- ESUtil.formatMoney(item.tien_du_phong_bt) %>" id="tien_du_phong_bt_hh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_cpk_hh" value="<%- ESUtil.formatMoney(item.tien_cpk) %>" id="tien_cpk_hh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an_hh" value="<%- ESUtil.formatMoney(item.tien_phuong_an) %>" id="tien_phuong_an_hh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong_hh" value="<%- ESUtil.formatMoney(item.tien_boi_thuong) %>" id="tien_boi_thuong_hh_<%- index%>" placeholder="Số tiền" />
        </td>
    </tr>
    <% }) %>
    <% }%>
</script>
@*Load danh sách ngày hiển thị cấu hình phân cấp*@
<script type="text/html" id="tblDsNgayBody_template">
    <% if(ds_ngay.length > 0){
    _.forEach(ds_ngay, function(item,index) { %>
    <tr style="cursor: pointer; text-align:center" id="bt_<%- item.so_id %>" onclick="getDetailPhanCapTheoNgay('<%- item.so_id %>')" class="item-ngay-hl-kt">
        <td style="font-weight:bold">
            <%- item.ngay_hl_hthi %> - <%- item.ngay_kt_hthi %>
        </td>
        <td>
            <span>
                <a href="#" onclick="suaNgayPhanCap('<%- item.so_id %>')">
                    <i class="fas fa-edit"></i>
                </a>
            </span>
        </td>
    </tr>
    <%})} %>
    <% if(ds_ngay.length < 12){
    for(var i = 0; i < 12 - ds_ngay.length;i++ ){
    %>
    <tr>
        <td style="height:36px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Phân cấp chung*@
<script type="text/html" id="modalPhanCapChung_template">
    <% if(ds_phan_cap_chung.length > 0){ %>
    <% _.forEach(ds_phan_cap_chung, function(item,index) { %>
    <tr class="row_item">
        <td style="display: none;">
            <input type="text" class="floating-input ma" autocomplete="off" maxlength="50" data-field="ma" data-val="<%- item.ma %>" value="<%- item.ma %>">
            <input type="text" class="floating-input ma" autocomplete="off" maxlength="50" data-field="ma_ty_le" data-val="<%- item.ma_ty_le %>" value="<%- item.ma_ty_le %>">
        </td>
        <td class="text-center"><%- index+1 %></td>
        <td style="text-align: left; font-weight:bold !important;">
            <input style="background-color: white;background-image:unset;" disabled="disabled" type="text" class="form-control floating-input name" readonly autocomplete="off" maxlength="100" data-field="name" data-val="<%- item.name %>" value="<%- item.name %>">
        </td>
        <td style="text-align: right;">
            <input style="font-weight:bold;" type="text" class="floating-input number value" autocomplete="off" maxlength="15" data-field="value" value="<%- ESUtil.formatMoney(item.value) %>">
        </td>
        <% if(item.loai === 'tong_chi_phi') { %>
        <td style="text-align: center;">
            <div class="col-sm-12">
                <% if(item.value_trang_thai == 'C'){ %>
                <div class="custom-control custom-switch">
                    <input style="display:none" type="text" checked="checked" data-field="ma_trang_thai" name="ma_trang_thai" data-val="<%- item.ma_trang_thai %>">
                    <input type="checkbox" checked="checked" data-field="value_trang_thai" name="value" class="custom-control-input checkbox" id="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>">
                    <label class="custom-control-label" for="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>"></label>
                </div>
                <% } else { %>
                <div class="custom-control custom-switch">
                    <input style="display:none" type="text" checked="checked" data-field="ma_trang_thai" name="ma_trang_thai" data-val="<%- item.ma_trang_thai%>">
                    <input type="checkbox" data-field="value_trang_thai" name="value" class="custom-control-input checkbox" id="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>">
                    <label class="custom-control-label" for="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>"></label>
                </div>
                <% } %>
            </div>
        </td>
        <% } else if(item.loai == 'tam_ung'){ %>
        <td style="text-align: left; font-weight:bold !important;display:none;">
            <input style="background-color: white;background-image:unset;" disabled="disabled" type="text" class="form-control floating-input name" readonly autocomplete="off">
        </td>
        <td>
            <input class="form-control floating-input text-center" type="number" min="0" max="100" data-field="value_ty_le" value="<%- item.value_ty_le??100 %>" />
        </td>
        <% } %>
    </tr>
    <% })} %>
</script>

@*Phân cấp chung tài sản kỹ thuật*@
<script type="text/html" id="modalPhanCapChungtskt_template">
    <% if(ds_phan_cap_chung.length > 0){ %>
    <% _.forEach(ds_phan_cap_chung, function(item,index) { %>
        <tr class="row_item">
            <td style="display: none;">
                <input type="text" class="floating-input ma" autocomplete="off" maxlength="50" data-field="ma" data-val="<%- item.ma %>" value="<%- item.ma %>">
            </td>
            <td class="text-center"><%- index+1 %></td>
            <td style="text-align: left; font-weight:bold !important;">
                <input style="background-color: white;background-image:unset;" disabled="disabled" type="text" class="form-control floating-input name" readonly autocomplete="off" maxlength="100" data-field="name" data-val="<%- item.name %>" value="<%- item.name %>">
            </td>
                <td style="text-align: right;">
                <input style="font-weight:bold;" type="text" class="floating-input number value" autocomplete="off" maxlength="15" data-field="value" value="<%- ESUtil.formatMoney(item.value) %>">
            </td>
        </tr>
    <% })} %>
</script>


<script type="text/html" id="templateDsChucNangCauHinh">
    <% _.forEach(ds_quyen, function(item, index) {
    %>
    <tr>
        <td style="width:20px" class="text-center"><%- index+1 %></td>
        <td>
            <%- item.ten_chuc_nang %>
            <input type="hidden" name="quyen[][nhom_chuc_nang]" value="<%- item.nhom_chuc_nang %>" />
        </td>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="onNhapChangeCauHinh(this)" name="quyen[<%- index %>][nhap]" value="1" id="nhap_cau_hinh_<%- item.nhom_chuc_nang %>" <% if(item.nhap){%> checked="checked" <%}%>  class="custom-control-input nhap-item-cau-hinh">
                <label class="custom-control-label" for="nhap_cau_hinh_<%- item.nhom_chuc_nang %>"><b style="font-weight:bold">&nbsp;</b></label>
            </div>
        </td>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="onXemChangeCauHinh(this)" name="quyen[<%- index %>][xem]" value="1" id="xem_cau_hinh_<%- item.nhom_chuc_nang %>" <% if(item.xem){%> checked="checked" <%}%> class="custom-control-input xem-item-cau-hinh">
                <label class="custom-control-label" for="xem_cau_hinh_<%- item.nhom_chuc_nang %>"><b style="font-weight:bold">&nbsp;</b></label>
            </div>
        </td>
    </tr>
    <%
    }) %>
</script>

<script type="text/html" id="modalNsdPhanCapDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsnsd" id="dsnsd_<%- item.ma %>" data-search="<%- ESUtil.xoaKhoangTrangText(item.ma) %><%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <input type="checkbox" id="nsd_pc_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNsdPhanCapItem">
        <label class="custom-control-label" style="cursor:pointer;" for="nsd_pc_<%- item.ma %>"><%- item.ma %> - <%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

@*Load danh sách ngày hiển thị cấu hình phân cấp*@
<script type="text/html" id="tblDsNgayBodyNhomPC_template">
    <% if(ds_ngay.length > 0){
    _.forEach(ds_ngay, function(item,index) { %>
    <tr style="cursor: pointer; text-align:center" id="btnhompc_<%- item.so_id %>" onclick="getDetailPhanCapTheoNgayNhomPC('<%- item.so_id %>')" class="item-ngay-hl-kt-nhom-pc">
        <td style="font-weight:bold">
            <%- item.ngay_hl_hthi %> - <%- item.ngay_kt_hthi %>
        </td>
        <td>
            <span>
                <a href="#" onclick="suaNgayPhanCapNhomPC('<%- item.so_id %>')">
                    <i class="fas fa-edit"></i>
                </a>
            </span>
        </td>
    </tr>
    <%})} %>
    <% if(ds_ngay.length < 10){
    for(var i = 0; i < 10 - ds_ngay.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapGiamDinhTNCloneNhomPC">
    <% if(ds_lhnv_tn.length > 0){ %>
    <% var arrLHNVGD = [] %>
    <% _.forEach(ds_lhnv_tn, function(item,index) { %>
    <tr class="text-center gd_tn">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% var tien_uoc_bcgd = 0 %>
        <% if(giam_dinh.length > 0){ %>
        <% var tien_t = 0; tien_s = 0 %>
        <% _.forEach(giam_dinh, function(item1, index1){ %>
        <% if(item1.lh_nv == item.ma && item1.thay_the_sc == 'T'){ %>
        <% tien_t = item1.tien %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% } %>
        <% if(item1.lh_nv == item.ma && item1.thay_the_sc == 'S'){ %>
        <% tien_s = item1.tien %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% } %>
        <% if(item1.lh_nv == item.ma && item1.thay_the_sc == ' '){ %>
        <% tien_s = item1.tien %>
        <% tien_t = item1.tien %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% } %>
        <% }) %>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney(tien_t) %>" id="gd_tien_t_<%- index %>" name="tien_t" />
        </td>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney(tien_s) %>" id="gd_tien_s_<%- index %>" name="tien_s" />
        </td>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney() %>" id="gd_tien_t_<%- index %>" name="tien_t" />
        </td>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney() %>" id="gd_tien_s_<%- index %>" name="tien_s" />
        </td>
        <% } %>
        <td>
            <input class="form-control number floating-input" maxlength="15" value="<%- ESUtil.formatMoney(tien_uoc_bcgd) %>" id="tien_uoc_bcgd_<%- index %>" name="tien_uoc_bcgd" />
        </td>
    </tr>
    <% })}%>

    <% if(ds_lhnv_tn.length < 3){
    for(var i = 0; i < 3 - ds_lhnv_tn.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapGiamDinhBBCloneNhomPC">
    <% if(ds_lhnv_bb.length > 0){ %>
    <% var arrLHNVGD = [] %>
    <% _.forEach(ds_lhnv_bb, function(item,index) { %>
    <tr class="text-center gd_bb">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% var tien_uoc_bcgd = 0 %>
        <% if(giam_dinh.length > 0){ %>
        <% var tien = 0 %>
        <% _.forEach(giam_dinh, function(item1, index1){ %>
        <% if(item1.lh_nv == item.ma){ %>
        <% tien_uoc_bcgd = item1.tien_uoc_bcgd %>
        <% tien = item1.tien %>
        <% } %>
        <% }) %>
        <td>
            <input class="form-control number floating-input" maxlength="15" data-field="gd_tien" value="<%- ESUtil.formatMoney(tien) %>" id="gd_tien_<%- index %>" name="tien" />
        </td>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" maxlength="15" data-field="gd_tien" value="<%- ESUtil.formatMoney() %>" id="gd_tien_<%- index %>" name="tien" />
        </td>
        <% } %>
        <td>
            <input class="form-control number floating-input" maxlength="15" data-field="tien_uoc_bcgd" value="<%- ESUtil.formatMoney(tien_uoc_bcgd) %>" id="tien_uoc_bcgd_<%- index %>" name="tien_uoc_bcgd" />
        </td>
    </tr>
    <% })}%>

    <% if(ds_lhnv_bb.length < 4){
    for(var i = 0; i < 4 - ds_lhnv_bb.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapBoiThuongCloneNhomPC">
    <% if(ds_lhnv.length > 0){ %>
    <% var arrLHNV = [] %>
    <% var arrLHNVBT = [] %>
    <% _.forEach(ds_lhnv, function(item,index) { %>
    <tr class="text-center btx">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="bt_lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% if(boi_thuong.length > 0){ %>
        <% _.forEach(boi_thuong, function(item1, index1){ %>
        <% arrLHNVBT.push(item1.lh_nv) %>
        <% if(item1.lh_nv == item.ma){ %>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_phuong_an" value="<%- ESUtil.formatMoney(item1.tien_phuong_an) %>" id="bt_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_bao_lanh" value="<%- ESUtil.formatMoney(item1.tien_bao_lanh) %>" id="bt_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_boi_thuong" value="<%- ESUtil.formatMoney(item1.tien_boi_thuong) %>" id="bt_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_tu_choi" value="<%- ESUtil.formatMoney(item1.tien_tu_choi) %>" id="bt_tien_tu_choi_<%- index%>" placeholder="Số tiền" />
        </td>
        <% } %>
        <% }) %>
        <% if(arrLHNVBT.includes(item.ma) == false){ %>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="bt_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="bt_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="bt_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_tu_choi" value="<%- ESUtil.formatMoney() %>" id="bt_tien_tu_choi_<%- index%>" placeholder="Số tiền" />
        </td>
        <% } %>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="bt_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="bt_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="bt_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="bt_tien_tu_choi" value="<%- ESUtil.formatMoney() %>" id="bt_tien_tu_choi_<%- index%>" placeholder="Số tiền" />
        </td>
        <% } %>
    </tr>
    <% }) %>
    <% }%>

    <% if(ds_lhnv.length < 8){
    for(var i = 0; i < 8 - ds_lhnv.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="templatePhanCapBoiThuongNguoiCloneNhomPC">
    <% if(sp_con_nguoi.length > 0){ %>
    <% var arrLHNV = [] %>
    <% var arrLHNVBT = [] %>
    <% _.forEach(sp_con_nguoi, function(item,index) { %>
    <tr class="text-center bt_ng">
        <td><%- index+1 %></td>
        <td>
            <input class="form-control floating-input" value="<%- item.ten %>" col-val="<%- item.ma %>" id="btn_lh_nv_<%- index %>" name="lh_nv" disabled="disabled" style="background-color: white; background-image:unset;" />
        </td>
        <% if(boi_thuong_nguoi.length > 0){ %>
        <% _.forEach(boi_thuong_nguoi, function(item1, index1){ %>
        <% arrLHNVBT.push(item1.lh_nv) %>
        <% if(item1.lh_nv == item.ma){ %>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an" value="<%- ESUtil.formatMoney(item1.tien_phuong_an) %>" id="btn_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_bao_lanh" value="<%- ESUtil.formatMoney(item1.tien_bao_lanh) %>" id="btn_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong" value="<%- ESUtil.formatMoney(item1.tien_boi_thuong) %>" id="btn_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_xac_nhan_uoc" value="<%- ESUtil.formatMoney(item1.tien_xac_nhan_uoc) %>" id="btn_tien_xac_nhan_uoc_<%- index%>" placeholder="Số tiền" />
        </td>
        <% } %>
        <% }) %>
        <% if(arrLHNVBT.includes(item.ma) == false){ %>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="btn_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="btn_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="btn_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_xac_nhan_uoc" value="<%- ESUtil.formatMoney() %>" id="btn_tien_xac_nhan_uoc_<%- index%>" placeholder="Số tiền" />
        </td>
        <% } %>
        <% }else{ %>
        <td>
            <input class="form-control number floating-input" data-field="tien_phuong_an" value="<%- ESUtil.formatMoney() %>" id="btn_tien_phuong_an_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_bao_lanh" value="<%- ESUtil.formatMoney() %>" id="btn_tien_bao_lanh_<%- index%>" placeholder="Số tiền" />
        </td>
        <td>
            <input class="form-control number floating-input" data-field="tien_boi_thuong" value="<%- ESUtil.formatMoney() %>" id="btn_tien_boi_thuong_<%- index%>" placeholder="Số tiền" />
        </td>
         <td>
            <input class="form-control number floating-input" data-field="tien_xac_nhan_uoc" value="<%- ESUtil.formatMoney() %>" id="btn_tien_xac_nhan_uoc_<%- index%>" placeholder="Số tiền" />
        </td>
        <% } %>
    </tr>
    <% }) %>
    <% }%>
    <% if(sp_con_nguoi.length < 8){
    for(var i = 0; i < 8 - sp_con_nguoi.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Phân cấp chung*@
<script type="text/html" id="modalPhanCapChungNhomPC_template">
    <% if(ds_phan_cap_chung.length > 0){ %>
    <% _.forEach(ds_phan_cap_chung, function(item,index) { %>
    <tr class="row_item">
        <td class="text-center"><%- index + 1%></td>
        <td style="display: none;">
            <input type="text" class="floating-input ma" autocomplete="off" maxlength="50" data-field="ma" data-val="<%- item.ma %>" value="<%- item.ma %>">
        </td>
        <td style="text-align: left; font-weight:bold !important;">
            <input style="background-color: white;background-image:unset;" disabled="disabled" type="text" class="form-control floating-input name" readonly autocomplete="off" maxlength="100" data-field="name" data-val="<%- item.name %>" value="<%- item.name %>">
        </td>
        <td style="text-align: right;">
            <input style="font-weight:bold;" type="text" class="floating-input number value" autocomplete="off" maxlength="15" data-field="value" value="<%- ESUtil.formatMoney(item.value) %>">
        </td>
        <% if(item.loai === 'tong_chi_phi') { %>
        <td style="text-align: center;">
            <div class="col-sm-12">
                <% if(item.value_trang_thai == 'C'){ %>
                <div class="custom-control custom-switch">
                    <input style="display:none" type="text" checked="checked" data-field="ma_trang_thai" name="ma_trang_thai" data-val="<%- item.ma_trang_thai %>">
                    <input type="checkbox" checked="checked" data-field="value_trang_thai" name="value" class="custom-control-input checkbox" id="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>">
                    <label class="custom-control-label" for="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>"></label>
                </div>
                <% } else { %>
                <div class="custom-control custom-switch">
                    <input style="display:none" type="text" checked="checked" data-field="ma_trang_thai" name="ma_trang_thai" data-val="<%- item.ma_trang_thai%>">
                    <input type="checkbox" data-field="value_trang_thai" name="value" class="custom-control-input checkbox" id="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>">
                    <label class="custom-control-label" for="checkbox_tong_chi_phi_<%- item.ma_trang_thai%>"></label>
                </div>
                <% } %>
            </div>
        </td>
        <% } else if(item.loai == 'tam_ung'){ %>
        <td style="text-align: left; font-weight:bold !important;">
            <input style="background-color: white;background-image:unset;" disabled="disabled" type="text" class="form-control floating-input name" readonly autocomplete="off">
        </td>
        <% } %>
    </tr>
    <% })} %>
</script>