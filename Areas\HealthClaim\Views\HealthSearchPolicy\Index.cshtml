﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Tra cứu GCN sức khỏe";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12 col-md-12 pd-3">
            <div class="card">
                <div class="card-body" style="padding-top:5px;">
                    <form name="frmTimKiem" method="post" class="row">
                        <div class="col-2">
                            <div class="form-group">
                                <label for="ma_chi_nhanh_ql">Đơn vị cấp đơn</label>
                                <div class="input-group">
                                    <input type="text" name="ma_chi_nhanh_ql" data-val="" style="cursor:pointer;background-color: #e9ecef;" class="form-control" autocomplete="off" placeholder="Click chọn chi nhánh cấp đơn" onclick="onChonDonViXuly(this)">
                                    <div class="input-group-append">
                                        <label class="input-group-text">
                                            <a href="javascript:void(0)">
                                                <i class="fas fa-search" title="Chọn chi nhánh"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label>Số hợp đồng</label>
                                <input type="text" name="so_hd" autocomplete="off" placeholder="Số hợp đồng" class="form-control">
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label>Số GCN</label>
                                <input type="text" name="so_gcn" autocomplete="off" placeholder="Số GCN" class="form-control">
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label>Tên người được bảo hiểm</label>
                                <input type="text" name="ten_ndbh" autocomplete="off" placeholder="Tên người được bảo hiểm" class="form-control">
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label>Số CCCD</label>
                                <input type="text" name="so_cmt" autocomplete="off" placeholder="Số CCCD" class="form-control">
                            </div>
                        </div>   
                        <div class="col-2">
                            <div class="form-group">
                                <label for="">Ngày sinh</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker_null" autocomplete="off" name="ngay_sinh" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.5em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>  
                        <div class="col-2">
                            <div class="form-group">
                                <label>Số điện thoại</label>
                                <input type="text" name="sdt" autocomplete="off" placeholder="Số điện thoại" class="form-control">
                            </div>
                        </div>
                        <div class="col-2" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search mr-2"></i>Tìm kiếm
                            </button>
                        </div>
                    </form>
                    <div class="row" style="margin-top:.5rem;">
                        <div class="col-12">
                            <div id="gridViewTimKiem" class="table-app bg-light" style="height: 59vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<partial name="_ModalXemThongTinQuyenLoiLSTT.cshtml" />
<partial name="_Template.cshtml" />
<partial name="/Views/Shared/_ModalChiNhanh.cshtml" />
@section Styles {
    <link href="~/css/app/CarClaim/CarClaimCommon.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" asp-append-version="true" />
}
@section Scripts {
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/libs/tree-js/vakata-jstree/dist/jstree.js" asp-append-version="true"></script>
    <script src="~/js/app/healthcare/services/HealthSearchPolicyService.js" asp-append-version="true"></script>
    <script src="~/js/app/ModalChiNhanhXuLyService.js"></script>
    <script src="~/js/app/healthcare/healthsearchpolicy.js" asp-append-version="true"></script>
}