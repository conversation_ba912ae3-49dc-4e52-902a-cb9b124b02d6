﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Đơn vị hành chính";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Đơn vị hành chính</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Đơn vị hành chính</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập tên tỉnh thành/quận huyện/xã phường" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2 d-none">
                            <div class="form-group">
                                <label class="">Tên miền</label>
                                <select class="select2 form-control custom-select" name="mien" style="width: 100%; height:36px;">
                                    <option value="">Chọn tên miền</option>
                                    <option value="B">Miền Bắc</option>
                                    <option value="T">Miền Trung</option>
                                    <option value="N">Miền Nam</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="0">Ngừng sử dụng</option>
                                    <option value="1">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapThongTinMaTinh">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcelAdministrativeunits">
                                <i class="fas fa-download"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                <i class="fas fa-upload"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewDonViHanhChinh" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapMaTinh" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinMaTinh" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin đơn vị hành chính <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="pm" value="BT" />
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Vùng miền</label>
                                <select class="select2 form-control custom-select" required name="mien" style="width: 100%; height:36px;">
                                    <option value="">Chọn tên miền</option>
                                    <option value="B">Miền Bắc</option>
                                    <option value="T">Miền Trung</option>
                                    <option value="N">Miền Nam</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="0">Ngừng sử dụng</option>
                                    <option value="1">Đang sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã tỉnh</label>
                                <input type="text" maxlength="20" name="ma_tinh" autocomplete="off" required class="form-control" placeholder="Mã tỉnh">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên tỉnh</label>
                                <input type="text" maxlength="100" name="ten_tinh" autocomplete="off" required class="form-control" placeholder="Tên tỉnh/TP">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Mã phường</label>
                                <input type="text" maxlength="20" name="ma_quan" autocomplete="off" required class="form-control" placeholder="Mã Phường/xã">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:5px">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Tên phường</label>
                                <input type="text" maxlength="100" name="ten_quan" autocomplete="off" required class="form-control" placeholder="Tên Phường/xã">
                            </div>
                        </div>
                        <div class="col-sm-4 d-none">
                            <div class="form-group">
                                <label class="_required">Mã phường</label>
                                <input type="text" maxlength="20" name="ma_phuong" autocomplete="off" required class="form-control" placeholder="Mã xã/phường/thị trấn">
                            </div>
                        </div>
                        <div class="col-sm-4 d-none">
                            <div class="form-group">
                                <label class="_required">Tên phường</label>
                                <input type="text" maxlength="100" name="ten_phuong" autocomplete="off" class="form-control" placeholder="Tên xã/phường/thị trấn">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinMaTinh"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinMaTinh"><i class="fas fa-trash-alt"></i> Xóa</button>
                </div>
            </form>
        </div>
    </div>
</div>
<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section Scripts{
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/AdministrativeUnitsService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/AdministrativeUnits.js" asp-append-version="true"></script>
}
