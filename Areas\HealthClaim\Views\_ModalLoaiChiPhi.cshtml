﻿<div id="modalLoaiChiPhi" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn loại chi phí</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_LoaiChiPhi" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalLoaiChiPhiElementSelect">
                
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLoaiChiPhiDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer" style="height: 32px; border-top: 1px solid #eaeaea; margin:0px 0px 10px 0px;">
        @*<button type="button" class="btn-outline-primary btn-sm wd-90" id="btnBoChonLoaiChiPhi">
            <i class="fas fa-times mr-1"></i> Bỏ chọn
        </button>*@
        <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnChonLoaiChiPhi">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalLoaiChiPhiDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dslcp" id="dslcp_<%- item.ma_day_du %>" data-text="<%- item.ten_day_du.toLowerCase() %>">
        <input type="checkbox" id="loai_chi_phi_<%- item.ma_day_du %>" data-loai="<%- item.loai %>" value="<%- item.ma %>" class="custom-control-input modalChonLoaiChiPhiItem">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_chi_phi_<%- item.ma_day_du %>"><%- item.ten_day_du %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<div id="modalLoaiChiPhiBK" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5><span class="modal-drag-title">Chọn loại chi phí</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" id="inputSearch_LoaiChiPhiBK" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalLoaiChiPhiBKElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLoaiChiPhiBKDanhSach">
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="modalLoaiChiPhiBKDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dslcp" id="dslcp_<%- item.ma_day_du %>" data-text="<%- item.ten_day_du.toLowerCase() %>">
        <input type="checkbox" id="loai_chi_phi_bk_<%- item.ma_day_du %>" value="<%- item.ma %>" class="custom-control-input modalChonLoaiChiPhiItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_chi_phi_bk_<%- item.ma_day_du %>"><%- item.ten_day_du %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>