﻿@*  trình duy<PERSON>t b<PERSON><PERSON> thườ<PERSON> *@
<script type="text/html" id="gridTrinhDuyet_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" name="objTrinh" value="<%- JSON.stringify(item) %>" />
            <%- item.ngay_trinh %>
        </td>
        <td class="text-center"><%- item.nguoi_trinh %></td>
        <td class="text-center"><%- item.loai_ten %></td>
        <td class="text-center"><%- item.nguoi_duyet %></td>
        <td class="text-center"><%- item.nd %></td>
        <td class="text-center"><%- item.trang_thai_ten %></td>
        <td class="text-center">
            <a href="#" onclick="xoaTrinhDuyet('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.bt %>', 'GD')"><i class="fa fa-trash"></i></a>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Chưa có trình duyệt</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="templateTimKiemGara">
    <% if(ds_gara.length > 0){
    _.forEach(ds_gara, function(item,index) { %>
    <tr>
        <td class="text-center">
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" id="gara_<%- item.ma %>" value="<%- item.ma %>" onchange="chonGara('<%- item.ma %>')" class="custom-control-input input-tkiem-gara">
                <label class="custom-control-label" for="gara_<%- item.ma %>">&nbsp;</label>
            </div>
        </td>
        <td><%- item.ten %></td>
        <td><%- item.dia_chi %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="3">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>
<script type="text/html" id="templateDsHoSoGiayTo">
    <% if(ho_so_giay_to.length > 0){
    _.forEach(ho_so_giay_to, function(item,index) { %>
    <tr>
        <td style="text-align:left">
            <a href="#" data-field="ten" data-val="<%- item.ten %>" onclick="openModalImagesPaging('<%- item.ma_hs %>')"><%- item.ten %></a>
        </td>
        <td style="text-align:center">
            <% if(item.so_id_doi_tuong != '' && item.so_id_doi_tuong != null){ %>
            <a href="#" class="cursor-pointer combobox" id="doi_tuong_<%- item.ma_hs %>" onclick="showNhomDoiTuong(this)" data-field="so_id_doi_tuong" data-val="<%- item.so_id_doi_tuong %>">
                <i class="fas fa-user" title="Nhóm đối tượng"></i>
            </a>
            <% }else { %>
            <a class="cursor-pointer combobox" id="doi_tuong_<%- item.ma_hs %>" onclick="showNhomDoiTuong(this)" data-field="so_id_doi_tuong" data-val="<%- item.so_id_doi_tuong %>">
                <i class="fas fa-user" title="Nhóm đối tượng"></i>
            </a>
            <% } %>
        </td>
        <td style="text-align: center">
            <a href="#" data-field="trang_thai" data-val="<%- item.trang_thai %>" style="display:none">
                <%= item.trang_thai_ten %>
            </a>

            <% if((item.ngay_bs??'')!=='') { %>
            <input type="text" data-field="ngay_bs" class="floating-input datepicker text-center" value="<%- item.ngay_bs %>" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
            <% } else { %>
            <input type="hidden" data-field="ngay_bs" value="<%- item.ngay_bs %>">
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% }else{ %>
            <% if(item.hop_le == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" checked="checked" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
            <% } %>
        </td>
        <td style="text-align: center">
            <a href="#" data-field="loai" data-val="<%- item.loai %>" onclick="chonLoaiHSGT(this)" id="loai_hsgt_<%- item.ma_hs %>">
                <% if(item.loai != '' && item.loai != null){ %>
                <%- item.loai_ten %>
                <% }else{ %>
                Chọn loại hồ sơ
                <% } %>
            </a>
        </td>
        <% if(item.trang_thai =="C"){
        if(item.chon==1){
        if(item.trang_thai_xoa =='K'){ %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <% } else{%>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <% }} else { %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <% }} else{ %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <% } %>
        <td style="text-align: center">
            <% if(item.gara_thu_ho == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" checked="checked" id="gara_thu_ho_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_gara_thu_ho">
                <label class="custom-control-label" for="gara_thu_ho_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" id="gara_thu_ho_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_gara_thu_ho">
                <label class="custom-control-label" for="gara_thu_ho_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.ghi_chu != null && item.ghi_chu !=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td style="text-align: center"><%- item.nsd %></td>
        <td style="text-align: center"><%- item.nguon %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="bodyHoSoGiayToLoi_template">
    <% if(loi.length > 0){
    _.forEach(loi, function(item,index) { %>
    <tr>
        <td><%- item.ten_loi %></td>
        <td class="text-center"><%= item.kq_tudong %></td>
        <td class="text-center">
            <input type="checkbox" value="<%- item.ma_loi %>" class="chkLoi" <% if(item.kq_nsd == 'D'){ %>checked<% } %> />
        </td>
        <td class="text-center"><%- item.nsd %></td>
        <td class="text-center">
            <% if(item.kq_nsd != 'K'){ %>
            <a href="#" title="<%- item.loi %>">
                <i class="fas fa-file-alt"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="4">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>

@*  Danh sách ảnh*@
<script type="text/html" id="lstImage_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold" style="cursor:pointer;" data-img-cat="<%- iteml.loai_tai_lieu %>">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="pt-1" id="nhom_anh_<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p class="m-0 font-weight-bold">
            <a href="#" onclick="onToggleImg('<%- index %>')" data-img-cat-child="<%- iteml.loai_tai_lieu %>">
                <%- item.nhom %>
                <% if(item.ten_doi_tuong!=undefined && item.ten_doi_tuong!=null && item.ten_doi_tuong!=''){%>
                <br /><i style="font-size:10px">(<%- item.ten_doi_tuong %>)</i>
                <%}%>
            </a>
            <a href="#" class="text-warning">
                <% if(item.ma_file !=undefined && item.ma_file!=null && item.ma_file!='' && item.ma_file.includes('ANH_KHAC')){%>
                    <br /><i class="fas fa-exclamation-triangle"></i><span class="ml-1" style="font-size:10px; font-style:italic;">Hạng mục cần phân loại lại</span>
                <%}%>
            </a>
        </p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTiet(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img<%- image.bt %>" class="nhom_anh_ton_that_<%- index %>" value="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" name="ds_anh_xe">
            <p class="fileNameImage" style="cursor:pointer"><%- image.ten_file %></p>
            <% if('url_cap_don' in image){ %>
            <img data-original="<%- image.url_cap_don %>" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/default.png" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
        <div class="mx-1 my-2 d-none" id="getAnhThumnailPaging">
            <button type="button" class="btn btn-sm btn-block btn-outline-primary mb-2" onclick="getAnhThumnailPaging?.(false);">
                <i class="far fa-images mr-2"></i>Hiện thêm ảnh
            </button>
            <button type="button" class="btn btn-sm btn-block btn-outline-danger" onclick="getAnhThumnail?.();">
                <i class="far fa-exclamation-triangle mr-2"></i>Hiện tất cả
            </button>
        </div>
        <div class="my-2 d-none" id="khoiPhucAnhDaXoa">
            <hr />
            <button type="button" class="btn btn-sm btn-block btn-outline-primary mb-2" onclick="khoiPhucAnhDaXoa?.();">
                <i class="fas fa-trash-undo mr-2"></i>Khôi phục
            </button>
        </div>
    <% } %>
</script>

<script type="text/html" id="templateDsGiamDinhVienTheoDiaBan">
    <% if(ds_gdv.length > 0){
    _.forEach(ds_gdv, function(item,index) { %>
    <tr style="cursor:pointer" onclick="chonGiamDinhVien('<%- index + 1 %>','<%- item.ma_doi_tac %>','<%- item.ma_chi_nhanh %>','<%- item.gdv %>')">
        <td style="text-align:center;vertical-align: middle;">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gdv_<%- index + 1 %>" class="custom-control-input input-tkiem-gdv">
                <label class="custom-control-label" for="gdv_<%- index + 1 %>">&nbsp;</label>
            </div>
        </td>
        <td style="vertical-align: middle; text-align: center;"><%- item.ten %></td>
        <td style="vertical-align: middle; text-align: center;"><%- item.gdv %></td>
        <td style="vertical-align: middle; text-align: center;"><%- item.dthoai %></td>
        <td style="vertical-align: middle; text-align: center;"><%- item.sl_dang_giam_dinh %></td>
        <td style="vertical-align: middle; text-align: center;"><%- item.sl_ngay_htai %></td>
        <td style="vertical-align: middle; text-align: center;"><%- item.sl_ngay_truoc %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="7">Không có dữ liệu tìm kiếm</td>
    </tr>
    <% } %>
</script>
<script type="text/html" id="templateDropdownGDVHT">
    <% if(ds_ket_noi!==undefined && ds_ket_noi!==null && ds_ket_noi.length > 0){
    _.forEach(ds_ket_noi, function(item,index) {
    if(item.connected===1)
    {
    %>
    <a class="dropdown-item" onclick="chatGiamDinhVien(this,'<%- ma_doi_tac %>','<%- so_id %>','<%- item.dvi_gdinh %>','<%- item.ma_gdv %>','<%- item.ten_gdv %>' )" href="#">
        <span class="dot online" style="vertical-align:middle"></span> <%- item.ten_gdv %>
    </a>
    <%
    }
    else
    {
    %>
    <a class="dropdown-item" onclick="chatGiamDinhVien(this,'<%- ma_doi_tac %>','<%- so_id %>','<%- item.dvi_gdinh %>','<%- item.ma_gdv %>','<%- item.ten_gdv %>' )" href="#">
        <span class="dot offline" style="vertical-align:middle"></span> <%- item.ten_gdv %>
    </a>
    <%
    }
    })
    }else
    { %>
    <a class="dropdown-item" href="#">
        Không tìm thấy cán bộ giám định
    </a>
    <% } %>
</script>
<script type="text/html" id="templateItemViewImage">
    <% if(data_info!==undefined && data_info!==null && data_info.length > 0){
    _.forEach(data_info, function(item,index) {
    %>
    <a href="#" onclick="xemAnh('<%- item.so_id %>','<%- item.bt %>')" class="item-image-view"><img src="data:image/png;base64,<%- item.duong_dan %>" alt="<%- item.ten_file %>"></a>
    <% }) } %>
</script>


@*Vật chất xe*@
<script type="text/html" id="modalChiTietTonThatVCXTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten_hang_muc)%>">
        <td class="text-center"><%- index + 1 %></td>
        <td class="text-center"><%- item.ten_doi_tuong %></td>
        <td>
            <input type="hidden" class="floating-input" data-field="ten_hang_muc" value="<%- item.ten_hang_muc %>" />
            <input type="hidden" class="floating-input" data-field="ten_doi_tuong" value="<%- item.ten_doi_tuong %>" />
            <input type="hidden" class="floating-input" data-field="muc_do_ten" value="<%- item.muc_do_ten %>" />
            <input type="hidden" class="floating-input" data-field="thay_the_sc_ten" value="<%- item.thay_the_sc_ten %>" />
            <input type="hidden" class="floating-input" data-field="chinh_hang_ten" value="<%- item.chinh_hang_ten %>" />
            <input type="hidden" class="floating-input" data-field="thu_hoi_ten" value="<%- item.thu_hoi_ten %>" />
            <input type="hidden" class="floating-input" data-field="gia" value="<%- item.gia %>" />
            <input type="hidden" class="floating-input" data-field="gia_giam_dinh" value="<%- item.gia_giam_dinh %>" />
            <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" class="floating-input" data-field="so_id" value="<%- item.so_id %>" />
            <input type="hidden" class="floating-input" data-field="ma_chi_nhanh" value="<%- item.ma_chi_nhanh %>" />
            <input type="hidden" class="floating-input" data-field="ma_doi_tac" value="<%- item.ma_doi_tac %>" />
            <input type="hidden" class="floating-input" data-field="muc_do_tt_ai" value="<%- item.muc_do_tt_ai %>" />
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <a href="#" onclick="openModalImagesPaging('<%- item.hang_muc %>')"><%- item.ten_hang_muc %></a>
            <%if(item.tl_khop == 0){%>
                <a href="#" class="text-warning" onclick="openModalImagesPaging('<%- item.hang_muc %>')"><i class="fas fa-exclamation-triangle ml-2"></i><span class="ml-1" style="font-size:10px; font-style:italic;">Hạng mục chưa phân loại theo bộ mã danh mục</span> </a>
            <%}%>
            <%if(item.dgrr != undefined && item.dgrr != null && item.dgrr != "" && item.dgrr == "C"){%>
                <a href="#" class="text-warning" onclick="onXemHangMucDGRR('<%- item.hang_muc %>')"><i class="fas fa-exclamation-triangle ml-2"></i><span class="ml-1" style="font-size:10px; font-style:italic;">Có tổn thất cấp đơn</span> </a>
            <%}%>
        </td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-center"><%= item.thay_the_sc_ten %></td>
        <td class="text-center"><%= item.chinh_hang_ten %></td>
        <td class="text-center"><%= item.thu_hoi_ten %></td>
        @*  <td class="text-right"><a class="combobox" data-field="gia" data-val="<%- item.gia_giam_dinh %>"><%-  ESUtil.formatMoney(item.gia_giam_dinh) %></a></td> *@
        <td class="text-center">
            <a href="#"><i class="far fa-edit" title="Sửa thông tin" onclick="suaHMTT('<%- item.vu_tt%>', '<%- item.hang_muc%>', '<%- item.so_id%>', '<%- item.ma_chi_nhanh%>', '<%- item.ma_doi_tac%>', '<%- item.muc_do_tt_ai%>')"></i></a>
        </td>
        <td class="text-center">
            <a href="#"><i class="fas fa-trash-alt" title="Sửa thông tin" onclick="xoaHMTT('<%- item.vu_tt%>', '<%- item.hang_muc%>', '<%- item.so_id%>', '<%- item.ma_chi_nhanh%>', '<%- item.ma_doi_tac%>')"></i></a>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        @* <td></td> *@
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Hàng hóa trên xe*@
<script type="text/html" id="modalChiTietTonThatHANGHOATemplate">
    <% _.forEach(danh_sach, function(item, index) { %>
     <tr class="chiTietChiTietItem">
         <td class="text-center">
             <%- index + 1%>
         </td>
         <td class="text-left">
             <input type="hidden" class="floating-input" data-field="bt" value="<%- item.bt %>" />
             <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
             <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
             <input type="hidden" class="floating-input" data-field="so_luong" value="<%- item.so_luong %>" />
             <input type="hidden" class="floating-input" data-field="thay_the_sc" value="<%- item.thay_the_sc %>" />
             <input type="hidden" class="floating-input" data-field="chinh_hang" value="<%- item.chinh_hang %>" />
             <input type="hidden" class="floating-input" data-field="thu_hoi" value="<%- item.thu_hoi %>" />
             <input type="hidden" class="floating-input" data-field="ma_chi_phi" value="<%- item.ma_chi_phi %>" />
             <input type="hidden" class="floating-input" data-field="nguyen_nhan" value="<%- item.nguyen_nhan %>" />
             <%if(item.ma_chi_phi === "" || item.ma_chi_phi == null){%>
             <input type="text" class="floating-input" data-field="ten_chi_phi" data-val="<%- item.ten_chi_phi %>" placeholder="Tên hạng mục chi tiết" value="<%- item.ten_chi_phi %>" />
             <%}else{%>
             <a class="combobox" onclick="openModalImagesPaging('<%- item.ma_chi_phi%>')" href="#" data-field="ten_chi_phi" data-val="<%- item.ten_chi_phi %>"><%- item.ten_chi_phi %></a>
             <%}%>
         </td>
         <td class="text-center">
             <input type="hidden" class="floating-input muc_do_ten" data-field="muc_do_ten" value="<%- item.muc_do_ten %>" />
             <input type="text" class="floating-input combobox" data-field="muc_do" data-val="<%- item.muc_do %>" onclick="capNhatMucDoChiTietTonThat(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.muc_do_ten %>" style="text-align:center; cursor:pointer" />
         </td>
         <td class="text-center">
             <input type="hidden" class="floating-input dvi_tinh_ten" data-field="dvi_tinh_ten" value="<%- item.dvi_tinh_ten %>" />
             <input type="text" class="floating-input combobox" data-field="dvi_tinh" data-val="<%- item.dvi_tinh %>" onclick="capNhatDviTinhChiTietTonThat(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.dvi_tinh_ten %>" style="text-align:center; cursor:pointer" />
         </td>
         <td class="text-center">
             <input type="text" class="floating-input number" data-field="so_luong" data-val="<%- item.so_luong %>" placeholder="Số lượng" value="<%- item.so_luong %>" />
         </td>
          <td style="text-align:right">
             <input type="text" class="floating-input number" data-field="tien_bgia_vtu" data-val="<%- item.tien_bgia_vtu %>" placeholder="Tiền báo giá vật tư" value="<%- ESUtil.formatMoney(item.tien_bgia_vtu) %>" />
         </td>
         <td style="text-align:right">
             <input type="text" class="floating-input number" data-field="tien_bgia_nhan_cong" data-val="<%- item.tien_bgia_nhan_cong %>" placeholder="Tiền báo giá nhân công" value="<%- ESUtil.formatMoney(item.tien_bgia_nhan_cong) %>" />
         </td>
         <td style="text-align:right">
             <input type="text" class="floating-input number" data-field="tien_bgia_khac" data-val="<%- item.tien_bgia_khac %>" placeholder="Tiền báo giá khác" value="<%- ESUtil.formatMoney(item.tien_bgia_khac) %>" />
         </td>
         <td style="text-align:right">
             <input type="text" class="floating-input number" data-field="tien_vtu" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_vtu %>" placeholder="Tiền vật tư" value="<%- ESUtil.formatMoney(item.tien_vtu) %>" />
         </td>
         <td style="text-align:right">
             <input type="text" class="floating-input number" data-field="tien_nhan_cong" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_nhan_cong %>" placeholder="Tiền nhân công" value="<%- ESUtil.formatMoney(item.tien_nhan_cong) %>" />
         </td>
         <td style="text-align:right">
             <input type="text" class="floating-input number" data-field="tien_khac" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_khac %>" placeholder="Tiền khác" value="<%- ESUtil.formatMoney(item.tien_khac) %>" />
         </td>
         <td class="text-center">
             <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
             <a href="#" class="cursor-pointer combobox" onclick="showGhiChuChiPhiCT(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                 <i class="far fa-file-alt" title="Ghi chú"></i>
             </a>
             <% }else{ %>
             <a class="cursor-pointer combobox" onclick="showGhiChuChiPhiCT(this)" data-field="ghi_chu" data-val="">
                 <i class="far fa-file-alt" title="Ghi chú"></i>
             </a>
             <% } %>
         </td>
         <td class="text-center">
             <a href="#" onclick="xoaDongChiPhiChiTiet(this)"><i class="fas fa-trash-alt"></i></a>
         </td>
     </tr>
     <%})%>
     <% if(danh_sach.length < 7){
     for(var i = 0; i < 7 - danh_sach.length;i++ ){
     %>
     <tr>
         <td style="height:35.2px;"></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
         <td></td>
     </tr>
     <% }} %>
</script>
@*TNDS về tài sản*@
<script type="text/html" id="modalChiTietTonThatTNDS_TAI_SANTemplate">
    <% _.forEach(danh_sach, function(item, index) { %>
    <tr class="chiTietChiTietItem">
        <td class="text-center">
            <%- index + 1%>
        </td>
        <td class="text-left">
            <input type="hidden" class="floating-input" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input" data-field="so_luong" value="<%- item.so_luong %>" />
            <input type="hidden" class="floating-input" data-field="thay_the_sc" value="<%- item.thay_the_sc %>" />
            <input type="hidden" class="floating-input" data-field="chinh_hang" value="<%- item.chinh_hang %>" />
            <input type="hidden" class="floating-input" data-field="thu_hoi" value="<%- item.thu_hoi %>" />
            <input type="hidden" class="floating-input" data-field="ma_chi_phi" value="<%- item.ma_chi_phi %>" />
            <input type="hidden" class="floating-input" data-field="nguyen_nhan" value="<%- item.nguyen_nhan %>" />
            <%if(item.ma_chi_phi === "" || item.ma_chi_phi == null){%>
            <input type="text" class="floating-input" data-field="ten_chi_phi" data-val="<%- item.ten_chi_phi %>" placeholder="Tên hạng mục chi tiết" value="<%- item.ten_chi_phi %>" />
            <%}else{%>
            <a class="combobox" onclick="openModalImagesPaging('<%- item.ma_chi_phi%>')" href="#" data-field="ten_chi_phi" data-val="<%- item.ten_chi_phi %>"><%- item.ten_chi_phi %></a>
            <%}%>
        </td>
        <td class="text-center">
            <input type="hidden" class="floating-input muc_do_ten" data-field="muc_do_ten" value="<%- item.muc_do_ten %>" />
            <input type="text" class="floating-input combobox" data-field="muc_do" data-val="<%- item.muc_do %>" onclick="capNhatMucDoChiTietTonThat(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.muc_do_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <input type="hidden" class="floating-input dvi_tinh_ten" data-field="dvi_tinh_ten" value="<%- item.dvi_tinh_ten %>" />
            <input type="text" class="floating-input combobox" data-field="dvi_tinh" data-val="<%- item.dvi_tinh %>" onclick="capNhatDviTinhChiTietTonThat(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.dvi_tinh_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <input type="text" class="floating-input number" data-field="so_luong" data-val="<%- item.so_luong %>" placeholder="Số lượng" value="<%- item.so_luong %>" />
        </td>
         <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_bgia_vtu" data-val="<%- item.tien_bgia_vtu %>" placeholder="Tiền báo giá vật tư" value="<%- ESUtil.formatMoney(item.tien_bgia_vtu) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_bgia_nhan_cong" data-val="<%- item.tien_bgia_nhan_cong %>" placeholder="Tiền báo giá nhân công" value="<%- ESUtil.formatMoney(item.tien_bgia_nhan_cong) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_bgia_khac" data-val="<%- item.tien_bgia_khac %>" placeholder="Tiền báo giá khác" value="<%- ESUtil.formatMoney(item.tien_bgia_khac) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_vtu" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_vtu %>" placeholder="Tiền vật tư" value="<%- ESUtil.formatMoney(item.tien_vtu) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_nhan_cong" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_nhan_cong %>" placeholder="Tiền nhân công" value="<%- ESUtil.formatMoney(item.tien_nhan_cong) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_khac" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_khac %>" placeholder="Tiền khác" value="<%- ESUtil.formatMoney(item.tien_khac) %>" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChuChiPhiCT(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChuChiPhiCT(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" onclick="xoaDongChiPhiChiTiet(this)"><i class="fas fa-trash-alt"></i></a>
        </td>
    </tr>
    <%})%>
    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Con người*@
<script type="text/html" id="modalChiTietTonThatNGUOITemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td class="text-center"><%- index + 1%></td>
        <td>
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" class="floating-input" data-field="ten" placeholder="Tên đối tượng" required="" value="<%- item.ten %>" />
            <input type="hidden" class="floating-input" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <input type="hidden" class="floating-input" data-field="tien_vtu" value="<%- item.tien_vtu %>" />
            <input type="hidden" class="floating-input" data-field="tien_nhan_cong" value="<%- item.tien_nhan_cong %>" />
            <input type="hidden" class="floating-input" data-field="tien_khac" value="<%- item.tien_khac %>" />
            <input type="hidden" class="floating-input" data-field="muc_do" value="<%- item.muc_do %>" />
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi !=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td>
            <input type="text" class="floating-input" data-field="cmnd" required="" placeholder="CMT/CCCD" value="<%- item.cmnd %>" />
        </td>
        <% if(item.ds_thuong_tat == undefined || item.ds_thuong_tat == null || item.ds_thuong_tat == ''){%>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td>
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= item.ds_thuong_tat %></a>
        </td>
        <%}%>

        <td>
            <input type="text" class="floating-input number" readonly="readonly" onchange="tinhTongTienNguoi()" data-field="tien_tt" placeholder="Tiền tổn thất" required="" value="<%- ESUtil.formatMoney(item.tien_tt) %>" />
        </td>
        <td>
            <input type="text" class="floating-input number" data-field="tien_thoa_thuan" placeholder="Tiền thỏa thuận" required="" value="<%- ESUtil.formatMoney(item.tien_thoa_thuan) %>" />
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*TNDS về người*@
<script type="text/html" id="modalChiTietTonThatTNDSNGUOITemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td class="text-center"><%- index + 1%></td>
        <td>
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" class="floating-input" data-field="ten" placeholder="Tên đối tượng" required="" value="<%- item.ten %>" />
            <input type="hidden" class="floating-input" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <input type="hidden" class="floating-input" data-field="tien_vtu" value="<%- item.tien_vtu %>" />
            <input type="hidden" class="floating-input" data-field="tien_nhan_cong" value="<%- item.tien_nhan_cong %>" />
            <input type="hidden" class="floating-input" data-field="tien_khac" value="<%- item.tien_khac %>" />
            <input type="hidden" class="floating-input" data-field="muc_do" value="<%- item.muc_do %>" />
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi !=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td>
            <input type="text" class="floating-input" data-field="cmnd" required="" placeholder="CMT/CCCD" value="<%- item.cmnd %>" />
        </td>
        <% if(item.ds_thuong_tat == undefined || item.ds_thuong_tat == null || item.ds_thuong_tat == ''){%>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td>
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= item.ds_thuong_tat %></a>
        </td>
        <%}%>
        <td>
            <input type="text" class="floating-input number" readonly="readonly" onchange="tinhTongTienNguoi()" data-field="tien_tt" placeholder="Tiền tổn thất" required="" value="<%- ESUtil.formatMoney(item.tien_tt) %>" />
        </td>
        <td>
            <input type="text" class="floating-input number" data-field="tien_thoa_thuan" placeholder="Tiền thỏa thuận" required="" value="<%- ESUtil.formatMoney(item.tien_thoa_thuan) %>" />
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>

</script>
@*TNDS về hành khách*@
<script type="text/html" id="modalChiTietTonThatTNDSNGUOI_HKTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td class="text-center"><%- index + 1%></td>
        <td>
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" class="floating-input" data-field="ten" placeholder="Tên đối tượng" required="" value="<%- item.ten %>" />
            <input type="hidden" class="floating-input" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <input type="hidden" class="floating-input" data-field="tien_vtu" value="<%- item.tien_vtu %>" />
            <input type="hidden" class="floating-input" data-field="tien_nhan_cong" value="<%- item.tien_nhan_cong %>" />
            <input type="hidden" class="floating-input" data-field="tien_khac" value="<%- item.tien_khac %>" />
            <input type="hidden" class="floating-input" data-field="muc_do" value="<%- item.muc_do %>" />
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi !=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td>
            <input type="text" class="floating-input" data-field="cmnd" required="" placeholder="CMT/CCCD" value="<%- item.cmnd %>" />
        </td>
        <% if(item.ds_thuong_tat == undefined || item.ds_thuong_tat == null || item.ds_thuong_tat == ''){%>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td>
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= item.ds_thuong_tat %></a>
        </td>
        <%}%>
        <td>
            <input type="text" class="floating-input number" readonly="readonly" onchange="tinhTongTienNguoi()" data-field="tien_tt" placeholder="Tiền tổn thất" required="" value="<%- ESUtil.formatMoney(item.tien_tt) %>" />
        </td>
        <td>
            <input type="text" class="floating-input number" data-field="tien_thoa_thuan" placeholder="Tiền thỏa thuận" required="" value="<%- ESUtil.formatMoney(item.tien_thoa_thuan) %>" />
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Lái phụ xe*@
<script type="text/html" id="modalChiTietTonThatLPHU_XETemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td class="text-center"><%- index + 1%></td>
        <td>
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" class="floating-input" data-field="ten" placeholder="Tên đối tượng" required="" value="<%- item.ten %>" />
            <input type="hidden" class="floating-input" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <input type="hidden" class="floating-input" data-field="tien_vtu" value="<%- item.tien_vtu %>" />
            <input type="hidden" class="floating-input" data-field="tien_nhan_cong" value="<%- item.tien_nhan_cong %>" />
            <input type="hidden" class="floating-input" data-field="tien_khac" value="<%- item.tien_khac %>" />
            <input type="hidden" class="floating-input" data-field="muc_do" value="<%- item.muc_do %>" />
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi !=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td>
            <input type="text" class="floating-input" data-field="cmnd" required="" placeholder="CMT/CCCD" value="<%- item.cmnd %>" />
        </td>
        <% if(item.ds_thuong_tat == undefined || item.ds_thuong_tat == null || item.ds_thuong_tat == ''){%>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td>
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= item.ds_thuong_tat %></a>
        </td>
        <%}%>

        <td>
            <input type="text" class="floating-input number" readonly="readonly" onchange="tinhTongTienNguoi()" data-field="tien_tt" placeholder="Tiền tổn thất" required="" value="<%- ESUtil.formatMoney(item.tien_tt) %>" />
        </td>
        <td>
            <input type="text" class="floating-input number" data-field="tien_thoa_thuan" placeholder="Tiền thỏa thuận" required="" value="<%- ESUtil.formatMoney(item.tien_thoa_thuan) %>" />
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalThemTNDS_template">
    <% if(lstTNDS.length > 0){
    _.forEach(lstTNDS, function(item,index) { %>
    <tr>
        <td>
            <input type="text" class="floating-input ten" placeholder="Tên đối tượng" required="" value="<%- item.ten %>" />
        </td>
        <td>
            <input type="text" class="floating-input dchi" required="" placeholder="Tên địa chỉ" value="<%- item.dchi %>" />
        </td>
        <td>
            <input type="text" class="floating-input number sl" placeholder="Số lượng" required="" value="<%- ESUtil.formatMoney(item.sl) %>" />
        </td>
        <td>
            <input type="text" class="floating-input tinh_trang" onclick="chonTinhTrangTNDS(this, 'TAI_SAN')" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.tinh_trang_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td>
            <input type="text" class="floating-input number tien_tthat" placeholder="Tiền tổn thất" required="" value="<%- ESUtil.formatMoney(item.tien_tthat) %>" />
        </td>
        <td>
            <input type="text" class="floating-input ghi_chu" placeholder="Ghi chú" value="<%- item.ghi_chu %>" />
        </td>
        <td class="text-center">
            <a href="#" class="remove_config"><i class="fas fa-trash-alt" title="Xóa thông tin"></i></a>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td>
            <input type="text" class="floating-input ten" placeholder="Tên đối tượng" required="" />
        </td>
        <td>
            <input type="text" class="floating-input dchi" placeholder="Tên địa chỉ" required="" />
        </td>
        <td>
            <input type="text" class="floating-input number sl" placeholder="Số lượng" required="" />
        </td>
        <td>
            <input type="text" class="floating-input tinh_trang" onclick="chonTinhTrangTNDS(this, 'TAI_SAN')" readonly="readonly" required="" placeholder="Click chọn" style="text-align:center; cursor:pointer" />
            @*<select class="select2 form-control custom-select tinh_trang" required="" style="width:100%;height:36px;">
                    <option value="HONG" selected="selected">Hỏng</option>
                </select>*@
        </td>
        <td>
            <input type="text" class="floating-input number tien_tthat" placeholder="Tiền tổn thất" required="" />
        </td>
        <td>
            <input type="text" class="floating-input ghi_chu" placeholder="Ghi chú" />
        </td>
        <td class="text-center">
            <a href="#" class="remove_config"><i class="fas fa-trash-alt" title="Xóa thông tin"></i></a>
        </td>
    </tr>
    <% } %>
</script>
<script type="text/html" id="modalThemTNDSNguoi_template">
    <% if(lstTNDS.length > 0){
    _.forEach(lstTNDS, function(item,index) { %>
    <tr>
        <td>
            <input type="text" class="floating-input ten" placeholder="Tên đối tượng" required="" value="<%- item.ten %>" />
            <input type="hidden" class="floating-input number sl" required="" value="<%- ESUtil.formatMoney(item.sl) %>" />
        </td>
        <td>
            <input type="text" class="floating-input dchi" placeholder="Địa chỉ" required="" value="<%- item.dchi %>" />
        </td>
        <td>
            <input type="text" class="floating-input tinh_trang" onclick="chonTinhTrangTNDS(this, 'NGUOI')" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.tinh_trang_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" class="thuong_tat" onclick="danhGiaThuongTat(this)">
                Đánh giá thương tật (<%- item.thuong_tat==null?0: item.thuong_tat.split(',').length %>)
            </a>
        </td>
        <td>
            <input type="text" class="floating-input number tien_tthat" placeholder="Tiền tổn thất" required="" value="<%- ESUtil.formatMoney(item.tien_tthat) %>" />
        </td>

        <td>
            <input type="text" class="floating-input ghi_chu" placeholder="Ghi chú" value="<%- item.ghi_chu %>" />
        </td>
        <td class="text-center">
            <a href="#" class="remove_config"><i class="fas fa-trash-alt" title="Xóa thông tin"></i></a>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td>
            <input type="text" class="floating-input ten" placeholder="Tên đối tượng" required="" />
            <input type="hidden" class="floating-input number sl" value="1" required="" />
        </td>
        <td>
            <input type="text" class="floating-input dchi" placeholder="Địa chỉ" required="" />
        </td>
        <td>
            <input type="text" class="floating-input tinh_trang" onclick="chonTinhTrangTNDS(this, 'NGUOI')" readonly="readonly" required="" placeholder="Click chọn" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <a href="#" data-val="" class="thuong_tat" onclick="danhGiaThuongTat(this)">Đánh giá thương tật(0)</a>

        </td>
        <td>
            <input type="text" class="floating-input number tien_tthat" placeholder="Tiền tồn thất" required="" />
        </td>

        <td>
            <input type="text" class="floating-input ghi_chu" placeholder="Ghi chú" />
        </td>
        <td class="text-center">
            <a href="#" class="remove_config"><i class="fas fa-trash-alt" title="Xóa thông tin"></i></a>
        </td>
    </tr>
    <% } %>
</script>
<script type="text/html" id="dsBenThamGiaGiamDinhTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr>
        <td><%- item.moi_qh_ten%></td>
        <td><%- item.ten%></td>
        <td><%- item.dien_thoai%></td>
        <td><%- item.tham_gia_gd_hthi%></td>
        <td>
            <a onclick="suaBenThamGiaGiamDinh('<%- item.bt%>')" href="#">
                <i class="far fa-file-alt"></i>
            </a>
        </td>
        <td>
            <a onclick="xoaBenThamGiaGiamDinh('<%- item.bt%>')" href="#">
                <i class="fas fa-trash-alt"></i>
            </a>
        </td>
    </tr>
    <% })} %>
    <% if(danh_sach.length < 2){
    for(var i = 0; i < 2 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>

</script>
<script type="text/html" id="divPLHMHangMucTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox mt-1 div-plhm-hangmuc-item" id="div-plhm-hangmuc-item-<%- item.ma %>" plhm-hangmuc-loai="<%- item.loai %>" plhm-hangmuc-nhom="<%- item.nhom %>" plhm-hangmuc-nv="<%- item.nv %>">
        <input type="checkbox" id="plhm_hangmuc_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input plhm-hangmuc-item" onchange="plhmChonHangMuc(this, '<%- item.ma %>','<%- item.loai %>','<%- item.nhom %>','<%- item.nv %>')">
        <label class="custom-control-label cursor-pointer" for="plhm_hangmuc_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <%})}%>
</script>
<script type="text/html" id="divPLHMMucDoTemplate">
    <% if(nhom.length > 0){
    _.forEach(nhom, function(item_nhom,index_nhom) {%>
    <div id="plhm_nhom_muctt_<%- item_nhom.nhom %>">
        <p style="margin:unset; font-weight:bold"><%- item_nhom.ten %></p>
    </div>
    <% if(item_nhom.danh_sach.length > 0){
    _.forEach(item_nhom.danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox mt-1 div-plhm-mucdo-item" plhm-div-ma="<%- item.ma %>" plhm-mucdo-nv="<%- item.nv %>" plhm-mucdo-nhom="<%- item.nhom %>">
        <input type="checkbox" onchange="plhmChonMucDo(this, '<%- item.ma %>', '<%- item.ma_ct %>', '<%- item.nv %>', '<%- item.nhom %>','<%- item.pa_khac_phuc %>')" id="plhm_mucdo_<%- item_nhom.nhom %>_<%- index %>" plhm-mucdo-ma-ct="<%- item.ma_ct %>" plhm-mucdo-ma="<%- item.ma %>" plhm-mucdo-nhom="<%- item.nhom %>" value="<%- item.ma %>" plhm-mucdo-nv="<%- item.nv %>" plhm-phuong-an="<%- item.pa_khac_phuc %>" class="custom-control-input plhm-mucdo-item">
        <label class="custom-control-label cursor-pointer" for="plhm_mucdo_<%- item_nhom.nhom %>_<%- index %>"><%- item.ten %></label>
    </div>
    <% if(item.children != undefined && item.children != null && item.children.length >0) {%>
    <div plhm-mucdo-nhom="<%- item.ma_ct %>" class="plhm_mucdo_con">
        <% _.forEach(item.children, function(item_children,index_children) {
        %>
        <div class="custom-control custom-checkbox mt-1 div-plhm-mucdo-item" plhm-div-ma="<%- item_children.ma %>" plhm-mucdo-nv="<%- item_children.nv %>" plhm-mucdo-nhom="<%- item.nhom %>">
            <input type="checkbox" onchange="plhmChonMucDo(this, '<%- item_children.ma %>', '<%- item_children.ma_ct %>', '<%- item_children.nv %>', '<%- item_children.nhom %>','<%- item_children.pa_khac_phuc %>')" id="plhm_mucdo_<%- item_nhom.nhom %>_<%- index %>_<%- index_children %>" plhm-mucdo-ma-ct="<%- item_children.ma_ct %>" plhm-mucdo-ma="<%- item_children.ma %>" plhm-mucdo-nhom="<%- item.nhom %>" plhm-mucdo-nv="<%- item_children.nv %>" plhm-phuong-an="<%- item_children.pa_khac_phuc %>" value="<%- item_children.ma %>" class="custom-control-input plhm-mucdo-item">
            <label class="custom-control-label cursor-pointer" for="plhm_mucdo_<%- item_nhom.nhom %>_<%- index %>_<%- index_children %>" style="font-style:italic"><%- item_children.ten %></label>
        </div>
        <%
        })%>
    </div>
    <%}})}})}%>
</script>

@*  Danh sách ảnh phân loại *@
@* <script type="text/html" id="divPLHMHinhAnhtemplate">
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <div style="margin-bottom: 2px;padding-left: 5px;font-weight: bold;float:left;width:100%;">
        <a href="#" onclick="plhmChonNhomAnh('<%- item.ma_file %>')"><%- item.nhom %></a>
    </div>
    <div>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div class="divAnhPhanLoai" data-ma-file="<%- item.ma_file %>" data-bt="<%- image.bt %>" id="divAnhPhanLoai_<%- image.bt %>" onclick="plhmChonAnh('<%- image.bt %>', '<%- item.ma_file %>')" ondblclick="plhmDblChonAnh('<%- image.bt %>')">
            <% if('url_cap_don' in image){ %>
            <img data-original="<%- image.url_cap_don %>" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/default.png" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
            <div class="divAnhPhanLoaiCheck">
                <i class="fas fa-check-circle"></i>
            </div>
        </div>
        <% }) %>
    </div>
    <% })} %>
</script> 
  *@
@* 
<script type="text/html" id="tbDsPhanLoaiNhanhTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr id="plhm_hang_muc_phan_<%- item.vu_tt %>_<%- item.hang_muc %>" class="plhm_hang_muc_phan" onclick="plhmChonHangMucDaPhan('<%- item.hang_muc %>','<%- item.vu_tt %>')">
        <td><a href="#"><%- item.ten_hang_muc %></a></td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-center">
            <%if(item.thay_the_sc=='T'){ %>
            <i class="fas fa-tools mr-2 icon_plhm active"></i>
            <%}else{%>
            <i class="fas fa-tools mr-2 icon_plhm"></i>
            <%} %>
            <%if(item.chinh_hang=='C'){ %>
            <i class="fad fa-car-garage mr-2 icon_plhm active"></i>
            <%}else{%>
            <i class="fad fa-car-garage mr-2 icon_plhm"></i>
            <%} %>
            <%if(item.thu_hoi=='C'){ %>
            <i class="fas fa-inbox-in icon_plhm active"></i>
            <%}else{%>
            <i class="fas fa-inbox-in icon_plhm"></i>
            <%} %>
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.gia_giam_dinh) %></td>
    </tr>
    <%})}%>
</script> *@

<script type="text/html" id="tblDanhSachVuTonThatTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr style="cursor: pointer" id="ds_vu_ton_that_<%- item.vu_tt %>" class="ds_vu_ton_that" onclick="xemChiTietVuTonThat('<%- item.vu_tt %>')">
        <td>
            <% if(item.pham_vi == "0"){
            %>
            <i class="fas fa-exclamation-triangle mr-2" style="color:#dc3545 !important"></i> <span><%- item.gio_xr %></span>
            <%}
            else
            {%>
            <span><%- item.gio_xr %></span>
            <%}%>
        </td>
        <td><%- item.ngay_xr %></td>
    </tr>
    <%})}%>
</script>

<script type="text/html" id="modalCarSearchDsGCNTemplate">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(item,index) { %>
    <tr style="cursor: pointer" data_ma_chi_nhanh="<%- item.ma_chi_nhanh %>" data_nv="<%- item.nghiep_vu %>" data_so_id="<%- item.so_id_hdong %>" data_so_id_dt="<%- item.so_id_gcn %>" data_loai="<%- item.loai_gcn %>" class="tkiem_kh" id="tkiem_kh_<%- item.so_id_hdong %>_<%- item.so_id_gcn %>_<%- item.loai_gcn %>" onclick="onChonKhachHang('<%- item.so_id_hdong %>','<%- item.so_id_gcn %>','<%- item.loai_gcn %>')">
        <td class="text-center"><%- item.so_hdong %></td>
        <td class="text-center"><%- item.bien_so_xe %></td>
        <td class="text-center">
            <a href="#" onclick="xemThongTinGiayChungNhan('<%- item.ma_chi_nhanh %>', '<%- item.so_id_hdong %>', '<%- item.so_id_gcn %>', '<%- item.nghiep_vu %>', '<%- item.so_gcn %>')"><%- item.so_gcn %></a>
        </td>
        <td class="text-center">
            <%if(item.loai_gcn=="TN"){%>
            <span>Tự nguyện</span>
            <%}%>

            <%if(item.loai_gcn=="BB"){%>
            <span>Bắt buộc</span>
            <%}%>

            <%if(item.loai_gcn=="CH"){%>
            <span>Tự nguyện & Bắt buộc</span>
            <%}%>
        </td>
        <td class="text-center"><%- item.ngay_hl_bh %> - <%- item.ngay_kt_bh %></td>
        <td><%- item.ten_chu_xe %></td>
        <td class="text-center"><%- item.ten_chi_nhanh %></td>
    </tr>
    <%})}%>
    <% if(data_info.length < 4){
    for(var i = 0; i < 4 - data_info.length;i++ ){ %>
    <tr style="cursor: pointer">
        <td style="height:34.6px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <%}}%>
</script>

<script type="text/html" id="danh_gia_hien_truong_template">
    <% if(kieu.length > 0){
    _.forEach(kieu, function(item,index) { %>
    <% if(item.kieu == 'RADIO'){ %>
    <div class="col-6 mb-2">
        <div class="form-group">
            <% if(item.bat_buoc_nhap == 1){ %>
            <label class="_required"><%- item.ten_loai %></label>
            <div class="row">
                <% _.forEach(data, function(item1,index1){ %>
                <% if(item1.loai == item.loai){ %>
                <div class="form-check form-check-inline ml-3" style="margin-right:unset">
                    <input class="form-check-input material-inputs with-gap" type="radio" required name="<%- item1.loai %>" id="<%- item1.ma %>" value="<%- item1.ma %>">
                    <label class="form-check-label" style="font-size:12px;" for="<%- item1.ma %>"><%- item1.ten %></label>
                </div>
                <% } %>
                <% }) %>
            </div>
            <% } else { %>
            <label class=""><%- item.ten_loai %></label>
            <div class="row">
                <% _.forEach(data, function(item1,index1){ %>
                <% if(item1.loai == item.loai){ %>
                <div class="form-check form-check-inline ml-3" style="margin-right:unset">
                    <input class="form-check-input material-inputs with-gap" type="radio" name="<%- item1.loai %>" id="<%- item1.ma %>" value="<%- item1.ma %>">
                    <label class="form-check-label" style="font-size:12px;" for="<%- item1.ma %>"><%- item1.ten %></label>
                </div>
                <% } %>
                <% }) %>
            </div>
            <% } %>
        </div>
    </div>
    <% }else if(item.kieu == 'CHECKBOX'){ %>
    <div class="col-6 mb-2">
        <div class="form-group">
            <% if(item.bat_buoc_nhap == 1){ %>
            <label class="_required"><%- item.ten_loai %></label>
            <div class="row">
                <% _.forEach(data, function(item1,index1){ %>
                <% if(item1.loai == item.loai){ %>
                <div class="custom-control custom-checkbox ml-3">
                    <input class="custom-control-input" type="checkbox" required name="<%- item1.loai %>" col-name="<%- item1.ten_loai %>" id="<%- item1.ma %>" value="<%- item1.ma %>">
                    <label class="custom-control-label" for="<%- item1.ma %>" style="font-size:12px"><%- item1.ten %></label>
                </div>
                <% } %>
                <% }) %>
            </div>
            <% }else{ %>
            <label class=""><%- item.ten_loai %></label>
            <div class="row">
                <% _.forEach(data, function(item1,index1){ %>
                <% if(item1.loai == item.loai){ %>
                <div class="custom-control custom-checkbox ml-3">
                    <input class="custom-control-input" type="checkbox" name="<%- item1.loai %>" col-name="<%- item1.ten_loai %>" id="<%- item1.ma %>" value="<%- item1.ma %>">
                    <label class="custom-control-label" for="<%- item1.ma %>" style="font-size:12px"><%- item1.ten %></label>
                </div>
                <% } %>
                <% }) %>
            </div>
            <% } %>
        </div>
    </div>
    <% }else if(item.kieu == 'TEXTAREA'){ %>
    <% if(item.kieu_loai == 'col4'){ %>
    <div class="col-4 mb-2">
        <div class="form-group">
            <% if(item.bat_buoc_nhap == 1){ %>
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <textarea class="form-control" name="<%- item.loai %>" required col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>" rows="3" spellcheck="false"></textarea>
            </div>
            <% }else{ %>
            <label><%- item.ten_loai %></label>
            <div class="input-group">
                <textarea class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>" rows="3" spellcheck="false"></textarea>
            </div>
            <% } %>
        </div>
    </div>
    <% } else if(item.kieu_loai == 'col12'){ %>
    <div class="col-12 mb-2">
        <div class="form-group">
            <% if(item.bat_buoc_nhap == 1){ %>
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <textarea class="form-control" name="<%- item.loai %>" required col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>" rows="3" spellcheck="false"></textarea>
            </div>
            <% }else{ %>
            <label><%- item.ten_loai %></label>
            <div class="input-group">
                <textarea class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>" rows="3" spellcheck="false"></textarea>
            </div>
            <% } %>
        </div>
    </div>
    <% } %>
    <% } else if(item.kieu == 'TEXT'){ %>
    <div class="col-4 mb-2">
        <div class="form-group">
            <% if(item.bat_buoc_nhap == 1){ %>
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" autocomplete="off" class="form-control" required name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>">
            </div>
            <% }else{ %>
            <label class=""><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" autocomplete="off" class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>">
            </div>
            <% } %>
        </div>
    </div>
    <% } else if(item.kieu == 'NUMBER'){ %>
    <% if(item.an_hien == 1){ %>
    <div class="col-4 mb-2">
        <div class="form-group">
            <% if(item.bat_buoc_nhap == 1){ %>
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" autocomplete="off" class="form-control number" required name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>">
            </div>
            <% }else{ %>
            <label class=""><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" autocomplete="off" class="form-control number" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>">
            </div>
            <% } %>
        </div>
    </div>
    <% } %>
    <%
    }%>
    <% })}%>
</script>

<script type="text/html" id="bao_cao_giam_dinh_template">
    <% if(kieu.length > 0){ %>
    <% _.forEach(kieu, function(item,index) { %>
    <% if(item.kieu == 'CHECKBOX'){ %>
    <div class="col-6">
        <a href="#"><%- item.ten_loai %></a>
    </div>
    <% _.forEach(data, function(item1,index1){ %>
    <% if(item1.loai == item.loai){ %>
    <div class="col-3">
        <div class="custom-control custom-checkbox">
            <input type="checkbox" name="<%- item1.loai %>" id="<%- item1.ma %>" value="<%- item1.ma %>" class="custom-control-input <%- item1.loai %> single_checked">
            <label class="custom-control-label" for="<%- item1.ma %>" style="cursor:pointer; padding-top:2px"><%- item1.ten %></label>
        </div>
    </div>
    <% } %>
    <% }) %>
    <% } else if(item.kieu == 'TEXT'){ %>
    <% if(item.kieu_loai == 'col12'){ %>
    <% if(item.bat_buoc_nhap == 1){ %>
    <div class="col-12">
        <div class="form-group">
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" required col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% }else{ %>
    <div class="col-12">
        <div class="form-group">
            <label><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } %>
    <% }else if(item.kieu_loai == 'col9'){ %>
    <% if(item.bat_buoc_nhap == 1){ %>
    <div class="col-9">
        <div class="form-group">
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" required col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% }else{ %>
    <div class="col-9">
        <div class="form-group">
            <label><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } %>
    <% }else if(item.kieu_loai == 'col6'){ %>
    <% if(item.bat_buoc_nhap == 1){ %>
    <div class="col-6">
        <div class="form-group">
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" required col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% }else{ %>
    <div class="col-6">
        <div class="form-group">
            <label><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } %>
    <% }else if(item.kieu_loai == 'col3'){ %>
    <% if(item.bat_buoc_nhap == 1){ %>
    <div class="col-3">
        <div class="form-group">
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" required col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% }else{ %>
    <div class="col-3">
        <div class="form-group">
            <label><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } %>
    <% } %>
    <%} else if (item.kieu == 'INPUT_DISABLED') {%>
    <% if(item.kieu_loai == 'col6'){ %>
    <% if(item.bat_buoc_nhap == 1){ %>
    <div class="col-6">
        <div class="form-group">
            <label for="" class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" class="form-control cursor-pointer" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" data-ma="" autocomplete="off" placeholder="Click chọn gara sửa chữa" onclick="chonGaraSuaChua(this)" style="background-color: #e9ecef;">
                <div class="input-group-append">
                    <label class="input-group-text">
                        <a href="#" onclick="xoaChonGara(this)">
                            <i class="fas fa-times" aria-hidden="true" title="Xóa chọn gara sửa chữa"></i>
                        </a>
                    </label>
                </div>
            </div>
        </div>
    </div>
    <% }else{ %>
    <div class="col-6">
        <div class="form-group">
            <label for=""><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" class="form-control cursor-pointer" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" data-ma="" autocomplete="off" placeholder="Click chọn gara sửa chữa" onclick="chonGaraSuaChua(this)" style="background-color: #e9ecef;">
                <div class="input-group-append">
                    <label class="input-group-text">
                        <a href="#" onclick="xoaChonGara(this)">
                            <i class="fas fa-times" aria-hidden="true" title="Xóa chọn gara sửa chữa"></i>
                        </a>
                    </label>
                </div>
            </div>
        </div>
    </div>
    <%}%>
    <%}else if (item.kieu_loai == 'col3'){%>
    <% if(item.bat_buoc_nhap == 1){ %>
    <div class="col-3">
        <div class="form-group">
            <label class="_required"><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" class="form-control number" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" data-ma="" autocomplete="off" required>
            </div>
        </div>
    </div>
    <% }else{ %>
    <div class="col-3">
        <div class="form-group">
            <label class=""><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" data-ma="" autocomplete="off" style="background-color: #e9ecef;">
            </div>
        </div>
    </div>
    <%}%>
    <%}%>
    <% }else if(item.kieu == 'NUMBER'){ %>
    <div class="col-3">
        <div class="form-group">
            <label class=""><%- item.ten_loai %></label>
            <div class="input-group">
                <input type="text" maxlength="15" autocomplete="off" class="form-control number placeholder-left" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } else if(item.kieu == 'TEXTAREA'){ %>
    <% if(item.kieu_loai == 'col12'){ %>
    <div class="col-12">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <label class="_required"><%- item.ten_loai %></label>
            <textarea class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <label><%- item.ten_loai %></label>
            <textarea class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% }else if(item.kieu_loai == 'col6'){ %>
    <div class="col-6">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <label class="_required"><%- item.ten_loai %></label>
            <textarea class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <label><%- item.ten_loai %></label>
            <textarea class="form-control" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" col-type="<%- item.kieu %>" col-required="<%- item.bat_buoc_nhap%>" maxlength="1000" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% } %>
    <% } %>
    <% }) %>
    <% }%>
</script>


<script type="text/html" id="navDanhGiaNghiepVuTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.doi_tuong %>" data-lhnv="<%- item.ma %>" data-hang-muc="<%- item.hang_muc %>"><a href="#" onclick="xemChiTietDTTonThat('<%- item.ma %>', '<%- item.nhom %>', '<%- item.doi_tuong %>', '<%- item.hang_muc %>')"><%- item.ten %></a></li>
    <%})}%>
</script>

<script type="text/html" id="dsHinhAnhHangMucTemplate">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <div class="imagesCategory" style="display: table-row" data-search="<%- ESUtil.xoaKhoangTrangText(item.nhom)%>">
        <p style="margin-bottom:5px;" class="font-weight-bold">
            <a href="#">
                <%if(item.loai == "TT"){%>
                <%- item.nhom %> <i style="font-size: 10px">(<%- item.muc_do_ten %>/<%-item.thay_the_sc_ten%>)</i><br />
                <i style="font-size: 10px">(<%- item.ten_doi_tuong %>)</i>
                <%}else{%>
                <%- item.nhom %><br />
                <i style="font-size: 10px">(<%- item.ten_doi_tuong %>)</i>
                <%}%>
            </a>
        </p>
        <p style="margin-bottom:5px;" class="font-weight-bold">
            <a href="javascript:void(0);" class="btn btn-sm btn-outline-primary" style="padding: 0.1rem 0.25rem;" onclick="bsAnhHMTTDaGD?.('<%- item.ma_file %>', '<%- item.so_id_doi_tuong %>')">
                <i class="fas fa-fw fa-plus mr-1"></i>Bổ sung hình ảnh
            </a>
        </p>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div style="width:60px; height:60px; margin-right:10px; margin-bottom:5px; cursor:pointer;float:left;">
            <img style="width: 100%; height:100%; border-radius:10px" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
        </div>
        <% }) %>
    </div>
    <% }) %>
    <% } %>
</script>

<script type="text/html" id="danhSachNV_template">
    <% if(lhnv.length > 0){
    _.forEach(lhnv, function(item,index) { %>
    <tr class="row-item" row-val="<%- item.ma %>" row-vcx="<%- item.vcx %>">
        <td class="text-center"><%- index+1 %></td>
        <td><%- item.ten_lhnv %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <% if(item.vcx == 'VCX'){ %>
        <%if(item.ktru=="K"){%>
        <td class="text-center">Không</td>
        <td class="text-right"><%- ESUtil.formatMoney(item.mien_thuong) %></td>
        <% } else { %>
        <td class="text-center">Có</td>
        <td class="text-right"><%- ESUtil.formatMoney(item.mien_thuong) %></td>
        <% } %>
        <% } else{ %>
        <td class="text-center">Không</td>
        <td class="text-right"><%- item.mien_thuong %></td>
        <% } %>
        <td>
            <%- item.dkbs %>
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.phi) %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="7">
            <p class="m-0">Chưa có dữ liệu hiển thị</p>
        </td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="navDoiTuongTNDSTaiSanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.so_id_doi_tuong %>"><a href="#" onclick="xemChiTietDTTonThatTaiSan('<%- item.nhom %>', '<%- item.loai%>', '<%- item.so_id_doi_tuong %>')"><%- item.ten_doi_tuong %></a></li>
    <%})}%>
</script>

<script type="text/html" id="dsDoiTuongTTTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="item_so_id_doi_tuong" data-so-id-doi-tuong="<%- item.so_id_doi_tuong %>" style="cursor: pointer; text-align:center;">
        <td>
            <% if(item.kieu_dt != undefined && item.kieu_dt != null && item.kieu_dt == "BH"){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="doi_tuong_tt_<%- item.so_id_doi_tuong %>" disabled="disabled" value="<%- item.so_id_doi_tuong %>" data-val="<%- item.ten_doi_tuong %>" class="custom-control-input input_chon_doi_tuong_tt">
                <label class="custom-control-label" for="doi_tuong_tt_<%- item.so_id_doi_tuong %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="doi_tuong_tt_<%- item.so_id_doi_tuong %>" value="<%- item.so_id_doi_tuong %>" data-val="<%- item.ten_doi_tuong %>" class="custom-control-input input_chon_doi_tuong_tt">
                <label class="custom-control-label" for="doi_tuong_tt_<%- item.so_id_doi_tuong %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td onclick="chonDoiTuongTT('<%- item.so_id_doi_tuong %>',)" id="ds_doi_tuong_tt_<%- item.so_id_doi_tuong %>" class="ds_doi_tuong_tt"><%- item.ten_doi_tuong %></td>
        <td onclick="chonDoiTuongTT('<%- item.so_id_doi_tuong %>',)" class="text-center"><%- item.ten_nhom %></td>
        <td onclick="chonDoiTuongTT('<%- item.so_id_doi_tuong %>',)" class="text-center"><%- item.loai %></td>
        <td class="text-center">
            <a href="#" onclick="suaDoiTuongTT('<%- item.so_id_doi_tuong %>')"><i class="fa fa-edit"></i></a>
        </td>
        <td class="text-center">
            <a href="#" onclick="xoaDoiTuongTT(this, '<%- item.so_id_doi_tuong %>')"><i class="fas fa-trash-alt"></i></a>
        </td>
    </tr>
    <%})}%>
    <% if(data.length < 5){
    for(var i = 0; i < 5 - data.length;i++ ){ %>
    <tr style="cursor: pointer">
        <td style="height:34.6px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <%}}%>
</script>
@*Template chi phí lần giám định*@
<script type="text/html" id="danhSachChiPhiGDLanTemplate">
    <% if(data.length > 0) {
    _.forEach(data, function(item, index) {%>
    <tr class="row-item">
        <td class="text-center cursor-pointer">
            <%if(item.ma_cp !== null &&  item.ma_cp !== ''){%>
            <a href="#" data-field="ma_cp" data-val="<%- item.ma_cp %>" onclick="chonLoaiChiPhiLanGD(this)"><%- item.ten_loai_chi_phi %></a>
            <%}else{%>
            <a href="#" data-field="ma_cp" data-val="<%- item.ma_cp %>" onclick="chonLoaiChiPhiLanGD(this)">Chọn loại chi phí</a>
            <%}%>
        </td>
        <td class="text-left">
            <%if(item.ma_cp == 'KHAC') {%>
            <input type="text" data-field="ten_cp" class="floating-input" value="<%- item.ten_cp %>" placeholder="Tên chi phí" />
            <%}else {%>
            <input type="text" data-field="ten_cp" class="floating-input form-control" value="<%- item.ten_cp %>" readonly placeholder="Tên chi phí" />
            <%}%>
        </td>
        <td class="text-right"><input type="text" data-field="tien" class="number floating-input" value="<%- ESUtil.formatMoney(item.tien) %>" onkeyup="tinhTongTienChiPhiLanGD();" placeholder="Số tiền" /></td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu !=''){ %>
            <a href="#" class="cursor-pointer combobox" data-field="ghi_chu" onclick="showGhiChuChiPhiGD(this)" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChuChiPhiGD(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" data-field="bt" data-val="<%- item.bt%>">
                <i class="fa fa-times text-danger cursor-pointer" onclick="xoaChiPhiLanGD(this)"></i>
            </a>
        </td>
    </tr>
    <%})}%>
    <% if(data.length < 8){
    for(var i = 0; i < 8 - data.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalDanhSachLoaiChiPhiLanGDTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dslcp" id="dslcp_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="loai_chi_phi_<%- item.ma %>" data-field="ma" value="<%- item.ma %>" class="custom-control-input item-chiphi modalChonLoaiChiPhiLanGDItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="loai_chi_phi_<%- item.ma %>"><%- item.ten %> </label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="titleUpdateContractTemplate">
    <% if(ho_so.so_hs!= null && ho_so.so_hs.trim()!= ""){%>
    <h4 class="esmodal-title">
        Hồ sơ: <a href="#" onclick="copyText(this)"><%- ho_so.so_hs %></a> - <span><%- ho_so.nsd %></span> - <span class="text-color"><%- ho_so.trang_thai %></span> - <a href="#" onclick="xemToanBoThongTinHoSoBoiThuong('<%- ho_so.ma_doi_tac %>','<%- ho_so.ma_chi_nhanh_ql %>','<%- ho_so.ma_chi_nhanh %>','<%- ho_so.so_id %>','<%- ho_so.so_id_hd%>','<%- ho_so.so_id_dt%>')">Xem chi tiết hồ sơ</a>
    </h4>
    <% }else{ %>
    <h4 class="esmodal-title">
        Hồ sơ: <a href="#" onclick="laySoHoSo()">Lấy số hồ sơ</a> - <span><%- ho_so.nsd %></span> - <span class="text-color"><%- ho_so.trang_thai %></span> - <a href="#" onclick="xemToanBoThongTinHoSoBoiThuong('<%- ho_so.ma_doi_tac %>','<%- ho_so.ma_chi_nhanh_ql %>','<%- ho_so.ma_chi_nhanh %>','<%- ho_so.so_id %>','<%- ho_so.so_id_hd%>','<%- ho_so.so_id_dt%>')">Xem chi tiết hồ sơ</a>
    </h4>
    <% } %>
</script>

<script type="text/html" id="danhSachCanhBaoTemplate">
    <% if(danh_sach.so_luong_cb > 0){%>
    <div class="esmodal-title d-flex justify-content-between">
        <ul class="navbar-nav float-right">
            <li class="nav-item dropdown">
                <a style="padding: 0 !important" class="nav-link dropdown" href="javascript:void(0)" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span style="color: #ff8c0096; font-size: 18px;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </span>
                    <i style="font-size: 15px;" class="text-danger">Có <span class="text-danger"><%- danh_sach.so_luong_cb%></span> thông báo cảnh báo</i>
                </a>
                <div class="dropdown-menu mailbox dropdown-menu-left scale-up pt-0"
                     style="margin-top: 21px; border: 1px solid #ccc; width: 255px !important; margin-left: -33px !important; transform: translate3d(0px, 43px, 0px) !important; height: 100vh; ">
                    <ul class="list-style-none">
                        <li>
                            <div class="border-bottom rounded-top p-2 text-center" style="background-color: #edeff0 ">
                                <h5 class="font-weight-medium mb-0">Danh sách thông báo cảnh báo</h5>
                            </div>
                        </li>
                        <li>
                            <div class="message-center message-body position-relative" id="canhBao">

                            </div>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
        <button type="button" class="close" onclick="hideModalBoiThuong()" aria-hidden="true">×</button>
    </div>
    <% }else{ %>
    <button type="button" class="close py-0 mt-1" onclick="hideModalBoiThuong()" aria-hidden="true">×</button>
    <% } %>
</script>

@*Xem danh sách thông báo cảnh báo*@
<script type="text/html" id="canhBao_template">
    <%if(danh_sach.length > 0){ %>
    <%_.forEach(danh_sach, function(item,index) { %>
    <a href="javascript:void(0)" id="notify-item-<%- item.so_id %>" class="message-item d-flex align-items-center border-bottom p-1">
        <span class="position-relative d-inline-block">
            <span class="dot n-noi-dung-tin-nhan-dot"></span>
        </span>
        <div class="d-inline-block v-middle pl-2">
            <p class="font-12 mb-0 mt-1 text-dark font-weight-bold"><%- item.noi_dung %></p>
            <span class="font-10 text-nowrap d-block text-muted"><%- item.ngay %></span>
        </div>
    </a>
    <% })} else { %>
    <a href="#" class="message-item d-flex align-items-center border-bottom p-1">
        <div class="d-inline-block v-middle pl-2" style="margin:0 auto">
            <p class="font-12 mb-0 mt-1 text-dark">Không có cảnh báo</p>
        </div>
    </a>
    <% } %>
</script>

@*Xác minh phí khi lấy số hồ sơ*@
<script type="text/html" id="dsXacMinhPhiLaySoHS_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien_da_tt) %>
        </td>
        <td class="<%- item.style %>"><%= item.ghi_chu %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="3">
            <p class="m-0">Chưa có dữ liệu hiển thị</p>
        </td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="dsUocTonThatNVLaySoHS_template">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="row-item">
        <td class="text-center">
            <div class="custom-control custom-checkbox ml-2">
                <input type="checkbox" data-field="checked_lhnv" value="<%- item.lh_nv%>" id="lhnv_<%- item.lh_nv%>" <% if(item.checked_lhnv == 'C'){ %>checked<% } %> class="checkbox custom-control-input lhnv_item single_checked">
                <label class="custom-control-label" for="lhnv_<%- item.lh_nv%>">&nbsp;</label>
            </div>
        </td>
        <td>
            <input type="hidden" data-field="lh_nv" value="<%- item.lh_nv %>" />
            <a href="#" data-field="ten_lhnv" data-val="<%- item.ten_lhnv %>"><%- item.ten_lhnv %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="uoc_ton_that" class="number floating-input" value="<%- ESUtil.formatMoney(item.uoc_ton_that) %>" />
        </td>
        <td>
            <input type="text" max="10" maxlength="3" data-field="tl_thue" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.tl_thue) %>" />
        </td>
        <td class="text-right">
            <a href="#" data-field="tien_thue">0</a>
        </td>
        <td class="text-right">
            <a href="#" data-field="tong_cong">0</a>
        </td>
    </tr>
    <% })}%>
</script>

@*Báo giá giám định*@
<script type="text/html" id="garaBaoGia_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(bao_gia,index) { %>
    <tr data-ma-gara="<%- bao_gia.gara %>" data-so-id-doi-tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt-gara="<%- bao_gia.bt_gara %>">
        <td class="text-center">
            <% if(bao_gia.so_id_pa == undefined || bao_gia.so_id_pa == null || bao_gia.so_id_pa == "" || bao_gia.so_id_pa == "0"){
            %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" value="<%- bao_gia.gara %>" class="custom-control-input garaBaoGiaChonItem">
                <label class="custom-control-label" for="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>">&nbsp;</label>
            </div>
            <%}else if(bao_gia.chon_pa_trinh == undefined || bao_gia.chon_pa_trinh == null || bao_gia.chon_pa_trinh == "" || bao_gia.chon_pa_trinh == "C"){%>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" @*onchange="chonPaTrinhGD(this, '<%- bao_gia.so_id_pa %>')"*@ id="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" value="<%- bao_gia.gara %>" class="custom-control-input garaBaoGiaChonItem">
                <label class="custom-control-label" for="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>">&nbsp;</label>
            </div>
            <%}else{%>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" @*onchange="chonPaTrinhGD(this, '<%- bao_gia.so_id_pa %>')"*@ checked="checked" id="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" value="<%- bao_gia.gara %>" class="custom-control-input garaBaoGiaChonItem">
                <label class="custom-control-label" for="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>">&nbsp;</label>
            </div>
            <%} %>
        </td>
        <td class="text-center"><%- index+1 %></td>
        <td class="layBaoGiaCT" onclick="xemBaoGiaCT(this,'<%- bao_gia.trang_thai %>', '<%- bao_gia.lien_ket_bg %>', '<%- bao_gia.trang_thai_bg %>', '<%- bao_gia.ngay_ycsc %>')" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>">
             <% if(bao_gia.lap_pa != undefined && bao_gia.lap_pa!= null && bao_gia.lap_pa!=""){%>
            <a href="javascript:void(0)"><%- bao_gia.ten %> - <i class="text-success">(<%- bao_gia.lap_pa %>)</i></a>
            <%}else{%>
            <a href="javascript:void(0)"><%- bao_gia.ten %></a>
            <%}%>
        </td>
        <td class="text-center"><%- bao_gia.ten_doi_tuong %></td>
        <td class="text-center">
            <%if(bao_gia.lien_ket_bg == 1){
            if(bao_gia.trang_thai_bg == null){%>
            <input type="checkbox" class="radio" name="chuyen_bao_gia_gara" onclick="chonGaraBaoGia('<%- bao_gia.gara %>')" value="<%= bao_gia.bt_gara %>" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" />
            <%}else{%>
            <a href="#" onclick="xemChiTietBaoGiaGara(this,'<%- bao_gia.gara %>','<%- bao_gia.so_id_bg %>')" class="ttbaogia ttbaogia_<%- bao_gia.trang_thai_bg %>"><span><%- bao_gia.trang_thai_bg_hthi %></span></a>
            <%}}else{%>
            <span>Chưa liên kết</span>
            <%}%>
        </td>
        <td class="text-center"><%- bao_gia.ngay_gio_bg %></td>
        <td class="text-center"><%- bao_gia.ngay_dong_bg %></td>
        <td class="text-right"><%- ESUtil.formatMoney(bao_gia.tong_tien) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(bao_gia.tong_duyet) %></td>
        <td class="text-center" style="width:60px">
            <% if(bao_gia.trang_thai == 'C'){ %>
            <a href="#" class="btnSuaBaoGia mr-1" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>" data-hours="<%- bao_gia.gio_bg %>" data-date="<%- bao_gia.ngay_gio_bg %>" data-placement="bottom bottom-right">
                <i class="fas fa-edit" title="Sửa thông tin gara"></i>
            </a>
            <a href="#" class="xoaBaoGia mr-1" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>">
                <i class="fas fa-trash-alt" title="Xóa chứng từ"></i>
            </a>
            <a href="#" id="btnLayBGCanhTranh" onclick="xemBGCanhTranh('<%- bao_gia.so_id_doi_tuong %>')" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>" data-hours="<%- bao_gia.gio_bg %>" data-date="<%- bao_gia.ngay_gio_bg %>" data-placement="bottom bottom-right">
                <i class="far fa-file-alt" title="Lấy báo giá cạnh tranh"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>
    <% if(data_info.length < 3){
    for(var i = 0; i < 3 - data_info.length;i++ ){
    %>
    <tr>
        <td style="height: 35.2px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
<script type="text/html" id="garaBaoGiaCT_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(bao_gia,index) { %>
    <tr class="gara_bg_ctiet" data-search="<%- ESUtil.xoaKhoangTrangText(bao_gia.ten_hang_muc) %>">
        <td class="text-center"><%- index + 1%></td>
        <td class="text-center">
            <a href="#" onclick="openModalImagesPaging('<%- bao_gia.hang_muc %>')">
                <i class="fas fa-image" title="Xem hình ảnh chi tiết"></i>
            </a>
        </td>
        <%if(bao_gia.loai_hang_muc == undefined|| bao_gia.loai_hang_muc == null || bao_gia.loai_hang_muc == ""|| bao_gia.loai_hang_muc == "G"){%>
        <td>
            <input type="hidden" name="loai_hang_muc" value="<%- bao_gia.loai_hang_muc %>" />
            <input type="hidden" name="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="hang_muc_ten" value="<%- bao_gia.ten_hang_muc %>" />
            <input type="hidden" name="muc_do" value="<%- bao_gia.muc_do %>" />
            <input type="hidden" name="thay_the_sc" value="<%- bao_gia.thay_the_sc %>" />

            <input type="hidden" name="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="tien_ht_gara" value="<%- bao_gia.tien_ht_gara %>" />
            <input type="hidden" name="bt" value="<%- bao_gia.bt %>" />

            <input type="hidden" name="tien_vtu_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_duyet) %>" />
            <input type="hidden" name="tien_nhan_cong_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong_duyet) %>" />
            <input type="hidden" name="tien_khac_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac_duyet) %>" />
            <input type="hidden" name="tien_duyet" class="form-control number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_duyet) %>" readonly />

            <a href="javascript:void(0);" data-field="ten_hang_muc" data-val="<%- bao_gia.ten_hang_muc %>" onclick="traCuuLichSuBaoGiaHangMuc(this)"><%- bao_gia.ten_hang_muc %></a>
        </td>
        <td class="text-center">
            <a href="#" data-field="muc_do_ten" data-val="<%- bao_gia.muc_do_ten %>" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>','G')"><%- bao_gia.muc_do_ten %></a>
        </td>
        <td class="text-center">
            <a href="#" data-field="thay_the_sc_ten" data-val="<%- bao_gia.thay_the_sc_ten %>" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>','G')"><%- bao_gia.thay_the_sc_ten %></a>
        </td>
        <%}else if(bao_gia.loai_hang_muc == "B"){%>
        <td>
            <input type="hidden" name="loai_hang_muc" value="<%- bao_gia.loai_hang_muc %>" />
            <input type="hidden" name="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="hang_muc_ten" value="<%- bao_gia.ten_hang_muc %>" />
            <input type="hidden" name="muc_do" value="<%- bao_gia.muc_do %>" />
            <input type="hidden" name="thay_the_sc" value="<%- bao_gia.thay_the_sc %>" />

            <input type="hidden" name="tien_ht_gara" value="0" />
            <input type="hidden" name="bt" value="0" />

            <input type="hidden" name="tien_vtu_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_duyet) %>" />
            <input type="hidden" name="tien_nhan_cong_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong_duyet) %>" />
            <input type="hidden" name="tien_khac_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac_duyet) %>" />
            <input type="hidden" name="tien_duyet" class="form-control number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_duyet) %>" readonly />
            <a href="javascript:void(0);" data-field="ten_hang_muc" data-val="<%- bao_gia.ten_hang_muc %>" onclick="traCuuLichSuBaoGiaHangMuc(this)"><%- bao_gia.ten_hang_muc %></a>
        </td>
        <td class="text-center">
            <%if(bao_gia.hang_muc_bo_sung_moi == "M"){%>
            <a href="#" data-field="muc_do_ten" data-val="<%- bao_gia.muc_do_ten %>" class="text-danger text-center" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.muc_do_ten %></a>
            <%}else{%>
            <a href="#" data-field="muc_do_ten" data-val="<%- bao_gia.muc_do_ten %>" class="text-center" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.muc_do_ten %></a>
            <%}%>

        </td>
        <td class="text-center">
            <%if(bao_gia.hang_muc_bo_sung_moi == "M"){%>
            <a href="#" data-field="thay_the_sc_ten" data-val="<%- bao_gia.thay_the_sc_ten %>" class="text-danger" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.thay_the_sc_ten %></a>
            <%}else{%>
            <a href="#" data-field="thay_the_sc_ten" data-val="<%- bao_gia.thay_the_sc_ten %>" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.thay_the_sc_ten %></a>
            <%}%>
        </td>
        <%}%>
        <td class="text-center">
            <% if(bao_gia.thay_the_sc=="T") {%>
            <input type="text" name="so_luong" class="decimal floating-input form-control" value="<%- bao_gia.so_luong %>" />
            <%}else{%>
            <input type="text" name="so_luong" readonly="readonly" class="decimal floating-input form-control" value="<%- bao_gia.so_luong %>" />
            <%}%>
        </td>
        <td data-column_collapse class="text-right" data-hang_muc="<%- bao_gia.hang_muc %>" data-gia_tran_vtu="<%- bao_gia.gia_tran_vtu %>" data-gia_tran_vtu_toi_da="<%- bao_gia.gia_tran_vtu_toi_da %>" title="Tối đa <%- ESUtil.formatMoney(bao_gia.gia_tran_vtu_toi_da) %>">
            <%- ESUtil.formatMoney(bao_gia.gia_tran_vtu) %>
        </td>
        <td data-column_collapse class="text-right" data-hang_muc="<%- bao_gia.hang_muc %>" data-gia_tran_nhan_cong="<%- bao_gia.gia_tran_nhan_cong %>" data-gia_tran_nhan_cong_toi_da="<%- bao_gia.gia_tran_nhan_cong_toi_da %>" title="Tối đa <%- ESUtil.formatMoney(bao_gia.gia_tran_nhan_cong_toi_da) %>">
            <%- ESUtil.formatMoney(bao_gia.gia_tran_nhan_cong) %>
        </td>
        <td data-column_collapse class="text-right" data-hang_muc="<%- bao_gia.hang_muc %>" data-gia_tran_khac="<%- bao_gia.gia_tran_khac %>" data-gia_tran_khac_toi_da="<%- bao_gia.gia_tran_khac_toi_da %>" title="Tối đa <%- ESUtil.formatMoney(bao_gia.gia_tran_khac_toi_da) %>">
            <%- ESUtil.formatMoney(bao_gia.gia_tran_khac) %>
        </td>
        <td class="text-right d-none"><%- ESUtil.formatMoney(bao_gia.gia_giam_dinh) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(bao_gia.tien_ht_gara) %></td>
        <td class="text-right">
            <%if(bao_gia.thay_the_sc=="T"){%>
            <input type="text" name="tien_vtu" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu) %>" />
            <%}else{%>
            <input type="text" name="tien_vtu" readonly="readonly" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu) %>" />
            <%}%>
        </td>
        <td class="text-right">
            <input type="text" name="tien_nhan_cong" class="number floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_khac" class="number floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac) %>" />
            <input type="text" name="tong_cong" class="form-control number floating-input d-none" value="<%- ESUtil.formatMoney(bao_gia.tong_cong) %>" readonly />
        </td>
        <td class="text-right">
            <span style="position:relative">
                <%if(bao_gia.thay_the_sc=="T"){%>
                <input type="text" name="tien_vtu_dx" class="number floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_dx) %>" data-ktra_gia_tran="vtu" data-hang_muc="<%- bao_gia.hang_muc %>" />
                <%}else{%>
                <input type="text" name="tien_vtu_dx" readonly="readonly" class="number floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_dx) %>" data-ktra_gia_tran="vtu" data-hang_muc="<%- bao_gia.hang_muc %>" />
                <%}%>
            </span>
        </td>
        <td class="text-right">
            <span style="position:relative">
                <input type="text" name="tien_nhan_cong_dx" class="number floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong_dx) %>" data-ktra_gia_tran="nhan_cong" data-hang_muc="<%- bao_gia.hang_muc %>" />
            </span>
        </td>
        <td class="text-right">
            <span style="position:relative">
                <input type="text" name="tien_khac_dx" class="number floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac_dx) %>" data-ktra_gia_tran="khac" data-hang_muc="<%- bao_gia.hang_muc %>" />
            </span>
            <input type="text" name="tien_dx" class="form-control number floating-input d-none" value="<%- ESUtil.formatMoney(bao_gia.tien_dx) %>" readonly />
        </td>
        <td class="text-right">
            <%if(bao_gia.thay_the_sc=="T"){%>
            <input type="text" name="tl_giam_gia_vtu" maxlength="3" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_vtu) %>" />
            <%}else{%>
            <input type="text" name="tl_giam_gia_vtu" readonly="readonly" maxlength="3" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_vtu) %>" />
            <%}%>
        </td>
        <td class="text-right">
            <input type="text" name="tl_giam_gia_nhan_cong" maxlength="3" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tl_giam_gia_khac" maxlength="3" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_khac) %>" />
        </td>
        <td class="text-center">
            <% if(bao_gia.ghi_chu != null && bao_gia.ghi_chu != ""){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- bao_gia.ghi_chu %>" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>

    <% if(data_info.length < 6){
    for(var i = 0; i < 6 - data_info.length;i++ ){
    %>
    <tr>
        <td style="height: 35px"></td>
        <td class="d-none"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td data-column_collapse></td>
        <td data-column_collapse></td>
        <td data-column_collapse></td>
    </tr>
    <% }} %>

    <%  for(var i = 0; i < 5;i++ ){ %>
    <tr class="tmp">
        <td style="height: 35px"></td>
        <td class="d-none"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td data-column_collapse></td>
        <td data-column_collapse></td>
        <td data-column_collapse></td>
    </tr>
    <% } %>
</script>
<script type="text/html" id="divGaraHopTac_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(item,index) { %>
    <div class="custom-control custom-checkbox divItemGaraHopTac" data-text="<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="gara_ht_<%- index %>" value="<%- item.gara %>" class="custom-control-input item_gara_ht">
        <label class="custom-control-label" for="gara_ht_<%- index %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Không có gara hợp tác</div>
    <% } %>
</script>
@*Code phương án*@
<script type="text/html" id="navPhuongAnNghiepVuTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item cursor" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.doi_tuong %>" data-lhnv="<%- item.ma %>"><a href="#" onclick="xemChiTietPhuongAn('<%- item.so_id_pa %>', '<%- item.ma %>')"><%- item.ten %></a></li>
    <%})}%>
</script>

<script type="text/html" id="divNguyenNhanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox nngt" id="nngt_<%- item.ma %>">
        <input type="checkbox" id="nguyen_nhan_giam_tru_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNguyenNhanGiamTruItem">
        <label class="custom-control-label" style="cursor:pointer;" for="nguyen_nhan_giam_tru_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="divNguyenNhanPATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox nngt" id="nngt_<%- item.ma %>">
        <input type="checkbox" id="nguyen_nhan_giam_tru_pa_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNguyenNhanGiamTruPAItem">
        <label class="custom-control-label" style="cursor:pointer;" for="nguyen_nhan_giam_tru_pa_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="modalVideoDanhSachDGRRHDTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item, index) {
    if(index == 0){%>
    <a class="nav-link rounded videoLinkDGRRHD active" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoDGRRHD('<%- item.bt %>')"><%- item.ten %></a>
    <%} else {%>
    <a class="nav-link rounded videoLinkDGRRHD" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoDGRRHD('<%- item.bt %>')"><%- item.ten %></a>
    <%}})}%>
</script>

<script type="text/html" id="tblYeuCauGDinhBThuongHoTemplate">
    <% if(data.length > 0){ %>
    <% _.forEach(data, function(item,index) { %>
    <tr class="row_item">
        <td class="text-center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="ma_dt_trinh_<%- item.bt %>" value="<%- item.bt %>" class="custom-control-input input_ma_dt_trinh single_checked">
                <label class="custom-control-label" for="ma_dt_trinh_<%- item.bt %>">&nbsp;</label>
            </div>
        </td>
        <td class="text-center"><%- item.ten_ma_dvi_yc %></td>
        <td class="text-center"><%- item.ma_nsd_yc %></td>
        <td class="text-center"><%- item.loai_ten %></td>
        <td class="text-center"><%- item.trang_thai %></td>
        <td class="text-center">
            <a href="#" onclick="suaYeuCauGDinhHo('<%- item.so_id%>', '<%- item.bt%>')">
                <i class="fa fa-edit"></i>
            </a>
        </td>
    </tr>
    <% })}%>

    <% if(data.length < 5){
    for(var i = 0; i < 5 - data.length;i++ ){
    %>
    <tr>
        <td style="height:30.5px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="dsNguoiLamChungTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr>
        <td><%- item.ten%></td>
        <td><%- item.dien_thoai%></td>
        <td><%- item.email%></td>
        <td>
            <a onclick="suaNguoiLamChung('<%- item.bt%>')" href="#">
                <i class="far fa-file-alt"></i>
            </a>
        </td>
        <td>
            <a onclick="xoaNguoiLamChung('<%- item.bt%>')" href="#">
                <i class="fas fa-trash-alt"></i>
            </a>
        </td>
    </tr>
    <% })} %>
    <% if(danh_sach.length < 2){
    for(var i = 0; i < 2 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblCapNhatUocTheoDiem_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr class="capNhatUoc">
            <td class="text-center">
                <input type="hidden" data-field="nv" name="nv" value="<%- item.nv %>" />
                <input type="hidden" data-field="lh_nv" name="lh_nv" value="<%- item.lh_nv %>" />
                <input type="hidden" data-field="diem" name="diem" value="<%- item.diem %>" />
                <input type="hidden" data-field="tich_hop" name="tich_hop" value="<%- item.tich_hop %>" />
                <input type="hidden" data-field="log_rq" name="log_rq" value="<%- item.log_rq %>" />
                <input type="hidden" data-field="log_res" name="log_res" value="<%- item.log_res %>" />
                <input type="hidden" data-field="bt" name="bt" value="<%- item.bt %>" />
                <%- index + 1 %>
            </td>
            <td class="text-center">
                <input type="text" data-field="ngay_dp" class="floating-input datepicker text-center" readonly value="<%- item.ngay_dp %>" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten_diem" data-field="ten_diem" value="<%- item.ten_diem %>" />
                <input type="text" class="floating-input combobox" data-field="ten_diem" data-val="<%- item.diem %>" onclick="chonDiemDuPhong(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten_diem %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten" data-field="ten" value="<%- item.ten %>" />
                <input type="text" class="floating-input combobox" data-field="ten" data-val="<%- item.lh_nv %>" onclick="chonNV(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien" name="tien" class="number floating-input tien_<%- item.bt %>" autocomplete="off" disabled value="<%- ESUtil.formatMoney(item.tien) %>" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien_chenh_lech" name="tien_chenh_lech" class="number floating-input tien_chenh_lech" disabled value="<%- ESUtil.formatMoney(item.tien_chenh_lech) %>" />
            </td>
            <td class="text-center">
                <% if(item.tich_hop == 1){%>
                     <i class="fas fa-check-circle text-success" title="Đã tích hợp"></i>
                <%}else{%>
                    <i class="fas fa-times" style="color:red" title="Chưa tích hợp"></i>
                <%}%>
            </td>
            <td class="text-center">
                <% if(item.log_rq != null && item.log_res !=""){ %>
                    <a href="#" class="cursor-pointer combobox" onclick="showLogRq(this)" data-rq="<%- JSON.stringify(item.log_rq) %>" data-res="<%- JSON.stringify(item.log_res) %>" >
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                    <% }else{ %>
                    <a class="cursor-pointer combobox" onclick="showLogRq(this)" data-val="" data-rq="" data-res="">
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                <% } %>
            </td>
            <td class="text-center">
                <% if(item.tich_hop != 1){%>
                    <a href="#" class="cursor-pointer" onclick="tichHopLaiUoc(<%- item.bt %>)">
                        <i class="fas fa-arrow-right" style="color: var(--escs-main-theme-color)" title="Tích hợp lại ước"></i>
                    </a>
                <%}else{%>
                    
                <%}%>
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblBienDo_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr>
            <td>
                <input type="hidden" data-field="diem" name="diem" data-val="<%- item.diem %>" value="<%- item.diem %>"/>
                <%- item.ten_diem %>
            </td>
            <td class="text-right">
                <input type="text" data-field="ty_le" name="ty_le" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(item.ty_le) %>" />
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 3){
    for(var i = 0; i < 3 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalChonDiemDPDanhSach_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox diemdp" data-id="diemdp_<%- item.diem %>" data-text="<%- item.diem.toLowerCase() %>-<%- item.ten_diem.toLowerCase() %>">
        <input type="checkbox" id="diem_<%- item.diem %>" value="<%- item.diem %>" class="custom-control-input modalChonDiemDPItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="diem_<%- item.diem %>"><%- item.ten_diem %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="modalChonNV_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox lhnv" data-id="lhnv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="lhnvv_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNVItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="lhnvv_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="tblBGCanhTranh_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(bao_gia,index) { %>
    <tr class="bao_gia_canh_tranh">
        <td class="text-center"><%- index + 1%></td>
        <td class="text-left">
            <input type="hidden" name="hang_muc" data-field="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="muc_do" data-field="muc_do" value="<%- bao_gia.muc_do %>" />
            <input type="hidden" name="thay_the_sc" data-field="thay_the_sc" value="<%- bao_gia.thay_the_sc %>" />
            <input type="hidden" name="bt" data-field="bt" value="<%- bao_gia.bt %>" />
            <input type="hidden" name="bt_gara" data-field="bt_gara" value="<%- bao_gia.bt_gara %>" />
            <input type="hidden" name="so_id_doi_tuong" data-field="so_id_doi_tuong" value="<%- bao_gia.so_id_doi_tuong %>" />
            <input type="hidden" name="gara" data-field="gara" value="<%- bao_gia.gara %>" />
            <input type="hidden" name="tl_giam_gia_vtu" data-field="tl_giam_gia_vtu" value="<%- bao_gia.tl_giam_gia_vtu %>" />
            <input type="hidden" name="tl_giam_gia_nhan_cong" data-field="tl_giam_gia_nhan_cong" value="<%- bao_gia.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" name="tl_giam_gia_khac" data-field="tl_giam_gia_khac" value="<%- bao_gia.tl_giam_gia_khac %>" />
            <input type="hidden" name="ghi_chu" data-field="ghi_chu" value="<%- bao_gia.ghi_chu %>" />

            <%- bao_gia.ten_hang_muc %>
        </td>
        <td class="text-center">
            <%- bao_gia.muc_do_ten %>
        </td>
        <td class="text-center">
            <%- bao_gia.thay_the_sc_ten %>
        </td>
        <td class="text-center">
            <input type="text" data-field="so_luong" name="so_luong" class="decimal floating-input form-control" readonly value="<%- bao_gia.so_luong %>" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_vtu" name="tien_vtu" onchange="tinhToanBGCanhTranh(this)" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_nhan_cong" name="tien_nhan_cong" onchange="tinhToanBGCanhTranh(this)" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_khac" name="tien_khac" onchange="tinhToanBGCanhTranh(this)" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(bao_gia.tien_khac) %>" />
        </td>
    </tr>
    <% })} %>

    <% if(danh_sach.length < 14){
    for(var i = 0; i < 14 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height: 30px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@* hoa don 2024/04/08 *@
<script type="text/html" id="step4_chung_tu_template">
    <% if(chung_tu.length > 0){
    _.forEach(chung_tu, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.ngay_ct %>
        </td>
        <td class="text-center"><%- item.mau_hdon %></td>
        <td class="text-center"><%- item.ky_hieu_hdon %></td>
        <td class="text-center"><%- item.so_hdon %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.thue) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tong_cong) %></td>
        <td class="text-center">
            <a href="#" class="edit_chung_tu" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="far fa-file-alt" title="Xem/sửa chi tiết chứng từ"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" class="xoaChungTu"><i class="fas fa-trash-alt" title="Xóa chứng từ"></i></a>
        </td>
    </tr>
    <% })} %>
    <% if(chung_tu.length < 3){
    for(var i = 0; i < 3 - chung_tu.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@* thu huong 2024/04/08 *@
<script type="text/html" id="step4_thu_huong_template">
    <% if(thu_huong.length > 0){
    _.forEach(thu_huong, function(item,index) { %>
    <tr>
        <td class="text-center" style="width:40px;">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.pttt %>
        </td>
        <td><%- item.ten %></td>
        <td class="text-center"><%- item.tk_cmt %></td>
        <td class="text-center"><%- item.ten_ngan_hang %></td>
        <td class="text-center"><%- item.ten_chi_nhanh %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-center"><%- item.loai_hthi %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="edit_thu_huong" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="far fa-file-alt" title="Xem/sửa chi tiết thông tin"></i>
            </a>
            <%
            }%>

        </td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="xoaNguoiThuHuong"><i class="fas fa-trash-alt" title="Xóa người thụ hưởng"></i></a>
            <%
            }%>
        </td>
    </tr>
    <% })}%>
    <% if(thu_huong.length < 3){
    for(var i = 0; i < 3 - thu_huong.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblSoTienThanhToanTheoLhnv_template">
    <% if (data.length > 0) {
    _.forEach(data, function(item,index) { %>
    <tr>
        <td>
            <input type="hidden" class="number" data-name="tien_duyet" data-field="tien_duyet" data-val="<%- item.tien_duyet %>" value="<%- ESUtil.formatMoney(item.tien_duyet) %>" />
            <input type="hidden" data-name="lh_nv" data-field="lh_nv" data-val="<%- item.lh_nv %>" value="<%- item.lh_nv %>" />
           <%- item.ten_lh_nv %> (<%- item.lh_nv %>)
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_duyet) %></td>
        <td class="text-right">
            <input type="text" data-name="tien_thu_huong" autocomplete="off" data-field="tien_thu_huong" data-val="<%- item.tien_thu_huong %>" class="floating-input number" value="<%- ESUtil.formatMoney(item.tien_thu_huong) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="thue_thu_huong" autocomplete="off" data-field="thue_thu_huong" data-val="<%- item.thue_thu_huong %>" class="floating-input number" value="<%- ESUtil.formatMoney(item.thue_thu_huong) %>" />
        </td>
    </tr>
    <% })} %>

    <% if(data.length < 3){
    for(var i = 0; i < 3 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35.5px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalBaoGiaGara_lan_template">
    <% if(bg_lan.length > 0){
    _.forEach(bg_lan, function(item,index) { %>
    <tr class="cursor-pointer modalBaoGiaGara_lan_item" data-lan="<%- item.lan_bg %>" onclick="xemChiTietLanBaoGia(this, '<%- item.lan_bg %>')">
        <td class="text-center"><%- item.lan %></td>
        <td class="text-center"><%- item.ngay_bg_hthi %></td>
        <td class="text-center"><%- item.trang_thai_yc_hthi %></td>
    </tr>
    <% })}%>
</script>

<script type="text/html" id="modalBaoGiaGara_lan_ct_template">
    <% if(bg_lan_ct.length > 0){
    _.forEach(bg_lan_ct, function(item,index) { %>
    <tr class="cursor-pointer" data-hang-muc="<%- item.hang_muc %>">
        <td>
            <a href="#"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-center">
            <%- item.muc_do_ten_gara %>
        </td>
        <td class="text-center">
            <% if(item.thay_the_sc_gara =="T"){%>
            <span>Thay thế</span>
            <%}%>
            <% if(item.thay_the_sc_gara =="S"){%>
            <span>Sửa chữa</span>
            <%}%>
        </td>
        <td class="text-center">
            <% if(item.chinh_hang_gara =="C"){%>
            <i class="fas fa-check mr-2" style="color:#28c690"></i>
            <%}%>
            <% if(item.chinh_hang_gara =="K"){%>
            <i class="fas fa-check mr-2"></i>
            <%}%>
        </td>
        <td class="text-center">
            <%- item.so_luong_gara %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_vtu_gara) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_nhan_cong_gara) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_khac_gara) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_luong_gara*item.tien_vtu_gara + item.tien_nhan_cong_gara + item.tien_khac_gara) %>
            <input type="hidden" class="floating-input number tien_dx_gara" readonly="readonly" value="<%- ESUtil.formatMoney(item.so_luong_gara*item.tien_vtu_gara + item.tien_nhan_cong_gara + item.tien_khac_gara) %>" />
        </td>
        <td>
            <input type="text" class="floating-input" readonly="readonly" value="<%- item.ghi_chu_gara %>" />
        </td>
        <td class="text-center">
            <input type="text" class="floating-input decimal so_luong" onchange="tinhTongTienBG()" value="<%- item.so_luong %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_vtu" onchange="tinhTongTienBG()" value="<%- ESUtil.formatMoney(item.tien_vtu) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_nhan_cong" onchange="tinhTongTienBG()" value="<%- ESUtil.formatMoney(item.tien_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_khac" onchange="tinhTongTienBG()" value="<%- ESUtil.formatMoney(item.tien_khac) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_dx form-control" readonly="readonly" value="<%- ESUtil.formatMoney(item.tien_dx) %>" />
        </td>
        <td>
            <input type="text" class="floating-input ghi_chu" value="<%- item.ghi_chu %>" />
        </td>
    </tr>
    <% })}%>
</script>

<script type="text/html" id="divUploadCTTTTemplate">
    <% _.forEach(danh_sach, function(item,index) { %>
        <div class="col-12" data-obj_url="<%- item.url %>">
            <div class="border rounded bg-light p-2 mb-2 d-flex flex-nowrap" style="cursor:pointer;">
                <span class="mr-auto"><%- index + 1 %>.&nbsp;</span>
                <span class="text-right"><%- item.name %> (<span class="font-italic"><%- item.size %>MB</span>)</span>
            </div>
        </div>
    <% }) %>
</script>