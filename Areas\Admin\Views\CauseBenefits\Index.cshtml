﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình nhóm nguyên nhân, quyền lợi sản phẩm";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<style>
    .streeHeight{
        height: 530px;
    }
    @@media only screen and (max-width: 1366px) {
        .streeHeight {
            height: 415px;
        }
    }
</style>
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON><PERSON><PERSON> hình nhóm nguyên nhân, quyền lợi sản phẩm</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active"><PERSON><PERSON><PERSON> hình nhóm NN, quyền lợi SP</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" autocomplete="off" name="tim" id="tim" placeholder="Nhập tên nguyên nhân, quyền lợi sản phẩm/bảo hiểm" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác quản lý</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac_ql" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThemMoi">
                                <i class="fa fa-plus"></i>
                            </button>
                            @*<button type="button" class="btn btn-primary btn-sm wd-24p" title="Export" id="btnExportExcel">
                                <i class="fas fa-download"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Import" id="btnImportExcel">
                                <i class="fas fa-upload"></i>
                            </button>*@
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridView" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="inside-modal" class="esmodal fade" tabindex="-1" data-keyboard="false" role="dialog" aria-hidden="true">
    <div class="esmodal-dialog">
        <div class="esmodal-content">
            <div class="esmodal-header py-1">
                <h4 class="esmodal-title" id="titleUpdateContract">Cấu hình nhóm nguyên nhân, quyền lợi sản phẩm</h4>
                <button type="button" class="close" onclick="hideModal()" aria-hidden="true">×</button>
            </div>
            <div class="esmodal-body" style="background-color:#54667a0a; padding-top:5px;">
                <div class="row">
                    <div class="col-12 info-tab">
                        <div class="card" style="margin-bottom:0px;">
                            <div class="card-body p-0">
                                <div class="border rounded">
                                    <div class="scrollable" style="padding:5px;">
                                        <form name="frmNhapNguyenNhanQuyenLoi" method="post" style="width:100%">
                                            <div class="row" style="width:100%">
                                                <div class="col-sm-3">
                                                    <div class="form-group">
                                                        <select class="select2 form-control custom-select" required name="ma_doi_tac_ql" style="width: 100%; height:36px;">
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3">
                                                    <div class="form-group">
                                                        <select class="select2 form-control custom-select" required name="san_pham" style="width: 100%; height:36px;"></select>
                                                    </div>
                                                </div>
                                                <div class="col col-4">
                                                    <button type="button" class="btn btn-primary wd-80 btn-sm wd-24p" title="Lưu" id="btnLuu">
                                                        <i class="fa fa-save mr-2"></i> Lưu
                                                    </button>
                                                    @*<button type="button" class="btn btn-outline-primary wd-80 btn-sm wd-24p" title="Xóa" id="btnDelete">
                                                        <i class="fas fa-trash-alt mr-2"></i> Xóa
                                                    </button>*@
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12  collapse show" id="sidebar_info">
                        <div class="row">
                            <div class="col-5 common-tab pr-0">
                                <div class="card" style="margin-bottom:0px;">
                                    <div class="card-body p-0">
                                        <div class="border rounded">
                                            <div>
                                                <div style="width:100%" class="mt-1 pd-l-10 pd-r-10">
                                                    <input type="text" placeholder="Nhập tên nguyên nhân" class="form-control" id="timKiemNguyenNhan" value="" />
                                                </div>
                                                <div class="scrollable streeHeight" id="treeNhomNguyenNhan"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-7 common-tab pr-0">
                                <div class="card" style="margin-bottom: 0px;">
                                    <div class="card-body p-0">
                                        <div class="border rounded">
                                            <div>
                                                <div style="width: 100%" class="mt-1 pd-l-10 pd-r-10">
                                                    <input type="text" placeholder="Nhập tên quyền lợi bảo hiểm" class="form-control" id="timKiemQuyenLoiBaoHiem" value="" />
                                                </div>
                                                <div class="scrollable streeHeight" id="treeQuyenLoiBaoHiem"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="ModalXemKetQua" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="width: 70%">
        <div class="modal-content">
            <form name="frmXemKetQua" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thêm cấu hình</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table class="table table-bordered fixed-header">
                                <thead class="font-weight-bold text-center uppercase">
                                <tr>
                                    <th style="width:5%">STT</th>
                                    <th style="width: 30%">Sản phẩm</th>
                                    <th style="width: 35%">Quyền lợi</th>
                                    <th style="width: 30%">Nhóm nguyên nhân</th>
                                </tr>
                                </thead>
                                <tbody id="tbXemKetQua">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display: block;">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-80 float-right" id="btnLuuThongTinCauHinh"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<partial name="_Template.cshtml" />

@section Styles{
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" />
    <style>
        .vakata-context {
            z-index: 9999999 !important;
        }
    </style>
}

@section Scripts{
    <script src="~/libs/tree-js/vakata-jstree/dist/jstree.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ProductHumanService.js" asp-append-version="true"></script>
    <script src="~/js/app/HealthCare/services/HealthClaimCommonService.js" asp-append-version="true"></script>
    <script src="~/js/app/Contract/services/PackageService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CauseBenefitsService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/CauseBenefits.js" asp-append-version="true"></script>
}
