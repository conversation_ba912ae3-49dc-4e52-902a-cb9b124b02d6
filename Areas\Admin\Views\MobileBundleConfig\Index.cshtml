﻿@model ESCS.Areas.Admin.Controllers.MobileBundle
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Mobile bundle";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
    IEnumerable<dynamic> data = (ViewData["data"] as IEnumerable<dynamic>) ?? Enumerable.Empty<dynamic>();
    var android = data.Where(item => item.platform == "ANDROID").OrderByDescending(item => item.bt).FirstOrDefault();
    var json_android = Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(android));
    var ios = data.Where(item => item.platform == "IOS").OrderByDescending(item => item.bt).FirstOrDefault();
    var json_ios = Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ios));
}

@section Scripts {
    <script src="~/libs/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <script type="text/javascript" asp-append-version="true">
        const android = @json_android;
        const ios = @json_ios;

        $(document).ready(function () {
            bsCustomFileInput.init();

            $('#frmUpload input[name="platform"]').on('change', function() {
                let obj = undefined;
                switch($(this).val()){
                    case 'ANDROID': obj = android; break;
                    case 'IOS':obj = ios; break;
                }
                $('#frmUpload input[name="app_version"]').val(obj?.app_version??'');
                $('#frmUpload input[name="build_number"]').val(obj?.build_number??'');
            });

            setTimeout(()=>{
                $('#platform-android').first().click();
            });
        });
    </script>
}

<style>
    .card > div.d-flex > div {
        display: flex;
        flex-direction: column;
    }

    .btn-group-toggle .btn:focus,
    .btn-group-toggle .btn.focus {
        box-shadow: unset !important;
    }

    body:has(input#platform-android:checked) #tblBundles tbody > tr:not(.ANDROID) {
        display: none;
    }

    body:has(input#platform-ios:checked) #tblBundles tbody > tr:not(.IOS) {
        display: none;
    }

    .custom-file > label:after {
        font-family: "Font Awesome 5 Pro";
    }

    .validation-summary-errors > ul {
        list-style: none;
        padding: unset;
    }
</style>

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="card mb-0">
        <div class="d-flex flex-nowrap" style="height:80vh;">
            <div style="width:300px;">
                <div class="card-body">
                    <form asp-area="Admin" asp-controller="MobileBundleConfig" asp-action="Index" id="frmUpload" method="post" enctype="multipart/form-data" class="row">
                        <div class="col-12 mb-2">
                            <div class="form-group">
                                <label asp-for="platform" class="d-block"></label>

                                <div class="d-flex btn-group btn-group-toggle" data-toggle="buttons">
                                    <label class="flex-fill btn btn-outline-primary">
                                        <input type="radio" asp-for="platform" id="platform-android" value="ANDROID" required>
                                        Android
                                    </label>
                                    <label class="flex-fill btn btn-outline-primary">
                                        <input type="radio" asp-for="platform" id="platform-ios" value="IOS" required>
                                        IOS
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-2">
                            <div class="form-group">
                                <label asp-for="app_version"></label>
                                <input type="text" asp-for="app_version" autocomplete="off" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-12 mb-2">
                            <div class="form-group">
                                <label asp-for="build_number"></label>
                                <input type="text" asp-for="build_number" autocomplete="off" class="form-control" required>
                            </div>
                        </div>
                        @* <div class="col-12 mb-2">
                            <div class="form-group">
                                <label>codepush_version</label>
                                <input type="text" name="codepush_version" autocomplete="off" class="form-control" readonly>
                            </div>
                        </div> *@
                        <div class="col-12 mb-2">
                            <div class="form-group">
                                <label asp-for="file" for="bundle-file"></label>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" asp-for="file" id="bundle-file" accept="application/zip" required>
                                    <label class="custom-file-label" for="bundle-file" data-browse=""></label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-2">
                            <label class="invisible">&nbsp;</label>
                            <button type="submit" class="btn btn-block btn-primary">
                                <i class="fal fa-upload"></i>
                                <span>Upload</span>
                            </button>
                        </div>
                        <div class="col-12 text-danger">
                            <div asp-validation-summary="All"></div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="border-left"></div>
            <div class="flex-fill" style="width:0px;">
                <div class="card-body">
                    <div class="h-100 table-responsive overflow-auto border text-center text-nowrap">
                        <table class="table table-bordered" id="tblBundles">
                            <thead class="bg-primary text-white sticky-top">
                                <tr>
                                    <th scope="col">ID</th>
                                    <th scope="col">PLATFORM</th>
                                    <th scope="col">APP_VERSION</th>
                                    <th scope="col">BUILD_NUMBER</th>
                                    <th scope="col">CODEPUSH_VERSION</th>
                                    <th scope="col">PATH</th>
                                    <th scope="col">MD5</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (dynamic item in data)
                                {
                                    <tr class="@(item.platform)">
                                        <td>@(item.bt)</td>
                                        <td>@(item.platform)</td>
                                        <td>@(item.app_version)</td>
                                        <td>@(item.build_number)</td>
                                        <td>@(item.codepush_version)</td>
                                        <td>@(item.path)</td>
                                        <td>@(item.md5)</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>