﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class BranchListController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> GetAll()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_CACHE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetAllByPartner()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_CTYBH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetAllNoneCache()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_TATCA, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveBankList()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_TK_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> DeleteBankList()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_TK_XOA, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Delete()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_XOA, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CN_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CN_LKE_CT, json);
            return Ok(data);
        }

        public async Task<IActionResult> GetList()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_TKIEM, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> getSignature()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DOI_TAC_CHI_NHANH_CHU_KY_SO_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> saveSignature()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DOI_TAC_CHI_NHANH_CHU_KY_SO_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDSKhuVucChiNhanh()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_HT_MA_DOI_TAC_CHI_NHANH_KHU_VUC, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuCauHinhHoaDon()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_DOI_TAC_CHI_NHANH_CH_HOA_DON, json);
            return Ok(data);
        }
    }
}