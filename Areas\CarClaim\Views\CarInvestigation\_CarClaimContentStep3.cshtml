﻿<div class="row mg-t-10" id="divCarClaimContentStep3HinhAnh">
    <div class="col-9 pr-0">
        <div class="card mb-0">
            <div class="card-body p-0">
                <div class="border rounded">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                        <h5 class="m-0" id="ImgContainerTitle"><PERSON><PERSON><PERSON><PERSON> hồ sơ</h5>
                        <div class="btn-group float-right">
                            <a class="btn btn-light rounded py-0" style="font-weight:bold" data-toggle="dropdown" data-display="static" aria-haspopup="true" aria-expanded="false">
                                <span style="font-size:13px; color:var(--escs_theme_color)">B<PERSON> sung thông tin đăng ký, đăng kiểm, bên tham gia giám định</span> <i class="fal fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right border">
                                <button class="dropdown-item" type="button" data-id="bs_tt_bang_lai_xe"
                                        data-toggle="popover-x" data-placement="top top-right" id="btnShowFormBsBangLxe" onclick="loadFormBsThongTinBangLaiXe()">
                                    Bổ sung thông tin bằng lái xe
                                </button>
                                <button class="dropdown-item" type="button" data-id="bs_tt_dang_kiem_xe"
                                        data-toggle="popover-x" data-placement="top top-right" onclick="loadFormBsThongTinDangKiem()">
                                    Bổ sung thông tin đăng kiểm
                                </button>

                                <button class="dropdown-item" type="button" data-id="bs_tt_ben_tg_gd"
                                        data-toggle="popover-x" data-placement="top top-right" onclick="loadFormBsThongTinBenTGGD()">
                                    Bên thêm gia giám định
                                </button>
                            </div>
                            <a class="btn btn-light rounded py-0 font-weight-bold d-none" id="image_fullsreen">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div id="img-container" style="height:62vh"></div>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top: -30px; margin-left: 2px; display: block;">
                    <form class="form-inline" name="frmNgayChup" method="post">
                        <div class="form-group mb-2">
                            <input type="text" autocomplete="off" class="form-control" style="width: 95px; background: #54667a0a; border: none; position: relative;" name="ngay" readonly placeholder="Ngày chụp">
                        </div>
                        <span style="margin-top: -8px; font-weight: bold;">-</span>
                        <div class="form-group mb-2">
                            <input type="text" autocomplete="off" class="form-control" style="width: 127px; background: #54667a0a; border: none; position: relative;" name="nsd" readonly placeholder="Người tải ảnh">
                        </div>
                    </form>
                </div>
                <div class="row">
                    <div class="col-4">
                        <form class="form-inline" name="frmToaDoAnh" method="post">
                            <div class="form-group mb-2">
                                <label style="padding-top:4px; padding-right:5px">X:</label>
                                <input type="text" autocomplete="off" class="form-control" style="width:80px" name="kinh_do" readonly placeholder="Kinh độ">
                            </div>
                            <div class="form-group mx-sm-3 mb-2">
                                <label style="padding-top:4px; padding-right:5px">Y:</label>
                                <input type="text" autocomplete="off" class="form-control" style="width:80px" name="vi_do" readonly placeholder="Vĩ độ">
                            </div>
                            <div class="form-group mb-2">
                                <a href="#" id="btnXemViTriChupAnh"><i class="fas fa-map-marker-alt mr-1" title="Xem chi tiết bản đồ"></i> Xem vị trí</a>
                            </div>
                        </form>
                    </div>
                    <div class="col-8">
                        <div class="form-group mb-2">
                            <a href="#" id="btnPhanLoaiHangMuc" style="padding-top:5px;" class="float-right mr-0"><i class="fas fa-car-crash mr-1"></i> Phân loại nhanh</a>
                            <a href="javascript: _modalDongBoAnhHoSoService?.show?.();" style="padding-top:5px;" class="float-right mr-2"><i class="fas fa-sync mr-1"></i>Đồng bộ ảnh</a>
                            <a href="#" style="padding-top:5px;" class="btnXemVideoDGRRHD float-right mr-2 d-none"><i class="fas fa-file-video mr-1"></i>Xem Video</a>
                            <a href="#" style="padding-top:5px;" class="btnXemVideo float-right mr-2"><i class="fas fa-file-video mr-1"></i>Xem Video, File ghi âm</a>
                            <a href="#" id="btnSoSanhDuLieu" style="padding-top:5px;" class="float-right mr-2"><i class="fas fa-adjust mr-1"></i> Đối chiếu OCR</a>
                            <a href="#" id="btnXemHangMucDGRR" onclick="onXemHangMucDGRR()" style="padding-top:5px;" class="float-right mr-2 d-none"><i class="fas fa-images mr-1"></i> Xem ĐGRR</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card m-0">
            <div class="card-body p-0">
                <div class="border rounded">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg border-bottom">
                        <div class="btn-group float-right" style="color:var(--escs_theme_color)">
                            <a class="btn btn-light rounded py-0" onclick="onClickChonLoaiGiayTo(this)">
                                <input type="hidden" id="input_loai_giay_to_chon" value="" />
                                <i class="fas fa-filter" title="Bộ lọc hình ảnh hồ sơ giấy tờ"></i>
                            </a>
                            <a class="btn btn-light rounded py-0" id="btnTransImageView">
                                <i class="fas fa-th"></i>
                            </a>
                            <a class="btn btn-light rounded py-0 mr-1" id="btnAnhHopDong">
                                <i class="fas fa-file-contract" title="Click để xem ảnh hợp đồng"></i>
                            </a>
                            <a class="btn btn-light rounded py-0 mr-1" id="btnAnhDaXoa">
                                <i class="fas fa-folder-times" title="Click để xem ảnh đã xóa"></i>
                            </a>
                            <a class="btn btn-light rounded py-0 mr-1" id="btnXemDanhSachTaiLieu">
                                <i class="fa fa-print" title="Click để xem danh sách tài liệu"></i>
                            </a>
                            <a class="btn btn-light rounded py-0 d-none" id="btnOCR">
                                <i class="fas fa-qrcode" title="Click để đọc OCR tài liệu"></i>
                            </a>
                            <a class="btn btn-light rounded py-0 d-none" id="btnAI">
                                <i class="fas fa-microchip" title="Click để nhận diện tổn thất"></i>
                            </a>
                        </div>
                        <div class="btn-group float-right">
                            <a class="btn btn-light rounded py-0" data-toggle="dropdown" data-display="static" aria-haspopup="true" aria-expanded="false">
                                <i class="fal fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right border" id="dsNhomAnh">
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid scrollable" id="lstImage" style="height: 57vh;">
                        <div class="row">
                            <div class="col-12 list-pictures" id="dsAnhTonThat">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="btn-group btn-group-justified text-center" role="group">
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Phân loại danh mục" id="btnPhanLoaiTonThat">
                        <i class="fas fa-atlas"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Tải xuống" id="btnDownLoadAnhDGTT">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Tải lên" id="btnUpLoadAnhDGTT">
                        <i class="fas fa-upload"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="In ảnh" id="btnXemTaiLieu">
                        <i class="fas fa-print"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Xóa" id="btnXoaLoadAnhDGTT">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        @* <div class="m-1 d-flex" style="gap:.25rem;" id="debugLoadFileThumbnailContainer" hidden>
            <button type="button" class="btn btn-sm btn-primary" onclick="debugLoadFileThumbnail?.(this);" data-state="load_new" id="debugLoadFileThumbnail">
                <i class="fas fa-code mr-2"></i>Debug thumbnail
            </button>
            <button type="button" class="btn btn-sm btn-primary" onclick="debugLoadFileThumbnailReset?.();">
                <i class="fas fa-redo mr-2"></i>Reset
            </button>
        </div> *@
    </div>
</div>

 <style>
    .divAnhPhanLoai img {
        width: 100%;
        height: 100%;
        border-radius: 10px;
    }

    .divAnhPhanLoai:hover {
        border: 2px solid var(--escs_theme_color);
    }

    .divAnhPhanLoai.active .divAnhPhanLoaiCheck {
        display: block;
    }

    .divAnhPhanLoai.active {
        border: 2px solid var(--escs_theme_color);
    }

    .divAnhPhanLoai {
        width: 62px;
        height: 62px;
        border-radius: 10px;
        border: 1px solid #e9ecef;
        float: left;
        margin-left: 5px;
        margin-bottom: 10px;
        cursor: pointer;
        position: relative;
    }

    .divAnhPhanLoaiCheck {
        width: 16px;
        font-size: 16px;
        position: absolute;
        bottom: -3px;
        right: 2px;
        color: #00CC66;
        display: none;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    .icon_plhm.active {
        color: var(--escs_theme_color);
        opacity: 1;
    }

    .icon_plhm {
        opacity: 0.5;
    }

    .plhm_mucdo_con {
        padding-left: 15px;
    }

    #tbDsPhanLoaiNhanh tr {
        cursor: pointer;
    }

    #divPhanLoaiXemAnh {
        position: relative;
    }

    .img-container-plhm {
        width: 95% !important;
        height: 100% !important;
    }
</style>


<div class="row mg-t-5" id="divCarClaimContentStep3PhanLoai" style="display:none;height:73vh">
    <div style="width:100%;padding-left:15px; padding-right:15px;">
        <form name="frmPhanLoaiNhanh" method="post">
            <div class="row">
                <div class="col-3 pr-0">
                    <div class="form-group">
                        <label class="_required">Vụ tổn thất</label>
                        <select class="select2 form-control custom-select" required name="vu_tt" style="width:100%">
                            <option value="">Chọn vụ tổn thất</option>
                        </select>
                    </div>
                </div>
                <div class="col-2 pr-0">
                    <div class="form-group">
                        <label class="_required">Nhóm tài liệu</label>
                        <select class="select2 form-control custom-select" required name="loai" style="width:100%">
                            <option value="TT" selected="selected">Tổn thất</option>
                            <option value="TL">Giấy tờ, tài liệu</option>
                            <option value="TC">Toàn cảnh, hiện trường</option>
                        </select>
                    </div>
                </div>
                <div class="col-2 pr-0">
                    <div class="form-group">
                        <label class="_required">Nghiệp vụ</label>
                        <select class="select2 form-control custom-select" required name="lh_nv" style="width:100%">
                            <option value="">Chọn nghiệp vụ</option>
                        </select>
                    </div>
                </div>
                <div class="col-5 pr-0" style="display:flex; padding-bottom:5px;">
                    <a href="#" class="mr-4" style="align-self:flex-end" id="btnXemViTriChupAnhPLHMNhanh">
                        <i class="fas fa-map-marker-alt" title="Xem chi tiết bản đồ"></i>
                        <span id="plhm_nhanh_xem_vi_tri_anh">Xem vị trí chụp ảnh</span>
                    </a>
                    <a href="#" style="align-self:flex-end" class="float-right mr-5" id="btnSoSanhDuLieuPLHMNhanh">
                        <i class="fas fa-adjust"></i> Đối chiếu dữ liệu hồ sơ gốc
                    </a>
                    <a href="#" style="align-self:flex-end" class="float-right mr-3" id="btnThoatPLHMNhanh"><i class="fas fa-sign-out"></i> Thoát</a>
                </div>
            </div>
        </form>
    </div>
    <div class="col-3 pr-0">
        <div class="card mb-0">
            <div class="card-body p-0">
                <div class="border rounded" style="height:68vh">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                        <h5 class="m-0">Hình ảnh hồ sơ</h5>
                    </div>
                    <div class="row">
                        <div class="col-12 mt-1">
                            <div style="max-height:465px;" class="scrollable list-pictures-plhm" id="divPLHMHinhAnh">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-2 pr-0">
        <div class="card mb-0">
            <div class="card-body p-0">
                <div class="border rounded" style="height:44vh">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                        <h5 class="m-0">Hạng mục tổn thất</h5>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <input type="text" style="z-index:99999999999;" class="form-control" id="inputPLHMTKiemHangMuc" placeholder="Nhập tên hạng mục" value="" />
                        </div>
                        <div class="col-12">
                            <div style="padding:5px;max-height:260px;" class="scrollable" id="divPLHMHangMuc">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mb-0 mt-2">
            <div class="card-body p-0">
                <div class="border rounded" style="height:23vh">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                        <h5 class="m-0">Phương án khắc phục</h5>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div style="padding:5px;" id="plhmPAKhacPhuc">
                                <p style="margin-bottom: 2px;padding-left: 5px;font-weight: bold;">
                                    <a href="#">PA khắc phục</a>
                                </p>
                                <div>
                                    <div class="form-check form-check-inline" style="margin-right:unset">
                                        <input class="form-check-input material-inputs with-gap" checked="checked" type="radio" name="plhmPASC" id="plhmPASC_S" value="S">
                                        <label class="form-check-label" style="font-size:12px; width: 113px;" for="plhmPASC_S">Sửa chữa</label>
                                    </div>
                                    <div class="form-check form-check-inline" style="margin-right:unset">
                                        <input class="form-check-input material-inputs with-gap" type="radio" name="plhmPASC" id="plhmPASC_T" value="T">
                                        <label class="form-check-label" style="font-size:12px;" for="plhmPASC_T">Thay thế</label>
                                    </div>
                                </div>
                                <p style="margin-bottom: 2px;padding-left: 5px;font-weight: bold;">
                                    <a href="#">Nơi sửa chữa</a>
                                </p>
                                <div>
                                    <div class="form-check form-check-inline" style="margin-right:unset">
                                        <input class="form-check-input material-inputs with-gap" checked="checked" type="radio" name="plhmPAChinhHang" id="plhmPAChinhHang_K" value="K">
                                        <label class="form-check-label" style="font-size:12px; width: 113px;" for="plhmPAChinhHang_K">Sửa chữa ngoài</label>
                                    </div>
                                    <div class="form-check form-check-inline" style="margin-right:unset">
                                        <input class="form-check-input material-inputs with-gap" type="radio" name="plhmPAChinhHang" id="plhmPAChinhHang_C" value="C">
                                        <label class="form-check-label" style="font-size:12px;" for="plhmPAChinhHang_C">Chính hãng</label>
                                    </div>
                                </div>
                                <p style="margin-bottom: 2px;padding-left: 5px;font-weight: bold;">
                                    <a href="#">Thu hồi vật tư</a>
                                </p>
                                <div>
                                    <div class="form-check form-check-inline" style="margin-right:unset">
                                        <input class="form-check-input material-inputs with-gap" checked="checked" type="radio" name="plhmPAThuHoi" id="plhmPAThuHoi_K" value="K">
                                        <label class="form-check-label" style="font-size:12px; width: 113px;" for="plhmPAThuHoi_K">Không</label>
                                    </div>
                                    <div class="form-check form-check-inline" style="margin-right:unset">
                                        <input class="form-check-input material-inputs with-gap" type="radio" name="plhmPAThuHoi" id="plhmPAThuHoi_C" value="C">
                                        <label class="form-check-label" style="font-size:12px;" for="plhmPAThuHoi_C">Có</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-2 pr-0">
        <div class="card mb-0">
            <div class="card-body p-0">
                <div class="border rounded" style="height:44vh">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                        <h5 class="m-0">Mức độ tổn thất</h5>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <input type="text" style="z-index:99999999" id="inputPLHMTKiemMucDo" class="form-control" placeholder="Nhập tên mức độ tổn thất" value="" />
                        </div>
                        <div class="col-12">
                            <div style="padding:5px;max-height:260px" class="scrollable" id="divPLHMMucDo">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mb-0 mt-2">
            <div class="card-body p-0">
                <div class="border rounded" style="height:23vh">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                        <h5 class="m-0">Xác định chi phí</h5>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div style="padding-left:10px; padding-right:10px">
                                <div class="form-group">
                                    <label for="">Chi phí hệ thống xác định tự động</label>
                                    <input type="text" class="form-control number" id="plhm-tien-tu-dong" readonly="readonly" value="0" autocomplete="off" placeholder="Chi phí hệ thống xác định tự động">
                                </div>
                            </div>
                            <div style="padding-left:10px; padding-right:10px">
                                <div class="form-group">
                                    <label for="">Chi phí giám định viên xác định</label>
                                    <input type="text" class="form-control number" id="plhm-tien-gd" autocomplete="off" value="0">
                                </div>
                            </div>
                            <div style="padding-left:10px; padding-right:10px">
                                <button type="button" class="btn btn-primary btn-sm btn-block" id="btnLuuPhanLoaiNhanh" title="Lưu thông tin phân loại">
                                    <i class="fa fa-save mr-2"></i> Lưu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-5 pr-0">
        <div class="card mb-0">
            <div class="card-body p-0">
                <div class="border rounded">
                    <div class="row" style="height:67.7vh">
                        <div class="col-12" id="divPhanLoaiDanhSach">
                            <div class="table-responsive scrollable" style="max-height:510px;">
                                <table class="table table-bordered fixed-header table-hover">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th>Hạng mục</th>
                                            <th style="width:165px">Mức độ tổn thất</th>
                                            <th style="width:90px">Phương án</th>
                                            <th style="width:94px">Chi phí</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tbDsPhanLoaiNhanh">
                                    </tbody>
                                    <tfoot>
                                        <tr class="card-title-bg">
                                            <td colspan="3">
                                                Tổng chi phí dự kiến:
                                            </td>
                                            <td class="text-right" id="tbDsPhanLoaiNhanhTongCong">1.000.000</td>
                                        </tr>
                                        <tr class="card-title-bg">
                                            <td colspan="4" style="font-size:10px; font-style:italic;">
                                                (<span style="color:red">*</span>):
                                                <i class="fas fa-tools ml-2 mr-1 icon_plhm"></i> <span class="mr-1">Sửa chữa</span>
                                                <i class="fas fa-tools mr-1 icon_plhm active"></i> <span class="mr-1">Thay thế</span>
                                                <i class="fad fa-car-garage mr-1 icon_plhm"></i> <span class="mr-1">Sửa chữa ngoài</span>
                                                <i class="fad fa-car-garage mr-1 icon_plhm active"></i> <span class="mr-1">Chính hãng</span>
                                                <i class="fas fa-inbox-in mr-1 icon_plhm"></i> <span class="mr-1">Không thu hồi</span>
                                                <i class="fas fa-inbox-in mr-1 icon_plhm active"></i> <span class="mr-1">Có thu hồi</span>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <div class="col-12" id="divPhanLoaiXemAnh">
                            <button type="button" onclick="anHienXemAnhPhanLoaiNhanh(false)" class="close" style="position:absolute; right:0px;right:20px; top:2px; z-index:9999999999;">×</button>
                            <div id="img-container-plhm" style="height:62vh"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
