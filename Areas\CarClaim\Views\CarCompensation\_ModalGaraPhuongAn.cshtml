﻿<div id="modalGaraPhuongAn" class="modal-drag" style="width:500px; z-index:9999999;">
    <div class="modal-drag-header">
        <h5><span class="modal-drag-title">Chọn gara</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_GaraPhuongAn" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalGaraPhuongAnElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalGaraPhuongAnDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnGaraPhuongAn">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalGaraPhuongAnDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsgarapa" data-id="ds_gara_pa_<%- item.ma_gara %>" data-text="<%- item.ma_gara.toLowerCase() %>-<%- item.ten_gara.toLowerCase() %>">
        <input type="checkbox" id="gara_pa_<%- item.ma_gara %>" data-bt-gara="<%- item.bt_gara %>" value="<%- item.ma_gara %>" class="custom-control-input modalGaraPhuongAnItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="gara_pa_<%- item.ma_gara %>"><%- item.ten_gara %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>