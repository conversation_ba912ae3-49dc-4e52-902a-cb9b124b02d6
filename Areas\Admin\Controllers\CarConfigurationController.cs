﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class CarConfigurationController : BaseController
    {
        #region Phương pháp tính khấu hao

        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> SavePhuongPhapKhauHao()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveLoaiXe()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_LOAI_XE_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SaveNgayKhauHao()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_NGAY_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Delete()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_LKE_CT, json);
            return Ok(data);
        }

        #endregion Phương pháp tính khấu hao

        #region Phương pháp tính giảm trừ - giảm trừ dkbs (Reduce)

        [AjaxOnly]
        public async Task<IActionResult> SaveReduce()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_GIAM_TRU_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetailReduce()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_GIAM_TRU_LKE_CT, json);
            return Ok(data);
        }

        #endregion Phương pháp tính giảm trừ - giảm trừ dkbs (Reduce)

        #region Cấu hình chung

        [AjaxOnly]
        public async Task<IActionResult> SaveCauHinhBT()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetailCar()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDsNgay()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_CAU_HINH_LKE_NGAY, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> deleteConfigKhauHao()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> deleteConfigKhauHaoLoaiXe()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_KHAU_HAO_LOAI_XE_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> deleteConfigGiamTru()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_XE_GIAM_TRU_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> deleteConfigCommonCar()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_X, json);
            return Ok(data);
        }

        #endregion Cấu hình chung

        #region Cấu hình bồi thường

        [AjaxOnly]
        public async Task<IActionResult> layCauHinhTheoNV()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuCauHinhBoiThuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_BOI_THUONG_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemCauHinhBoiThuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_BOI_THUONG_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaCauHinhBoiThuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_BOI_THUONG_X, json);
            return Ok(data);
        }

        #endregion Cấu hình bồi thường

        #region Cấu hình hồ sơ chứng từ

        [AjaxOnly]
        public async Task<IActionResult> luuThongTinHoSoChungTu()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_HO_SO_GIAY_TO_CAU_HINH_NHAP, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemCauHinhHoSo()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_HO_SO_GIAY_TO_CAU_HINH_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaCauHinhHoSo()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_HO_SO_GIAY_TO_CAU_HINH_XOA, json);
            return Ok(data);
        }

        #endregion Cấu hình hồ sơ chứng từ

        #region Cấu hình KPI

        [AjaxOnly]
        public async Task<IActionResult> luuThongTinTienTrinhKPI()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_TIEN_TRINH_XE_KPI_NHAP, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaThongTinTienTrinhKPI()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_TIEN_TRINH_XE_KPI_XOA, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xemThongTinChiTietNgayTien()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_TIEN_TRINH_XE_KPI_LKE_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layThongTinChiTietKPI()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_TIEN_TRINH_XE_KPI_LKE_CT, json);
            return Ok(data);
        }

        #endregion Cấu hình KPI

        #region Cấu hình SLA

        [AjaxOnly]
        public async Task<IActionResult> luuThongTinCauHinhSLA()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_SLA_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> lietKeThongTinCauHinhSLA()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_SLA_LKE_CT, json);
            return Ok(data);
        }

        #endregion Cấu hình SLA

        #region Cấu hình phân công theo địa bàn

        [AjaxOnly]
        public async Task<IActionResult> luuThongTinPhanCong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_PHAN_CONG_DIA_BAN_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> lietKeThongTinPhanCongDiaBan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_PHAN_CONG_DIA_BAN_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layChiTietThongTinPhanCongDiaBan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_PHAN_CONG_DIA_BAN_LKE_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> xoaThongTinPhanCongDiaBan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_PHAN_CONG_DIA_BAN_X, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layThongTinChiTietPhanCongDiaBan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_PHAN_CONG_DIA_BAN_LKE_CT, json);
            return Ok(data);
        }

        #endregion Cấu hình phân công theo địa bàn

        #region Cấu hình duyệt giá tự động

        [AjaxOnly]
        public async Task<IActionResult> layDsNgayADDuyetGiaTuDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layChiTietCHDuyetGiaTuDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuCHDuyetGiaTuDongNgayAD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_NGAY_AD_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuCHDuyetGiaDanhMuc()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_DANH_MUC_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layHangHieuXeCHDG()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_HANG_HIEU_XE_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layDSHangMucCHDGNhap()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_HANG_MUC_NH_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuDuLieuHangMucCHDGNhap()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_HANG_MUC_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> timKiemDsCauHinhDuyetGia()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DUYET_GIA_HANG_MUC_NH_TKIEM, json);
            return Ok(data);
        }

        #endregion Cấu hình duyệt giá tự động

        #region Cấu hình bên tham gia giám định mặc định

        [AjaxOnly]
        public async Task<IActionResult> luuThongTinCHBenGDMD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_CH_BEN_THAM_GIA_GD_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> lietKeThongTinCHBenGDMD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_CH_BEN_THAM_GIA_GD_LKE, json);
            return Ok(data);
        }

        #endregion Cấu hình bên tham gia giám định mặc định

        #region Cấu hình xử lý hồ sơ

        [AjaxOnly]
        public async Task<IActionResult> lietKeCauHinhXyLyBoiThuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_XLY_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuThongTinCauHinhXuLy()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_XE_CAU_HINH_XLY_NH, json);
            return Ok(data);
        }

        #endregion Cấu hình xử lý hồ sơ

        #region Cấu hình đơn vị thanh toán

        [AjaxOnly]
        public async Task<IActionResult> LayCauHinhDviThanhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DVI_THANH_TOAN_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> LuuCauHinhDviThanhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DVI_THANH_TOAN_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> XoaCauHinhDviThanhToan()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_DVI_THANH_TOAN_XOA, json);
            return Ok(data);
        }

        #endregion Cấu hình đơn vị thanh toán

        #region Cấu hình chặn biên độ ước

        [AjaxOnly]
        public async Task<IActionResult> lietKeCauHinhChanBienDoUoc()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_CAU_HINH_BIEN_DO_UOC_LKE, json);
            return Ok(data);
        }

        public async Task<IActionResult> luuCauHinhChanBienDoUoc()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_CAU_HINH_BIEN_DO_UOC_NH, json);
            return Ok(data);
        }

        #endregion Cấu hình chặn biên độ ước

        #region Cấu hình chặn biên độ ước theo điểm

        [AjaxOnly]
        public async Task<IActionResult> lietKeCauHinhChanBienDoUocTheoDiem()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_CAU_HINH_BIEN_DO_UOC_THEO_DIEM_LKE, json);
            return Ok(data);
        }

        public async Task<IActionResult> luuCauHinhChanBienDoUocTheoDiem()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_MA_CAU_HINH_BIEN_DO_UOC_THEO_DIEM_NH, json);
            return Ok(data);
        }

        #endregion Cấu hình chặn biên độ ước theo điểm

        #region Cấu hình mở hồ sơ nhà đồng

        [AjaxOnly]
        public async Task<IActionResult> layDsNgayADMoSoHoNhaDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_MO_HO_SO_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> layChiTietCHMoHoSoNhaDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_MO_HO_SO_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuCHMoHoSoNhaDongNgayAD()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_MO_HO_SO_NGAY_AD_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> luuCHMoHoSoNhaDong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_MO_HO_SO_DANH_MUC_NH, json);
            return Ok(data);
        }

        #endregion Cấu hình mở hồ sơ nhà đồng

        #region Cấu hình phương án chi trả đồng bảo hiểm

        [AjaxOnly]
        public async Task<IActionResult> layThongTinPhuongAnChiTraDongBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_PHUONG_AN_TINH_TOAN_DONG_BH_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> nhapThongTinPhuongAnChiTraDongBH()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_CAU_HINH_PHUONG_AN_TINH_TOAN_DONG_BH_NH, json);
            return Ok(data);
        }

        #endregion Cấu hình phương án chi trả đồng bảo hiểm

        #region Cấu hình tạm ứng bồi thường

        [AjaxOnly]
        public async Task<IActionResult> layCauHinhTamUngBoiThuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_CAU_HINH_TAM_UNG_BOI_THUONG_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> nhapCauHinhTamUngBoiThuong()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PBH_BT_CAU_HINH_TAM_UNG_BOI_THUONG_NH, json);
            return Ok(data);
        }

        #endregion Cấu hình tạm ứng bồi thường
    }
}