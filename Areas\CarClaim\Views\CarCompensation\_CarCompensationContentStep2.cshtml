﻿<div class="row mg-t-10">
    <div class="col-9 pr-0">
        <div class="card mb-0 modal-main-content">
            <div class="card-body px-0" style="padding-top:0 !important">
                <div class="border rounded">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                        <h5 class="m-0" id="ImgContainerTitle"><PERSON><PERSON><PERSON><PERSON> s<PERSON></h5>
                        <div class="btn-group float-right">
                            <a class="btn btn-light rounded py-0 font-weight-bold d-none" id="image_fullsreen">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div id="img-container" style="height:62vh"></div>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top: -30px; margin-left: 2px; display: block;">
                    <form class="form-inline" name="frmNgayChup" method="post">
                        <div class="form-group mb-2">
                            <input type="text" autocomplete="off" class="form-control" style="width:95px; background: #54667a0a; border: none; position: relative;" name="ngay" readonly placeholder="Ngày chụp">
                        </div>
                        <span style="margin-top: -8px; font-weight: bold;">-</span>
                        <div class="form-group mb-2">
                            <input type="text" autocomplete="off" class="form-control" style="width: 127px; background: #54667a0a; border: none; position: relative;" name="nsd" readonly placeholder="Người tải ảnh">
                        </div>
                    </form>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <form class="form-inline" name="frmToaDoAnh" method="post">
                            <div class="form-group mb-2">
                                <label style="padding-top:4px; padding-right:5px">X:</label>
                                <input type="text" autocomplete="off" style="width:80px" class="form-control" name="kinh_do" readonly placeholder="Kinh độ">
                            </div>
                            <div class="form-group mx-sm-3 mb-2">
                                <label style="padding-top:4px; padding-right:5px">Y:</label>
                                <input type="text" autocomplete="off" style="width:80px" class="form-control" name="vi_do" readonly placeholder="Vĩ độ">
                            </div>
                            <div class="form-group mb-2">
                                <a href="#" id="btnXemViTriChupAnh"><i class="fas fa-map-marker-alt mr-1" title="Xem chi tiết bản đồ"></i> Xem vị trí</a>
                            </div>
                        </form>
                    </div>
                    <div class="col-6">
                        <div class="form-group mb-2">
                            <a href="#" style="padding-top:5px;" class="btnXemVideoDGRRHD float-right d-none"><i class="fas fa-file-video mr-1"></i>Xem Video</a>
                            <a href="#" style="padding-top:5px;" class="btnXemVideo float-right"><i class="fas fa-file-video mr-1"></i>Xem Video, File ghi âm</a>
                            <a href="#" id="btnSoSanhDuLieu" style="padding-top:5px;" class="float-right mr-3"><i class="fas fa-adjust mr-1"></i> Đối chiếu OCR</a>
                            <a href="#" id="btnXemHangMucDGRR" onclick="onXemHangMucDGRR()" style="padding-top:5px;" class="float-right mr-3 d-none"><i class="fas fa-images mr-1"></i> Xem ĐGRR</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card m-0">
            <div class="card-body p-0">
                <div class="border rounded">
                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg border-bottom">
                        <div class="btn-group float-right" style="color: var(--escs_theme_color);">
                            <a class="btn btn-light rounded py-0" onclick="onClickChonLoaiGiayTo(this)">
                                <input type="hidden" id="input_loai_giay_to_chon" value="" />
                                <i class="fas fa-filter" title="Bộ lọc hình ảnh hồ sơ giấy tờ"></i>
                            </a>
                            <a class="btn btn-light rounded py-0" id="btnTransImageView">
                                <i class="fas fa-th"></i>
                            </a>
                            <a class="btn btn-light rounded py-0" id="btnAnhHopDong">
                                <i class="fas fa-file-contract" title="Click để xem ảnh hợp đồng"></i>
                            </a>
                            <a class="btn btn-light rounded py-0 px-2 mr-1" id="btnAnhDaXoa">
                                <i class="fas fa-folder-times" title="Click để xem ảnh đã xóa"></i>
                            </a>
                            <a class="btn btn-light rounded py-0 mr-1" id="btnXemDanhSachTaiLieu">
                                <i class="fa fa-print" title="Click để xem danh sách tài liệu"></i>
                            </a>
                        </div>
                        <div class="btn-group float-right">
                            <a class="btn btn-light rounded py-0" data-toggle="dropdown" data-display="static" aria-haspopup="true" aria-expanded="false">
                                <i class="fal fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right border" id="dsNhomAnh">
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid scrollable" id="lstImage" style="height: 57vh;">
                        <div class="row">
                            <div class="col-12 list-pictures" id="dsAnhTonThat">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="btn-group btn-group-justified text-center" role="group">
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Phân loại danh mục" id="btnViewAnhListDGTT">
                        <i class="fas fa-atlas"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Tải xuống" id="btnDownLoadAnhDGTT">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Tải lên" id="btnUpLoadAnhDGTT">
                        <i class="fas fa-upload"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="In ảnh" id="btnXemTaiLieu">
                        <i class="fas fa-print"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Xóa" id="btnXoaLoadAnhDGTT">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
