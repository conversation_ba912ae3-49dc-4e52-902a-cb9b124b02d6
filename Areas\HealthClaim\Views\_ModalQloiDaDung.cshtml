﻿<div id="modalQuyenLoiDaDung" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title"><PERSON><PERSON> sơ quyền lợi đã đư<PERSON><PERSON> sử dụng</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-2">
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table id="tableDsHoSoQLDaDung" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold text-center uppercase">
                                    <tr>

                                        <th width="15%"><PERSON><PERSON> <PERSON>ồ sơ</th>
                                        <th width="15%"><PERSON><PERSON><PERSON></th>
                                        <th width="10%">Ngày mở</th>
                                        <th width="10%">Ng<PERSON>y vào viện</th>
                                        <th width="10%">Ngày ra viện</th>
                                        <th width="10%">Ngày duyệt</th>
                                        <th width="10%">Tiền duyệt</th>
                                        <th width="10%">Hồ sơ BL</th>
                                        <th width="10%">Hồ sơ BT</th>
                                    </tr>
                                </thead>
                                <tbody id="bodyDsHoSoQLDaDung">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-2">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongYeuCauBoSungHoSo">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

@*Danh sách hồ sơ đã sử dụng quyền lợi*@
<script type="text/html" id="bodyDsHoSoQLDaDungTemplate">
    <% if(data.length > 0){ %>
    <% _.forEach(data, function(item, index) { %>
    <tr>

        <td style="text-align:center">
            <a data-field="so_hs"><%- item.so_hs %></a>
        </td>
        <td class="text-center">
            <a data-field="lan"><%- item.lan %></a>
        </td>
        <td style="text-align:center">
            <a data-field="ngay_ht"><%- item.ngay_ht %></a>
        </td>
        <td class="text-center">
            <a data-field="ngay_vv"><%- item.ngay_vv %></a>
        </td>
        <td class="text-center">
            <a data-field="ngay_rv"><%- item.ngay_rv %></a>
        </td>
        <td style="text-align:center">
            <a data-field="ngay_duyet"><%- item.ngay_duyet %></a>
        </td>
        <td style="text-align:right">
            <a data-field="so_tien_duyet"><%- ESUtil.formatMoney(item.so_tien_duyet) %></a>
        </td>
        <td class="text-center">
            <% if(item.loai == 'BLVP'){ %>
            <a onclick="xemHoSoConNguoi('<%- item.so_id %>', '<%- item.loai %>')">
                <i class="fas fa-shield-cross" style="color:red" title="Bảo lãnh viện phí"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.loai == 'HSTT'){ %>
            <a onclick="xemHoSoConNguoi('<%- item.so_id %>', '<%- item.loai %>')">
                <i class="fas fa-user-nurse" style="color:blue" title="Tiếp nhận hồ sơ trực tiếp"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="9">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>