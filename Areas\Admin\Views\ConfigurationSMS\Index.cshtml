﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình mẫu sms";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình mẫu SMS</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Mẫu SMS</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate" data-select2-id="20">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin mã, tên mẫu sms" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="N">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top:21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnNhapCauHinhSMS">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewCauHinhSMS" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapCauHinhSMS" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinCauHinhSMS" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin cấu hình mẫu SMS</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="ngay_ad" class="_required">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" required="" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="N">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Kiểm duyệt</label>
                                <select class="select2 form-control custom-select" required name="kiem_duyet" style="width: 100%; height:36px;">
                                    <option value="">Chọn kiểm duyệt</option>
                                    <option value="C">Có</option>
                                    <option value="K">Không</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Mã</label>
                                <input type="text" maxlength="30" placeholder="Mã cấu hình mẫu sms" name="ma" autocomplete="off" required class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-group">
                                <label class="_required">Tên</label>
                                <input type="text" maxlength="200" placeholder="Tên cấu hình mẫu sms" name="ten" autocomplete="off" required class="form-control">
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="_required">Nội dung</label>
                                <textarea class="form-control" maxlength="250" required placeholder="Nội dung mẫu sms: Ma xac thuc ho so {SO_HS} là : {A}" autocomplete="off" rows="2" name="noi_dung"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="">Mô tả tham số</label>
                                <textarea class="form-control" maxlength="250" placeholder="Các tham số cấu hình: {A}, {B}" autocomplete="off" rows="2" name="mo_ta_tham_so"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinCauHinhSMS"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ConfigurationSMSService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ConfigurationSMS.js" asp-append-version="true"></script>
}