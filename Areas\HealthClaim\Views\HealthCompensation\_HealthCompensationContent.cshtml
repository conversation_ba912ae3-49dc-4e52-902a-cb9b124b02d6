﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
<div class="card border mb-0 p-2 h-100 d-flex flex-column" style="height:unset">
    <div class="card-body p-1" style="padding: 0px; height: 83vh !important;" id="navBoiThuong">
        <ul class="nav nav-pills font-weight-bold" id="HealthCareContentTab" role="tablist">
            <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                <a class="nav-link" data-toggle="collapse" href="#sidebar_info" role="button" id="main_collapse">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                <a class="nav-link active" data-toggle="tab" id="tabThongTinYeuCau_click" href="#tabThongTinYeuCau" role="tab" aria-controls="home" aria-selected="true">
                    <i class="fas fa-align-justify mr-2"></i>Yêu cầu trả tiền BH
                </a>
            </li>
            <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                <a class="nav-link" data-toggle="tab" onclick="layDsTaiLieu()" href="#tabHoSoGiayTo" role="tab" aria-controls="home" aria-selected="false">
                    <i class="fas fa-file-image  mr-2"></i>Hồ sơ, chứng từ yêu cầu
                </a>
            </li>
            <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                <a class="nav-link" data-toggle="tab" href="#tabTinhToanBoiThuong" role="tab" aria-controls="profile" aria-selected="false">
                    <i class="far fa-calculator-alt mr-2"></i>Tính toán bồi thường
                </a>
            </li>
            <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                <a class="nav-link" data-toggle="tab" onclick="loadChungTuThuHuong()" href="#tabThongTinThanhToan" role="tab" aria-controls="profile" aria-selected="false">
                    <i class="fal fa-money-bill-alt mr-2"></i>Hóa đơn, thụ hưởng
                </a>
            </li>
            <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                <a class="nav-link" data-toggle="tab" onclick="layLichSuTonThat()" href="#tabThongTinLichSuTonThat" role="tab" aria-controls="profile" aria-selected="false">
                    <i class="fas fa-history mr-2"></i>Lịch sử trả tiền BH
                </a>
            </li>
            <li class="nav-item mr-2" style="font-weight: bold; background-color: #f8f9fa; border-radius: 5px;">
                <a class="nav-link" data-toggle="tab" onclick="layLichSuTonThatTop5()" href="#tabThongTinTop5LichSuTonThat" role="tab" aria-controls="profile" aria-selected="false">
                    <i class="fas fa-layer-group mr-2"></i>Top 5 HĐ tái tục
                </a>
            </li>
        </ul>
        <div class="tab-content" style="border: unset;">
            <div class="tab-pane active" role="tabpanel" id="tabThongTinYeuCau" style="padding:10px;">
                <partial name="_HealthCompensationContent_1" />
            </div>
            <div class="tab-pane" role="tabpanel" id="tabHoSoGiayTo" style="padding:10px;">
                <partial name="_HealthCompensationContent_2" />
            </div>
            <div class="tab-pane" role="tabpanel" id="tabTinhToanBoiThuong" style="padding:10px;">
                <partial name="_HealthCompensationContent_3" />
            </div>
            <div class="tab-pane" role="tabpanel" id="tabThongTinThanhToan" style="padding:10px;">
                <partial name="_HealthCompensationContent_4" />
            </div>
            <div class="tab-pane pl-0" role="tabpanel" id="tabThongTinLichSuTonThat" style="position: absolute; width:100%; padding:10px;">
                <partial name="_HealthCompensationContent_5" />
            </div>
            <div class="tab-pane pl-0" role="tabpanel" id="tabThongTinTop5LichSuTonThat" style="position: absolute; width:100%; padding:10px;">
                <partial name="_HealthCompensationContent_6" />
            </div>
        </div>
    </div>
</div>
<div class="row tab-navigator">
    <div class="col-12 mg-t-10 px-2">
        <a href="javascript:void(0)" id="btnNhanHoSo" class="escs_pquyen mr-2">
            <i class="fas fa-files-medical mr-1"></i> Nhận hồ sơ
        </a>
        <a href="javascript:void(0)" id="btnTraHoSo" class="escs_pquyen mr-2">
            <i class="fas fa-chevron-double-left mr-1"></i> Trả hồ sơ về bộ phận tiếp nhận
        </a>
        <a href="javascript:void(0)" id="btnXemThongTinTyGia" class="mr-2">
            <i class="fas fa-balance-scale mr-1"></i> Tỷ giá
        </a>
        <a href="javascript:void(0)" id="btnXacNhanKyTay" class="escs_pquyen mr-2">
            <i class="fas fa-check mr-1"></i> Xác nhận KH ký tay
        </a>
        <a href="javascript:void(0)" id="btnXemQRCode" class="mr-2">
            <i class="fas fa-qrcode mr-1"></i> Xem QRCode
        </a>
        <a href="javascript:void(0)" id="btnBangKe" class="mr-2">
            <i class="fas fa-edit mr-1"></i> Bảng kê chi tiết
        </a>
        <a href="javascript:void(0)" id="btnYeuCauBoSungHoSo" class="escs_pquyen mr-2">
            <i class="fas fa-files-medical mr-1"></i> Yêu cầu bổ sung hồ sơ
        </a>
        <a href="#" class="mr-2 d-none" id="btnPhuongAnChiTra">
            <i class="fas fa-usd-circle mr-1"></i>Phương án chi trả
        </a>
        <a href="javascript:void(0)" id="btnTrinhPhuongAn" class="escs_pquyen mr-2" data-toggle="modal">
            <i class="fas fa-share-square mr-1"></i> Trình phương án
        </a>
        <a href="javascript:void(0)" id="btnTrinhTuChoiBT" class="escs_pquyen mr-2" data-toggle="modal">
            <i class="fas fa-share-square mr-1"></i> Trình từ chối chi tiền BH
        </a>
        <a href="javascript:void(0)" id="btnChuyenThanhToan" class="escs_pquyen mr-2">
            <i class="fas fa-share-square mr-1"></i> Chuyển thanh toán
        </a>
        <a href="#" id="btnHuychuyenboithuong" class="escs_pquyen mr-2">
            <i class="fas fa-undo mr-1"></i> Hủy chuyển thanh toán
        </a>
        <a href="#" id="btnCopyHoSo" class="escs_pquyen mr-2">
            <i class="fas fa-share-square mr-1"></i> Yêu cầu trả thêm tiền bảo hiểm
        </a>
        <a href="javascript:void(0)" id="btnXemQlMIC" class="mr-2">
            <i class="fas fa-file-search mr-1"></i> Xem quyền lợi MIC
        </a>
        <a href="javascript:void(0)" id="btnGuiEmailThongBao" class="escs_pquyen mr-2" data-toggle="modal">
            <i class="fas fa-envelope mr-1"></i> Gửi email
        </a>
        <a href="#" id="btnPrint" class="mr-2">
            <i class="fas fa-print mr-1"></i> In ấn
        </a>
        <a href="#" onclick="TransYKienTraoDoi()" class="mr-2">
            <i class="fad fa-comment-alt-dots mr-1"></i>Trao đổi ý kiến
        </a>
    </div>
</div>

