﻿<div id="modalChonDoiTuongTT" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header px-3 border-bottom">
        <h5><span class="modal-drag-title"><PERSON><PERSON><PERSON> tượng tổn thất</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonDoiTuongTTElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonDoiTuongTTDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonDoiTuongTT">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalChonDoiTuongTTDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox" data-text="">
        <input type="checkbox" id="doi_tuong_tt_<%- item.so_id_doi_tuong %>" data-val="<%- item.ten_doi_tuong %>" value="<%- item.so_id_doi_tuong %>" class="custom-control-input modalChonDoiTuongTTItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="doi_tuong_tt_<%- item.so_id_doi_tuong %>"><%- item.ten_doi_tuong %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>