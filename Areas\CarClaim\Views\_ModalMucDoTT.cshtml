﻿<div id="modalMucDoTT" class="modal-drag" style="width:280px; z-index:9999999;">
    <div class="modal-drag-header px-2 border-bottom">
        <h5><span class="modal-drag-title">Ch<PERSON><PERSON> mức độ tổn thất</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_MucDoTT" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalMucDoTTElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalMucDoTTDanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonMucDoTT">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalMucDoTTDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dsmdtt" data-id="dsmdtt_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="mdtt_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalMucDoTTItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="mdtt_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<div id="modalChonMucDoTT" class="modal-drag" style="width:280px; z-index:9999999;">
    <div class="modal-drag-header px-2 border-bottom">
        <h6><span class="modal-drag-title">Chọn mức độ tổn thất</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h6>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input id="inputSearch_ChonMucDoTT" type="text" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonMucDoTTElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonMucDoTTDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonMucDoTonThat">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalChonMucDoTTDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox dscmdtt" data-id="dscmdtt_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="mdttts_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonMucDoTTItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="mdttts_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>