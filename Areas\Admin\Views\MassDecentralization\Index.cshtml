﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    ViewData["Title"] = "Cấu hình phân quyền";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Đơn vị</label>
                                <select class="select2 form-control custom-select" required name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2 d-none">
                            <div class="form-group">
                                <label class="">Chức năng</label>
                                <select class="select2 form-control custom-select" name="nhom_chuc_nang" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin tên/ mã" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnThem">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div id="gridViewDsChucNang" class="table-app" style="height: 64vh;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapChucNang" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-xl" role="document" style="max-width:unset; width:60%">
        <div class="modal-content">
            <form name="frmLuuChucNang" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Cấu hình phân quyền</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="">Đơn vị</label>
                                <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Chức năng</label>
                                <select class="select2 form-control custom-select" required name="nhom_chuc_nang" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive" id="divPhanQuyen" style="max-height: 350px;">
                        <table id="tablePhanQuyen" class="table table-bordered fixed-header">
                            <thead class="font-weight-bold card-title-bg">
                                <tr class="text-center uppercase" style="background-color: var(--escs-main-theme-color);color:#FFF;">
                                    <th style="width:20px" class="text-center">STT</th>
                                    <th style="width:35%">Mã cán bộ</th>
                                    <th style="width:35%">Tên cán bộ</th>
                                    <th style="width:10%">Đơn vị</th>
                                    <th style="width: 10%">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" onchange="onNhapTatCaChange(this)" id="nhap_tat_ca" class="custom-control-input">
                                            <label class="custom-control-label" for="nhap_tat_ca"><b style="font-weight:bold">Nhập</b></label>
                                        </div>
                                    </th>
                                    <th style="width: 10%">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" onchange="onXemTatCaChange(this)" id="xem_tat_ca" class="custom-control-input">
                                            <label class="custom-control-label" for="xem_tat_ca"><b style="font-weight:bold">Xem</b></label>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="tblPhanQuyen"></tbody>
                            <tfoot>
                                <tr class="card-title-bg">
                                    <td></td>
                                    <td colspan="2">
                                        <input type="text" name="tim" id="inputSearchCanBo" autocomplete="off" placeholder="Nhập thông tin mã/tên cán bộ" class="form-control">
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-block pb-5">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-1"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinPhanQuyen"><i class="fa fa-save mr-1"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/html" id="tblPhanQuyen_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="divPhanQuyenSearch" data-search="<%- ESUtil.xoaKhoangTrangText(item.ma + item.ten)%>">
        <td class="text-center"><%- index + 1 %></td>
        <td>
            <input type="hidden" data-val="<%- item.ma %>" data-field="ma" />
            <input type="hidden" data-val="<%- item.ten %>" data-field="ten" />
            <input type="hidden" data-val="<%- item.ma_chi_nhanh %>" data-field="ma_chi_nhanh" />
            <%- item.ma %>
        </td>
        <td><%- item.ten %></td>
        <td class="text-center"><%- item.ma_chi_nhanh %></td>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="onNhapChange(this)" data-field="nhap" name="quyen[<%- index %>][nhap]" id="nhap_<%- item.ma %>" <% if(item.nhap === '1'){%> checked="checked" <%}%>  class="custom-control-input nhap-item">
                <label class="custom-control-label" for="nhap_<%- item.ma %>"><b style="font-weight:bold">&nbsp;</b></label>
            </div>
        </td>
        <td class="text-center">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" onchange="onXemChange(this)" data-field="xem" name="quyen[<%- index %>][xem]" id="xem_<%- item.ma %>" <% if(item.xem === '1'){%> checked="checked" <%}%> class="custom-control-input xem-item">
                <label class="custom-control-label" for="xem_<%- item.ma %>"><b style="font-weight:bold">&nbsp;</b></label>
            </div>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 6) {
    for(var i = 0; i < 6 - danh_sach.length;i++ ) { %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@section Scripts {
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/FunctionService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/MassDecentralizationService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/MassDecentralization.js" asp-append-version="true"></script>
}