﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh sách tin nhắn MCM";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Danh sách tin nhắn MCM</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Danh sách tin nhắn MCM</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập tên chiến dịch" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapDsTinNhan">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewDanhSachTinNhan" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapDsTinNhan" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="max-width:65%;">
        <div class="modal-content">
            <form name="frmLuuThongTinDsTinNhan" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Danh sách tin nhắn MCM</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Temid</label>
                                <input type="text" autocomplete="off" maxlength="20" name="tempid" required class="form-control" placeholder="Temid">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Tên chiến dịch</label>
                                <input type="text" autocomplete="off" maxlength="250" name="ten_chien_dich" required class="form-control" placeholder="Tên chiến dịch">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Channel</label>
                                <input type="text" autocomplete="off" maxlength="20" name="channel" required class="form-control" placeholder="Channel">
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:3px">
                        <div class="col-sm-6">
                            <div class="form-group green-border-focus">
                                <label class="_required">Nội dung SMS</label>
                                <textarea style="height: 100px;" class="form-control" required maxlength="500" name="nd_sms" placeholder="Nội dung SMS" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group green-border-focus">
                                <label class="">Param zalo</label>
                                <textarea style="height: 100px;" class="form-control" maxlength="500" name="param_zalo" placeholder="Param zalo" rows="2"></textarea>
                            </div>
                        </div>
                        
                    </div>
                    <div class="row" style="margin-top:3px">
                        <div class="col-sm-6">
                            <div class="form-group green-border-focus">
                                <label class="">Param viber</label>
                                <textarea style="height: 100px;" class="form-control" maxlength="500" name="param_viber" placeholder="Param viber" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group green-border-focus">
                                <label class="">Param sms</label>
                                <textarea style="height: 100px;" class="form-control" maxlength="500" name="param_sms" placeholder="Param sms" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongTinDsTinNhan"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongTinDsTinNhan"><i class="fas fa-trash-alt"></i> Xóa</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ListMessageMCMService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ListMessageMCM.js" asp-append-version="true"></script>
}