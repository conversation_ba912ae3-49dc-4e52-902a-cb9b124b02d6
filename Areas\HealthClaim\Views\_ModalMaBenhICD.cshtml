﻿<div id="popoverMaBenhICD" class="modal-drag" style="width: 280px; z-index: 9999999; margin-top: 5px !important; margin-left: -10px !important; max-width:unset;width:650px">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Chọn nhóm nguyên nhân</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div style="width:100%; margin-bottom:10px;">
            <div class="input-group">
                <input type="text" class="form-control" id="inputTimKiemMaBenhICD" placeholder="Tìm kiếm bệnh" value="" />
                <input type="hidden" id="inputTimKiemMaBenhICD_ma" />

                <div class="input-group-append">
                    <label class="input-group-text">
                        <a href="javascript:void(0)" onclick="getPagingMaBenhICD(1)">
                            <i class="fa fa-search"></i>
                        </a>
                    </label>
                </div>
            </div>
        </div>
        <div id="dsMaBenhICD" class="scrollable" style="max-height:450px">
        </div>
        <div id="dsMaBenhICD_pagination"></div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnMaBenhICD">
            <i class="fa fa-save mr-2"></i>Lưu
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongMaBenhICD">
            <i class="fas fa-window-close mr-2"></i>Đóng
        </button>
    </div>
</div>


@* <div id="popoverMaBenhICD" class="popover popover-x popover-default" style="display:none; max-width:unset;width:650px">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span class="close pull-right" data-dismiss="popover-x">&times;</span>Mã bệnh ICD
    </h3>
    <div class="popover-body popover-content">
        <div style="width:100%; margin-bottom:10px;">
            <div class="input-group">
                <input type="text" class="form-control" id="inputTimKiemMaBenhICD" placeholder="Tìm kiếm bệnh" value="" />
                <input type="hidden" id="inputTimKiemMaBenhICD_ma" />

                <div class="input-group-append">
                    <label class="input-group-text">
                        <a href="javascript:void(0)" onclick="getPagingMaBenhICD(1)">
                            <i class="fa fa-search"></i>
                        </a>
                    </label>
                </div>
            </div>
        </div>
        <div id="dsMaBenhICD" class="scrollable" style="max-height:450px">
        </div>
        <div id="dsMaBenhICD_pagination"></div>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" id="btnMaBenhICD">
            <i class="fa fa-save mr-2"></i>Lưu
        </button>
        <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongMaBenhICD">
            <i class="fas fa-window-close mr-2"></i>Đóng
        </button>
    </div>
</div> *@

@*Danh sách mã bệnh ICD*@
<script type="text/html" id="dsMaBenhICDTemplate">
    <% _.forEach(ds_ma_benh_icd, function(item, index) { %>
    <div class="custom-control custom-checkbox" data-text="<%- item.ten_tim %>">
        <input type="checkbox" onchange="onChonMaBenhICD(this)" id="ma_benh_icd_<%- item.ma.replace(/\./g, '') %>" value="<%- item.ma %>" class="custom-control-input item-ma-benh-icd">
        <label class="custom-control-label" for="ma_benh_icd_<%- item.ma.replace(/\./g, '') %>"><%- item.ten_v %></label>
    </div>
    <%})%>
</script>