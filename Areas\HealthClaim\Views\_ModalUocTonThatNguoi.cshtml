﻿<div id="modalUocTonThatNguoi" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true" style="z-index: 1600;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Ước tổn thất</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmUocTonThatNguoi" name="frmUocTonThatNguoi" method="post">
                    <input type="hidden" name="ma_doi_tac" value="" />
                    <input type="hidden" name="so_id" value="" />
                    <input type="hidden" name="so_id_hd" value="" />
                    <input type="hidden" name="so_id_dt" value="" />
                    <div class="row">
                        <div class="col col-12">
                            <div class="table-responsive" style="max-height:380px">
                                <table id="tableDSUocTonThatNVNguoi" class="table table-bordered fixed-header" style="width:100%">
                                    <thead class="font-weight-bold">
                                        <tr class="text-center uppercase">
                                            <th width="13%">Số tiền ước</th>
                                            <th width="13%">% Thuế</th>
                                            <th width="13%">Tiền thuế</th>
                                            <th width="13%">Tổng cộng</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dsUocTonThatNVNguoi"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="display:block;">
                <button type="button" class="btn btn-primary btn-sm mg-t-22" id="btnChuyenCoreNguoi"><i class="fas fa-angle-double-right mr-2"></i>Chuyển core</button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
                <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-22 float-right" id="btnLuuDongUocTTNguoi"><i class="fas fa-hdd mr-2"></i>Lưu & đóng</button>
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22 float-right" id="btnLuuUocTTNguoi"><i class="fas fa-save mr-2"></i>Lưu</button>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="dsUocTonThatNVNguoi_template">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="row-item">
        <td class="text-right">
            <input type="text" data-field="uoc_ton_that" name="uoc_ton_that" class="number floating-input" value="<%- ESUtil.formatMoney(item.uoc_ton_that) %>" />
        </td>
        <td>
            <input type="text" maxlength="2" name="tl_thue" data-field="tl_thue" class="number floating-input" value="<%- item.tl_thue %>" />
        </td>
        <td class="text-right">
            <a href="#" data-field="tien_thue" id="tien_thue" name="tien_thue" value="<%- item.tien_thue %>">0</a>
        </td>
        <td class="text-right">
            <a href="#" data-field="tong_cong">0</a>
        </td>
    </tr>
    <% })} else{ %>
    <tr>
        <td>
            <input type="text" name="uoc_ton_that" class="number floating-input" placeholder="Nhập số tiền ước" value=""/>
        </td>
        <td>
            <input type="text" name="tl_thue" class="number floating-input" placeholder="Nhập tỷ lệ thuế" value=""/>
        </td>
        <td>
            <a href="#" data-field="tien_thue" id="tien_thue" name="tien_thue">0</a>
        </td>
        <td>
            <a href="#" data-field="tong_cong">0</a>
        </td>
    </tr>
    <% } %>
</script>