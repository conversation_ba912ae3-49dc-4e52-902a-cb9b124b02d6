﻿@*  Tab Thông tin chung *@
<script type="text/html" id="navThongTinChung_template">
    <div class="card-body p-1">
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Thông tin xe tổn thất</h5>
            </div>
            <table class="table">
                <tr>
                    <td style="width:105px; color:#999999">Ng<PERSON>y thông báo</td>
                    <td><%- ho_so.ngay_tb %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999"><PERSON><PERSON><PERSON> tổn thất</td>
                    <td><%- ho_so.ngay_xr %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Nguồn TB</td>
                    <td><%- ho_so.nguon_tb %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Họ tên lái xe</td>
                    <td><%- ho_so.ten_lxe %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Số GPLX</td>
                    <td>
                        <span><%- ho_so.gplx_so %></span>
                        <span class="float-right">
                             <a href="#" onclick="onClickXemDanhSachHoSo(this)"><i class="fas fa-print-search" title="Xem danh sách hồ sơ"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Thời hạn GPLX</td>
                    <td><%- ho_so.thoi_han_gplx %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Tên nghiệp vụ</td>
                    <td><%- ho_so.nv_ct %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Trạng thái xử lý</td>
                    <td style="color:var(--escs_theme_color)"><%- ho_so.trang_thai %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Giám định viên</td>
                    <td><%- ho_so.ten_gdv %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Bồi thường viên</td>
                    <td><%- ho_so.ten_btv %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Tổng tiền duyệt</td>
                    <td><%- ESUtil.formatMoney(ho_so.so_tien) %> VND</td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Ước tổn thất</td>
                    <td>
                        <span class="uoc_ton_that"><%- ESUtil.formatMoney(ho_so.uoc_ton_that) %></span> VND
                    </td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Ước theo điểm</td>
                    <td>
                        <span class="uoc_ton_that"><%- ESUtil.formatMoney(ho_so.uoc_ton_that_diem) %></span> VND
                        <span class="float-right">
                            <a href="#" onclick="capNhatUocTheoDiem(this)"><i class="fas fa-edit" title="Cập nhật ước tổn thất theo điểm"></i></a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Ngày thanh toán</td>
                    <td><%- ho_so.ngay_thanh_toan %></td>
                </tr>
            </table>
        </div>
        <div class="border rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Đối tượng bảo hiểm</h5>
            </div>
            <table class="table">
                <tbody>
                    <tr>
                        <td style="width:105px; color:#999999">Biển xe</td>
                        <td><%- ho_so.doi_tuong_hthi %></td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Số GCN</td>
                        <td>
                            <span><%- ho_so.gcn %></span>
                            <span class="float-right">
                                <a href="#" onclick="xemThongTinGiayChungNhanCarcompensation()">
                                    <i class="fas fa-eye" title="Xem chi tiết GCN bảo hiểm"></i>
                                </a>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Số hợp đồng</td>
                        <td><%- ho_so.so_hd %></td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Chương trình BH</td>
                        <td><%- ho_so.ma_sp_doi_tac %></td>
                    </tr>
                    <tr>
                         <td style="width:105px; color:#999999">Đồng tái BH</td>
                        <td>
                            <span class="dong_tai_bh"><%- ho_so.dong_tai_bh %></span> 
                            <span class="float-right" id="uoc_ton_that">
                                <a href="#" onclick="xemThongTinDongTaiBH?.()"><i class="fas fa-sync-alt" title="Cập nhật thông tin đồng tái"></i></a>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Hiệu lực BH</td>
                        <td><%- ho_so.hieu_luc %></td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Giá trị xe</td>
                        <td><%- ho_so.gia_tri %> VND</td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Số tiền BH</td>
                        <td><%- ho_so.stbh %></td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Mức khấu trừ</td>
                        <td><%- ho_so.mien_thuong_hs%> VND</td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Thanh toán phí</td>
                        <td><%=ho_so.thanh_toan_phi%></td>
                    </tr>
                    @* <tr>
                        <td style="width:105px; color:#999999">Xác minh phí</td>
                        <td>
                             <a href="#" onclick="xemMauInXacMinhPhi?.()">Xem mẫu in xác minh phí</a>
                        </td>
                    </tr> *@
                    <tr>
                        <td style="width:105px; color:#999999">Chủ xe</td>
                        <td>
                            <a href="javascript:void(0);" onclick="_modalThongTinChuXe?.show?.()">
                                <span style="width:auto"><%- ho_so.chu_xe %></span>
                            </a>
                            <span class="float-right">
                                <% if(ho_so.nhom_kh_vip == 'C'){ %>
                                    <span  style="color:var(--orange);font-weight:bold !important;">VIP</span>
                                <% } else{ %>
                                    <span></span>
                                <% } %>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Điện thoại</td>
                        <td>
                            <span><%- ho_so.dien_thoai %></span>
                            <span class="float-right">
                                <a href="#" onclick="ESUtil.voiceCall('<%- ho_so.dien_thoai %>')">
                                    <i class="fal fa-phone-rotary" title="Gọi điện thoại"></i>
                                </a>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Email</td>
                        <td><%- ho_so.email %></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="text-right card-title-bg">
                        <td colspan="2" class="text-center">
                            @*id="btnHuyHoSo"*@
                            <a href="javascript:void(0)" id="btnHuyHoSo" onclick="huyHoSo()" class="d-none escs_pquyen mr-4">
                                <i class="fas fa-trash-alt mr-2"></i>Hủy hồ sơ
                            </a>
                            @*id="btnChuyenNguoiXuLy"*@
                            <a href="javascript:void(0)" id="btnChuyenNguoiXuLy" class="d-none escs_pquyen" onclick="chuyenNguoiXuLy()">
                                <i class="far fa-user-friends mr-2"></i>Chuyển người xử lý
                            </a>
                            @*id="btnGoHuyHoSo"*@
                            <a href="javascript:void(0)" id="btnGoHuyHoSo" onclick="goHuyHoSo()" class="d-none escs_pquyen">
                                <i class="fa fa-undo mr-2"></i>Gỡ hủy hồ sơ
                            </a>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</script>

@*  Tab Thông tin liên hệ*@
<script type="text/html" id="navThongTinLienHe_template">
    <div class="card-body p-1">
        <div class="border mb-2 rounded">
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                <h5 class="m-0">Người thông báo</h5>
                <span><a href="#" onclick="suaThongTinLienHe()"><i class="fas fa-edit"></i></a></span>
            </div>
            <table class="table" id="CarClaimCustomer3">
                <tr>
                    <td style="width:105px; color:#999999">Người yêu cầu</td>
                    <td><%- ho_so.nguoi_tb %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Mối quan hệ</td>
                    <td><%- ho_so.moi_qh_tb_ten %></td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Điện thoại</td>
                    <td>
                        <span><%- ho_so.dthoai_tb %></span>
                        <span class="float-right">
                            <a href="#" onclick="ESUtil.voiceCall('<%- ho_so.dthoai_tb %>')">
                                <i class="fal fa-phone-rotary" title="Gọi điện thoại"></i>
                            </a>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="width:105px; color:#999999">Email</td>
                    <td><%- ho_so.email_tb %></td>
                </tr>
            </table>
            <div class="d-flex justify-content-between align-items-center p-2 card-title-bg border-top">
                <h5 class="m-0">Người liên hệ</h5>
                <span><a href="#" onclick="suaThongTinLienHe()"><i class="fas fa-edit"></i></a></span>
            </div>
            <table class="table">
                <tbody>
                    <tr>
                        <td style="width:105px; color:#999999">Họ tên</td>
                        <td><%- ho_so.nguoi_lhe %></td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Mối quan hệ</td>
                        <td><%- ho_so.moi_qh_lhe_ten %></td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Điện thoại</td>
                        <td>
                            <span><%- ho_so.dthoai_lhe %></span>
                            <span class="float-right">
                                <a href="#" onclick="ESUtil.voiceCall('<%- ho_so.dthoai_lhe %>')">
                                    <i class="fal fa-phone-rotary" title="Gọi điện thoại"></i>
                                </a>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:105px; color:#999999">Email</td>
                        <td><%- ho_so.email_lhe %></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</script>

@*  Step 1, tab 1: Diễn biến tổn thất, nguyên nhân hậu quả *@
<script type="text/html" id="CarCompensationContentStep1Table2_template">
    <table class="table">
        <tr>
            <td style="width:130px; color:#999999">Ngày giờ xảy ra</td>
            <td colspan="5"><%- gio_xr %> - <%- ngay_xr %></td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Địa điểm xảy ra</td>
            <td colspan="5"><%- dia_diem %></td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Sự kiện bảo hiểm</td>
            <td colspan="5"><%- ten_nhom_su_kien %></td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Nhóm nguyên nhân</td>
            <td colspan="5"><%- nhom_nguyen_nhan %></td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Nguyên nhân</td>
            <td colspan="5">
                <%- nguyen_nhan %>
            </td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Hậu quả</td>
            <td colspan="5">
                <%- hau_qua %>
            </td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Hậu quả bên thứ 3</td>
            <td colspan="5">
                <%- hau_qua_ntba %>
            </td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Lái xe</td>
            <td><%- ten_lxe %></td>
            <td style="width:130px; color:#999999">Điện thoại</td>
            <td><%- dthoai_lxe %></td>
            <td style="width:130px; color:#999999">Email</td>
            <td><%- email_lxe %></td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Số GPLX</td>
            <td><%- gplx_so %></td>
            <td style="width:130px; color:#999999">Hạng</td>
            <td><%- gplx_hang %></td>
            <td style="width:130px; color:#999999;">Hiệu lực</td>
            <%
            if(gplx_hieu_luc ===null || gplx_hieu_luc==="" || gplx_het_han===null || gplx_het_han==="")
            {
            %>
            <td></td>
            <%
            }
            else
            {
            %>
            <td><%- gplx_hieu_luc %> - <%- gplx_het_han %></td>
            <%
            }
            %>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Số đăng kiểm</td>
            <td><%- dangkiem_so %></td>
            <td style="width:130px; color:#999999;" colspan="3">Hiệu lực đăng kiểm</td>
            <%
            if(dangkiem_hieu_luc ===null || dangkiem_hieu_luc==="" || dangkiem_het_han===null || dangkiem_het_han==="")
            {
            %>
            <td></td>
            <%
            }
            else
            {
            %>
            <td><%- dangkiem_hieu_luc %> - <%- dangkiem_het_han %></td>
            <%
            }
            %>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Phạm vi bảo hiểm</td>
            <td colspan="5">
                <%- pham_vi %>
            </td>
        </tr>
        <tr>
            <td style="width:130px; color:#999999">Ghi chú</td>
            <td colspan="5">
                <%- ly_do %>
            </td>
        </tr>
    </table>
</script>
@*  Step 1, tab 2: Thông tin giám định *@
<script type="text/html" id="CarCompensationContentStep1Table3_template">
    <table class="table">
        <tbody>
            <tr>
                <td style="width:130px; color:#999999">Ngày giờ giám định</td>
                <td colspan="5"><%- gio_gd %> - <%- ngay_gd %></td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Địa điểm giám định</td>
                <td colspan="5"><%- dia_diem %></td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Đơn vị GĐ</td>
                <td colspan="5"><%- ten_dvi_gd %></td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Tên GĐV</td>
                <td>
                    <%- ten_gdv %>
                </td>
                <td style="width:130px; color:#999999">Điện thoại</td>
                <td>
                    <%- dthoai_gdv %>
                </td>
                <td style="width:130px; color:#999999">Email</td>
                <td>
                    <%- email_gdv %>
                </td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Đối tượng GĐ</td>
                <td colspan="5"><%- doi_tuong_gd_ten %></td>
            </tr>
        </tbody>
    </table>
</script>

<script type="text/html" id="CarCompensationContentStep1Table3_template_none">
    <table class="table">
        <tbody>
            <tr>
                <td style="width:130px; color:#999999">Ngày giám định</td>
                <td colspan="5"></td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Địa điểm giám định</td>
                <td colspan="5"></td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Đơn vị GĐ</td>
                <td colspan="5"></td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Tên GĐV</td>
                <td></td>
                <td style="width:130px; color:#999999">Điện thoại</td>
                <td></td>
                <td style="width:130px; color:#999999">Email</td>
                <td></td>
            </tr>
            <tr>
                <td style="width:130px; color:#999999">Đối tượng GĐ</td>
                <td colspan="5"></td>
            </tr>
        </tbody>
    </table>
</script>

@*  Step 1, tab 2: Đại diện các bên tham gia giám định *@
<script type="text/html" id="CarCompensationContentStep1Table4_template">
    <%if(arrNguoiGiamDinh.length > 0){
    _.forEach(arrNguoiGiamDinh, function(nguoiGiamDinh) { %>
    <tr>
        <td><%- nguoiGiamDinh.dai_dien %></td>
        <td><%- nguoiGiamDinh.ten %></td>
        <td><%- nguoiGiamDinh.dien_thoai %></td>
        <td><%- nguoiGiamDinh.email %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="4">
            <p class="m-0">Chưa có dữ liệu</p>
        </td>
    </tr>
    <% } %>
</script>

@*  Step 1, tab 3: Đánh giá sơ bộ tổn thất *@
@*Vật chất xe*@
<script type="text/html" id="modalChiTietTonThatGDVCXTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten_hang_muc)%>">
        <td class="text-center">
            <input type="hidden" data-field="ten_doi_tuong" value="<%- item.ten_doi_tuong %>" />
            <input type="hidden" data-field="ten_hang_muc" value="<%- item.ten_hang_muc %>" />
            <input type="hidden" data-field="muc_do_ten" value="<%- item.muc_do_ten %>" />
            <input type="hidden" data-field="thay_the_sc_ten" value="<%- item.thay_the_sc_ten %>" />
            <input type="hidden" data-field="chinh_hang_ten" value="<%- item.chinh_hang_ten %>" />
            <input type="hidden" data-field="thu_hoi_ten" value="<%- item.thu_hoi_ten %>" />
            <input type="hidden" data-field="gia_giam_dinh" value="<%- item.gia_giam_dinh %>" />
            <%- item.ten_doi_tuong %>
        </td>
        <td>
            <a href="#" onclick="openModalImagesPaging('<%- item.hang_muc %>')"><%- item.ten_hang_muc %></a>
        </td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-center"><%= item.thay_the_sc_ten %></td>
        <td class="text-center"><%= item.chinh_hang_ten %></td>
        <td class="text-center"><%= item.thu_hoi_ten %></td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Hàng hóa trên xe*@
<script type="text/html" id="modalChiTietTonThatGDHANGHOATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) {
    %>
    <tr class="hmChiTietItem">
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <%- item.muc_do_ten %>
        </td>
        <td class="text-center">
            <%- ESUtil.formatMoney(item.so_luong) %>
        </td>
        <td class="text-center">
            <%- item.dvi_tinh_ten %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.gia) %>
        </td>
        <td class="text-center">
            <%- ESUtil.formatMoney(item.so_luong_tt) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_tt) %>
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <%
    })}%>

    <% if(danh_sach.length < 6){
    for(var i = 0; i < 6 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*TNDS về tài sản*@
<script type="text/html" id="modalChiTietTonThatGDTNDS_TAI_SANTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) {%>
    <tr class="hmChiTietItem">
        <td>
            <a class="combobox" href="#" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_vtu) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_nhan_cong) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_khac) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_tt) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_thoa_thuan) %></td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <%})}%>

    <% if(danh_sach.length < 6){
    for(var i = 0; i < 6 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Con người*@
<script type="text/html" id="modalChiTietTonThatGDNGUOITemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center"><%- item.cmnd %></td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_tt) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_thoa_thuan) %></td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 6){
    for(var i = 0; i < 6 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*TNDS về người*@
<script type="text/html" id="modalChiTietTonThatGDTNDSNGUOITemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <%- item.cmnd %>
        </td>
        <td class="text-center">
            <%- item.muc_do_ten %>
        </td>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_tt) %>
        </td>
           <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_thoa_thuan) %>
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 6){
    for(var i = 0; i < 6 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>

</script>
@*TNDS về hành khách*@
<script type="text/html" id="modalChiTietTonThatGDTNDSNGUOI_HKTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <%- item.cmnd %>
        </td>
        <td class="text-center">
            <%- item.muc_do_ten %>
        </td>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- item.bt %>', '<%- item.vu_tt %>', '<%- item.lh_nv %>', '<%- item.hang_muc %>', '<%- item.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_tt) %>
        </td>
          <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_thoa_thuan) %>
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 6){
    for(var i = 0; i < 6 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Step 1, tab 3: Đánh giá chung *@
<script type="text/html" id="Step1YKien_template">
    <tr>
        <td style="width:150px; color:#999999">Kiến nghị xử lý</td>
        <td>
            <% _.forEach(danh_gia_gd_chung, function(danhGia, index) {
            if(danhGia.loai == "KNGQ"){ %>
            + <%- danhGia.ten %>
            <br />
            <% }}); %>
        </td>
    </tr>
    <tr>
        <td style="width:150px; color:#999999">Kiến nghị khác</td>
        <td>
            <%- ho_so.y_kien %>
        </td>
    </tr>
</script>

@*  Step 1, tab 4: Ho So giay to *@
<script type="text/html" id="tblStep1_HoSoGiayTo_template">
    <% if(ho_so_giay_to.length > 0){
    _.forEach(ho_so_giay_to, function(item,index) { %>
    <tr>
        <td>
            <input type="hidden" name="objTrinh" value="<%- JSON.stringify(item) %>" />
            @*<a href="#" onclick="xemChiTietAnhHangMucGT('<%- item.ma_doi_tac %>','<%- item.so_id %>','<%- item.ma_hs %>')"> <%- item.ten %></a>*@
            <a href="#" onclick="openModalImagesPaging('<%- item.ma_hs%>')"> <%- item.ten %></a>
        </td>
        <td class="text-center"><%- item.ngay_bs %></td>
        <td style="text-align: center">
            <% if(item.hop_le == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" checked="checked" disabled="disabled" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.loai != '' && item.loai != null){ %>
            <%- item.loai_ten %>
            <% }else{ %>
            Chưa bổ sung
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.gara_thu_ho == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" checked="checked" disabled="disabled" class="custom-control-input input_chon_gara_thu_ho">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline" style="margin:unset;">
                <input type="checkbox" class="custom-control-input input_chon_gara_thu_ho">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td style="text-align: center">
            <%- item.ghi_chu %>
        </td>
        <td class="text-center"><%- item.nsd %></td>
        <td class="text-center"><%- item.nguon %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="7">Chưa có hồ sơ giấy tờ</td>
    </tr>
    <% } %>
</script>

@*  Step 3: Phương án khắc phục *@
<script type="text/html" id="tblPhuongAnVCXTemplate">
    <% if(pa_khac_phuc.length > 0){
    _.forEach(pa_khac_phuc, function(item, index) { %>
    <tr class="hmChiTietItem" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten) %>">
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <input type="hidden" data-val="<%- item.vu_tt_ten %>" data-field="vu_tt_ten" />
            <input type="hidden" data-val="<%- item.ten %>" data-field="ten" />
            <input type="hidden" data-val="<%- item.muc_do_ten %>" data-field="muc_do_ten" />
            <input type="hidden" data-val="<%= item.thay_the_sc_ten %>" data-field="thay_the_sc_ten" />
            <input type="hidden" data-val="<%= item.chinh_hang_ten %>" data-field="chinh_hang_ten" />
            <input type="hidden" data-val="<%= item.thu_hoi_ten %>" data-field="thu_hoi_ten" />
            <input type="hidden" data-val="<%= item.gia_giam_dinh %>" data-field="gia_giam_dinh" />
            <input type="hidden" data-val="<%= item.gia_duyet_dx %>" data-field="gia_duyet_dx" />
            <input type="hidden" data-val="<%= item.gia_duyet %>" data-field="gia_duyet" />
            <input type="hidden" data-val="<%= item.ghi_chu %>" data-field="ghi_chu" />
            <input type="hidden" data-val="<%- item.loai %>" data-field="loai" />
            <%- item.vu_tt_ten %>
        </td>
        <td>
            <a href="#" onclick="openModalImagesPaging('<%- item.hang_muc %>')"><%- item.ten %></a>
            <%if(item.dgrr != undefined && item.dgrr != null && item.dgrr != "" && item.dgrr == "C"){%>
            <a href="#" class="text-warning" onclick="onXemHangMucDGRR('<%- item.hang_muc %>')"><i class="fas fa-exclamation-triangle ml-2"></i><span class="ml-1" style="font-size:10px; font-style:italic;">Có tổn thất cấp đơn</span> </a>
            <%}%>
        </td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-center"><%= item.thay_the_sc_ten %></td>
        <td class="text-center"><%= item.chinh_hang_ten %></td>
        <td class="text-center"><%= item.thu_hoi_ten %></td>
        <td class="text-right d-none"><%- ESUtil.formatMoney(item.gia_giam_dinh) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet) %></td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != ""){ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a href="#" onclick="showGhiChu(this)" style="cursor: pointer">
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <% }else{ %>
            <p class="m-0 cursor-pointer combobox" data-field="ghi_chu" title="<%- item.ghi_chu %>">
                <a onclick="showGhiChu(this)">
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <%}%>
        </td>
    </tr>
    <% })} %>
    <% if(pa_khac_phuc.length < 9){
    for(var i = 0; i < 9 - pa_khac_phuc.length;i++ ){
    %>
    <tr>
        <td style="height: 35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td class="d-none"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblPhuongAnHANGHOATemplate">
    <% if(pa_khac_phuc.length > 0){
    _.forEach(pa_khac_phuc, function(item, index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.vu_tt_ten %>
        </td>
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-center"><%= item.thu_hoi_ten %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.gia_giam_dinh) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet) %></td>
        <td class="text-center">
            <% if(item.ghi_chu !== null && item.ghi_chu != ""){ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a href="#">
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <% }else{ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a>
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <%}%>
        </td>
        <td class="text-center">
            <% if(item.loai == 'PHU'){ %>
            <a href="#" class="xoaHangMucTonThat">
                <i class="fas fa-trash-alt" title="Xóa hạng mục bồi thường"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>
    <% if(pa_khac_phuc.length < 10){
    for(var i = 0; i < 10 - pa_khac_phuc.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblPhuongAnNNTXTemplate">
    <% if(pa_khac_phuc.length > 0){
    _.forEach(pa_khac_phuc, function(item, index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.vu_tt_ten %>
        </td>
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.gia_giam_dinh) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet) %></td>
        <td class="text-center">
            <% if(item.ghi_chu !== null && item.ghi_chu != ""){ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a href="#">
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <% }else{ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a>
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <%}%>
        </td>
        <td class="text-center">
            <% if(item.loai == 'PHU'){ %>
            <a href="#" class="xoaHangMucTonThat">
                <i class="fas fa-trash-alt" title="Xóa hạng mục bồi thường"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>
    <% if(pa_khac_phuc.length < 10){
    for(var i = 0; i < 10 - pa_khac_phuc.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblPhuongAnTNDSNguoiTemplate">
    <% if(pa_khac_phuc.length > 0){
    _.forEach(pa_khac_phuc, function(item, index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.vu_tt_ten %>
        </td>
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.gia_giam_dinh) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet) %></td>
        <td class="text-center">
            <% if(item.ghi_chu !== null && item.ghi_chu != ""){ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a href="#">
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <% }else{ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a>
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <%}%>
        </td>
        <td class="text-center">
            <% if(item.loai == 'PHU'){ %>
            <a href="#" class="xoaHangMucTonThat">
                <i class="fas fa-trash-alt" title="Xóa hạng mục bồi thường"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>
    <% if(pa_khac_phuc.length < 10){
    for(var i = 0; i < 10 - pa_khac_phuc.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblPhuongAnTNDSTaiSanTemplate">
    <% if(pa_khac_phuc.length > 0){
    _.forEach(pa_khac_phuc, function(item, index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.vu_tt_ten %>
        </td>
        <td>
            <a href="#"><%- item.ten %></a>
        </td>
        <td class="text-center"><%- item.muc_do_ten %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.gia_giam_dinh) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
        <td class="text-right" style="color:blue"><%- ESUtil.formatMoney(item.gia_duyet) %></td>
        <td class="text-center">
            <% if(item.ghi_chu !== null && item.ghi_chu != ""){ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a href="#">
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <% }else{ %>
            <p class="m-0" data-toggle="tooltip" title="<%- item.ghi_chu %>">
                <a>
                    <i class="far fa-file-alt"></i>
                </a>
            </p>
            <%}%>
        </td>
        <td class="text-center">
            <% if(item.loai == 'PHU'){ %>
            <a href="#" class="xoaHangMucTonThat">
                <i class="fas fa-trash-alt" title="Xóa hạng mục bồi thường"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>
    <% if(pa_khac_phuc.length < 10){
    for(var i = 0; i < 10 - pa_khac_phuc.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Step 3: Danh sách gara báo giá *@
<script type="text/html" id="garaBaoGia_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(bao_gia,index) { %>
    <tr data-ma-gara="<%- bao_gia.gara %>" data-so-id-doi-tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt-gara="<%- bao_gia.bt_gara %>">
        <td class="text-center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" value="<%- bao_gia.gara %>" class="custom-control-input garaBaoGiaChonItem">
                <label class="custom-control-label" for="garaBaoGiaChon_<%- bao_gia.gara %>_<%- bao_gia.so_id_doi_tuong %>">&nbsp;</label>
            </div>
        </td>
        <td class="text-center"><%- index+1 %></td>
        <td class="layBaoGiaCT" onclick="xemBaoGiaCT(this,'<%- bao_gia.trang_thai %>', '<%- bao_gia.lien_ket_bg %>', '<%- bao_gia.trang_thai_bg %>', '<%- bao_gia.ngay_ycsc %>')" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>">
            <% if(bao_gia.lap_pa != undefined && bao_gia.lap_pa!= null && bao_gia.lap_pa!=""){%>
            <a href="javascript:void(0)"><%- bao_gia.ten %> - <i class="text-success">(<%- bao_gia.lap_pa %>)</i></a>
            <%}else{%>
            <a href="javascript:void(0)"><%- bao_gia.ten %></a>
            <%}%>

        </td>
        <td class="text-center"><%- bao_gia.ten_doi_tuong %></td>
        <td class="text-center">
            <%if(bao_gia.lien_ket_bg == 1){
            if(bao_gia.trang_thai_bg == null){%>
            <input type="checkbox" class="radio" name="chuyen_bao_gia_gara" onclick="chonGaraBaoGia('<%- bao_gia.gara %>')" value="<%= bao_gia.bt_gara %>" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" />
            <%}else{%>
            <a href="#" onclick="xemChiTietBaoGiaGara(this,'<%- bao_gia.gara %>','<%- bao_gia.so_id_bg %>')" class="ttbaogia ttbaogia_<%- bao_gia.trang_thai_bg %>"><span><%- bao_gia.trang_thai_bg_hthi %></span></a>
            <%}}else{%>
            <span>Chưa liên kết</span>
            <%}%>
        </td>
        <td class="text-center"><%- bao_gia.ngay_gio_bg %></td>
        <td class="text-center"><%- bao_gia.ngay_dong_bg %></td>
        <td class="text-right"><%- ESUtil.formatMoney(bao_gia.tong_tien) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(bao_gia.tong_duyet) %></td>
        <td class="text-center" style="width:60px">
            <% if(bao_gia.trang_thai == 'C'){ %>
            <a href="#" class="btnSuaBaoGia" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>" data-hours="<%- bao_gia.gio_bg %>" data-date="<%- bao_gia.ngay_gio_bg %>" data-placement="bottom bottom-right">
                <i class="fas fa-edit" title="Sửa thông tin gara"></i>
            </a>
            <a href="#" class="xoaBaoGia" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>">
                <i class="fas fa-trash-alt" title="Xóa chứng từ"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center" style="width:60px">
            <a href="#" id="btnLayBGCanhTranh" onclick="xemBGCanhTranh(<%- bao_gia.so_id_doi_tuong %>)" data-gara="<%- bao_gia.gara %>" data-so_id_doi_tuong="<%- bao_gia.so_id_doi_tuong %>" data-bt_gara="<%- bao_gia.bt_gara %>" data-hours="<%- bao_gia.gio_bg %>" data-date="<%- bao_gia.ngay_gio_bg %>" data-placement="bottom bottom-right">
                <i class="far fa-file-alt" title="Lấy báo giá cạnh tranh"></i>
            </a>
        </td>
    </tr>
    <% })} %>
    <% if(data_info.length < 3){
    for(var i = 0; i < 3 - data_info.length;i++ ){
    %>
    <tr>
        <td style="height: 35.2px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Step 3: Gara báo giá chi tiết <%= bao_gia.thay_the_sc_ten %>*@
<script type="text/html" id="garaBaoGiaCT_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(bao_gia,index) { %>
    <tr class="gara_bg_ctiet" data-search="<%- ESUtil.xoaKhoangTrangText(bao_gia.ten_hang_muc) %>">
        <td class="text-center"><%- index + 1%></td>
        <td class="text-center">
            <a href="#" onclick="openModalXemHinhAnhCTiet('<%- bao_gia.hang_muc %>')">
                <i class="fas fa-image" title="Xem hình ảnh chi tiết"></i>
            </a>
        </td>
        <%if(bao_gia.loai_hang_muc == undefined|| bao_gia.loai_hang_muc == null || bao_gia.loai_hang_muc == ""|| bao_gia.loai_hang_muc == "G"){%>
        <td>
            <input type="hidden" name="loai_hang_muc" value="<%- bao_gia.loai_hang_muc %>" />
            <input type="hidden" name="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="hang_muc_ten" value="<%- bao_gia.ten_hang_muc %>" />
            <input type="hidden" name="muc_do" value="<%- bao_gia.muc_do %>" />
            <input type="hidden" name="thay_the_sc" value="<%- bao_gia.thay_the_sc %>" />

            <input type="hidden" name="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="tien_ht_gara" value="<%- bao_gia.tien_ht_gara %>" />
            <input type="hidden" name="bt" value="<%- bao_gia.bt %>" />

            <input type="hidden" name="tien_vtu_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_duyet) %>" />
            <input type="hidden" name="tien_nhan_cong_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong_duyet) %>" />
            <input type="hidden" name="tien_khac_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac_duyet) %>" />
            <input type="hidden" name="tien_duyet" class="form-control number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_duyet) %>" readonly />

            <a href="javascript:void(0);" data-field="ten_hang_muc" data-val="<%- bao_gia.ten_hang_muc %>" onclick="traCuuLichSuBaoGiaHangMuc(this)"><%- bao_gia.ten_hang_muc %></a>
        </td>
        <td class="text-center">
            <a href="#" data-field="muc_do_ten" data-val="<%- bao_gia.muc_do_ten %>" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'G')"><%- bao_gia.muc_do_ten %></a>
        </td>
        <td class="text-center">
            <a href="#" data-field="thay_the_sc_ten" data-val="<%- bao_gia.thay_the_sc_ten %>" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'G')"><%- bao_gia.thay_the_sc_ten %></a>
        </td>
        <%}else if(bao_gia.loai_hang_muc == "B"){%>
        <td>
            <input type="hidden" name="loai_hang_muc" value="<%- bao_gia.loai_hang_muc %>" />
            <input type="hidden" name="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="muc_do" value="<%- bao_gia.muc_do %>" />
            <input type="hidden" name="thay_the_sc" value="<%- bao_gia.thay_the_sc %>" />

            <input type="hidden" name="tien_ht_gara" value="0" />
            <input type="hidden" name="bt" value="0" />

            <input type="hidden" name="tien_vtu_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_duyet) %>" />
            <input type="hidden" name="tien_nhan_cong_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong_duyet) %>" />
            <input type="hidden" name="tien_khac_duyet" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac_duyet) %>" />
            <input type="hidden" name="tien_duyet" class="form-control number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_duyet) %>" readonly />

            <%if(bao_gia.hang_muc_bo_sung_moi == "M"){%>
            <a href="#" data-field="ten_hang_muc" data-val="<%- bao_gia.ten_hang_muc %>" class="text-danger" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.ten_hang_muc %></a>
            <%}else{%>
            <a href="#" data-field="ten_hang_muc" data-val="<%- bao_gia.ten_hang_muc %>" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.ten_hang_muc %></a>
            <%}%>

        </td>
        <td class="text-center">
            <%if(bao_gia.hang_muc_bo_sung_moi == "M"){%>
            <a href="#" data-field="muc_do_ten" data-val="<%- bao_gia.muc_do_ten %>" class="text-danger text-center" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.muc_do_ten %></a>
            <%}else{%>
            <a href="#" data-field="muc_do_ten" data-val="<%- bao_gia.muc_do_ten %>" class="text-center" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.muc_do_ten %></a>
            <%}%>

        </td>
        <td class="text-center">
            <%if(bao_gia.hang_muc_bo_sung_moi == "M"){%>
            <a href="#" data-field="thay_the_sc_ten" data-val="<%- bao_gia.thay_the_sc_ten %>" class="text-danger" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.thay_the_sc_ten %></a>
            <%}else{%>
            <a href="#" data-field="thay_the_sc_ten" data-val="<%- bao_gia.thay_the_sc_ten %>" onclick="capNhatThongTinHangMuc(this, '<%- bao_gia.nhom %>', '<%- bao_gia.loai_doi_tuong %>', 'B')"><%- bao_gia.thay_the_sc_ten %></a>
            <%}%>
        </td>
        <%}%>
        <td class="text-center">
            <% if(bao_gia.thay_the_sc=="T") {%>
            <input type="text" name="so_luong" class="decimal floating-input form-control" value="<%- bao_gia.so_luong %>" />
            <%}else{%>
            <input type="text" name="so_luong" readonly="readonly" class="decimal floating-input form-control" value="<%- bao_gia.so_luong %>" />
            <%}%>

        </td>
        <td class="text-right d-none"><%- ESUtil.formatMoney(bao_gia.gia_giam_dinh) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(bao_gia.tien_ht_gara) %></td>
        <td class="text-right">
            <%if(bao_gia.thay_the_sc=="T"){%>
            <input type="text" name="tien_vtu" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu) %>" />
            <%}else{%>
            <input type="text" name="tien_vtu" readonly="readonly" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu) %>" />
            <%}%>
        </td>
        <td class="text-right">
            <input type="text" name="tien_nhan_cong" class="number floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_khac" class="number floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac) %>" />
            <input type="text" name="tong_cong" class="form-control number floating-input d-none" value="<%- ESUtil.formatMoney(bao_gia.tong_cong) %>" readonly />
        </td>
        <td class="text-right">
            <%if(bao_gia.thay_the_sc=="T"){%>
            <input type="text" name="tien_vtu_dx" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_dx) %>" />
            <%}else{%>
            <input type="text" name="tien_vtu_dx" readonly="readonly" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu_dx) %>" />
            <%}%>
        </td>
        <td class="text-right">
            <input type="text" name="tien_nhan_cong_dx" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong_dx) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_khac_dx" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tien_khac_dx) %>" />
            <input type="text" name="tien_dx" class="form-control number wd-80-f floating-input d-none" value="<%- ESUtil.formatMoney(bao_gia.tien_dx) %>" readonly />
        </td>
        <td class="text-right">
            <%if(bao_gia.thay_the_sc=="T"){%>
            <input type="text" name="tl_giam_gia_vtu" maxlength="3" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_vtu) %>" />
            <%}else{%>
            <input type="text" name="tl_giam_gia_vtu" readonly="readonly" maxlength="3" class="number wd-80-f floating-input form-control" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_vtu) %>" />
            <%}%>
        </td>
        <td class="text-right">
            <input type="text" name="tl_giam_gia_nhan_cong" maxlength="3" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tl_giam_gia_khac" maxlength="3" class="number wd-80-f floating-input" value="<%- ESUtil.formatMoney(bao_gia.tl_giam_gia_khac) %>" />
        </td>
        <td class="text-center">
            <% if(bao_gia.ghi_chu != null && bao_gia.ghi_chu != ""){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- bao_gia.ghi_chu %>" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChuBaoGia(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })} %>

    <% if(data_info.length < 6){
    for(var i = 0; i < 6 - data_info.length;i++ ){
    %>
    <tr>
        <td style="height: 35px"></td>
        <td class="d-none"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="divGaraHopTac_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(item,index) { %>
    <div class="custom-control custom-checkbox divItemGaraHopTac" data-search="<%- ESUtil.xoaKhoangTrangText(item.ten)%>">
        <input type="checkbox" id="gara_ht_<%- index %>" value="<%- item.gara %>" class="custom-control-input item_gara_ht">
        <label class="custom-control-label" for="gara_ht_<%- index %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Không có gara hợp tác</div>
    <% } %>
</script>

@*  Step 4, tab 2: Chứng từ *@
<script type="text/html" id="step4_chung_tu_template">
    <% if(chung_tu.length > 0){
    _.forEach(chung_tu, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.ngay_ct %>
        </td>
        <td class="text-center"><%- item.mau_hdon %></td>
        <td class="text-center"><%- item.ky_hieu_hdon %></td>
        <td class="text-center"><%- item.so_hdon %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.thue) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tong_cong) %></td>
        <td class="text-center">
            <a href="#" class="edit_chung_tu" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="far fa-file-alt" title="Xem/sửa chi tiết chứng từ"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" class="xoaChungTu"><i class="fas fa-trash-alt" title="Xóa chứng từ"></i></a>
        </td>
    </tr>
    <% })} %>
    <% if(chung_tu.length < 3){
    for(var i = 0; i < 3 - chung_tu.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Step 4, tab 2: Thụ hưởng *@
<script type="text/html" id="step4_thu_huong_template">
    <% if(thu_huong.length > 0){
    _.forEach(thu_huong, function(item,index) { %>
    <tr>
        <td class="text-center" style="width:40px;">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.pttt %>
        </td>
        <td><%- item.ten %></td>
        <td class="text-center"><%- item.tk_cmt %></td>
        <td class="text-center"><%- item.ten_ngan_hang %></td>
        <td class="text-center"><%- item.ten_chi_nhanh %></td>
        <td><%- item.dien_giai %></td>
        <td class="text-center"><%- item.loai_hthi %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="edit_thu_huong" data-toggle="modal" data-target="#" data-backdrop="static" data-keyboard="false">
                <i class="far fa-file-alt" title="Xem/sửa chi tiết thông tin"></i>
            </a>
            <%
            }%>

        </td>
        <td class="text-center">
            <%if(item.loai=='TH') {
            %>
            <a href="#" class="xoaNguoiThuHuong"><i class="fas fa-trash-alt" title="Xóa người thụ hưởng"></i></a>
            <%
            }%>
        </td>
    </tr>
    <% })}%>
    <% if(thu_huong.length < 3){
    for(var i = 0; i < 3 - thu_huong.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="step4_tam_ung_template">
    <% if(tam_ung.length > 0){
    _.forEach(tam_ung, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" value="<%- JSON.stringify(item) %>" name="objInfo" />
            <%- item.pttt %>
        </td>
        <td><%- item.ten %></td>
        <td class="text-center"><%- item.tk_cmt %></td>
        <td><%- item.ten_nh %></td>
        <td><%- item.nd %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <td class="text-center"><%- item.trang_thai_hthi %></td>
        <td class="text-center">
            <a href="#" class="cursor-pointe" onclick="xemCtietTamUng('<%- item.so_id_tu %>')">
                <i class="fas fa-eye" title="Xem chi tiết"></i>
            </a>
        </td>
    </tr>
    <% })}%>
    <% if(tam_ung.length < 4){
    for(var i = 0; i < 4 - tam_ung.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*  Step 4, tab 3: Hồ sơ giấy tờ *@
<script type="text/html" id="tblStep4_HoSoGiayTo_template">
    <% if(ho_so_giay_to.length > 0){
    _.forEach(ho_so_giay_to, function(item,index) { %>
    <tr>
        <td style="text-align:left">
            @*<a href="#" data-field="ten" data-val="<%- item.ten %>" onclick="xemChiTietAnhHangMucGT('<%- item.ma_doi_tac %>','<%- item.so_id %>','<%- item.ma_hs %>')" id="chon_ten_hsgt_<%- item.ma_hs %>">
                    <%- item.ten %>
                </a>*@
            <a href="#" data-field="ten" data-val="<%- item.ten %>" onclick="openModalImagesPaging('<%- item.ma_hs%>')" id="chon_ten_hsgt_<%- item.ma_hs %>">
                <%- item.ten %>
            </a>
        </td>
        <td style="text-align:center">
            <% if(item.so_id_doi_tuong != '' && item.so_id_doi_tuong != null){ %>
            <a href="#" class="cursor-pointer combobox" id="doi_tuong_<%- item.ma_hs %>" onclick="showNhomDoiTuong(this)" data-field="so_id_doi_tuong" data-val="<%- item.so_id_doi_tuong %>">
                <i class="fas fa-user" title="Nhóm đối tượng"></i>
            </a>
            <% }else { %>
            <a class="cursor-pointer combobox" id="doi_tuong_<%- item.ma_hs %>" onclick="showNhomDoiTuong(this)" data-field="so_id_doi_tuong" data-val="<%- item.so_id_doi_tuong %>">
                <i class="fas fa-user" title="Nhóm đối tượng"></i>
            </a>
            <% } %>
        </td>
        <td style="text-align: center">
            <a href="#" data-field="trang_thai" data-val="<%- item.trang_thai %>" style="display:none;">
                <%= item.trang_thai_ten %>
            </a>
            
            <% if((item.ngay_bs??'')!=='') { %>
                <input type="text" data-field="ngay_bs" class="floating-input datepicker text-center" value="<%- item.ngay_bs %>" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
            <% } else { %>
                <input type="hidden" data-field="ngay_bs" value="<%- item.ngay_bs %>">
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="">&nbsp;</label>
            </div>
            <% }else{ %>
            <% if(item.hop_le == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" checked="checked" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="hop_le_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_hop_le">
                <label class="custom-control-label" for="hop_le_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
            <% } %>
        </td>
        <td style="text-align: center">
            <% if(item.ngay_bs == '' || item.ngay_bs == null){ %>
            <a href="#" data-field="loai" data-val="">
                Chọn loại hồ sơ
            </a>
            <% }else{ %>
            <a href="#" data-field="loai" data-val="<%- item.loai %>" onclick="chonLoaiHSGT(this)" id="loai_hsgt_<%- item.ma_hs %>">
                <% if(item.loai != '' && item.loai != null){ %>
                <%- item.loai_ten %>
                <% }else{ %>
                Chọn loại hồ sơ
                <% } %>
            </a>
            <% } %>
        </td>
        <% if(item.trang_thai =="C")
        {
        if(item.chon==1)
        {
        if(item.trang_thai_xoa =='K')
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" checked="checked" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%}
        }
        else
        {
        %>
        <td style="text-align: center">
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" disabled="disabled" id="chon_hang_muc_hsgt_<%- item.ma_hs %>" value="<%- item.ma_hs %>" class="custom-control-input input_chon_hsgt_bs">
                <label class="custom-control-label" for="chon_hang_muc_hsgt_<%- item.ma_hs %>">&nbsp;</label>
            </div>
        </td>
        <%
        }
        %>
        <td style="text-align: center">
            <% if(item.gara_thu_ho == 1){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" checked="checked" id="gara_thu_ho_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_gara_thu_ho">
                <label class="custom-control-label" for="gara_thu_ho_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="gara_thu_ho_<%- item.ma_hs %>" value="" class="custom-control-input input_chon_gara_thu_ho">
                <label class="custom-control-label" for="gara_thu_ho_<%- item.ma_hs %>">&nbsp;</label>
            </div>
            <% } %>
        </td>
        <td style="text-align: center">
            <input type="text" class="floating-input" data-field="ghi_chu" placeholder="Ghi chú" required="" value="<%- item.ghi_chu %>" />
        </td>
        <td style="text-align: center"><%- item.nsd %></td>
        <td style="text-align: center"><%- item.nguon %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>

@*  Step 4, tab 4: Hồ sơ giấy tờ lỗi *@
<script type="text/html" id="bodyHoSoGiayToLoi_template">
    <% if(loi.length > 0){
    _.forEach(loi, function(item,index) { %>
    <tr>
        <td><%- item.ten_loi %></td>
        <td class="text-center"><%= item.kq_tudong %></td>
        <td class="text-center">
            <input type="checkbox" value="<%- item.ma_loi %>" class="chkLoi" <% if(item.kq_nsd == 'D'){ %>checked<% } %>/>
        </td>
        <td class="text-center"><%- item.nsd %></td>
        <td class="text-center">
            <% if(item.kq_nsd != 'K'){ %>
            <a href="#" title="<%- item.loi %>">
                <i class="fas fa-file-alt"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="4">Chưa có dữ liệu</td>
    </tr>
    <% } %>
</script>

@*  Trình duyệt bồi thường*@
<script type="text/html" id="gridTrinhDuyet_template">
    <% if(data_info.length > 0){
    _.forEach(data_info, function(item,index) { %>
    <tr>
        <td class="text-center">
            <input type="hidden" name="objTrinh" value="<%- JSON.stringify(item) %>" />
            <%- item.ngay_trinh %>
        </td>
        <td class="text-center"><%- item.nguoi_trinh %></td>
        <td class="text-center"><%- item.loai_ten %></td>
        <td class="text-center"><%- item.nguoi_duyet %></td>
        <td class="text-center"><%- item.nd %></td>
        <td class="text-center"><%- item.trang_thai_ten %></td>
        <td class="text-center">
            <a href="#" onclick="xoaTrinhDuyet('<%- item.ma_doi_tac %>', '<%- item.so_id %>', '<%- item.bt %>', 'BT')"><i class="fa fa-trash"></i></a>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Chưa có trình duyệt</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="divNguyenNhanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox nngt" id="nngt_<%- item.ma %>">
        <input type="checkbox" id="nguyen_nhan_giam_tru_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNguyenNhanGiamTruItem">
        <label class="custom-control-label" style="cursor:pointer;" for="nguyen_nhan_giam_tru_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="divNguyenNhanPATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox nngt" id="nngt_<%- item.ma %>">
        <input type="checkbox" id="nguyen_nhan_giam_tru_pa_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNguyenNhanGiamTruPAItem">
        <label class="custom-control-label" style="cursor:pointer;" for="nguyen_nhan_giam_tru_pa_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
@*  Danh sách dkbs *@
<script type="text/html" id="divDKBS_template">
    <% if(dkbs.length > 0){
    _.forEach(dkbs, function(item,index) {
    if(item.chon==1)
    {
    %>
    <div class="custom-control custom-checkbox custom-control-inline">
        <input type="checkbox" checked="checked" id="chkDKBS<%- item.ma_dkbs.toString().replaceAll('.', '') %>" name="chkDKBS" value="<%- item.ma_dkbs %>" data-hang-muc="<%- item.ma_hang_muc %>" class="custom-control-input hang_muc_dkbs">
        <label class="custom-control-label" for="chkDKBS<%- item.ma_dkbs.toString().replaceAll('.', '') %>"><%- item.ten %></label>
    </div>
    <%
    }
    else
    {
    %>
    <div class="custom-control custom-checkbox custom-control-inline">
        <input type="checkbox" id="chkDKBS<%- item.ma_dkbs.toString().replaceAll('.', '') %>" name="chkDKBS" value="<%- item.ma_dkbs %>" data-hang-muc="<%- item.ma_hang_muc %>" class="custom-control-input hang_muc_dkbs">
        <label class="custom-control-label" for="chkDKBS<%- item.ma_dkbs.toString().replaceAll('.', '') %>"><%- item.ten %></label>
    </div>
    <%
    }
    });

    }else{ %>
    <p class="text-center">Chưa có ĐKBS</p>
    <% } %>
</script>

@*  Danh sách nguyên nhân *@
<script type="text/html" id="divNguyenNhan_template">
    <% if(nguyen_nhan.length > 0){
    _.forEach(nguyen_nhan, function(item,index) {
    if(item.chon==1)
    {
    %>
    <div class="custom-control custom-checkbox custom-control-inline">
        <input type="checkbox" checked="checked" id="chkNguyenNhan<%- item.ma.toString().replaceAll('.', '') %>" name="chkNguyenNhan" value="<%- item.ma %>" class="custom-control-input hang_muc_nguyen_nhan">
        <label class="custom-control-label" for="chkNguyenNhan<%- item.ma.toString().replaceAll('.', '') %>"><%- item.ten %></label>
    </div>
    <%
    }
    else
    {
    %>
    <div class="custom-control custom-checkbox custom-control-inline">
        <input type="checkbox" id="chkNguyenNhan<%- item.ma.toString().replaceAll('.', '') %>" name="chkNguyenNhan" value="<%- item.ma %>" class="custom-control-input hang_muc_nguyen_nhan">
        <label class="custom-control-label" for="chkNguyenNhan<%- item.ma.toString().replaceAll('.', '') %>"><%- item.ten %></label>
    </div>
    <%
    }
    })}else{ %>
    <p class="text-center">Chưa có nguyên nhân</p>
    <% } %>
</script>

@*  Danh sách ghi chú *@
<script type="text/html" id="divGhiChu_template">
    <div class="form-group">
        <input type="text" class="form-control" id="inpGhiChu" value="<%- ghi_chu %>" />
    </div>
</script>

@*  Danh sách ảnh *@
<script type="text/html" id="lstImage_template">
    <% if(arrLoai.length > 0){ %>
    <% _.forEach(arrLoai, function(iteml,indexl) { %>
    <% if(iteml.so_luong_tai_lieu > 0){ %>
    <div style="border-radius: 3px; text-align: center; background-color: #ececec; padding: 3px 0px; margin: 5px 0px; border: 1px solid #607d8b; ">
        <p class="m-0 font-weight-bold" style="cursor:pointer;" data-img-cat="<%- iteml.loai_tai_lieu %>">
            <%- iteml.ten_loai_tai_lieu %>
        </p>
    </div>
    <% if(arrAnh.length > 0){
    _.forEach(arrAnh, function(item,index) { %>
    <% if(item.loai == iteml.loai_tai_lieu){ %>
    <div class="pt-1" id="nhom_anh_<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p class="m-0 font-weight-bold">
            <a href="#" onclick="onToggleImg('<%- index %>')" data-img-cat-child="<%- iteml.loai_tai_lieu %>">
                <%- item.nhom %>
                <% if(item.ten_doi_tuong!=undefined && item.ten_doi_tuong!=null && item.ten_doi_tuong!=''){%>
                <br /><i style="font-size:10px">(<%- item.ten_doi_tuong %>)</i>
                <%}%>
            </a>
        </p>
    </div>
    <ul class="docs-pictures clearfix">
        <% _.forEach(item.children, function(image,index_anh) { %>
        <li class="p-1">
            <input type="checkbox" onclick="onClickGDChiTiet(this, <%- JSON.stringify(image, ESUtil.replacerImg) %>)" id="img<%- image.bt %>" class="nhom_anh_ton_that_<%- index %>" value="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" name="ds_anh_xe">
            <p class="fileNameImage" style="cursor:pointer"><%- image.ten_file %></p>
            <% if('url_cap_don' in image){ %>
            <img data-original="<%- image.url_cap_don %>" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/default.png" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".jpg", ".png", ".gif",".jpeg"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
            <% }else if(_.includes([".pdf", ".doc", ".docx"], image.extension)){ %>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/pdf-image.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xml"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/xml.png" alt="<%- image.ten_file %>">
            <% } else if(_.includes([".xlsx", ".xls"], image.extension)){%>
            <img data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="/images/excel-logo.jpg" alt="<%- image.ten_file %>">
            <% } %>
        </li>
        <% }) %>
    </ul>
    <% } %>
    <% })} %>
    <% } %>
    <% }) %>
        <div class="mx-1 my-2 d-none" id="getAnhThumnailPaging">
            <button type="button" class="btn btn-sm btn-block btn-outline-primary mb-2" onclick="getAnhThumnailPaging?.(false);">
                <i class="far fa-images mr-2"></i>Hiện thêm ảnh
            </button>
            <button type="button" class="btn btn-sm btn-block btn-outline-danger" onclick="getAnhThumnail?.();">
                <i class="far fa-exclamation-triangle mr-2"></i>Hiện tất cả
            </button>
        </div>
        <div class="my-2 d-none" id="khoiPhucAnhDaXoa">
            <hr />
            <button type="button" class="btn btn-sm btn-block btn-outline-primary mb-2" onclick="khoiPhucAnhDaXoa?.();">
                <i class="fas fa-trash-undo mr-2"></i>Khôi phục
            </button>
        </div>
    <% } %>
</script>

@* Thông tin 1 *@
<script type="text/html" id="docEbill_template">
    <tr>
        <td class="text-center"><%- hoa_don.so_hoa_don %></td>
        <td class="text-center"><%- hoa_don.seri %></td>
        <td class="text-center"><%- hoa_don.mau %></td>
        <td><%- hoa_don.ten_hoa_don %></td>
        <td class="text-center"><%- hoa_don.ngay_phat_hanh %></td>
        <td class="text-center"><%- hoa_don.nguyen_te %></td>

        <td><%- hoa_don.ten_ben_ban %></td>
        <td class="text-center"><%- hoa_don.mst_ben_ban %></td>
        <td><%- hoa_don.dia_chi_ben_ban %></td>

        <td><%- hoa_don.ten_ben_mua %></td>
        <td class="text-center"><%- hoa_don.mst_ben_mua %></td>
        <td><%- hoa_don.dia_chi_ben_mua %></td>

        <td class="text-right"><%- ESUtil.formatMoney(hoa_don.tien_khong_vat) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(hoa_don.tien_vat) %></td>
        <td class="text-right"><%- ESUtil.formatMoney(hoa_don.thanh_tien) %></td>
        <td class="text-left"><%- hoa_don.thanh_chu %></td>
    </tr>
</script>

@* Thông tin nhiều *@
<script type="text/html" id="docEbillCT_template">
    <% if(hoa_don_ct.length > 0){
    _.forEach(hoa_don_ct, function(item,index) { %>
    <tr>
        <td class="text-center"><%- item.dong %></td>
        <td><%- item.ten_hang_muc %></td>
        <td class="text-center"><%- item.so_luong %></td>
        <td class="text-center"><%- item.don_vi_tinh %></td>
        <td class="text-center"><%- ESUtil.formatMoney(item.don_gia) %></td>
        <td class="text-center"><%- item.phan_tram_thue %></td>
        <td class="text-center"><%- ESUtil.formatMoney(item.thue) %></td>
        <td class="text-center"><%- ESUtil.formatMoney(item.thanh_tien) %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Chưa có hóa đơn</td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="templateItemViewImage">
    <% if(data_info!==undefined && data_info!==null && data_info.length > 0){
    _.forEach(data_info, function(item,index) {
    %>
    <a href="#" onclick="xemAnh('<%- item.so_id %>','<%- item.bt %>')" class="item-image-view"><img src="data:image/png;base64,<%- item.duong_dan %>" alt="<%- item.ten_file %>"></a>
    <% }) } %>
</script>

<script type="text/html" id="templateChiTietHangMuc">
    <div class="col-2 font-weight-bold">Nhóm tài liệu: </div>
    <div class="col-4"><%- loai_ten %></div>
    <div class="col-2 font-weight-bold">Loại hình nghiệp vụ: </div>
    <div class="col-4"><%- lhnv_ten %></div>

    <div class="col-2 font-weight-bold">Hạng mục: </div>
    <div class="col-4"><%- ten_hang_muc %></div>
    <div class="col-2 font-weight-bold">Mức độ tổn thất: </div>
    <div class="col-4"><%- muc_do_ten %></div>

    <div class="col-2 font-weight-bold">Phương án: </div>
    <div class="col-4"><%- thay_the_sc_ten_hthi %></div>
    <div class="col-2 font-weight-bold">Nơi sửa chữa: </div>
    <div class="col-4"><%- chinh_hang_hthi %></div>

    <div class="col-2 font-weight-bold">Vụ tổn thất: </div>
    <div class="col-4"><%- vu_tt_ten %></div>
    <div class="col-2 font-weight-bold">Thu hồi vật tư: </div>
    <div class="col-4"><%- thu_hoi_ten %></div>

    <div class="col-2 font-weight-bold">Chi phí tự động: </div>
    <div class="col-4"><%- ESUtil.formatMoney(tien_tu_dong) %></div>
    <div class="col-2 font-weight-bold">Chi phí GĐV xác định: </div>
    <div class="col-4"><%- ESUtil.formatMoney(tien_gd) %></div>

    <div class="col-2 font-weight-bold">Ghi chú: </div>
    <div class="col-10"><%- ghi_chu %></div>
</script>

<script type="text/html" id="tableThuHoiVatTu_template">
    <% if(thu_hoi.length > 0){
    _.forEach(thu_hoi, function(item,index) { %>
    <tr class="uppercase">
        <td class="text-center"><%- index + 1 %></td>
        <td>
            <%= item.ten_kho_thu_hoi %>
        </td>
        <td>
            <a href="#" onclick="openModalImagesPaging('<%- item.hang_muc %>')">
                <%- item.ten %>
            </a>
        </td>
        <td class="text-center">
            <%- item.ten_doi_tuong %>
        </td>
        <td class="text-center">
            <%- item.loai_ten %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien) %>
        </td>
        <td style="width:300px">
            <%- item.ghi_chu %>
        </td>
        <td class="text-center">
            <a href="#" onclick="suaVatTuThuHoi('<%- item.bt %>')" class="btnSuaBaoGia" data-placement="bottom bottom-right">
                <i class="fas fa-edit" title="Sửa thông tin"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" onclick="xoaVatTuThuHoi('<%- item.ma_doi_tac %>','<%- item.so_id %>','<%- item.bt %>')" class="xoaBaoGia">
                <i class="fas fa-trash-alt" title="Xóa thông tin"></i>
            </a>
        </td>
    </tr>
    <% })}%>
    <% if(thu_hoi.length < 3){
    for(var i = 0; i < 3 - thu_hoi.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tableDanhSachThuDoiNTBA_template">
    <% if(ntba.length > 0){
    _.forEach(ntba, function(item,index) { %>
    <tr class="uppercase">
        <td class="text-center"><%- index + 1 %></td>
        <td><%- item.ten %></td>
        <td class="text-center" style="width:120px"><%- item.dthoai %></td>
        <td><%- item.dia_chi %></td>
        <td class="text-center">
            <%- item.ten_doi_tuong %>
        </td>
        <td class="text-center">
            <%- item.loai_ten %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien) %>
        </td>
        <td>
            <%- item.ghi_chu %>
        </td>
        <td class="text-center">
            <a href="#" onclick="suaThuDoiNTBA('<%- item.bt %>')" class="btnSuaBaoGia" data-placement="bottom bottom-right">
                <i class="fas fa-edit" title="Sửa thông tin gara"></i>
            </a>
        </td>
        <td class="text-center">
            <a href="#" onclick="xoaThuDoiNTBA('<%- item.ma_doi_tac %>','<%- item.so_id %>','<%- item.bt %>')" class="xoaBaoGia">
                <i class="fas fa-trash-alt" title="Xóa chứng từ"></i>
            </a>
        </td>
    </tr>
    <% })}%>
    <% if(ntba.length < 3){
    for(var i = 0; i < 3 - ntba.length;i++ ){
    %>
    <tr>
        <td>
            <div class="tabulator-cell" role="gridcell" tabulator-field="sott" title="" style="width: 55px; text-align: center; height: 19px;">&nbsp;<div class="tabulator-col-resize-handle"></div><div class="tabulator-col-resize-handle prev"></div></div>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalBaoGiaGara_lan_template">
    <% if(bg_lan.length > 0){
    _.forEach(bg_lan, function(item,index) { %>
    <tr class="cursor-pointer modalBaoGiaGara_lan_item" data-lan="<%- item.lan_bg %>" onclick="xemChiTietLanBaoGia(this, '<%- item.lan_bg %>')">
        <td class="text-center"><%- item.lan %></td>
        <td class="text-center"><%- item.ngay_bg_hthi %></td>
        <td class="text-center"><%- item.trang_thai_yc_hthi %></td>
    </tr>
    <% })}%>
</script>

<script type="text/html" id="modalBaoGiaGara_lan_ct_template">
    <% if(bg_lan_ct.length > 0){
    _.forEach(bg_lan_ct, function(item,index) { %>
    <tr class="cursor-pointer" data-hang-muc="<%- item.hang_muc %>">
        <td>
            <a href="#"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-center">
            <%- item.muc_do_ten_gara %>
        </td>
        <td class="text-center">
            <% if(item.thay_the_sc_gara =="T"){%>
            <span>Thay thế</span>
            <%}%>
            <% if(item.thay_the_sc_gara =="S"){%>
            <span>Sửa chữa</span>
            <%}%>
        </td>
        <td class="text-center">
            <% if(item.chinh_hang_gara =="C"){%>
            <i class="fas fa-check mr-2" style="color:#28c690"></i>
            <%}%>
            <% if(item.chinh_hang_gara =="K"){%>
            <i class="fas fa-check mr-2"></i>
            <%}%>
        </td>
        <td class="text-center">
            <%- item.so_luong_gara %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_vtu_gara) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_nhan_cong_gara) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_khac_gara) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_luong_gara*item.tien_vtu_gara + item.tien_nhan_cong_gara + item.tien_khac_gara) %>
            <input type="hidden" class="floating-input number tien_dx_gara" readonly="readonly" value="<%- ESUtil.formatMoney(item.so_luong_gara*item.tien_vtu_gara + item.tien_nhan_cong_gara + item.tien_khac_gara) %>" />
        </td>
        <td>
            <input type="text" class="floating-input" readonly="readonly" value="<%- item.ghi_chu_gara %>" />
        </td>

        <td class="text-center">
            <input type="text" class="floating-input decimal so_luong" onchange="tinhTongTienBG()" value="<%- item.so_luong %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_vtu" onchange="tinhTongTienBG()" value="<%- ESUtil.formatMoney(item.tien_vtu) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_nhan_cong" onchange="tinhTongTienBG()" value="<%- ESUtil.formatMoney(item.tien_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_khac" onchange="tinhTongTienBG()" value="<%- ESUtil.formatMoney(item.tien_khac) %>" />
        </td>
        <td class="text-right">
            <input type="text" class="floating-input number tien_dx form-control" readonly="readonly" value="<%- ESUtil.formatMoney(item.tien_dx) %>" />
        </td>
        <td>
            <input type="text" class="floating-input ghi_chu" value="<%- item.ghi_chu %>" />
        </td>
    </tr>
    <% })}%>
</script>

<script type="text/html" id="navDanhGiaGiamDinhTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.doi_tuong %>" data-lhnv="<%- item.ma %>" data-hang-muc="<%- item.hang_muc %>"><a href="#" onclick="xemChiTietDTTonThatGD('<%- item.ma %>', '<%- item.nhom %>', '<%- item.doi_tuong %>', '<%- item.hang_muc %>')"><%- item.ten %></a></li>
    <%})}%>
</script>

<script type="text/html" id="navDanhGiaNghiepVuTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.doi_tuong %>" data-lhnv="<%- item.ma %>"><a href="#" onclick="xemChiTietDTTonThat('<%- item.ma %>')"><%- item.ten %></a></li>
    <%})}%>
</script>

<script type="text/html" id="navPhuongAnNghiepVuTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item cursor" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.doi_tuong %>" data-lhnv="<%- item.ma %>"><a href="#" onclick="xemChiTietPhuongAn('<%- item.so_id_pa %>', '<%- item.ma %>')"><%- item.ten %></a></li>
    <%})}%>
</script>

<script type="text/html" id="navNghiepVuTab4Template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.doi_tuong %>" data-lhnv="<%- item.ma %>"><a href="#" onclick="xemTinhToanNghiepVu('<%- item.ma %>')"><%- item.ten %></a></li>
    <%})}%>
</script>

<script type="text/html" id="navPhuongAnNghiepVuTNDSTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-lhnv="<%- item.lh_nv %>" data-hang-muc="<%- item.hang_muc %>"><a href="#" onclick="xemPhuongAnNghiepVuTNDS('<%- item.lh_nv %>', '<%- item.hang_muc %>')"><%- item.ten_hang_muc %></a></li>
    <%})}%>
</script>
@* Tab 3 Phương án *@
<script type="text/html" id="modalChiTietTonThatHANGHOATemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" class="floating-input number" data-field="tien_thoa_thuan" value="<%- item.tien_thoa_thuan %>" />
            <input type="hidden" class="floating-input number" data-field="tien_tt" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_tru" value="<%- item.tien_giam_tru %>" />
            <input type="hidden" class="floating-input number" data-field="tien_dx_pa" value="<%- item.tien_dx_pa %>" />
            <input type="hidden" class="floating-input number" data-field="tien_duyet_pa" value="<%- item.tien_duyet_pa %>" />
            <a class="combobox" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <a class="combobox" data-field="muc_do" data-val="<%- item.muc_do %>"><%- item.muc_do_ten %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="so_luong" data-val="<%- item.so_luong %>"><%- item.so_luong %></a>
        </td>
        <td class="text-center">
            <a class="combobox" data-field="dvi_tinh" data-val="<%- item.dvi_tinh %>"><%- item.dvi_tinh_ten %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="gia" data-val="<%- item.gia %>"><%- ESUtil.formatMoney(item.gia) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="so_luong_tt" data-val="<%- item.so_luong_tt %>"><%- item.so_luong_tt %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_tt" data-val="<%- item.tien_tt %>"><%- ESUtil.formatMoney(item.tien_tt) %></a>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_dx_pa) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_duyet_pa) %>
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 9){
    for(var i = 0; i < 9 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalChiTietTonThatTNDS_TAI_SANTemplate">
    <% _.forEach(danh_sach, function(item, index) { %>
    <tr class="chiTietChiTietItem">
        <td class="text-center">
            <%- index + 1%>
        </td>
        <td class="text-left">
            <input type="hidden" class="floating-input" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" class="floating-input" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input" data-field="so_luong" value="<%- item.so_luong %>" />
            <input type="hidden" class="floating-input" data-field="thay_the_sc" value="<%- item.thay_the_sc %>" />
            <input type="hidden" class="floating-input" data-field="chinh_hang" value="<%- item.chinh_hang %>" />
            <input type="hidden" class="floating-input" data-field="thu_hoi" value="<%- item.thu_hoi %>" />
            <input type="hidden" class="floating-input" data-field="ma_chi_phi" value="<%- item.ma_chi_phi %>" />
            <input type="hidden" class="floating-input" data-field="nguyen_nhan" value="<%- item.nguyen_nhan %>" />
            <%if(item.ma_chi_phi === "" || item.ma_chi_phi == null){%>
            <input type="text" class="floating-input" data-field="ten_chi_phi" data-val="<%- item.ten_chi_phi %>" placeholder="Tên hạng mục chi tiết" value="<%- item.ten_chi_phi %>" />
            <%}else{%>
            <a class="combobox" onclick="openModalImagesPaging('<%- item.ma_chi_phi%>')" href="#" data-field="ten_chi_phi" data-val="<%- item.ten_chi_phi %>"><%- item.ten_chi_phi %></a>
            <%}%>
        </td>
        <td class="text-center">
            <input type="hidden" class="floating-input muc_do_ten" data-field="muc_do_ten" value="<%- item.muc_do_ten %>" />
            <input type="text" class="floating-input combobox" data-field="muc_do" data-val="<%- item.muc_do %>" onclick="capNhatMucDoChiTietTonThat(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.muc_do_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <input type="hidden" class="floating-input dvi_tinh_ten" data-field="dvi_tinh_ten" value="<%- item.dvi_tinh_ten %>" />
            <input type="text" class="floating-input combobox" data-field="dvi_tinh" data-val="<%- item.dvi_tinh %>" onclick="capNhatDviTinhChiTietTonThat(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.dvi_tinh_ten %>" style="text-align:center; cursor:pointer" />
        </td>
        <td class="text-center">
            <input type="text" class="floating-input number" data-field="so_luong" data-val="<%- item.so_luong %>" placeholder="Số lượng" value="<%- item.so_luong %>" />
        </td>
         <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_bgia_vtu" data-val="<%- item.tien_bgia_vtu %>" placeholder="Tiền báo giá vật tư" value="<%- ESUtil.formatMoney(item.tien_bgia_vtu) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_bgia_nhan_cong" data-val="<%- item.tien_bgia_nhan_cong %>" placeholder="Tiền báo giá nhân công" value="<%- ESUtil.formatMoney(item.tien_bgia_nhan_cong) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_bgia_khac" data-val="<%- item.tien_bgia_khac %>" placeholder="Tiền báo giá khác" value="<%- ESUtil.formatMoney(item.tien_bgia_khac) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_vtu" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_vtu %>" placeholder="Tiền vật tư" value="<%- ESUtil.formatMoney(item.tien_vtu) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_nhan_cong" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_nhan_cong %>" placeholder="Tiền nhân công" value="<%- ESUtil.formatMoney(item.tien_nhan_cong) %>" />
        </td>
        <td style="text-align:right">
            <input type="text" class="floating-input number" data-field="tien_khac" onchange="tinhTongChiPhiChiTiet(this)" data-val="<%- item.tien_khac %>" placeholder="Tiền khác" value="<%- ESUtil.formatMoney(item.tien_khac) %>" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChuChiPhiCT(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChuChiPhiCT(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <%})%>
    <% if(danh_sach.length < 7){
    for(var i = 0; i < 7 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalChiTietTonThatNGUOITemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" class="floating-input number" data-field="tien_thoa_thuan" value="<%- item.tien_thoa_thuan %>" />
            <input type="hidden" class="floating-input number" data-field="tien_tt" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_tru" value="<%- item.tien_giam_tru %>" />
            <input type="hidden" class="floating-input number" data-field="tien_dx_pa" value="<%- item.tien_dx_pa %>" />
            <input type="hidden" class="floating-input number" data-field="tien_duyet_pa" value="<%- item.tien_duyet_pa %>" />
            <a class="combobox" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <% if(item.ds_thuong_tat == undefined || item.ds_thuong_tat == null || item.ds_thuong_tat == ''){%>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" class="thuong_tat combobox">Chưa xác định</a>
        </td>
        <%}else{%>
        <td>
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" class="thuong_tat combobox"><%= item.ds_thuong_tat %></a>
        </td>
        <%}%>
        <td class="text-right">
            <a class="combobox" data-field="tien_tt" data-val="<%- item.tien_tt %>"><%- ESUtil.formatMoney(item.tien_tt) %></a>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_thoa_thuan) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_dx_pa) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_duyet_pa) %>
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 9){
    for(var i = 0; i < 9 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalChiTietTonThatTNDS_NGUOITemplate">
    <% if(danh_sach.length > 0){%>
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>
            <input type="hidden" class="floating-input number" data-field="tien_tt" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="tien_thoa_thuan" value="<%- item.tien_thoa_thuan %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_tru" value="<%- item.tien_giam_tru %>" />
            <input type="hidden" class="floating-input number" data-field="tien_dx_pa" value="<%- item.tien_dx_pa %>" />
            <input type="hidden" class="floating-input number" data-field="tien_duyet_pa" value="<%- item.tien_duyet_pa %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <a class="combobox" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <% if(item.ds_thuong_tat == undefined || item.ds_thuong_tat == null || item.ds_thuong_tat == ''){%>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" class="thuong_tat combobox">Chưa xác định</a>
        </td>
        <%}else{%>
        <td>
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" class="thuong_tat combobox"><%= item.ds_thuong_tat %></a>
        </td>
        <%}%>
        <td class="text-right">
            <a class="combobox" data-field="tien_tt" data-val="<%- item.tien_tt %>"><%- ESUtil.formatMoney(item.tien_tt) %></a>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_thoa_thuan) %>
        </td>

        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_dx_pa) %>
        </td>

        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_duyet_pa) %>
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 9){
    for(var i = 0; i < 9 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
<script type="text/html" id="modalChiTietTonThatTNDS_NGUOI_HKTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td>

            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong_cha" value="<%- item.so_id_doi_tuong_cha %>" />
            <input type="hidden" class="floating-input" data-field="tien_tt" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input" data-field="tien_giam_tru" value="<%- item.tien_giam_tru %>" />
            <input type="hidden" class="floating-input" data-field="tien_thoa_thuan" value="<%- item.tien_thoa_thuan %>" />
            <input type="hidden" class="floating-input" data-field="tien_dx_pa" value="<%- item.tien_dx_pa %>" />
            <input type="hidden" class="floating-input" data-field="tien_duyet_pa" value="<%- item.tien_duyet_pa %>" />
            <a class="combobox" data-field="ten" data-val="<%- item.ten %>"><%- item.ten %></a>
        </td>
        <td class="text-center">
            <% if(item.dia_chi != null && item.dia_chi!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="<%- item.dia_chi %>">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showDiaChi(this)" data-field="dia_chi" data-val="">
                <i class="fas fa-map-marked-alt" title="Địa chỉ"></i>
            </a>
            <% } %>
        </td>
        <% if(item.ds_thuong_tat == undefined || item.ds_thuong_tat == null || item.ds_thuong_tat == ''){%>
        <td class="text-center">
            <a href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" class="thuong_tat combobox">Chưa xác định</a>
        </td>
        <%}else{%>
        <td>
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- item.thuong_tat %>" data-field="thuong_tat" class="thuong_tat combobox"><%= item.ds_thuong_tat %></a>
        </td>
        <%}%>
        <td class="text-right">
            <a class="combobox" data-field="tien_tt" data-val="<%- item.tien_tt %>"><%- ESUtil.formatMoney(item.tien_tt) %></a>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_thoa_thuan) %>
        </td>

        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_dx_pa) %>
        </td>

        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_duyet_pa) %>
        </td>
        <td class="text-center">
            <% if(item.mo_ta != null && item.mo_ta!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="<%- item.mo_ta %>">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showMoTa(this)" data-field="mo_ta" data-val="">
                <i class="far fa-file-alt" title="Mô tả"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer combobox" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 9){
    for(var i = 0; i < 9 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Tính toán: Vật chất xe*@
<script type="text/html" id="tblTinhToanVCXTemplate">
    <% if(tinh_toan_bt.length > 0){
    _.forEach(tinh_toan_bt, function(tinh_toan, index) { %>
    <tr class="tblTinhToanVCXItem hmTinhToanItem" data-search="<%- ESUtil.xoaKhoangTrangText(tinh_toan.ten) %>">
        <td class="text-center">
            <input type="hidden" data-field="bt" value="<%- tinh_toan.bt %>" />
            <input type="hidden" data-field="ten" value="<%- tinh_toan.ten %>" />
            <input type="hidden" data-field="vu_tt_ten" value="<%- tinh_toan.vu_tt_ten %>" />
            <input type="hidden" data-field="vu_tt" value="<%- tinh_toan.vu_tt %>" />
            <input type="hidden" data-field="gia_duyet" value="<%- tinh_toan.gia_duyet %>" />
            <input type="hidden" data-field="pt_giam_tru" value="<%- tinh_toan.pt_giam_tru %>" />
            <input type="hidden" data-field="tl_ktru_tien_bh" value="<%- tinh_toan.tl_ktru_tien_bh %>" />
            <input type="hidden" data-field="tl_giam_gia_vtu" value="<%- tinh_toan.tl_giam_gia_vtu %>" />
            <input type="hidden" data-field="tl_giam_gia_nhan_cong" value="<%- tinh_toan.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tl_giam_gia_khac" value="<%- tinh_toan.tl_giam_gia_khac %>" />
            <input type="hidden" data-field="tl_thue_vtu" value="<%- tinh_toan.tl_thue_vtu %>" />
            <input type="hidden" data-field="tl_thue_nhan_cong" value="<%- tinh_toan.tl_thue_nhan_cong %>" />
            <input type="hidden" data-field="tl_thue_khac" value="<%- tinh_toan.tl_thue_khac %>" />

            <input type="hidden" data-field="tien_giam_gia_vtu" value="<%- tinh_toan.tien_giam_gia_vtu %>" />
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" value="<%- tinh_toan.tien_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tien_giam_gia_khac" value="<%- tinh_toan.tien_giam_gia_khac %>" />
            <input type="hidden" data-field="lh_giam_gia" value="<%- tinh_toan.lh_giam_gia %>" />
            <input type="hidden" data-field="lh_tt_giam_gia" value="<%- tinh_toan.lh_tt_giam_gia %>" />

            <input type="hidden" data-field="so_id_doi_tuong" value="<%- tinh_toan.so_id_doi_tuong %>" />
            <input type="hidden" data-field="so_id_doi_tuong_cha" value="<%- tinh_toan.so_id_doi_tuong_cha %>" />
            <input type="hidden" data-field="hang_muc" value="<%- tinh_toan.hang_muc %>" />
            <input type="hidden" data-field="gia_khac_duyet" value="<%- tinh_toan.gia_khac_duyet %>" />
            <input type="hidden" data-field="tien_dtru_thanh_ly" value="<%- tinh_toan.tien_dtru_thanh_ly %>" />
            <input type="hidden" data-field="tien_dtru_ntba" value="<%- tinh_toan.tien_dtru_ntba %>" />
            <a class="combobox d-none" data-field="giam_gia" data-val="<%- tinh_toan.giam_gia %>"><%- ESUtil.formatMoney(tinh_toan.giam_gia) %></a>
            <a class="combobox d-none" data-field="tien_con_lai" data-val="<%- tinh_toan.tien_con_lai %>"><%- ESUtil.formatMoney(tinh_toan.tien_con_lai) %></a>
            <a class="combobox d-none" data-field="dkbs" data-val="<%- tinh_toan.dkbs %>"></a>
            <%- tinh_toan.vu_tt_ten %>
        </td>
        <td>
            <a href="#" onclick="openModalImagesPaging('<%- tinh_toan.hang_muc %>')"><%- tinh_toan.ten %></a>
            <%if(tinh_toan.dgrr != undefined && tinh_toan.dgrr != null && tinh_toan.dgrr != "" && tinh_toan.dgrr == "C"){%>
            <a href="#" class="text-warning" onclick="onXemHangMucDGRR('<%- tinh_toan.hang_muc %>')"><i class="fas fa-exclamation-triangle ml-2"></i><span class="ml-1" style="font-size:10px; font-style:italic;">Có tổn thất cấp đơn</span> </a>
            <%}%>
        </td>
        <td class="text-right">
            <%if(tinh_toan.hang_muc != undefined && tinh_toan.hang_muc != null && tinh_toan.hang_muc != "" && tinh_toan.hang_muc == "BT_TOAN_BO"){%>
                <input type="text" data-field="gia_vtu_duyet" class="number floating-input" data-val="<%- ESUtil.formatMoney(tinh_toan.gia_vtu_duyet) %>" value="<%- ESUtil.formatMoney(tinh_toan.gia_vtu_duyet) %>" onchange="tinhToan(this)"/>
            <%}else{%>
                <a class="combobox" data-field="gia_vtu_duyet" data-val="<%- tinh_toan.gia_vtu_duyet %>"><%- ESUtil.formatMoney(tinh_toan.gia_vtu_duyet) %></a>
            <%}%>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="gia_nhan_cong_duyet" data-val="<%- tinh_toan.gia_nhan_cong_duyet %>"><%- ESUtil.formatMoney(tinh_toan.gia_nhan_cong_duyet) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="gia_khac_duyet" data-val="<%- tinh_toan.gia_khac_duyet %>"><%- ESUtil.formatMoney(tinh_toan.gia_khac_duyet) %></a>
        </td>
        <td class="text-right tblTinhToanVCX_T">
            <a class="combobox"><%- ESUtil.formatMoney(tinh_toan.giam_gia) %></a>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_khau_hao" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_khau_hao) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru_loi" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru_loi) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_giam_tru" data-val="<%- tinh_toan.tien_giam_tru %>"><%- ESUtil.formatMoney(tinh_toan.tien_giam_tru) %></a>
        </td>
        @*<td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru) %>" onchange="tinhToan(this)" />
        </td>*@
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_bao_hiem) %>" onchange="tinhToan(this)" />
        </td>
        
        <td class="text-right tblTinhToanVCX_S">
            <a class="combobox" data-field="giam_gia" data-val="<%- tinh_toan.giam_gia %>"><%- ESUtil.formatMoney(tinh_toan.giam_gia) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_ktru_tien_bh" data-val="<%- tinh_toan.tien_ktru_tien_bh %>"><%- ESUtil.formatMoney(tinh_toan.tien_ktru_tien_bh) %></a>
        </td>
        <td class="text-right">
            <a class="combobox"><%- ESUtil.formatMoney(tinh_toan.tien_dtru_thanh_ly) %></a>
        </td>
        <td class="text-right">
            <a class="combobox"><%- ESUtil.formatMoney(tinh_toan.tien_dtru_ntba) %></a>
        </td>
        <td class="text-right">
            <a class="combobox d-none" data-field="tien_thue" data-val="<%- tinh_toan.tien_thue %>"><%- ESUtil.formatMoney(tinh_toan.tien_thue) %></a>
            @*<input type="text" data-field="tl_thue" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.tl_thue) %>" onchange="tinhToan(this)" />*@
        </td>
        <td class="text-center">
            <% if(tinh_toan.nguyen_nhan != null && tinh_toan.nguyen_nhan != ""){ %>
            <a href="#" data-field="nguyen_nhan" onclick="showNguyenNhan(this)" data-val="<%- tinh_toan.nguyen_nhan %>" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(tinh_toan.ghi_chu != null && tinh_toan.ghi_chu != ""){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- tinh_toan.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>

    <% if(tinh_toan_bt.length < 6){
    for(var i = 0; i < 6 - tinh_toan_bt.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Tính toán: Hàng hóa*@
<script type="text/html" id="tblTinhToanHANGHOATemplate">
    <% if(tinh_toan_bt.length > 0){
    _.forEach(tinh_toan_bt, function(tinh_toan, index) { %>
    <tr class="tblTinhToanHANGHOAItem">
        <td class="text-center">
            <input type="hidden" data-field="bt" value="<%- tinh_toan.bt %>" />
            <input type="hidden" data-field="so_id_doi_tuong" value="<%- tinh_toan.so_id_doi_tuong %>" />
            <input type="hidden" data-field="so_id_doi_tuong_cha" value="<%- tinh_toan.so_id_doi_tuong_cha %>" />
            <input type="hidden" data-field="hang_muc" value="<%- tinh_toan.hang_muc %>" />
            <input type="hidden" data-field="gia_khac_duyet" value="<%- tinh_toan.tien_khac_duyet_pa %>" />
            <%- tinh_toan.vu_tt_ten %>
        </td>
        <td><%- tinh_toan.ten %></td>
        <td class="text-right">
            <a class="combobox" data-field="gia_vtu_duyet" data-val="<%- tinh_toan.tien_vtu_duyet_pa %>"><%- ESUtil.formatMoney(tinh_toan.tien_vtu_duyet_pa) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="gia_nhan_cong_duyet" data-val="<%- tinh_toan.tien_nhan_cong_duyet_pa %>"><%- ESUtil.formatMoney(tinh_toan.tien_nhan_cong_duyet_pa) %></a>
        </td>

        <td class="text-right">
            <a class="combobox" data-field="gia_duyet" data-val="<%- tinh_toan.tien_duyet_pa %>"><%- ESUtil.formatMoney(tinh_toan.tien_duyet_pa) %></a>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_khau_hao" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_khau_hao) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_bao_hiem) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="tl_thue" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.tl_thue) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-center">
            <% if(tinh_toan.nguyen_nhan_giam != null){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- tinh_toan.nguyen_nhan_giam %>" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(tinh_toan.ghi_chu != null){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- tinh_toan.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="10">Chưa có dữ liệu</td>
    </tr>
    <% } %>

    <% if(tinh_toan_bt.length < 6){
    for(var i = 0; i < 6 - tinh_toan_bt.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Tính toán: Người ngồi trên xe*@
<script type="text/html" id="tblTinhToanNNTXTemplate">
    <% if(tinh_toan_bt.length > 0){
    _.forEach(tinh_toan_bt, function(tinh_toan, index) { %>
    <tr class="tblTinhToanNNTXItem">
        <td class="text-center">
            <input type="hidden" data-field="bt" value="<%- tinh_toan.bt %>" />
            <input type="hidden" data-field="so_id_doi_tuong" value="<%- tinh_toan.so_id_doi_tuong %>" />
            <input type="hidden" data-field="so_id_doi_tuong_cha" value="<%- tinh_toan.so_id_doi_tuong_cha %>" />
            <input type="hidden" data-field="hang_muc" value="<%- tinh_toan.hang_muc %>" />
            <input type="hidden" data-field="tl_thue" value="<%- tinh_toan.tl_thue %>" />
            <input type="hidden" data-field="vu_tt" value="<%- tinh_toan.vu_tt %>" />
            <input type="hidden" data-field="gia_vtu_duyet" value="<%- tinh_toan.gia_vtu_duyet %>" />
            <input type="hidden" data-field="gia_nhan_cong_duyet" value="<%- tinh_toan.gia_nhan_cong_duyet %>" />
            <input type="hidden" data-field="gia_khac_duyet" value="<%- tinh_toan.gia_khac_duyet %>" />
            <input type="hidden" data-field="tl_giam_gia_vtu" value="<%- tinh_toan.tl_giam_gia_vtu %>" />
            <input type="hidden" data-field="tl_giam_gia_nhan_cong" value="<%- tinh_toan.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tl_giam_gia_khac" value="<%- tinh_toan.tl_giam_gia_khac %>" />
            <input type="hidden" data-field="tien_giam_gia_vtu" value="<%- tinh_toan.tien_giam_gia_vtu %>" />
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" value="<%- tinh_toan.tien_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tien_giam_gia_khac" value="<%- tinh_toan.tien_giam_gia_khac %>" />
            <input type="hidden" data-field="tl_thue_vtu" value="<%- tinh_toan.tl_thue_vtu %>" />
            <input type="hidden" data-field="tl_thue_nhan_cong" value="<%- tinh_toan.tl_thue_nhan_cong %>" />
            <input type="hidden" data-field="tl_thue_khac" value="<%- tinh_toan.tl_thue_khac %>" />
            <input type="hidden" data-field="tl_ktru_tien_bh" value="<%- tinh_toan.tl_ktru_tien_bh %>" />
            <input type="hidden" data-field="lh_giam_gia" value="<%- tinh_toan.lh_giam_gia %>" />
            <input type="hidden" data-field="lh_tt_giam_gia" value="<%- tinh_toan.lh_tt_giam_gia %>" />
            <input type="hidden" data-field="cmnd" value="<%- tinh_toan.cmnd %>" />
            <%- tinh_toan.vu_tt_ten %>
        </td>
        <td><%- tinh_toan.ten %></td>

        <% if(tinh_toan.ds_thuong_tat == undefined || tinh_toan.ds_thuong_tat == null || tinh_toan.ds_thuong_tat == ''){%>
        <td class="text-center lg">
            <a href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td class="lg">
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= tinh_toan.ds_thuong_tat %></a>
        </td>
        <%}%>

        <td class="text-right">
            <a class="combobox" data-field="gia_duyet" data-val="<%- tinh_toan.tien_duyet_pa %>"><%- ESUtil.formatMoney(tinh_toan.tien_duyet_pa) %></a>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru_loi" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru_loi) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_bao_hiem) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right">
             <input type="text" data-field="tien_thoa_thuan" class="number floating-input" value="<%- ESUtil.formatMoney(tinh_toan.tien_thoa_thuan) %>" />
         </td>
        <td class="text-center">
            <% if(tinh_toan.nguyen_nhan_giam != null){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- tinh_toan.nguyen_nhan_giam %>" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(tinh_toan.ghi_chu != null){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- tinh_toan.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Chưa có dữ liệu</td>
    </tr>
    <% } %>

    <% if(tinh_toan_bt.length < 6){
    for(var i = 0; i < 6 - tinh_toan_bt.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Tính toán: Tai nạn lái phụ xe*@
<script type="text/html" id="tblTinhToanLPHU_XETemplate">
    <% if(tinh_toan_bt.length > 0){
    _.forEach(tinh_toan_bt, function(tinh_toan, index) { %>
    <tr class="tblTinhToanLPHU_XEItem">
        <td class="text-center">
            <input type="hidden" data-field="bt" value="<%- tinh_toan.bt %>" />
            <input type="hidden" data-field="so_id_doi_tuong" value="<%- tinh_toan.so_id_doi_tuong %>" />
            <input type="hidden" data-field="so_id_doi_tuong_cha" value="<%- tinh_toan.so_id_doi_tuong_cha %>" />
            <input type="hidden" data-field="hang_muc" value="<%- tinh_toan.hang_muc %>" />
            <input type="hidden" data-field="tl_thue" value="<%- tinh_toan.tl_thue %>" />
            <input type="hidden" data-field="vu_tt" value="<%- tinh_toan.vu_tt %>" />
            <input type="hidden" data-field="gia_vtu_duyet" value="<%- tinh_toan.gia_vtu_duyet %>" />
            <input type="hidden" data-field="gia_nhan_cong_duyet" value="<%- tinh_toan.gia_nhan_cong_duyet %>" />
            <input type="hidden" data-field="gia_khac_duyet" value="<%- tinh_toan.gia_khac_duyet %>" />
            <input type="hidden" data-field="tl_giam_gia_vtu" value="<%- tinh_toan.tl_giam_gia_vtu %>" />
            <input type="hidden" data-field="tl_giam_gia_nhan_cong" value="<%- tinh_toan.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tl_giam_gia_khac" value="<%- tinh_toan.tl_giam_gia_khac %>" />
            <input type="hidden" data-field="tien_giam_gia_vtu" value="<%- tinh_toan.tien_giam_gia_vtu %>" />
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" value="<%- tinh_toan.tien_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tien_giam_gia_khac" value="<%- tinh_toan.tien_giam_gia_khac %>" />
            <input type="hidden" data-field="tl_thue_vtu" value="<%- tinh_toan.tl_thue_vtu %>" />
            <input type="hidden" data-field="tl_thue_nhan_cong" value="<%- tinh_toan.tl_thue_nhan_cong %>" />
            <input type="hidden" data-field="tl_thue_khac" value="<%- tinh_toan.tl_thue_khac %>" />
            <input type="hidden" data-field="tl_ktru_tien_bh" value="<%- tinh_toan.tl_ktru_tien_bh %>" />
            <input type="hidden" data-field="lh_giam_gia" value="<%- tinh_toan.lh_giam_gia %>" />
            <input type="hidden" data-field="lh_tt_giam_gia" value="<%- tinh_toan.lh_tt_giam_gia %>" />
            <input type="hidden" data-field="cmnd" value="<%- tinh_toan.cmnd %>" />
            <%- tinh_toan.vu_tt_ten %>
        </td>
        <td><%- tinh_toan.ten %></td>

        <% if(tinh_toan.ds_thuong_tat == undefined || tinh_toan.ds_thuong_tat == null || tinh_toan.ds_thuong_tat == ''){%>
        <td class="text-center lg">
            <a href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td class="lg">
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= tinh_toan.ds_thuong_tat %></a>
        </td>
        <%}%>

        <td class="text-right">
            <a class="combobox" data-field="gia_duyet" data-val="<%- tinh_toan.tien_duyet_pa %>"><%- ESUtil.formatMoney(tinh_toan.tien_duyet_pa) %></a>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru_loi" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru_loi) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_bao_hiem) %>" onchange="tinhToan(this)" />
        </td>
         <td class="text-right">
             <input type="text" data-field="tien_thoa_thuan" class="number floating-input" value="<%- ESUtil.formatMoney(tinh_toan.tien_thoa_thuan) %>" />
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_de_xuat" data-val="<%- tinh_toan.tien_de_xuat %>"><%- ESUtil.formatMoney(tinh_toan.tien_de_xuat) %></a>
        </td>
        <td class="text-center">
            <% if(tinh_toan.nguyen_nhan_giam != null){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- tinh_toan.nguyen_nhan_giam %>" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(tinh_toan.ghi_chu != null){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- tinh_toan.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Chưa có dữ liệu</td>
    </tr>
    <% } %>

    <% if(tinh_toan_bt.length < 6){
    for(var i = 0; i < 6 - tinh_toan_bt.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Tính toán: TNDS về người*@
<script type="text/html" id="tblTinhToanTNDS_NGUOITemplate">
    <% if(tinh_toan_bt.length > 0){
    _.forEach(tinh_toan_bt, function(tinh_toan, index) { %>
    <tr class="tblTinhToanTNDS_NGUOIItem">
        <td class="text-center">
            <input type="hidden" data-field="bt" value="<%- tinh_toan.bt %>" />
            <input type="hidden" data-field="so_id_doi_tuong" value="<%- tinh_toan.so_id_doi_tuong %>" />
            <input type="hidden" data-field="so_id_doi_tuong_cha" value="<%- tinh_toan.so_id_doi_tuong_cha %>" />
            <input type="hidden" data-field="hang_muc" value="<%- tinh_toan.hang_muc %>" />
            <input type="hidden" data-field="tl_thue" value="<%- tinh_toan.tl_thue %>" />
            <input type="hidden" data-field="vu_tt" value="<%- tinh_toan.vu_tt %>" />
            <input type="hidden" data-field="gia_vtu_duyet" value="<%- tinh_toan.gia_vtu_duyet %>" />
            <input type="hidden" data-field="gia_nhan_cong_duyet" value="<%- tinh_toan.gia_nhan_cong_duyet %>" />
            <input type="hidden" data-field="gia_khac_duyet" value="<%- tinh_toan.gia_khac_duyet %>" />
            <input type="hidden" data-field="tl_giam_gia_vtu" value="<%- tinh_toan.tl_giam_gia_vtu %>" />
            <input type="hidden" data-field="tl_giam_gia_nhan_cong" value="<%- tinh_toan.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tl_giam_gia_khac" value="<%- tinh_toan.tl_giam_gia_khac %>" />
            <input type="hidden" data-field="tien_giam_gia_vtu" value="<%- tinh_toan.tien_giam_gia_vtu %>" />
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" value="<%- tinh_toan.tien_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tien_giam_gia_khac" value="<%- tinh_toan.tien_giam_gia_khac %>" />
            <input type="hidden" data-field="tl_thue_vtu" value="<%- tinh_toan.tl_thue_vtu %>" />
            <input type="hidden" data-field="tl_thue_nhan_cong" value="<%- tinh_toan.tl_thue_nhan_cong %>" />
            <input type="hidden" data-field="tl_thue_khac" value="<%- tinh_toan.tl_thue_khac %>" />
            <input type="hidden" data-field="tl_ktru_tien_bh" value="<%- tinh_toan.tl_ktru_tien_bh %>" />
            <input type="hidden" data-field="lh_giam_gia" value="<%- tinh_toan.lh_giam_gia %>" />
            <input type="hidden" data-field="lh_tt_giam_gia" value="<%- tinh_toan.lh_tt_giam_gia %>" />
            <input type="hidden" data-field="cmnd" value="<%- tinh_toan.cmnd %>" />
            <%- tinh_toan.vu_tt_ten %>
        </td>
        <td><%- tinh_toan.ten %></td>
        <% if(tinh_toan.ds_thuong_tat == undefined || tinh_toan.ds_thuong_tat == null || tinh_toan.ds_thuong_tat == ''){%>
        <td class="text-center lg">
            <a href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td class="lg">
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= tinh_toan.ds_thuong_tat %></a>
        </td>
        <%}%>
        <td class="text-right">
            <a class="combobox" data-field="gia_duyet" data-val="<%- tinh_toan.tien_duyet_pa %>"><%- ESUtil.formatMoney(tinh_toan.tien_duyet_pa) %></a>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru_loi" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru_loi) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_bao_hiem) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right">
             <input type="text" data-field="tien_thoa_thuan" class="number floating-input" value="<%- ESUtil.formatMoney(tinh_toan.tien_thoa_thuan) %>" />
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_de_xuat" data-val="<%- tinh_toan.tien_de_xuat %>"><%- ESUtil.formatMoney(tinh_toan.tien_de_xuat) %></a>
        </td>
        <td class="text-center">
            <% if(tinh_toan.nguyen_nhan_giam != null){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- tinh_toan.nguyen_nhan_giam %>" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(tinh_toan.ghi_chu != null){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- tinh_toan.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Chưa có dữ liệu</td>
    </tr>
    <% } %>

    <% if(tinh_toan_bt.length < 6){
    for(var i = 0; i < 6 - tinh_toan_bt.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Tính toán: TNDS về hành khách*@
<script type="text/html" id="tblTinhToanTNDS_NGUOI_HKTemplate">
    <% if(tinh_toan_bt.length > 0){
    _.forEach(tinh_toan_bt, function(tinh_toan, index) { %>
    <tr class="tblTinhToanTNDS_NGUOI_HKItem">
        <td class="text-center">
            <input type="hidden" data-field="bt" value="<%- tinh_toan.bt %>" />
            <input type="hidden" data-field="so_id_doi_tuong" value="<%- tinh_toan.so_id_doi_tuong %>" />
            <input type="hidden" data-field="so_id_doi_tuong_cha" value="<%- tinh_toan.so_id_doi_tuong_cha %>" />
            <input type="hidden" data-field="hang_muc" value="<%- tinh_toan.hang_muc %>" />
            <input type="hidden" data-field="tl_thue" value="<%- tinh_toan.tl_thue %>" />
            <input type="hidden" data-field="vu_tt" value="<%- tinh_toan.vu_tt %>" />
            <input type="hidden" data-field="gia_vtu_duyet" value="<%- tinh_toan.gia_vtu_duyet %>" />
            <input type="hidden" data-field="gia_nhan_cong_duyet" value="<%- tinh_toan.gia_nhan_cong_duyet %>" />
            <input type="hidden" data-field="gia_khac_duyet" value="<%- tinh_toan.gia_khac_duyet %>" />
            <input type="hidden" data-field="tl_giam_gia_vtu" value="<%- tinh_toan.tl_giam_gia_vtu %>" />
            <input type="hidden" data-field="tl_giam_gia_nhan_cong" value="<%- tinh_toan.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tl_giam_gia_khac" value="<%- tinh_toan.tl_giam_gia_khac %>" />
            <input type="hidden" data-field="tien_giam_gia_vtu" value="<%- tinh_toan.tien_giam_gia_vtu %>" />
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" value="<%- tinh_toan.tien_giam_gia_nhan_cong %>" />
            <input type="hidden" data-field="tien_giam_gia_khac" value="<%- tinh_toan.tien_giam_gia_khac %>" />
            <input type="hidden" data-field="tl_thue_vtu" value="<%- tinh_toan.tl_thue_vtu %>" />
            <input type="hidden" data-field="tl_thue_nhan_cong" value="<%- tinh_toan.tl_thue_nhan_cong %>" />
            <input type="hidden" data-field="tl_thue_khac" value="<%- tinh_toan.tl_thue_khac %>" />
            <input type="hidden" data-field="tl_ktru_tien_bh" value="<%- tinh_toan.tl_ktru_tien_bh %>" />
            <input type="hidden" data-field="lh_giam_gia" value="<%- tinh_toan.lh_giam_gia %>" />
            <input type="hidden" data-field="lh_tt_giam_gia" value="<%- tinh_toan.lh_tt_giam_gia %>" />
            <input type="hidden" data-field="cmnd" value="<%- tinh_toan.cmnd %>" />
            <%- tinh_toan.vu_tt_ten %>
        </td>
        <td><%- tinh_toan.ten %></td>
        <% if(tinh_toan.ds_thuong_tat == undefined || tinh_toan.ds_thuong_tat == null || tinh_toan.ds_thuong_tat == ''){%>
        <td class="text-center lg">
            <a href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox">Đánh giá thương tật</a>
        </td>
        <%}else{%>
        <td class="lg">
            <a style="font-size:10px; font-style:italic;" href="#" data-val="<%- tinh_toan.thuong_tat %>" data-field="thuong_tat" onclick="nhapThuongTat('<%- tinh_toan.bt %>', '<%- tinh_toan.vu_tt %>', '<%- tinh_toan.lh_nv %>', '<%- tinh_toan.hang_muc %>', '<%- tinh_toan.so_id_doi_tuong %>')" class="thuong_tat combobox"><%= tinh_toan.ds_thuong_tat %></a>
        </td>
        <%}%>
        <td class="text-right">
            <a class="combobox" data-field="gia_duyet" data-val="<%- tinh_toan.tien_duyet_pa %>"><%- ESUtil.formatMoney(tinh_toan.tien_duyet_pa) %></a>
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru_loi" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru_loi) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_giam_tru" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_giam_tru) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right wd-110-f">
            <input type="text" data-field="pt_bao_hiem" class="decimal floating-input" value="<%- ESUtil.formatMoney(tinh_toan.pt_bao_hiem) %>" onchange="tinhToan(this)" />
        </td>
         <td class="text-right">
             <input type="text" data-field="tien_thoa_thuan" class="number floating-input" value="<%- ESUtil.formatMoney(tinh_toan.tien_thoa_thuan) %>" />
        </td>
        <td class="text-right">
            <a class="combobox" data-field="tien_de_xuat" data-val="<%- tinh_toan.tien_de_xuat %>"><%- ESUtil.formatMoney(tinh_toan.tien_de_xuat) %></a>
        </td>
        <td class="text-center">
            <% if(tinh_toan.nguyen_nhan_giam != null){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- tinh_toan.nguyen_nhan_giam %>" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(tinh_toan.ghi_chu != null){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- tinh_toan.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="8">Chưa có dữ liệu</td>
    </tr>
    <% } %>

    <% if(tinh_toan_bt.length < 6){
    for(var i = 0; i < 6 - tinh_toan_bt.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Tính toán: TNDS về tài sản*@
<script type="text/html" id="tblTinhToanTNDS_TAISANTemplate">
    <% if(tinh_toan_bt.length > 0){
    _.forEach(tinh_toan_bt, function(item,index) { %>
    <tr class="hmChiTietItem">
        <td class="text-center"><%- item.vu_tt_ten %></td>
        <td>
            <input type="hidden" class="floating-input number" data-field="tien_thoa_thuan" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="tien_tt" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="tien_khau_hao" value="<%- item.tien_khau_hao %>" />
            <input type="hidden" class="floating-input number" data-field="tien_bao_hiem" value="<%- item.tien_bao_hiem %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_tru_loi" value="<%- item.tien_giam_tru_loi %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_tru" value="<%- item.tien_giam_tru %>" />
            <input type="hidden" class="floating-input number" data-field="tien_giam_gia" value="<%- item.tien_giam_gia %>" />
            <input type="hidden" class="floating-input number" data-field="thue" value="<%- item.thue %>" />
            <input type="hidden" class="floating-input number" data-field="vu_tt" value="<%- item.vu_tt %>" />
            <input type="hidden" class="floating-input number" data-field="bt" value="<%- item.bt %>" />
            <input type="hidden" class="floating-input" data-field="hang_muc" value="<%- item.hang_muc %>" />
            <input type="hidden" class="floating-input number" data-field="gia_duyet" value="<%- item.tien_tt %>" />
            <input type="hidden" class="floating-input number" data-field="so_id_doi_tuong" value="<%- item.so_id_doi_tuong %>" />
            <input type="hidden" data-field="tl_giam_gia_vtu" value="" />
            <input type="hidden" data-field="tl_giam_gia_nhan_cong" value="" />
            <input type="hidden" data-field="tl_giam_gia_khac" value="" />
            <input type="hidden" data-field="tien_giam_gia_vtu" value="" />
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" value="" />
            <input type="hidden" data-field="tien_giam_gia_khac" value="" />
            <input type="hidden" data-field="tl_thue_vtu" value="<%- item.tl_thue_vtu %>" />
            <input type="hidden" data-field="tl_thue_nhan_cong" value="<%- item.tl_thue_nhan_cong %>" />
            <input type="hidden" data-field="tl_thue_khac" value="<%- item.tl_thue_khac %>" />
            <input type="hidden" data-field="tl_ktru_tien_bh" value="" />
            <input type="hidden" data-field="lh_giam_gia" value="" />
            <input type="hidden" data-field="lh_tt_giam_gia" value="" />
            <a href="#" class="combobox" data-field="ten_chi_phi" onclick="openModalImagesPaging('<%= item.hang_muc %>')"><%= item.ten_chi_phi %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="gia_vtu_duyet" data-val="<%- item.tien_vtu %>"><%- ESUtil.formatMoney(item.tien_vtu) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="gia_nhan_cong_duyet" data-val="<%- item.tien_nhan_cong %>"><%- ESUtil.formatMoney(item.tien_nhan_cong) %></a>
        </td>
        <td class="text-right">
            <a class="combobox" data-field="gia_khac_duyet" data-val="<%- item.tien_khac %>"><%- ESUtil.formatMoney(item.tien_khac) %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_khau_hao" data-val="<%- item.pt_khau_hao %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_khau_hao) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_giam_tru_loi" data-val="<%- item.pt_giam_tru_loi %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru_loi) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_giam_tru" data-val="<%- item.pt_giam_tru %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_giam_tru) %>" onchange="tinhToan(this)" />
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_bao_hiem" data-val="<%- item.pt_bao_hiem %>" class="decimal floating-input" value="<%- ESUtil.formatMoney(item.pt_bao_hiem) %>" onchange="tinhToan(this)" />
        </td>
        
        <td class="text-right">
            <a class="combobox d-none" data-field="tien_thue" data-val="<%- item.thue %>"><%- ESUtil.formatMoney(item.thue) %></a>
        </td>
        <td class="text-center">
            <% if(item.nguyen_nhan != null && item.nguyen_nhan != '' && item.nguyen_nhan != undefined){ %>
            <a href="#" data-field="nguyen_nhan" data-val="<%- item.nguyen_nhan %>" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân giảm"></i>
            </a>
            <% }else{ %>
            <a data-field="nguyen_nhan" data-val="" onclick="showNguyenNhan(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Nguyên nhân giảm"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu != '' && item.ghi_chu != undefined){ %>
            <a href="#" data-field="ghi_chu" data-val="<%- item.ghi_chu %>" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a data-field="ghi_chu" data-val="" onclick="showGhiChu(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(tinh_toan_bt.length < 6){
    for(var i = 0; i < 6 - tinh_toan_bt.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*  Danh sách tài liệu hồ sơ hình ảnh *@
<script type="text/html" id="dsHinhAnhHangMucTemplate">
    <% if(danh_sach.length > 0){ %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <div class="imagesCategory" style="display: table-row" data-search="<%- ESUtil.xoaKhoangTrangText(item.nhom) %>">
        <p style="margin-bottom:5px;" class="font-weight-bold">
            <a href="#">
                <%if(item.loai == "TT"){%>
                <%- item.nhom %> <i style="font-size: 10px">(<%- item.muc_do_ten %>/<%-item.thay_the_sc_ten%>)</i><br />
                <i style="font-size: 10px">(<%- item.ten_doi_tuong %>)</i>
                <%}else{%>
                <%- item.nhom %><br />
                <i style="font-size: 10px">(<%- item.ten_doi_tuong %>)</i>
                <%}%>
            </a>
        </p>
        <% _.forEach(item.children, function(image,index_anh) { %>
        <div style="width:60px; height:60px; margin-right:10px; margin-bottom:5px; cursor:pointer;float:left;">
            <img style="width: 100%; height:100%; border-radius:10px" data-original="" location-x="<%- image.x %>" location-y="<%- image.y %>" data-ngay="<%- image.ngay %>" data-nsd="<%- image.nsd%>" data-id="<%- image.so_id %>" data-bt="<%- image.bt %>" data-ma-file="<%- image.ma_file %>" data-pm="<%- image.pm %>" data-cnhanh="<%- image.ma_chi_nhanh %>" src="data:image/png;base64, <%- image.duong_dan %>" alt="<%- image.ten_file %>">
        </div>
        <% }) %>
    </div>
    <% }) %>
        <div class="mx-1 my-2 d-none" id="openModalImagesPaging">
            <button type="button" class="btn btn-sm btn-block btn-outline-primary mb-2" onclick="openModalImagesPaging?.('<%- ma_file %>', false);">
                <i class="far fa-images mr-2"></i>Hiện thêm ảnh
            </button>
            <button type="button" class="btn btn-sm btn-block btn-outline-danger" onclick="openModalImages?.('<%- ma_file %>');">
                <i class="far fa-exclamation-triangle mr-2"></i>Hiện tất cả
            </button>
        </div>
    <% } %>
</script>

<script type="text/html" id="dsXacMinhPhiLaySoHS_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien) %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.so_tien_da_tt) %>
        </td>
        <td class="<%- item.style %>"><%= item.ghi_chu %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="3">
            <p class="m-0">Chưa có dữ liệu hiển thị</p>
        </td>
    </tr>
    <% } %>
</script>

<script type="text/html" id="danhSachNV_template">
    <% if(lhnv.length > 0){
    _.forEach(lhnv, function(item,index) { %>
    <tr class="row-item" row-val="<%- item.ma %>" row-vcx="<%- item.vcx %>">
        <td class="text-center"><%- index+1 %></td>
        <td><%- item.ten_lhnv %></td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien) %></td>
        <% if(item.vcx == 'VCX'){ %>
        <%if(item.ktru=="K"){%>
        <td class="text-center">Không</td>
        <td class="text-right"><%- ESUtil.formatMoney(item.mien_thuong) %></td>
        <% } else { %>
        <td class="text-center">Có</td>
        <td class="text-right"><%- ESUtil.formatMoney(item.mien_thuong) %></td>
        <% } %>
        <% } else{ %>
        <td class="text-center">Không</td>
        <td class="text-right"><%- item.mien_thuong %></td>
        <% } %>
        <td>
            <%- item.dkbs %>
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.phi) %></td>
    </tr>
    <% })}else{ %>
    <tr>
        <td class="text-center" colspan="7">
            <p class="m-0">Chưa có dữ liệu hiển thị</p>
        </td>
    </tr>
    <% } %>
</script>

@*Danh sách hạng mục giảm giá"*@
<script type="text/html" id="tblHangMucGiamGiaTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblHangMucGiamGiaItem">
        <td>
            <a class="d-none" data-field="hang_muc" data-val="<%- item.hang_muc %>"></a>
            <a data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_giam_gia_vtu" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tl_giam_gia_vtu')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_giam_gia_vtu) %>">
            <input type="hidden" data-field="tien_giam_gia_vtu" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tien_giam_gia_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %>">
        </td>
        @*<td class="text-right">
                <input type="text" readonly data-field="tien_giam_gia_vtu" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tien_giam_gia_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %>">
            </td>*@
        <td class="text-right">
            <input type="text" data-field="tl_giam_gia_nhan_cong" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tl_giam_gia_nhan_cong')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_giam_gia_nhan_cong) %>">
            <input type="hidden" data-field="tien_giam_gia_nhan_cong" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tien_giam_gia_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %>">
        </td>
        @*<td class="text-right">
                <input type="text" readonly data-field="tien_giam_gia_nhan_cong" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tien_giam_gia_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %>">
            </td>*@
        <td class="text-right">
            <input type="text" data-field="tl_giam_gia_khac" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tl_giam_gia_khac')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_giam_gia_khac) %>">
            <input type="hidden" data-field="tien_giam_gia_khac" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tien_giam_gia_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_khac) %>">
        </td>
        @*<td class="text-right">
                <input type="text" readonly data-field="tien_giam_gia_khac" onchange="tinhGiamGia('<%- item.hang_muc %>', 'tien_giam_gia_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_giam_gia_khac) %>">
            </td>*@
        @*<td class="text-right">
                <a data-field="tong_giam_gia" data-val="<%- item.tong_giam_gia %>"><%- ESUtil.formatMoney(item.tong_giam_gia) %></a>
            </td>*@
    </tr>
    <% })}%>
    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        @*<td></td>
            <td></td>
            <td></td>
            <td></td>*@
    </tr>
    <% }} %>
</script>
@*Danh sách hạng mục giảm trừ"*@
<script type="text/html" id="tblGiamTruTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblGiamTruItem">
        <td>
            <a class="d-none combobox" data-field="hang_muc" data-val="<%- item.hang_muc %>"></a>
            <a class="combobox" data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="pt_giam_tru" onchange="tinhGiamTru('<%- item.hang_muc %>', 'pt_giam_tru')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.pt_giam_tru) %>">
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 5){
    for(var i = 0; i < 5 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Danh sách hạng mục khấu trừ"*@
<script type="text/html" id="tblKhauTruTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblKhauTruItem">
        <td>
            <a class="d-none combobox" data-field="hang_muc" data-val="<%- item.hang_muc %>"></a>
            <a class="combobox" data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_ktru_tien_bh" onchange="tinhKhauTru('<%- item.hang_muc %>', 'tl_ktru_tien_bh')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_ktru_tien_bh) %>">
        </td>
        <td class="text-center">
            <% if(item.dkbs != null){ %>
            <a href="#" data-field="dkbs" data-val="<%- item.dkbs %>" onclick="showDKBS(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Điều khoản bổ sung"></i>
            </a>
            <% }else{ %>
            <a data-field="dkbs" data-val="" onclick="showDKBS(this)" class="cursor-pointer combobox">
                <i class="far fa-file-alt" title="Điều khoản bổ sung"></i>
            </a>
            <% } %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 5){
    for(var i = 0; i < 5 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Danh sách hạng mục thuế"*@
<script type="text/html" id="tblHangMucThueTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblHangMucThueItem">
        <td>
            <a class="d-none" data-field="hang_muc" data-val="<%- item.hang_muc %>"></a>
            <a data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_vtu" onchange="tinhThue('<%- item.hang_muc %>', 'tl_thue_vtu')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_vtu) %>">
            <input type="hidden" data-field="tien_thue_vtu" onchange="tinhThue('<%- item.hang_muc %>', 'tien_thue_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_vtu) %>">
        </td>
        @*<td class="text-right">
                <input type="text" readonly data-field="tien_thue_vtu" onchange="tinhThue('<%- item.hang_muc %>', 'tien_thue_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_vtu) %>">
            </td>*@
        <td class="text-right">
            <input type="text" data-field="tl_thue_nhan_cong" onchange="tinhThue('<%- item.hang_muc %>', 'tl_thue_nhan_cong')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_nhan_cong) %>">
            <input type="hidden" data-field="tien_thue_nhan_cong" onchange="tinhThue('<%- item.hang_muc %>', 'tien_thue_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_nhan_cong) %>">
        </td>
        @*<td class="text-right">
                <input type="text" readonly data-field="tien_thue_nhan_cong" onchange="tinhThue('<%- item.hang_muc %>', 'tien_thue_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_nhan_cong) %>">
            </td>*@
        <td class="text-right">
            <input type="text" data-field="tl_thue_khac" onchange="tinhThue('<%- item.hang_muc %>', 'tl_thue_khac')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_khac) %>">
            <input type="hidden" data-field="tien_thue_khac" onchange="tinhThue('<%- item.hang_muc %>', 'tien_thue_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_khac) %>">
        </td>
        @*<td class="text-right">
                <input type="text" readonly data-field="tien_thue_khac" onchange="tinhThue('<%- item.hang_muc %>', 'tien_thue_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_khac) %>">
            </td>*@
        @*<td class="text-right">
                <a data-field="tong_thue" data-val="<%- item.tong_thue %>"><%- ESUtil.formatMoney(item.tong_thue) %></a>
            </td>*@
    </tr>
    <% })}%>
    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        @*<td></td>
            <td></td>
            <td></td>
            <td></td>*@
    </tr>
    <% }} %>
</script>

<script type="text/html" id="danhSachCanhBaoTemplate">
    <% if(danh_sach.so_luong_cb > 0){%>
    <div class="esmodal-title d-flex justify-content-between">
        <ul class="navbar-nav float-right">
            <li class="nav-item dropdown">
                <a style="padding: 0 !important" class="nav-link dropdown" href="javascript:void(0)" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span style="color: #ff8c0096; font-size: 18px;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </span>
                    <i style="font-size: 15px;" class="text-danger">Có <span class="text-danger"><%- danh_sach.so_luong_cb%></span> thông báo cảnh báo</i>
                </a>
                <div class="dropdown-menu mailbox dropdown-menu-left scale-up pt-0"
                     style="margin-top: 21px; border: 1px solid #ccc; width: 255px !important; margin-left: -33px !important; transform: translate3d(0px, 43px, 0px) !important; height: 100vh; ">
                    <ul class="list-style-none">
                        <li>
                            <div class="border-bottom rounded-top p-2 text-center" style="background-color: #edeff0 ">
                                <h5 class="font-weight-medium mb-0">Danh sách thông báo cảnh báo</h5>
                            </div>
                        </li>
                        <li>
                            <div class="message-center message-body position-relative" id="canhBao">

                            </div>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
        <button type="button" class="close" data-dismiss="esmodal" aria-hidden="true">×</button>
    </div>
    <% }else{ %>
    <button type="button" class="close" data-dismiss="esmodal" aria-hidden="true">×</button>
    <% } %>
</script>

@*Xem danh sách thông báo cảnh báo*@
<script type="text/html" id="canhBao_template">
    <%if(danh_sach.length > 0){ %>
    <%_.forEach(danh_sach, function(item,index) { %>
    <a href="javascript:void(0)" id="notify-item-<%- item.so_id %>" class="message-item d-flex align-items-center border-bottom p-1">
        <span class="position-relative d-inline-block">
            <span class="dot n-noi-dung-tin-nhan-dot"></span>
        </span>
        <div class="d-inline-block v-middle pl-2">
            <p class="font-12 mb-0 mt-1 text-dark font-weight-bold"><%- item.noi_dung %></p>
            <span class="font-10 text-nowrap d-block text-muted"><%- item.ngay %></span>
        </div>
    </a>
    <% })} else { %>
    <a href="#" class="message-item d-flex align-items-center border-bottom p-1">
        <div class="d-inline-block v-middle pl-2" style="margin:0 auto">
            <p class="font-12 mb-0 mt-1 text-dark">Không có cảnh báo</p>
        </div>
    </a>
    <% } %>
</script>

<script type="text/html" id="titleCarCompensationTemplate">
    <% if(ho_so.so_hs!= null && ho_so.so_hs.trim()!= ""){%>
    <h4 class="esmodal-title">
        Hồ sơ: <a href="#" onclick="copyText(this)"><%- ho_so.so_hs %></a> - <span><%- ho_so.nsd %></span> - <span class="text-color"><%- ho_so.trang_thai %></span> - <a href="#" onclick="xemToanBoThongTinHoSoBoiThuong('<%- ho_so.ma_doi_tac %>','<%- ho_so.ma_chi_nhanh_ql %>','<%- ho_so.ma_chi_nhanh %>','<%- ho_so.so_id %>','<%- ho_so.so_id_hd%>','<%- ho_so.so_id_dt%>')">Xem chi tiết hồ sơ</a>
    </h4>
    <% } else {%>
    <h4 class="esmodal-title">
        Hồ sơ: <span class="text-color">Chưa lấy số hồ sơ</span> - <span><%- ho_so.nsd %></span> - <span class="text-color"><%- ho_so.trang_thai %></span> - <a href="#" onclick="xemToanBoThongTinHoSoBoiThuong('<%- ho_so.ma_doi_tac %>','<%- ho_so.ma_chi_nhanh_ql %>','<%- ho_so.ma_chi_nhanh %>','<%- ho_so.so_id %>','<%- ho_so.so_id_hd%>','<%- ho_so.so_id_dt%>')">Xem chi tiết hồ sơ</a>
    </h4>
    <%}%>
</script>

@*Danh sách báo giá gara dọc"*@
<script type="text/html" id="bodyDsBaoGiaGaraDocTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item, index) { %>
    <tr class="dsBaoGiaGaraDocItem" data-rowindex="<%- index %>">
        <td>
            <a data-field="bt" data-val="<%- index + 1 %>" class="d-none"></a>
            <a data-field="ten_hang_muc_he_thong" data-val="<%- item.ten_hang_muc_he_thong %>" class="d-none"></a>
            <%if(item.ma_hang_muc_he_thong == null || item.ma_hang_muc_he_thong==""){%>
            <a href="#" style="color:red" data-field="ma_hang_muc_he_thong" title="<%- item.ten_hang_muc_he_thong %>" data-val="<%- item.ma_hang_muc_he_thong %>"><%- ESUtil.rutGonText(32, item.ten_hang_muc_he_thong) %></a>
            <%}else{%>
            <a href="#" data-field="ma_hang_muc_he_thong" title="<%- item.ten_hang_muc_he_thong %>" data-val="<%- item.ma_hang_muc_he_thong %>"><%- ESUtil.rutGonText(32, item.ten_hang_muc_he_thong) %></a>
            <%}%>
        </td>
        <td class="itemVAT_TU" ondrop="onDropHM(event, 'VAT_TU')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'VAT_TU')" data-field="ten_hang_muc_vtu" title="<%- item.ten_hang_muc_vtu %>" data-val="<%- item.ten_hang_muc_vtu %>"><%- ESUtil.rutGonText(32, item.ten_hang_muc_vtu) %></a>
        </td>
        <td class="itemVAT_TU text-center" ondrop="onDropHM(event, 'VAT_TU')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'VAT_TU')" class="number" data-field="so_luong_vtu" data-val="<%- item.so_luong_vtu %>"><%- item.so_luong_vtu %></a>
        </td>
        <td class="itemVAT_TU text-right" ondrop="onDropHM(event, 'VAT_TU')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'VAT_TU')" class="number" data-field="tien_vtu" data-val="<%- item.tien_vtu %>"><%- ESUtil.formatMoney(item.tien_vtu) %></a>
        </td>
        <td class="itemVAT_TU text-center" ondrop="onDropHM(event, 'VAT_TU')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'VAT_TU')" data-field="tl_khop" data-val="<%- item.tl_khop %>"><%- Math.round(item.tl_khop*100) %> %</a>
        </td>

        <td class="itemNHAN_CONG" ondrop="onDropHM(event, 'NHAN_CONG')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'NHAN_CONG')" data-field="ten_hang_muc_nhan_cong" title="<%- item.ten_hang_muc_nhan_cong %>" data-val="<%- item.ten_hang_muc_nhan_cong %>"><%- ESUtil.rutGonText(32, item.ten_hang_muc_nhan_cong) %></a>
        </td>
        <td class="itemNHAN_CONG text-right" ondrop="onDropHM(event, 'NHAN_CONG')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'NHAN_CONG')" class="number" data-field="tien_nhan_cong" data-val="<%- item.tien_nhan_cong %>"><%- ESUtil.formatMoney(item.tien_nhan_cong) %></a>
        </td>
        <td class="itemNHAN_CONG text-center" ondrop="onDropHM(event, 'NHAN_CONG')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'NHAN_CONG')" data-field="tl_khop_nhan_cong" data-val="<%- item.tl_khop_nhan_cong %>"><%- Math.round(item.tl_khop_nhan_cong*100) %> %</a>
        </td>
        <td class="text-center">
            <a href="javascript:void(0)" onclick="onTachDong(this, 'NHAN_CONG')">Tách</a>
        </td>
        <td class="itemSON" ondrop="onDropHM(event, 'SON')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'SON')" data-field="ten_hang_muc_son" title="<%- item.ten_hang_muc_son %>" data-val="<%- item.ten_hang_muc_son %>"><%- ESUtil.rutGonText(32, item.ten_hang_muc_son) %></a>
        </td>
        <td class="itemSON text-right" ondrop="onDropHM(event, 'SON')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'SON')" class="number" data-field="tien_son" data-val="<%- item.tien_son %>"><%- ESUtil.formatMoney(item.tien_son) %></a>
        </td>
        <td class="itemSON text-center" ondrop="onDropHM(event, 'SON')" ondragover="onDragOverHM(event)">
            <a draggable="true" ondragstart="onDragHM(event, 'SON')" data-field="tl_khop_son" data-val="<%- item.tl_khop_son %>"><%- Math.round(item.tl_khop_son*100) %> %</a>
        </td>
        <td class="text-center">
            <a href="javascript:void(0)" onclick="onTachDong(this, 'SON')">Tách</a>
        </td>
    </tr>
    <% })}%>
    <% if(data.length < 8){
    for(var i = 0; i < 8 - data.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Xem bảng tính toán chi tiết*@
<script type="text/html" id="modalBangTinhToanChiTietTemplate">
    <div class="table-responsive" style="height: 400px;">
        <table class="table table-bordered fixed-header">
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh VCXXE">
                <%if(vcx != null){%>
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ VẬT CHẤT XE</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN DUYỆT GIÁ (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienDuyetGia text-danger"><%- ESUtil.formatMoney(vcx.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">II</td>
                    <td colspan="3" class="font-weight-bold">KHẤU HAO, GIẢM TRỪ BỒI THƯỜNG, GIẢM GIÁ</td>
                </tr>
                <% if(vcx.lh_tt_giam_gia =="T"){%>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">1</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(vcx.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">2</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(vcx.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">3</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(vcx.tien_giam_gia_khac) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">4</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(vcx.tien_khau_hao) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">5</td>
                    <td>Giảm trừ lỗi</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTruLoi"><%- ESUtil.formatMoney(vcx.tien_giam_tru_loi) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">6</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(vcx.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">7</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(vcx.tien_bao_hiem) %></td>
                </tr>
                
                <%} %>
                <% if(vcx.lh_tt_giam_gia =="S"){%>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">1</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(vcx.tien_khau_hao) %></td>
                </tr>
                 <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">2</td>
                    <td>Giảm trừ lỗi</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTruLoi"><%- ESUtil.formatMoney(vcx.tien_giam_tru_loi) %></td>
                </tr>
                 <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">3</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(vcx.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">4</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(vcx.tien_bao_hiem) %></td>
                </tr>
               
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">5</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(vcx.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">6</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(vcx.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">7</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(vcx.tien_giam_gia_khac) %></td>
                </tr>
                <%} %>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, GIẢM GIÁ</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauHaoGiamTruGiamGia text-danger"><%- ESUtil.formatMoney(vcx.tong_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">III</td>
                    <td colspan="3" class="font-weight-bold">KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Khấu trừ theo % số tiền bồi thường</td>
                    <td class="text-right tinhToanNVPATienKhauTruPTBoiThuong"><%- ESUtil.formatMoney(vcx.tien_ktru_tien_bh) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right tinhToanNVPAMienThuong"><%- ESUtil.formatMoney(vcx.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauTru text-danger"><%- ESUtil.formatMoney(vcx.tong_khau_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">IV</td>
                    <td colspan="3" class="font-weight-bold">ĐỐI TRỪ BẢO HIỂM</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Đối trừ thanh lý vật tư</td>
                    <td class="text-right"><%- ESUtil.formatMoney(vcx.tien_dtru_thanh_ly) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Đối trừ thu đòi người thứ 3</td>
                    <td class="text-right"><%- ESUtil.formatMoney(vcx.tien_dtru_ntba) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG ĐỐI TRỪ BẢO HIỂM</td>
                    <td class="text-right font-weight-bold text-danger"><%- ESUtil.formatMoney(vcx.tong_doi_tru) %></td>
                </tr>
            </tbody>
            <%}%>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh TNDSTAI_SAN">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ TNDS VỀ TÀI SẢN</td>
                </tr>
            </tbody>
                @*Tài sản là khác*@
            <% _.forEach(tnds_tsan_khac, function(item,index) { %>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh TNDSTAI_SAN">
                <tr>
                    <td colspan="4"><span class="text-danger font-weight-bold"><%- item.ten_doi_tuong %></span></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN DUYỆT GIÁ (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienDuyetGia text-danger"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">II</td>
                    <td colspan="3" class="font-weight-bold">KHẤU HAO, GIẢM TRỪ BỒI THƯỜNG, GIẢM GIÁ</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_gia_khac) %></td>
                </tr>
                <tr>
                    <td class="text-center">4</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_khau_hao) %></td>
                </tr>
                <tr>
                    <td class="text-center">5</td>
                    <td>Giảm trừ lỗi</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_tru_loi) %></td>
                </tr>
                <tr>
                    <td class="text-center">6</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center">7</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, GIẢM GIÁ</td>
                    <td class="text-right font-weight-bold text-danger"><%- ESUtil.formatMoney(item.tong_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">III</td>
                    <td colspan="3" class="font-weight-bold">KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Khấu trừ theo % số tiền bồi thường</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_ktru_tien_bh) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right"><%- ESUtil.formatMoney(item.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                    <td class="text-right font-weight-bold text-danger"><%- ESUtil.formatMoney(item.tong_khau_tru) %></td>
                </tr>
            </tbody>
            <%})%>
                @*Tài sản là xe*@
            <% _.forEach(tnds_tsan_xe, function(item,index) { %>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh TNDSTAI_SAN">
                <tr>
                    <td colspan="4"><span class="text-danger font-weight-bold"><%- item.ten_doi_tuong %></span></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN DUYỆT GIÁ (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienDuyetGia text-danger"><%- ESUtil.formatMoney(item.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">II</td>
                    <td colspan="3" class="font-weight-bold">KHẤU HAO, GIẢM TRỪ BỒI THƯỜNG, GIẢM GIÁ</td>
                </tr>
                <% if(item.lh_tt_giam_gia =="T"){%>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">1</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">2</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">3</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(item.tien_giam_gia_khac) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">4</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(item.tien_khau_hao) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">5</td>
                    <td>Giảm trừ lỗi</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTruLoi"><%- ESUtil.formatMoney(item.tien_giam_tru_loi) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">6</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(item.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_T">
                    <td class="text-center">7</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(item.tien_bao_hiem) %></td>
                </tr>
                
                <%} %>
                <% if(item.lh_tt_giam_gia =="S"){%>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">1</td>
                    <td>Khấu hao phụ tùng thay mới</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienKhauHao"><%- ESUtil.formatMoney(item.tien_khau_hao) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">2</td>
                    <td>Giảm trừ lôi</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTruLoi"><%- ESUtil.formatMoney(item.tien_giam_tru_loi) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">3</td>
                    <td>Giảm trừ bồi thường</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPAGiamTru"><%- ESUtil.formatMoney(item.tien_giam_tru) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">4</td>
                    <td>Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienBH"><%- ESUtil.formatMoney(item.tien_bao_hiem) %></td>
                </tr>
                
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">5</td>
                    <td>Giảm giá phụ tùng thay thế</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaVtu"><%- ESUtil.formatMoney(item.tien_giam_gia_vtu) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">6</td>
                    <td>Giảm giá chi phí nhân công sửa chữa</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaNhanCong"><%- ESUtil.formatMoney(item.tien_giam_gia_nhan_cong) %></td>
                </tr>
                <tr class="tblPhuongAnCTGiamGia_S">
                    <td class="text-center">7</td>
                    <td>Giảm giá chi phí sơn</td>
                    <td class="text-center">%</td>
                    <td class="text-right tinhToanNVPATienGiamGiaKhac"><%- ESUtil.formatMoney(item.tien_giam_gia_khac) %></td>
                </tr>
                <%} %>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, GIẢM GIÁ</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauHaoGiamTruGiamGia text-danger"><%- ESUtil.formatMoney(item.tong_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">III</td>
                    <td colspan="3" class="font-weight-bold">KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Khấu trừ theo % số tiền bồi thường</td>
                    <td class="text-right tinhToanNVPATienKhauTruPTBoiThuong"><%- ESUtil.formatMoney(item.tien_ktru_tien_bh) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right tinhToanNVPAMienThuong"><%- ESUtil.formatMoney(item.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU TRỪ THEO HĐ, QUY TẮC BH</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongKhauTru text-danger"><%- ESUtil.formatMoney(item.tong_khau_tru) %></td>
                </tr>
            </tbody>
            <%})%>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh HHHANG_HOA">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ HÀNG HÓA TRÊN XE</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN TỔN THẤT (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPAHHTienTT text-danger"><%- ESUtil.formatMoney(hhoa.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Số tiền khấu hao</td>
                    <td class="text-right tinhToanNVPAHHTienKhauHao"><%- ESUtil.formatMoney(hhoa.tien_khau_hao) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-right tinhToanNVPAHHTienBaoHiem"><%- ESUtil.formatMoney(hhoa.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Giảm trừ bồi thường</td>
                    <td class="text-right tinhToanNVPAHHTienGiamTru"><%- ESUtil.formatMoney(hhoa.tien_giam_tru) %></td>
                </tr>
                 <tr>
                    <td class="text-center">4</td>
                    <td colspan="2">Giảm trừ lỗi</td>
                    <td class="text-right tinhToanNVPAHHTienGiamTruLoi"><%- ESUtil.formatMoney(hhoa.tien_giam_tru_loi) %></td>
                </tr>
                <tr>
                    <td class="text-center">5</td>
                    <td colspan="2">Khấu trừ theo số tiền/vụ (miễn thường)</td>
                    <td class="text-right tinhToanNVPAHHTienMienThuong"><%- ESUtil.formatMoney(hhoa.tien_mien_thuong) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ, KHẤU TRỪ</td>
                    <td class="text-right font-weight-bold tinhToanNVPAHHTienBoiThuong text-danger"><%- ESUtil.formatMoney(hhoa.tong_giam) %></td>
                </tr>
            </tbody>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh NNTXNGUOI">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ NGƯỜI NGỒI TRÊN XE</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN TỔN THẤT (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPANNTXTienTT text-danger"><%- ESUtil.formatMoney(nntx.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Giảm trừ lỗi</td>
                    <td class="text-right tinhToanNVPANNTXTienGiamTruLoi"><%- ESUtil.formatMoney(nntx.tien_giam_tru_loi) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Giảm trừ bồi thường</td>
                    <td class="text-right tinhToanNVPANNTXTienGiamTru"><%- ESUtil.formatMoney(nntx.tien_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-right tinhToanNVPANNTXTienBaoHiem"><%- ESUtil.formatMoney(nntx.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ</td>
                    <td class="text-right font-weight-bold tinhToanNVPANNTXTienBoiThuong text-danger"><%- ESUtil.formatMoney(nntx.tong_giam) %></td>
                </tr>
            </tbody>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh LPHU_XENGUOI">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ NGƯỜI - LÁI PHỤ XE</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN TỔN THẤT (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATNDS_NGTienTT text-danger"><%- ESUtil.formatMoney(lphu_xe.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Giảm trừ lỗi</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienGiamTruLoi"><%- ESUtil.formatMoney(lphu_xe.tien_giam_tru_loi) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Giảm trừ bồi thường</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienGiamTru"><%- ESUtil.formatMoney(lphu_xe.tien_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienBaoHiem"><%- ESUtil.formatMoney(lphu_xe.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ</td>
                    <td class="text-right font-weight-bold tinhToanNVPATNDS_NGTienBoiThuong text-danger"><%- ESUtil.formatMoney(lphu_xe.tong_giam) %></td>
                </tr>
            </tbody>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh TNDSNGUOI_HK">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ NGƯỜI - HÀNH KHÁCH TRÊN XE</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN TỔN THẤT (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATNDS_NGTienTT text-danger"><%- ESUtil.formatMoney(tnds_nguoi_hk.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Giảm trừ lỗi</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienGiamTruLoi"><%- ESUtil.formatMoney(tnds_nguoi_hk.tien_giam_tru_loi) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Giảm trừ bồi thường</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienGiamTru"><%- ESUtil.formatMoney(tnds_nguoi_hk.tien_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienBaoHiem"><%- ESUtil.formatMoney(tnds_nguoi_hk.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ</td>
                    <td class="text-right font-weight-bold tinhToanNVPATNDS_NGTienBoiThuong text-danger"><%- ESUtil.formatMoney(tnds_nguoi_hk.tong_giam) %></td>
                </tr>
            </tbody>
            <tbody class="table-bang-gia pa_nghiep_vu_bang_tinh TNDSNGUOI">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">NGHIỆP VỤ TNDS VỀ NGƯỜI</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">I</td>
                    <td colspan="2" class="font-weight-bold">TỔNG TIỀN TỔN THẤT (CHƯA VAT)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATNDS_NGTienTT text-danger"><%- ESUtil.formatMoney(tnds_nguoi.gia_duyet_dx) %></td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Giảm trừ lỗi</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienGiamTruLoi"><%- ESUtil.formatMoney(tnds_nguoi.tien_giam_tru_loi) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Giảm trừ bồi thường</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienGiamTru"><%- ESUtil.formatMoney(tnds_nguoi.tien_giam_tru) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Giảm trừ theo tỷ lệ giá trị tham gia</td>
                    <td class="text-right tinhToanNVPATNDS_NGTienBaoHiem"><%- ESUtil.formatMoney(tnds_nguoi.tien_bao_hiem) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG KHẤU HAO, GIẢM TRỪ</td>
                    <td class="text-right font-weight-bold tinhToanNVPATNDS_NGTienBoiThuong text-danger"><%- ESUtil.formatMoney(tnds_nguoi.tong_giam) %></td>
                </tr>
            </tbody>
            <tbody class="table-bang-gia">
                <tr>
                    <td colspan="4" class="text-danger text-center font-weight-bold">CHI PHÍ CẨU/KÉO/KHÁC</td>
                </tr>
                <tr>
                    <td class="text-center font-weight-bold">IV</td>
                    <td colspan="3" class="font-weight-bold">CHI PHÍ CẨU KÉO (GỒM GIẢM TRỪ NẾU CÓ)</td>
                </tr>
                <tr>
                    <td class="text-center">1</td>
                    <td colspan="2">Chi phí cẩu xe</td>
                    <td class="text-right tinhToanNVPAChiPhiCau"><%- ESUtil.formatMoney(chi_phi.chi_phi_cau) %></td>
                </tr>
                <tr>
                    <td class="text-center">2</td>
                    <td colspan="2">Chi phí kéo xe</td>
                    <td class="text-right tinhToanNVPAChiPhiKeo"><%- ESUtil.formatMoney(chi_phi.chi_phi_keo) %></td>
                </tr>
                <tr>
                    <td class="text-center">3</td>
                    <td colspan="2">Chi phí khác</td>
                    <td class="text-right tinhToanNVPAChiPhiKhac"><%- ESUtil.formatMoney(chi_phi.chi_phi_khac) %></td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">TỔNG CHI PHÍ CẨU KÉO</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongChiPhi text-danger"><%- ESUtil.formatMoney(chi_phi.tong_chi_phi) %></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="table-responsive " style="height: 50px; bottom: 0px;">
        <table class="table table-bordered fixed-header table-bang-gia">
            <tbody>
                <tr class="text-danger">
                    <td colspan="4" class="text-center font-weight-bold">SỐ TIỀN BỒI THƯỜNG (STBT)</td>
                </tr>
                <tr>
                    <td colspan="3" class="font-weight-bold">STBT TRƯỚC THUẾ (I-II-III+IV)</td>
                    <td class="text-right font-weight-bold tinhToanNVPATienBoiThuongChuaVAT"><%- ESUtil.formatMoney(tong_hop.tong_tien_bt) %></td>
                </tr>
                <tr class="d-none">
                    <td colspan="3" class="font-weight-bold">THUẾ VAT</td>
                    <td class="text-right font-weight-bold tinhToanNVPATongThue"><%- ESUtil.formatMoney(tong_hop.tong_thue) %></td>
                </tr>
                <tr class="d-none">
                    <td colspan="3" class="font-weight-bold">CTYBH CHI TRẢ (BAO GỒM THUẾ)</td>
                    <td class="text-right font-weight-bold text-danger tinhToanNVPATienBoiThuongBaoGomVAT"><%- ESUtil.formatMoney(tong_hop.tong_tien_bt_gom_vat) %></td>
                </tr>
            </tbody>
        </table>
    </div>
</script>

<script type="text/html" id="modalVideoDanhSachDGRRHDTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item, index) {
    if(index == 0){%>
    <a class="nav-link rounded videoLinkDGRRHD active" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoDGRRHD('<%- item.bt %>')"><%- item.ten %></a>
    <%} else {%>
    <a class="nav-link rounded videoLinkDGRRHD" href="javascript:void(0);" data-bt="<%- item.bt %>" onclick="xemVideoDGRRHD('<%- item.bt %>')"><%- item.ten %></a>
    <%}})}%>
</script>

<script type="text/html" id="danh_gia_btv_template">
    <% if(kieu.length > 0){ %>
    <% _.forEach(lhnv, function(item_lhnv, index_lhnv){ %>
    <div class="col-12">
        <hr class="mt-2" />
        <h5 class="mb-2 mt-2" style="font-weight:bold">
            <%- item_lhnv.ten %>
            <% if(item_lhnv.trang_thai == 'C'){ %>
            - <label style="color:red">Chưa thực hiện đánh giá</label>
            <% }else if(item_lhnv.trang_thai == 'D'){ %>
            - <label style="color:green">Đã thực hiện đánh giá</label>
            <% } %>
        </h5>
    </div>

    <% if(item_lhnv.so_luong == 0){ %>
    <div class="col-12">
        <span>Chưa có dữ liệu cấu hình</span>
    </div>
    <% }else{ %>
    <% _.forEach(kieu, function(item,index) { %>
    <% if(item.kieu == 'CHECKBOX'){ %>
    <% if(item.lhnv == item_lhnv.ma){ %>
    <div class="col-6">
        <a href="#"><%- item.ten_loai %></a>
    </div>
    <% _.forEach(data, function(item1,index1){ %>
    <% if(item1.loai == item.loai && item1.lhnv == item_lhnv.ma){ %>
    <div class="col-3">
        <div class="custom-control custom-checkbox">
            <input type="checkbox" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.loai %>" id="dg_<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.ma %>" value="<%- item1.ma %>" class="custom-control-input <%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.loai %> single_checked">
            <label class="custom-control-label" for="dg_<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item1.ma %>" style="cursor:pointer; padding-top:2px"><%- item1.ten %></label>
        </div>
    </div>
    <% } %>
    <% }) %>
    <% } %>
    <% } else if(item.kieu == 'TEXT'){ %>
    <div class="col-6">
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <div class="input-group">
                <input type="text" maxlength="1000" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" autocomplete="off" class="form-control" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% }else if(item.kieu == 'NUMBER'){ %>
    <div class="col-6">
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <div class="input-group">
                <input type="text" maxlength="15" autocomplete="off" class="form-control number placeholder-left" name="<%- item.loai %>" col-name="<%- item.ten_loai %>" placeholder="<%- item.ten_loai %>">
            </div>
        </div>
    </div>
    <% } else if(item.kieu == 'TEXTAREA'){ %>
    <% if(item.kieu_loai == 'col12'){ %>
    <div class="col-12">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <a href="#" class="_required"><%- item.ten_loai %></a>
            <textarea class="form-control text-area" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <textarea class="form-control text-area" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% }else if(item.kieu_loai == 'col6'){ %>
    <div class="col-6">
        <% if(item.bat_buoc_nhap == 1){ %>
        <div class="form-group">
            <a href="#" class="_required"><%- item.ten_loai %></a>
            <textarea class="form-control text-area" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% }else{ %>
        <div class="form-group">
            <a href="#"><%- item.ten_loai %></a>
            <textarea class="form-control text-area" name="<%- item_lhnv.ma.replaceAll('.', '') %>_<%- item.loai %>" col-name="<%- item.ten_loai %>" col-loai="<%- item.loai %>" col-lhnv="<%- item.lhnv %>" autocomplete="off" placeholder="<%- item.ten_loai %>" rows="3"></textarea>
        </div>
        <% } %>
    </div>
    <% } %>
    <% } %>
    <% }) %>
    <% } %>

    <% }) %>
    <% }%>
</script>

<script type="text/html" id="navDanhGiaDoiTuongTNDSTaiSanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.so_id_doi_tuong %>"><a href="#" onclick="xemChiTietDTTonThatTaiSan('<%- item.nhom %>', '<%- item.loai%>', '<%- item.so_id_doi_tuong %>')"><%- item.ten_doi_tuong %></a></li>
    <%})}%>
</script>

<script type="text/html" id="navDoiTuongTNDSTaiSanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.so_id_doi_tuong %>"><a href="#" onclick="xemChiTietDTTonThatTaiSanPA('<%- item.nhom %>', '<%- item.loai%>', '<%- item.so_id_doi_tuong %>')"><%- item.ten_doi_tuong %></a></li>
    <%})}%>
</script>

<script type="text/html" id="navTinhToanTNDSTaiSanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-loai="<%- item.loai%>" data-doi-tuong="<%- item.so_id_doi_tuong %>"><a href="#" onclick="xemChiTietTinhToanTNDSTaiSan('<%- item.nhom %>', '<%- item.loai%>', '<%- item.so_id_doi_tuong %>')"><%- item.ten_doi_tuong %></a></li>
    <%})}%>
</script>

<script type="text/html" id="navDoiTuongTNDSTaiSanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <li class="breadcrumb-item" data-nhom="<%- item.nhom %>" data-doi-tuong="<%- item.so_id_doi_tuong %>"><a href="#" onclick="xemChiTietDTTonThatTaiSanPA('<%- item.nhom %>', '<%- item.loai%>', '<%- item.so_id_doi_tuong %>')"><%- item.ten_doi_tuong %></a></li>
    <%})}%>
</script>

<script type="text/html" id="dsDoiTuongTTTemplate">
    <% if(data.length > 0){
    _.forEach(data, function(item,index) { %>
    <tr class="item_so_id_doi_tuong" data-so-id-doi-tuong="<%- item.so_id_doi_tuong %>" style="cursor: pointer; text-align:center;">
        <td>
            <% if(item.kieu_dt != undefined && item.kieu_dt != null && item.kieu_dt == "BH"){ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="doi_tuong_tt_<%- item.so_id_doi_tuong %>" disabled="disabled" value="<%- item.so_id_doi_tuong %>" data-val="<%- item.ten_doi_tuong %>" class="custom-control-input input_chon_doi_tuong_tt">
                <label class="custom-control-label" for="doi_tuong_tt_<%- item.so_id_doi_tuong %>">&nbsp;</label>
            </div>
            <% }else{ %>
            <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                <input type="checkbox" id="doi_tuong_tt_<%- item.so_id_doi_tuong %>" value="<%- item.so_id_doi_tuong %>" data-val="<%- item.ten_doi_tuong %>" class="custom-control-input input_chon_doi_tuong_tt">
                <label class="custom-control-label" for="doi_tuong_tt_<%- item.so_id_doi_tuong %>">&nbsp;</label>
            </div>
            <% } %> 
        </td>
        <td onclick="chonDoiTuongTT('<%- item.so_id_doi_tuong %>',)" id="ds_doi_tuong_tt_<%- item.so_id_doi_tuong %>" class="ds_doi_tuong_tt"><%- item.ten_doi_tuong %></td>
        <td onclick="chonDoiTuongTT('<%- item.so_id_doi_tuong %>',)" class="text-center"><%- item.ten_nhom %></td>
        <td onclick="chonDoiTuongTT('<%- item.so_id_doi_tuong %>',)" class="text-center"><%- item.loai %></td>
        <td class="text-center">
            <a href="#" onclick="suaDoiTuongTT('<%- item.so_id_doi_tuong %>')"><i class="fa fa-edit"></i></a>
        </td>
    </tr>
    <%})}%>
    <% if(data.length < 5){
    for(var i = 0; i < 5 - data.length;i++ ){ %>
    <tr style="cursor: pointer">
        <td style="height:34.6px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <%}}%>
</script>

<script type="text/html" id="tblHangMucThueTaiSanTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblHangMucThueItem">
        <td>
            <a class="d-none" data-field="bt" data-val="<%- item.bt %>"></a>
            <a data-field="hang_muc_ten" data-val="<%- item.hang_muc_ten %>"><%- item.hang_muc_ten %></a>
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_vtu" onchange="tinhThueTaiSan('<%- item.bt %>', 'tl_thue_vtu')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_vtu) %>">
            <input type="hidden" data-field="tien_thue_vtu" onchange="tinhThueTaiSan('<%- item.bt %>', 'tien_thue_vtu')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_vtu) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_nhan_cong" onchange="tinhThueTaiSan('<%- item.bt %>', 'tl_thue_nhan_cong')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_nhan_cong) %>">
            <input type="hidden" data-field="tien_thue_nhan_cong" onchange="tinhThueTaiSan('<%- item.bt %>', 'tien_thue_nhan_cong')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_nhan_cong) %>">
        </td>
        <td class="text-right">
            <input type="text" data-field="tl_thue_khac" onchange="tinhThueTaiSan('<%- item.bt %>', 'tl_thue_khac')" maxlength="3" class="decimal floating-input form-control" value="<%- ESUtil.formatMoney(item.tl_thue_khac) %>">
            <input type="hidden" data-field="tien_thue_khac" onchange="tinhThueTaiSan('<%- item.bt %>', 'tien_thue_khac')" maxlength="15" class="number floating-input form-control" value="<%- ESUtil.formatMoney(item.tien_thue_khac) %>">
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 8){
    for(var i = 0; i < 8 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:35.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>


<script type="text/html" id="tblCapNhatUocTheoDiem_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr class="capNhatUoc">
            <td class="text-center">
                <input type="hidden" data-field="nv" name="nv" value="<%- item.nv %>" />
                <input type="hidden" data-field="lh_nv" name="lh_nv" value="<%- item.lh_nv %>" />
                <input type="hidden" data-field="diem" name="diem" value="<%- item.diem %>" />
                <input type="hidden" data-field="tich_hop" name="tich_hop" value="<%- item.tich_hop %>" />
                <input type="hidden" data-field="log_rq" name="log_rq" value="<%- item.log_rq %>" />
                <input type="hidden" data-field="log_res" name="log_res" value="<%- item.log_res %>" />
                <input type="hidden" data-field="bt" name="bt" value="<%- item.bt %>" />
                <%- index + 1 %>
            </td>
            <td class="text-center">
                <input type="text" data-field="ngay_dp" class="floating-input datepicker text-center" readonly value="<%- item.ngay_dp %>" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten_diem" data-field="ten_diem" value="<%- item.ten_diem %>" />
                <input type="text" class="floating-input combobox" data-field="ten_diem" data-val="<%- item.diem %>" onclick="chonDiemDuPhong(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten_diem %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-center">
                <input type="hidden" class="floating-input ten" data-field="ten" value="<%- item.ten %>" />
                <input type="text" class="floating-input combobox" data-field="ten" data-val="<%- item.lh_nv %>" onclick="chonNV(this)" readonly="readonly" required="" placeholder="Click chọn" value="<%- item.ten %>" style="text-align:center; cursor:pointer" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien" name="tien" class="number floating-input tien_<%- item.bt %>" autocomplete="off" disabled value="<%- ESUtil.formatMoney(item.tien) %>" />
            </td>
            <td class="text-right">
                <input type="text" data-field="tien_chenh_lech" name="tien_chenh_lech" class="number floating-input tien_chenh_lech" disabled value="<%- ESUtil.formatMoney(item.tien_chenh_lech) %>" />
            </td>
            <td class="text-center">
                <% if(item.tich_hop == 1){%>
                     <i class="fas fa-check-circle text-success" title="Đã tích hợp"></i>
                <%}else{%>
                    <i class="fas fa-times" style="color:red" title="Chưa tích hợp"></i>
                <%}%>
            </td>
            <td class="text-center">
                <% if(item.log_rq != null && item.log_res !=""){ %>
                    <a href="#" class="cursor-pointer combobox" onclick="showLogRq(this)" data-rq="<%- JSON.stringify(item.log_rq) %>" data-res="<%- JSON.stringify(item.log_res) %>" >
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                    <% }else{ %>
                    <a class="cursor-pointer combobox" onclick="showLogRq(this)" data-val="" data-rq="" data-res="">
                        <i class="far fa-file-alt" title="Log request"></i>
                    </a>
                <% } %>
            </td>
            <td class="text-center">
                <% if(item.tich_hop != 1){%>
                    <a href="#" class="cursor-pointer" onclick="tichHopLaiUoc(<%- item.bt %>)">
                        <i class="fas fa-arrow-right" style="color: var(--escs-main-theme-color)" title="Tích hợp lại ước"></i>
                    </a>
                <%}else{%>

                <%}%>
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblBGCanhTranh_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(bao_gia,index) { %>
    <tr class="bao_gia_canh_tranh">
        <td class="text-center"><%- index + 1%></td>
        <td class="text-left">
            <input type="hidden" name="hang_muc" data-field="hang_muc" value="<%- bao_gia.hang_muc %>" />
            <input type="hidden" name="muc_do" data-field="muc_do" value="<%- bao_gia.muc_do %>" />
            <input type="hidden" name="thay_the_sc" data-field="thay_the_sc" value="<%- bao_gia.thay_the_sc %>" />
            <input type="hidden" name="bt" data-field="bt" value="<%- bao_gia.bt %>" />
            <input type="hidden" name="bt_gara" data-field="bt_gara" value="<%- bao_gia.bt_gara %>" />
            <input type="hidden" name="so_id_doi_tuong" data-field="so_id_doi_tuong" value="<%- bao_gia.so_id_doi_tuong %>" />
            <input type="hidden" name="gara" data-field="gara" value="<%- bao_gia.gara %>" />
            <input type="hidden" name="tl_giam_gia_vtu" data-field="tl_giam_gia_vtu" value="<%- bao_gia.tl_giam_gia_vtu %>" />
            <input type="hidden" name="tl_giam_gia_nhan_cong" data-field="tl_giam_gia_nhan_cong" value="<%- bao_gia.tl_giam_gia_nhan_cong %>" />
            <input type="hidden" name="tl_giam_gia_khac" data-field="tl_giam_gia_khac" value="<%- bao_gia.tl_giam_gia_khac %>" />
            <input type="hidden" name="ghi_chu" data-field="ghi_chu" value="<%- bao_gia.ghi_chu %>" />

            <%- bao_gia.ten_hang_muc %>
        </td>
        <td class="text-center">
            <%- bao_gia.muc_do_ten %>
        </td>
        <td class="text-center">
            <%- bao_gia.thay_the_sc_ten %>
        </td>
        <td class="text-center">
            <input type="text" data-field="so_luong" name="so_luong" class="decimal floating-input form-control" readonly value="<%- bao_gia.so_luong %>" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_vtu" name="tien_vtu" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(bao_gia.tien_vtu) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_nhan_cong" name="tien_nhan_cong" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(bao_gia.tien_nhan_cong) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-field="tien_khac" name="tien_khac" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(bao_gia.tien_khac) %>" />
        </td>
    </tr>
    <% })} %>

    <% if(danh_sach.length < 14){
    for(var i = 0; i < 14 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height: 30px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="tblBienDo_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
        <tr>
            <td>
                <input type="hidden" data-field="diem" name="diem" data-val="<%- item.diem %>" value="<%- item.diem %>"/>
                <%- item.ten_diem %>
            </td>
            <td class="text-right">
                <input type="text" data-field="ty_le" name="ty_le" class="number floating-input" autocomplete="off" value="<%- ESUtil.formatMoney(item.ty_le) %>" />
            </td>
        </tr>
    <% })} %>
    <% if(danh_sach.length < 3){
    for(var i = 0; i < 3 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:34px;"></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="modalChonDiemDPDanhSach_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox diemdp" data-id="diemdp_<%- item.diem %>" data-text="<%- item.diem.toLowerCase() %>-<%- item.ten_diem.toLowerCase() %>">
        <input type="checkbox" id="diem_<%- item.diem %>" value="<%- item.diem %>" class="custom-control-input modalChonDiemDPItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="diem_<%- item.diem %>"><%- item.ten_diem %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

<script type="text/html" id="modalChonNV_template">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox lhnv" data-id="lhnv_<%- item.ma %>" data-text="<%- item.ma.toLowerCase() %>-<%- item.ten.toLowerCase() %>">
        <input type="checkbox" id="lhnvv_<%- item.ma %>" value="<%- item.ma %>" class="custom-control-input modalChonNVItem single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="lhnvv_<%- item.ma %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>

    <script type="text/html" id="tblPhuongAnChiTraTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <tr class="tblPhuongAnChiTraItem">
        <td class="text-center">
            <%- index + 1 %>
        </td>
        <td class="text-center">
            <%- item.dvi_dong_bh %>
        </td>
        <td class="text-center">
            <%- item.kieu_dong %>
        </td>
          <td class="text-center">
           <%- item.tl_dong %>
        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item.tien_dong) %>
        </td>
    </tr>
    <% })}%>
    <% if(danh_sach.length < 4){
    for(var i = 0; i < 4 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:38.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
            
<script type="text/html" id="tblSoTienThanhToanTheoLhnv_template">
    <% if (data.length > 0) {
    _.forEach(data, function(item,index) { %>
    <tr>
        <td>
            <input type="hidden" class="number" data-name="tien_duyet" data-field="tien_duyet" data-val="<%- item.tien_duyet %>" value="<%- ESUtil.formatMoney(item.tien_duyet) %>" />
            <input type="hidden" data-name="lh_nv" data-field="lh_nv" data-val="<%- item.lh_nv %>" value="<%- item.lh_nv %>" />
            <%- item.ten_lh_nv %> (<%- item.lh_nv %>)
        </td>
        <td class="text-right"><%- ESUtil.formatMoney(item.tien_duyet) %></td>
        <td class="text-right">
            <input type="text" data-name="tien_thu_huong" autocomplete="off" data-field="tien_thu_huong" data-val="<%- item.tien_thu_huong %>" class="floating-input number" value="<%- ESUtil.formatMoney(item.tien_thu_huong) %>" />
        </td>
        <td class="text-right">
            <input type="text" data-name="thue_thu_huong" autocomplete="off" data-field="thue_thu_huong" data-val="<%- item.thue_thu_huong %>" class="floating-input number" value="<%- ESUtil.formatMoney(item.thue_thu_huong) %>" />
        </td>
    </tr>
    <% })} %>

    <% if(data.length < 3){
    for(var i = 0; i < 3 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35.5px;"></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

<script type="text/html" id="divUploadCTTTTemplate">
    <% _.forEach(danh_sach, function(item,index) { %>
        <div class="col-12" data-obj_url="<%- item.url %>">
            <div class="border rounded bg-light p-2 mb-2 d-flex flex-nowrap" style="cursor:pointer;">
                <span class="mr-auto"><%- index + 1 %>.&nbsp;</span>
                <span class="text-right"><%- item.name %> (<span class="font-italic"><%- item.size %>MB</span>)</span>
            </div>
        </div>
    <% }) %>
</script>