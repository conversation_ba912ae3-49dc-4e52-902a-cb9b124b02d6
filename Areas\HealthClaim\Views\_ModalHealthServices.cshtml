﻿<div id="modalTraCuuDichVu" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-modal="true" role="dialog">
    <div class="modal-dialog" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Tra cứu bảng giá dịch vụ</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-2">
                <form name="frmDichVuSucKhoe" method="post">
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label>Dịch vụ cấp trên</label>
                                <select class="select2 form-control custom-select" name="ma_dich_vu_ct" style="width:100%">
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>T<PERSON><PERSON> kiếm</label>
                                <input type="text" name="tim" class="form-control" autocomplete="off" placeholder="Nhập vào mã/tên dịch vụ">
                            </div>
                        </div>
                        <div class="col-2" style="padding-top: 23px;">
                            <div class="form-group">
                                <button type="button" class="btn btn-primary btn-sm wd-85" id="btnTimKiemDichVuSucKhoe" title="Tìm kiếm dịch vụ">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive" style="max-height: 520px;">
                            <div id="gridViewBangGiaDichVu" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-2">
                <button type="button" class="btn btn-primary btn-sm wd-85 mg-t-22" data-dismiss="modal" id="btnDongYeuCauBoSungHoSo">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade show" id="modalChiPhiChiTiet" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document" style="max-width: 90vw; margin-top:10px;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Chi phí chi tiết</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body px-2 py-0">
                <div class="card mb-0">
                    <div class="card-body" style="padding:0px; height: 580px; overflow: auto">
                        <input type="text" id="chi_phi_ma_ct" class="d-none" autocomplete="off">
                        <input type="text" id="chi_phi_bt_ct" class="d-none" autocomplete="off">
                        <input type="text" id="chi_phi_ten_ct" class="d-none" autocomplete="off">
                        <table id="tbl_lan_bao_lanh_quyen_loi_ct" class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0;">
                            <thead class="font-weight-bold card-title-bg-primary">
                                <tr class="text-center ">
                                    <th style="width:8px; vertical-align:middle">STT</th>
                                    <th style="width:300px; vertical-align:middle">Tên chi phí chi tiết</th>
                                    <th style="width:70px; vertical-align:middle">Đơn vị tính</th>
                                    <th style="width:20px; vertical-align:middle ">Số lượng</th>
                                    <th style="width:80px; vertical-align:middle">Đơn giá</th>
                                    <th style="width:80px; vertical-align:middle">Thành tiền</th>
                                    <th style="width:80px; vertical-align:middle">Số tiền giảm trừ</th>
                                    <th style="width:80px; vertical-align:middle">Số tiền sau GT</th>
                                    <th style="width:80px; vertical-align:middle">Số tiền sau đồng</th>
                                    <th style="width:150px; vertical-align:middle">Nguyên nhân giảm trừ</th>
                                    <th style="width:20px; vertical-align:middle">Ghi chú</th>
                                    <th style="width:8px"></th>
                                </tr>
                            </thead>
                            <tbody id="tbDsChiPhiCTiet">
                            </tbody>
                            <tfoot>
                                <tr style="height:39.2px">
                                    <td colspan="3">
                                        <a href="javascript:void(0)" id="btnThemChiPhiCT" class="mr-3">
                                            <i class="fas fa-plus-square mr-1"></i> Thêm chi phí chi tiết
                                        </a>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_so_luong_yc" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_don_gia_yc" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_tien_cphi_yc" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_tien_giam_tru" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_tien_sau_gt" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_tien_sau_dong_bh" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" colspan="3">
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2 mg-t-22" id="btnLuuChiPhiChiTiet">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-22" id="btnLuuDongChiPhiChiTiet">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade show" id="modalChiPhiChiTiet2" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document" style="max-width: 90vw; margin-top:10px;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Chi phí chi tiết</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body px-2 py-0">
                <div class="card mb-0">
                    <div class="card-body" style="padding:0px; height: 580px; overflow: auto">
                        <input type="text" id="chi_phi_ma_ct" class="d-none" autocomplete="off">
                        <input type="text" id="chi_phi_bt_ct" class="d-none" autocomplete="off">
                        <input type="text" id="chi_phi_ten_ct" class="d-none" autocomplete="off">
                        <table id="tbl_thong_tin_chi_phi_ct2" class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0;">
                            <thead class="font-weight-bold card-title-bg-primary">
                                <tr class="text-center ">
                                    <th style="width:5px; vertical-align:middle">STT</th>
                                    <th style="width:700px; vertical-align:middle">Tên chi phí chi tiết</th>
                                    <th style="width:150px; vertical-align:middle">Loại thuốc</th>
                                    <th style="width:150px; vertical-align:middle">Đơn vị tính</th>
                                    <th style="width:150px; vertical-align:middle">Số lượng</th>
                                    <th style="width:150px; vertical-align:middle">Đơn giá</th>
                                    <th style="width:150px; vertical-align:middle">Thành tiền</th>
                                    <th style="width:50px; vertical-align:middle">Ghi chú</th>
                                    <th style="width:8px"></th>
                                </tr>
                            </thead>
                            <tbody id="tbDsChiPhiCTiet2">
                            </tbody>
                            <tfoot>
                                <tr style="height:39.2px">
                                    <td colspan="4">
                                        <a href="javascript:void(0)" id="btnThemChiPhiCT2" class="mr-3">
                                            <i class="fas fa-plus-square mr-1"></i> Thêm chi phí chi tiết
                                        </a>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_so_luong_yc2" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_don_gia_yc2" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" class="text-right py-0">
                                        <span id="tong_tien_cphi_yc2" class="font-weight-bold">0</span>
                                    </td>
                                    <td style="vertical-align:middle" colspan="2">
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2 mg-t-22" id="btnLuuChiPhiChiTiet2">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-110 mg-t-22" id="btnLuuDongChiPhiChiTiet2">
                    <i class="fas fa-hdd mr-2"></i>Lưu & đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right mg-t-22" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

@*Dịch vụ sức khỏe*@
<script type="text/html" id="bodyBangGiaDichVuTemplate">
    <% _.forEach(data, function(item, index) { %>
    <tr>
        <td class="text-center">
            <a data-field="sott"><%- item.sott %></a>
        </td>
        <td class="text-center">
            <a data-field="ma_dich_vu" data-val="<%- item.ma_dich_vu %>"><%- item.ma_dich_vu %></a>
        </td>
        <td class="text-center">
            <a data-field="ma_dich_vu" data-val="<%- item.ten_dich_vu %>"><%- item.ten_dich_vu %></a>
        </td>
        <td class="text-center">
            <a data-field="ma_dich_vu_ct" data-val="<%- item.ma_dich_vu_ct %>"><%- item.ma_dich_vu_ct %></a>
        </td>
        <td class="text-center">
            <a data-field="ma_dich_vu_byt" data-val="<%- item.ma_dich_vu_byt %>"><%- item.ma_dich_vu_byt %></a>
        </td>
        <td class="text-right">
            <a data-field="gia_dich_vu_byt" data-val="<%- item.gia_dich_vu_byt %>"><%- ESUtil.formatMoney(item.gia_dich_vu_byt) %></a>
        </td>
        <td class="text-right">
            <a data-field="gia_vien_phi" data-val="<%- item.gia_vien_phi %>"><%- ESUtil.formatMoney(item.gia_vien_phi) %></a>
        </td>
        <td class="text-right">
            <a data-field="gia_dich_vu" data-val="<%- item.gia_dich_vu %>"><%- ESUtil.formatMoney(item.gia_dich_vu) %></a>
        </td>
        <td class="">
            <a data-field="ghi_chu" data-val="<%- item.ghi_chu %>"><%- item.ghi_chu %></a>
        </td>
    </tr>
    <%})%>
    <% if(data.length < 13){
    for(var i = 0; i < 13 - data.length;i++ ){
    %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>

@*Thêm quyền lợi*@
<script type="text/html" id="tbDsChiPhiCTietTemplate">
    <% var stt = 0 %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="khoanChiItem" data-bt="<%- stt %>">
        <% stt = stt + 1 %>
        <td class="text-center">
            <%- stt %>
        </td>
        <td>
            <input type="hidden" data-field="ma" name="ma" value="<%- item.ma %>" />
            <input type="hidden" data-field="bt" name="bt" value="<%- item.bt %>" />
            <input type="hidden" data-field="guid_ocr" name="guid_ocr" value="<%- item.guid_ocr %>" />
            <input type="hidden" data-field="bt_bang_ke" name="bt_bang_ke" value="<%- item.bt_bang_ke %>" />
            <a href="#" name="ten" class="floating-input text-primary" type="text" data-field="ten" data-val="<%- item.ten %>"  data-bt ="<%- item.bt%>" value="<%- item.ten %>" onclick="xemChiTietQuyenLoiChiTiet(this)" onmouseenter="hover(this)"/>
                <%- item.ten %>
            </a>
        </td>
        <td>
            <input name="dvi_tinh" class="floating-input" type="text" data-field="dvi_tinh" value="<%- item.dvi_tinh %>" />
        </td>
        <td>
            <input type="text" name="so_luong_yc" autocomplete="off" data-field="so_luong_yc" class="floating-input decimal" onchange="tinhTienChiPhiCT(this)" value="<%- item.so_luong_yc %>" />
        </td>
        <td class="text-right ">
            <input type="text" name="don_gia_yc" autocomplete="off" data-field="don_gia_yc" class="floating-input money" onchange="tinhTienChiPhiCT(this)" value="<%- ESUtil.formatMoney(item.don_gia_yc) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_yc" autocomplete="off" data-field="tien_yc" class="floating-input money" onchange="tinhTienChiPhiCT(this)"  value="<%- ESUtil.formatMoney(item.tien_yc) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_giam_tru" autocomplete="off" data-field="tien_giam_tru" class="floating-input money" onchange="tinhTienChiPhiCT(this)"  value="<%- ESUtil.formatMoney(item.tien_giam_tru) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_sau_gt" readonly autocomplete="off" data-field="tien_sau_gt" class="floating-input money" value="<%- ESUtil.formatMoney(item.tien_sau_gt) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_sau_dong_bh" readonly autocomplete="off" data-field="tien_sau_dong_bh" class="floating-input money" value="<%- ESUtil.formatMoney(item.tien_sau_dong_bh) %>" />
        </td>
        <td>
            <input type="hidden" data-name="nguyen_nhan_nhap" autocomplete="off" data-field="nguyen_nhan_nhap" class="floating-input" value="<%- item.nguyen_nhan_nhap %>" />
            <%if(item.nguyen_nhan_gt !=null &&  item.nguyen_nhan_gt !=''){%>
            <a href="#" data-field="nguyen_nhan_gt" data-val="<%- item.nguyen_nhan_gt %>" data-text="<%- item.nguyen_nhan_nhap %>" onclick="chonNguyenNhanGiamTru(this)">
                <p style="margin:unset"><%- item.nguyen_nhan_nhap %></p>
            </a>
            <%}else{%>
            <a href="#" data-field="nguyen_nhan_gt" data-val="" data-text="" onclick="chonNguyenNhanGiamTru(this)">Chọn nguyên nhân giảm trừ</a>
            <%}%>
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" onclick="xoaChiPhiCT(this)"></i>
        </td>
    </tr>
    <% }) %>

    <% if(danh_sach.length < 10){
    for(var i = 0; i < 10 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:39.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
@*Thêm quyền lợi màn tiếp nhận*@
<script type="text/html" id="tbDsChiPhiCTiet2Template">
    <% var stt = 0 %>
    <% _.forEach(danh_sach, function(item,index) { %>
    <tr class="khoanChiItem" data-bt="<%- stt %>">
        <% stt = stt + 1 %>
        <td class="text-center">
            <%- stt %>
        </td>
        <td>
            <input type="hidden" data-field="ma" name="ma" value="<%- item.ma %>" />
            <input type="hidden" data-field="bt" name="bt" value="<%- item.bt %>" />
            <input type="hidden" data-field="guid_ocr" name="guid_ocr" value="<%- item.guid_ocr %>" />
            <input type="hidden" data-field="bt_bang_ke" name="bt_bang_ke" value="<%- item.bt_bang_ke %>" />
            <a href="#" name="ten" class="floating-input text-primary" type="text" data-field="ten" data-val="<%- item.ten %>"  data-bt ="<%- item.bt%>" value="<%- item.ten %>" onclick="xemChiTietQuyenLoiChiTiet(this)" onmouseenter="hover(this)"/>
                <%- item.ten %>
            </a>
            </td>
        <td class="text-center">
            <input type="hidden" data-field="loai_thuoc_ten" name="loai_thuoc_ten" value="<%- item.loai_thuoc_ten %>" />
            <a href="#" name="loai_thuoc" class="floating-input text-primary" type="text" data-field="loai_thuoc" data-val="<%- item.loai_thuoc %>" value="<%- item.loai_thuoc %>" /><%- item.loai_thuoc_ten %></a>
        </td>
        <td>
            <input name="dvi_tinh" class="floating-input" type="text" data-field="dvi_tinh" value="<%- item.dvi_tinh %>" />
        </td>
        <td>
            <input type="text" name="so_luong_yc" autocomplete="off" data-field="so_luong_yc" class="floating-input decimal" onchange="tinhTienChiPhiCT2(this)" value="<%- item.so_luong_yc %>" />
        </td>
        <td class="text-right ">
            <input type="text" name="don_gia_yc" autocomplete="off" data-field="don_gia_yc" class="floating-input money" onchange="tinhTienChiPhiCT2(this)" value="<%- ESUtil.formatMoney(item.don_gia_yc) %>" />
        </td>
        <td class="text-right">
            <input type="text" name="tien_yc" autocomplete="off" data-field="tien_yc" class="floating-input money" onchange="tinhTienChiPhiCT2(this)"  value="<%- ESUtil.formatMoney(item.tien_yc) %>" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="btnPopupGhiChu cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <i class="fa fa-times text-danger cursor-pointer" onclick="xoaChiPhiCT2(this)"></i>
        </td>
    </tr>
    <% }) %>

    <% if(danh_sach.length < 10){
    for(var i = 0; i < 10 - danh_sach.length;i++ ){
    %>
    <tr>
        <td style="height:39.2px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% }} %>
</script>
<div id="popoverGhiChuChiPhi" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChuChiPhi" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuChiPhiCT" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChu">
                    <textarea class="form-control" id="divGhiChu_NoiDungChiPhi" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuGhiChuChiPhi">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>
<div id="popoverGhiChuQloiTruLui" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 400px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_popGhiChuQloiTruLui" class="close pull-right" data-dismiss="popover-x">&times;</span>Nội dung ghi chú
    </h3>
    <div class="popover-body popover-content">
        <form name="frmGhiChuQloiTruLuiCT" method="post">
            <div class="row">
                <div class="col-12" id="divGhiChu">
                    <textarea class="form-control" id="divGhiChu_NoiDungQloiTruLui" rows="10"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div class="popover-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="luuGhiChuQloiTruLui">
            <i class="far fa-check mr-2"></i>Lưu
        </button>
    </div>
</div>
<div id="modalDSLHNV" class="modal-drag" style="width:450px; z-index:9999999;">
    <div class="modal-drag-header border-bottom px-2">
        <h5><span class="modal-drag-title px-2">Chọn danh sách quyền lợi</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12 mt-2 d-flex scrollable" style="max-height:300px; flex-wrap: wrap" id="modalDSLHNVDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer border-top">
        <button type="button" class="btn btn-primary btn-sm wd-80" id="btnChonLHNV">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>
<script type="text/html" id="modalDSLHNVDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox col-12" data-text="<%- item.lh_nv %>">
        <input type="checkbox" id="lhnv_<%- item.lh_nv %>" value="<%- item.lh_nv %>" class="custom-control-input modalDSLHNVItem">
        <label class="custom-control-label" style="cursor:pointer;" for="lhnv_<%- item.lh_nv %>"><%- item.ten_hien_thi %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>
<div id="popoverXemTT_ChiPhi" class="popover popover-x popover-default" style="display: none; max-width: unset; width: 500px;">
    <div class="arrow"></div>
    <h3 class="popover-header popover-title">
        <span id="close_XemTT_ChiPhi" class="close pull-right" data-dismiss="popover-x">&times;</span>Thông tin chi tiết
    </h3>
    <div class="popover-body popover-content">
        <table id="tbl_lan_bao_lanh_quyen_loi_ct" class="table table-bordered fixed-header mb-1" style="border-collapse: separate; border-spacing: 0;">
            <tbody id="tblTT_ChiPhi "></tbody>
        </table>
    </div>
</div>
<div id="modalXemTT_ChiPhi" class="modal-drag" style="width: 500px; z-index: 9999999; left: 120px;">
    <div class="modal-drag-header">
        <h5 style="margin-left:10px;"><span class="modal-drag-title">Thông tin chi tiết</span> <span data-dismiss="modal-drag" style="margin-right:10px;"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="col-12">
            <table id="tbl_lan_bao_lanh_quyen_loi_ct" class="table table-bordered fixed-header mb-1" style="border-collapse: separate; border-spacing: 0;">
                <thead class="font-weight-bold card-title-bg-primary">
                </thead>
                <tbody id="tblTT_ChiPhi"></tbody>
            </table>
        </div>
    </div>
</div>
<div id="modalBangKeChiTiet" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-hidden="true">
    <div id="" class="modal-dialog" style="max-width: 75%; margin:5px auto;">
        <div class="modal-content" style="max-height: 95vh">
            <div class="modal-header py-1">
                <h5 class="modal-title" id="">
                    Thông tin bảng kê
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body p-0" style="height: 75vh; overflow: auto;">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div id="divDataBangKeCt" class="table-responsive" style="max-height:75vh">
                                <table class="table table-bordered fixed-header" style="border-collapse: separate; border-spacing: 0">
                                    <thead class="font-weight-bold card-title-bg-primary text-nowrap" style="position: sticky; top: 0; z-index: 52 !important;">
                                        <tr class="head">
                                            <th style="text-align:center; width:4%">STT</th>
                                            <th style="text-align:center; width: 40%">Tên chi phí/dịch vụ</th>
                                            <th style="text-align:center; width: 6%">Số lượng</th>
                                            <th style="text-align:center; width: 10%">Đơn giá</th>
                                            <th style="text-align:center; width: 10%">Thành tiền</th>
                                            <th style="text-align:center; width: 6%">ĐVT</th>
                                            <th style="text-align:center; width: 8%">Ghi chú</th>
                                            <th style="text-align:center; width: 10%">Loại chi phí</th>
                                            <th style="text-align:center; width:4%"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="tableDataBangKeCt">
                                    </tbody>
                                    <tfoot>
                                        <tr style="height:39.2px; vertical-align:middle">
                                            <td colspan="2">
                                                <a href="javascript:void(0)" id="btnThemChiPhiBk" class="mr-3">
                                                    <i class="fas fa-plus-square mr-1"></i> Thêm chi phí chi tiết
                                                </a>
                                            </td>
                                            <td style="vertical-align:middle" class="text-right py-0">
                                                <span id="tong_so_luong_bk" class="font-weight-bold">0</span>
                                            </td>
                                            <td style="vertical-align:middle" class="text-right py-0">
                                                <span id="tong_don_gia_bk" class="font-weight-bold">0</span>
                                            </td>
                                            <td style="vertical-align:middle" class="text-right py-0">
                                                <span id="tong_thanh_tien_bk" class="font-weight-bold">0</span>
                                            </td>
                                            <td style="vertical-align:middle" colspan="4">
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button class="btn btn-primary btn-sm wd-85 mg-t-22  float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2 float-right" id="btnLuuBangKe">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>
<div id="modalThemChiPhiCT" class="modal fade show" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog" role="document" style="max-width: 75vw; margin-top:45px;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Thông tin chi tiết</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body px-2 py-0">
                <div class="table-responsive border" style="max-height:450px">
                    <table id="tbl_thong_tin_chi_phi_ct" class="table table-bordered fixed-header mb-1" style="border-collapse: separate; border-spacing: 0; color:#000000 !important">
                        <thead class="font-weight-bold card-title-bg-primary">
                            <tr class="text-center uppercase">
                                @* <th style="width:50px"></th> *@
                                <th colspan="2" style="width:450px; vertical-align:middle">Tên chi phí chi tiết</th>
                                <th style="width:100px; vertical-align:middle">Đơn vị tính</th>
                                <th style="width:100px; vertical-align:middle">Số lượng yêu cầu</th>
                                <th style="width:100px; vertical-align:middle">Đơn giá</th>
                                <th style="width:120px; vertical-align:middle">Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody id="tblThemChiPhiCT"></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-sm wd-85 ml-2 float-right" id="btnChonChiTiet">
                    <i class="fas fa-save mr-2"></i>Chọn
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="tblTT_ChiPhi_template">
    <tr>
        <td style="width:105px; color:#999999">Tên thuốc</td>
        <td><%- obj.ten_thuoc %></td>
    </tr>
    <tr>
        <td style="width:105px; color:#999999">Loại thuốc</td>
        <td><%- obj.loai_thuoc_ten %></td>
    </tr>
    <tr>
        <td style="width:105px; color:#999999">Link tra cứu</td>
        <td><%- obj.link_tra_cuu %></td>
    </tr>
    <tr>
        <td style="width:105px; color:#999999">Số đăng ký</td>
        <td><%- obj.so_dang_ky %></td>
    </tr>
    <tr>
        <td style="width:105px; color:#999999">Công dụng</td>
        <td><%- obj.cong_dung %></td>
    </tr>
    <tr>
        <td style="width:105px; color:#999999">Nguồn gốc</td>
        <td><%- obj.nguon_goc %></td>
    </tr>
</script>

@* Danh sách chi tiết bảng kê *@
<script type="text/html" id="tableDataBangKeCt_template">
    <% var so_tt = 0 %>
    <% if(data_nhom_chi_phi.length > 0){ %>
    <% _.forEach(data_nhom_chi_phi, function(item1, index1) { %>
    <tr style="font-weight: bold; font-size:14px; background-color: #a8bbbf42">
        <td colspan="2">
            <%- item1.ten %>
        </td>
        <td class="text-right">
            <%- item1.so_luong %>
        </td>
        <td class="text-right">

        </td>
        <td class="text-right">
            <%- ESUtil.formatMoney(item1.thanh_tien) %>
        </td>
        <td colspan="4">

        </td>
    </tr>

    <% _.forEach(data, function(item, index) { %>
    <% if(item.ma_ct == item1.ma_ct){ %>
    <% so_tt++ %>
    <tr class="itemBkct">
        <td class="text-center"><%- so_tt %></td>
        <td class="">
            <input type="hidden" name="bt" value="<%- item.bt %>" />
            <input type="hidden" name="guid_ocr" value="<%- item.guid_ocr %>" />
            <input type="text" name="ten" autocomplete="off" class="floating-input" value="<%- item.ten %>" />
        </td>
        <td class="text-center">
            <input type="text" name="so_luong" autocomplete="off" class="floating-input number" value="<%- item.so_luong %>" onchange="tinhTienChiPhiBk(this)" />
        </td>
        <td class="text-center">
            <input type="text" name="don_gia" autocomplete="off" class="floating-input money" value="<%- ESUtil.formatMoney(item.don_gia) %>" onchange="tinhTienChiPhiBk(this)" />
        </td>
        <td class="text-right">
            <input type="text" name="thanh_tien" autocomplete="off" class="floating-input money" value="<%- ESUtil.formatMoney(item.thanh_tien) %>" onchange="tinhTienChiPhiBk(this)" />
        </td>
        <td class="text-center">
            <input type="text" name="dvi_tinh" autocomplete="off" class="floating-input" value="<%- item.dvi_tinh %>" />
        </td>
        <td class="text-center">
            <% if(item.ghi_chu != null && item.ghi_chu!=""){ %>
            <a href="#" class="cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="<%- item.ghi_chu %>">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% }else{ %>
            <a class="cursor-pointer" onclick="showGhiChu(this)" data-field="ghi_chu" data-val="">
                <i class="far fa-file-alt" title="Ghi chú"></i>
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <% if(item.ma_ct != null && item.ma_ct!=""){ %>
            <a href="#" class="cursor-pointer" data-field="ten_ct" data-val="<%- item.ten_ct %>" val="<%- item.ma_ct %>" onclick="chonLoaiChiPhiBangKe(this)">
                <%- item.ten_ct %>
            </a>
            <% }else{ %>
            <a href="#" class="cursor-pointer" data-field="ten_ct" data-val="<%- item.ten_ct %>" val="<%- item.ma_ct %>" onclick="chonLoaiChiPhiBangKe(this)">
                Chọn loại chi phí
            </a>
            <% } %>
        </td>
        <td class="text-center">
            <a href="#" style="color:red" onclick="xoaDongDataOCRBangKeCtQlct(this)"><i class="fa fa-times"></i></a>
        </td>
    </tr>
    <% } %>
    <%})%>
    <% }) %>
    <% }else{ %>
    <% for(var i = 0; i < 12 - data.length;i++ ){ %>
    <tr>
        <td style="height:35px;"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <% } %>
    <% } %>
</script>
<script type="text/html" id="tblThemChiPhiCT_template">
    <% if (ho_so_chi_tiet.nhom_chi_phi_ct.length == 0){
        for(var i = 0; i < 10; i++ ){
        %>
        <tr>
            <td style="height:39.2px;"></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <% }
    }%>

    <% _.forEach(obj.nhom_chi_phi_ct, function (item, index) { %>
        <tr class="text-left card-title-bg" style="background:#f2faff " data-ma-ct="<%- item.ma_ct %>" onclick="anHienChildren(this)">
            <td style="width: 45px">
                <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                    <input type="checkbox" id="chi_phi_ct_<%- item.ma_ct %>" value="<%- item.ma_ct %>" class="custom-control-input chiPhiCTItem" onchange="onChonTatCaChiPhiCT(this)">
                    <label class="custom-control-label" for="chi_phi_ct_<%- item.ma_ct %>">&nbsp;</label>
                </div>
            </td>
            <td colspan = "5">
                <span style="font-weight: bold; font-size:14px"><%- item.ten %></span>
            </td>
        </tr>
       <% _.forEach(obj.bang_ke, function (item2, index) {
            if (item2.ma_ct == item.ma_ct) { %>
                <tr style="vertical-align:middle;" data-parent-ma-ct="<%- item2.ma_ct %>">
                    <td style="text-align: center; max-width: 45px" >
                        <div class="custom-control custom-checkbox custom-control-inline ml-2" style="margin:unset;">
                            <input type="checkbox" id="chi_phi_ct_<%- item2.bt %>" value="<%- item2.bt %>" data_ma_ct="<%-item2.ma_ct%>" class="custom-control-input chiPhiCTItem">
                            <label class="custom-control-label" for="chi_phi_ct_<%- item2.bt %>">&nbsp;</label>
                        </div>
                    </td>
                    <td ><%- item2.ten %></td>
                    <td class="text-center"><%- item2.dvi_tinh %></td>
                    <td class="text-center"><%- item2.so_luong %></td>
                    <td class="text-right"><%- ESUtil.formatMoney(item2.don_gia) %></td>
                    <td class="text-right">
                        <%- ESUtil.formatMoney(item2.thanh_tien) %>
                    </td>
                </tr>
            <%}
        })
    })%>
</script>
