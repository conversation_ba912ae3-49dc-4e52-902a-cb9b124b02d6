﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục thông báo app khách hàng";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON>h mục thông báo app khách hàng</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Thông báo app khách hàng</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col col-2">
                            <div class="form-group">
                                <label for="ngay_d">Ngày tìm kiếm</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_d" display-format="date" value-format="number" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-2">
                            <div class="form-group">
                                <label for="ngay_c">&nbsp;</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" name="ngay_c" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-2 d-none">
                            <div class="form-group">
                                <label>Nguồn áp dụng</label>
                                <select class="select2 form-control custom-select" name="nguon_ad" style="width:100%">
                                    <option value="">Chọn nguồn áp dụng</option>
                                    <option value="MOBILE_KH">App khách hàng</option>
                                    <option value="WEB">Website bồi thường</option>
                                    <option value="ALL">Tất cả</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top:21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnThemThongTin">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row">
                    <div class="col-md-12" style="margin-top: 5px;">
                        <div class="table-responsive">
                            <div id="gridViewAppNotifications" class="table-app" style="height: 65vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modalCauHinhThongBao" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width: 50%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình thông báo</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmCauHinhThongBao" novalidate="novalidate" method="post">
                    <input type="hidden" name="so_id" value=""/>
                    <div class="row">
                        <div class="col col-3">
                            <div class="form-group">
                                <label class="_required" for="ngay_ad">Ngày bắt đầu</label>
                                <div class="input-group">
                                    <input type="text" required class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-3">
                            <div class="form-group">
                                <label class="_required" for="ngay_kt">Ngày kết thúc</label>
                                <div class="input-group">
                                    <input type="text" required class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" name="ngay_kt" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-3 d-none">
                            <div class="form-group">
                                <label class="_required">Nguồn áp dụng</label>
                                <select class="select2 form-control custom-select" name="nguon_ad" style="width:100%">
                                    <option value="">Chọn nguồn áp dụng</option>
                                    <option value="MOBILE_KH">App khách hàng</option>
                                    <option value="WEB">Website bồi thường</option>
                                    <option value="ALL">Tất cả</option>
                                </select>
                            </div>
                        </div>
                        <div class="col col-3">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width:100%">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="K">Không sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="_required">Tiêu đề</label>
                                <textarea name="tieu_de" required class="form-control" rows="4"></textarea>
                            </div>
                        </div>
                        <div class="col col-6">
                            <div class="form-group">
                                <label class="_required">Nội dung tóm tắt</label>
                                <textarea name="nd_tom_tat" required class="form-control" rows="4"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col col-12">
                            <div class="form-group">
                                <label class="_required">Nội dung</label>
                                <textarea name="noi_dung" required class="form-control" rows="4"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer pt-0 pb-0" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinhThongBao">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/app/Admin/services/AppNotificationService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/AppNotification.js" asp-append-version="true"></script>
}