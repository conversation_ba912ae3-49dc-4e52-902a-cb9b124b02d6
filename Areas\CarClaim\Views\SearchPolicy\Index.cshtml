﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.AspNetCore.Http
@using ESCS.COMMON.Contants
@using Newtonsoft.Json
@using ESCS.MODEL.ESCS.ModelView
@{
    ViewData["Title"] = "Tra cứu lịch sử GCN";
    Layout = "~/Views/Shared/_Layout.cshtml";
    string key_google = ESCS.Common.EscsUtils.dv_google == null ? "" : ESCS.Common.EscsUtils.dv_google.key;
    key_google = string.Empty;
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">@ViewData["Title"]</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12 col-md-12 pd-3">
            <div class="card">
                <div class="card-body" style="padding-top:5px;">
                    <form name="frmTimKiem" method="post" class="row">
                        <div class="col-3">
                            <div class="form-group">
                                <label>Đơn vị</label>
                                <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Số HĐ</label>
                                <input type="text" name="so_hdong" autocomplete="off" placeholder="Số hợp đồng" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Số GCN bảo hiểm</label>
                                <input type="text" name="so_gcn" autocomplete="off" placeholder="Số GCN bảo hiểm" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Biển số xe</label>
                                <input type="text" name="bien_so_xe" autocomplete="off" placeholder="Biển số xe" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Số khung</label>
                                <input type="text" name="so_khung" autocomplete="off" placeholder="Số khung" class="form-control">
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <label>Số máy</label>
                                <input type="text" name="so_may" autocomplete="off" placeholder="Số máy" class="form-control">
                            </div>
                        </div>
                        <div class="col-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search mr-2"></i>Tìm kiếm
                            </button>
                        </div>
                    </form>
                    <div class="row" style="margin-top:.5rem;">
                        <div class="col-12">
                            <div id="gridViewTimKiem" class="table-app bg-light" style="height: 59vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalXemChiTietGCN" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable" style="min-width:1200px;">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h5 class="modal-title">Xem chi tiết GCN</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="min-height:50vh;">
                <div class="row">
                    <pre class="col-12" id="data"></pre>
                </div>
            </div>
            <div class="modal-footer py-1">
                <button type="button" class="btn btn-sm btn-primary" data-dismiss="modal"><i class="fas fa-window-close mr-2"></i>Đóng</button>
            </div>
        </div>
    </div>
</div>

<partial name="/Views/Shared/_ModalXemThongTinGiayChungNhanLSTT.cshtml" />

@section scripts{
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/CarClaim/services/CarInvestigationService.js" asp-append-version="true"></script>
    <script src="~/js/app/CarClaim/services/SearchPolicyService.js" asp-append-version="true"></script>
    <script src="~/js/app/CarClaim/SearchPolicy.js" asp-append-version="true"></script>
}