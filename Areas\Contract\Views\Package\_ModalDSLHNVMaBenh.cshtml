﻿<div id="modalDSLHNVMaBenh" class="modal-drag" style="width:350px; z-index:9999999;">
    <div class="modal-drag-header border-bottom px-2">
        <h5><span class="modal-drag-title px-2">Chọ<PERSON> danh sách quyền lợi</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12 mt-2 d-flex scrollable" style="max-height:250px; flex-wrap: wrap" id="modalDSLHNVMaBenhDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer border-top">
        <button type="button" class="btn btn-primary btn-sm wd-80" id="btnChonLHNVMaBenh">
            <i class="fas fa-mouse-pointer mr-1"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalDSLHNVMaBenhDanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox col-12" data-text="">
        <input type="checkbox" id="lhnv_ma_benh_<%- item.lh_nv %>" value="<%- item.lh_nv %>" class="custom-control-input modalDSLHNVMaBenhItem">
        <label class="custom-control-label" style="cursor:pointer;" for="lhnv_ma_benh_<%- item.lh_nv %>"><%- item.ten_hien_thi %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>