﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@model List<string>
@{
    ViewData["Title"] = "";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="p-3">
    <div class="card">
        <div class="card-body overflow-auto" style="height: calc( 100vh - 90px );">
            <h3 class="font-weight-bold text-primary">Source code chứa @Model.Count file ".js"</h3>
            @foreach (string line in Model)
            {
                <div class="d-flex align-items-center border-bottom mb-3">
                    <h4 class="mb-0 mr-auto">@line</h4>
                    <a class="btn btn-sm btn-primary" target="_blank" href="@Url.Action("DocFile", "SourceReader", new { Area = "Admin", url = line })">action</a>
                </div>
            }
        </div>
    </div>
</div>