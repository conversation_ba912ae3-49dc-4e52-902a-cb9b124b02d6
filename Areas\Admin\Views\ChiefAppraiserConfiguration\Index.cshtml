﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình giám định viên trưởng";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình giám định viên trưởng</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="javascript:void(0)">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">@ViewData["Title"]</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body" style="padding-top:5px;">
                    <form name="frmTimKiem" method="post">
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label for="tim">Thông tin tìm kiếm</label>
                                    <input type="text" autocomplete="off" class="form-control" name="tim" placeholder="Nhập tên nhóm GDVT">
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="ma_chi_nhanh">Đối tác</label>
                                    <select class="select2 form-control custom-select" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label>Chi nhánh</label>
                                    <select class="select2 form-control custom-select" name="ma_chi_nhanh" style="width: 100%; height:36px;"></select>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label for="trang_thai">Trạng thái</label>
                                    <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                        <option value="">Chọn trạng thái</option>
                                        <option value="1">Đang sử dụng</option>
                                        <option value="0">Ngừng sử dụng</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-2" style="padding-top: 21px;">
                                <button type="button" class="btn btn-primary btn-sm wd-40p mr-2" id="btnTimKiem">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnThemMoiGDVT">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                    <div class="row" style="margin-top:3px;">
                        <div class="col-12">
                            <div id="gridViewGDVT" class="table-app" style="height: 65vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<partial name="_Modal.cshtml" />
<partial name="_Template.cshtml" />

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BranchListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/UserManagementService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/ChiefAppraiserConfigurationService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/ChiefAppraiserConfiguration.js" asp-append-version="true"></script>
}