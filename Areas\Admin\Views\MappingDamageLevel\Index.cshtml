﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục mã đối tác";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">C<PERSON>u hình mức độ tổn thất</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">C<PERSON><PERSON> hình mức độ tổn thất</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin tìm kiếm" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;">
                                    <option value="">Chọn đối tác</option>
                                    <option value="CTYBHABC">Công ty bảo hiểm ABC</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required name="doi_tac_ai" style="width: 100%; height:36px;">
                                    <option value="">Chọn đối tác AI</option>
                                    <option value="FPT">FPT</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhap">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:12px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridView" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhap" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel" style="overflow:hidden;">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmSaveMucDoTT_AI" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Cấu hình mức độ tổn thất</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="pm" value="BT" />
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Mã đối tác</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Đối tác AI</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" required name="doi_tac_ai" style="width: 100%; height:36px;">
                                    <option value="">Chọn đối tác</option>
                                    <option value="FPT">FPT</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-5">
                            <div class="form-group">
                                <input type="text" name="timKiemMucDo" id="timKiemMucDo" placeholder="Tìm kiếm mã/tên mức độ" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-2" style="margin-left: -15px;">
                            <button type="button" class="btn btn-primary btn-sm wd-50p" title="Tìm kiếm mã/tên mức độ" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="table-responsive scrollable" style="max-height: 400px; margin: 5px 10px 5px 10px">
                            <table class="table table-bordered fixed-header" style="width:100%">
                                <thead class="font-weight-bold text-center uppercase">
                                    <tr>
                                        <th style="width: 15%">Mã mức độ</th>
                                        <th style="width:25%">Tên mức độ</th>
                                        <th style="width:15%">Mã mapping</th>
                                        <th style="width:25%">Tên mapping</th>
                                        <th style="width:15%">Tên tắt</th>
                                    </tr>
                                </thead>
                                <tbody id="tableMapping">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnSave"><i class="fa fa-save"></i> Lưu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<partial name="_Template.cshtml" />
@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/MappingDamageLevelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/DamageLevelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/MappingDamageLevel.js" asp-append-version="true"></script>
}