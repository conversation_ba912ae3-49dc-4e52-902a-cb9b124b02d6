﻿using ESCS.Attributes;
using ESCS.COMMON.ESCSStoredProcedures;
using ESCS.COMMON.ExtensionMethods;
using ESCS.Controllers;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace ESCS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [SystemAuthen]
    public class ConfigApproveController : BaseController
    {
        public IActionResult Index()
        {
            return View();
        }

        [AjaxOnly]
        public async Task<IActionResult> Save()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_NH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> Delete()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_XOA, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPaging()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetDetail()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_LKE_CT, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SearchUserApprove()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_TKIEM_NSD, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> SearchUserByBranch()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_TKIEM_NSD_CNHANH, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> GetPagingDSNhomTrinhCN()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_CA_NHAN_LKE, json);
            return Ok(data);
        }

        [AjaxOnly]
        public async Task<IActionResult> capNhatNguoiDuyet()
        {
            var json = Request.GetDataRequestNew(GetUser());
            var data = await Request.GetResponeNew(StoredProcedure.PHT_NGUOI_DUYET_MAU_UPDATE, json);
            return Ok(data);
        }
    }
}