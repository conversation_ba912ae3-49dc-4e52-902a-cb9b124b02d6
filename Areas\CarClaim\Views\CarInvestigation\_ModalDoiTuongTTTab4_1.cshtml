﻿<div id="modalChonDoiTuongTTTab4_1" class="modal-drag" style="width:300px; z-index:9999999;">
    <div class="modal-drag-header border-bottom">
        <h5><span class="modal-drag-title">Chọn đối tượng tổn thất</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" autocomplete="off" placeholder="Tìm kiếm thông tin" class="form-control">
                <input type="hidden" id="modalChonDoiTuongTTTab4_1ElementSelect">

            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalChonDoiTuongTTTab4_1DanhSach">

            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonDoiTuongTTTab4_1">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<script type="text/html" id="modalChonDoiTuongTTTab4_1DanhSachTemplate">
    <% if(danh_sach.length > 0){
    _.forEach(danh_sach, function(item,index) { %>
    <div class="custom-control custom-checkbox" data-text="">
        <input type="checkbox" id="doi_tuong_tt_tab_4_1_<%- item.so_id_doi_tuong %>" data-val="<%- item.ten %>" value="<%- item.so_id_doi_tuong %>" class="custom-control-input modalChonDoiTuongTTTab4_1Item single_checked">
        <label class="custom-control-label" style="cursor:pointer;" for="doi_tuong_tt_tab_4_1_<%- item.so_id_doi_tuong %>"><%- item.ten %></label>
    </div>
    <% })}else{ %>
    <div class="text-center" style="width:100%">Chưa có dữ liệu</div>
    <% } %>
</script>