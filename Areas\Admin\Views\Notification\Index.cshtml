﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục thông báo";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0"><PERSON>h mục thông báo</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Thông báo</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="maDoiTac" id="ma_doi_tac" placeholder="Nhập thông tin cần tìm" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Tiêu đề</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="tieu_de" style="width: 100%; height:36px;">
                                    <option value="">Chọn tiêu đề</option>
                                    <option value="Thông báo">Thông báo</option>
                                    <option value="Bảo trì hệ thống">Bảo trì hệ thống</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top:21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnNhapThongTin">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row">
                    <div class="col-md-12" style="margin-top: 5px">
                        <div class="table-responsive">
                            <div id="gridViewThongBao" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapThongBao" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinThongBao" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Nhập thông tin thông báo</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="pm" value="BT" />
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">NSD</label>
                                <div class="input-group">
                                    <input type="text" name="nsd" required="" class="form-control email-inputmask" im-insert="true">
                                    <div class="input-group-append">
                                        <label class="input-group-text" for="email">
                                            <a href="#">
                                                <i class="fa fa-envelope" aria-hidden="true" title="Thông tin emaill"></i>
                                            </a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>Nội dung tóm tắt</label>
                                <input type="text" autocomplete="off" maxlength="100" name="nd_tom_tat" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Mã đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;">
                                    <option>CTYABC</option>
                                    <option>ESCS</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Tiêu đề</label>
                                <select class="select2 form-control custom-select" required name="tieu_de" style="width: 100%; height:36px;">
                                    <option>Chọn tiêu đề</option>
                                    <option>Thông báo</option>
                                    <option>Cảnh báo</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="ngay_d">Thời gian thông báo</label>
                                <div class="input-group">
                                    <input type="text" autocomplete="off" class="form-control datepicker" name="tg_thong_bao" required display-format="date" value-format="number" placeholder="mm/dd/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Loại thông báo</label>
                                <input type="text" autocomplete="off" maxlength="100" name="loai_thong_bao" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm wd-80" id="btnLuuThongTin"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-primary btn-sm wd-80" id="btnXoaThongTin"><i class="fas fa-trash-alt"></i> Xóa</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                </div>
            </form>
        </div>
    </div>
</div>
@section Scripts{
    <script src="~/js/app/Admin/services/NotificationService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/Notification.js" asp-append-version="true"></script>
}



