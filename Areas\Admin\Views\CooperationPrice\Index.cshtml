﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Cấu hình giá hợp tác/giá trần";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Cấu hình giá hợp tác/giá trần</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Cấu hình giá hợp tác/giá trần</li>
        </ol>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col col-2">
                            <div class="form-group">
                                <label for="ngay_d">Từ ngày</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_d" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-2">
                            <div class="form-group">
                                <label for="ngay_c">Đến ngày</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_c" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" autocomplete="off" name="tim" id="tim" placeholder="Nhập mã/tên gara" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;" required>
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="K">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-2 ml-auto" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-32p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-32p" title="Thêm mới" id="btnNhap">
                                <i class="fa fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-32p" title="Import" id="btnUploadCauHinh">
                                <i class="fas fa-upload"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewPhanTrang" class="table-app"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalNhap" tabindex="-1">
    <div class="modal-dialog" style="max-width:calc( 100vw - 20px );">
        <div class="modal-content">
            <div class="modal-header p-2">
                <h5 class="modal-title">Nhập cấu hình giá gara</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-2 bg-light">
                <form name="frmSave" method="post" class="row">
                    <input type="hidden" name="so_id" />
                    <div class="col-3">
                        <div class="form-group">
                            <label class="_required">Gara</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="gara" style="width: 100%; height:36px;" required>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label for="ngay_ad" class="_required">Ngày áp dụng</label>
                            <div class="input-group">
                                <input required type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                <div class="input-group-append">
                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label for="ngay_kt" class="_required">Ngày kết thúc</label>
                            <div class="input-group">
                                <input required type="text" class="form-control datepicker" autocomplete="off" name="ngay_kt" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                <div class="input-group-append">
                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label class="_required">Trạng thái</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;" required>
                                <option value="">Chọn trạng thái</option>
                                <option value="D">Đang áp dụng</option>
                                <option value="K">Không áp dụng</option>
                            </select>
                        </div>
                    </div>
                    <div class="col d-flex justify-content-end" style="padding-top: 21px; gap: 4px;">
                        <button type="button" class="btn btn-primary btn-sm" id="btnLuuThongTin">
                            <i class="fas fa-save mr-2"></i>Lưu
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="btnDownload">
                            <i class="fas fa-download mr-2"></i>Download
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="btnUpload">
                            <i class="fas fa-upload mr-2"></i>Upload
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="btnUploadTatCa">
                            <i class="fas fa-upload mr-2"></i>Upload
                        </button>
                    </div>
                </form>
                <div class="row">
                    <div class="col-12 mt-2">
                        <label class="mb-1">Danh sách cấu hình giá</label>
                        <div class="card m-0 border">
                            <div class="card-body p-2">
                                <form name="frmTimKiemCT" method="post" class="row mx-n2">
                                    <div class="col-2 px-2">
                                        <div class="form-group">
                                            <label class="">Hãng xe</label>
                                            <select class="select2 form-control custom-select select2-hidden-accessible" name="hang_xe" style="width: 100%; height:36px;">
                                                <option value="">Chọn hãng xe</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-2 px-2">
                                        <div class="form-group">
                                            <label class="">Hiệu xe</label>
                                            <select class="select2 form-control custom-select select2-hidden-accessible" name="hieu_xe" style="width: 100%; height:36px;">
                                                <option value="">Chọn hiệu xe</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-2 px-2">
                                        <div class="form-group">
                                            <label>Năm SX từ</label>
                                            <input type="number" maxlength="4" min="0" max="3000" autocomplete="off" name="nam_sx_d" placeholder="" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-2 px-2">
                                        <div class="form-group">
                                            <label>Năm SX đến</label>
                                            <input type="number" maxlength="4" min="0" max="3000" autocomplete="off" name="nam_sx_c" placeholder="" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-2 px-2">
                                        <div class="form-group">
                                            <label>Tìm kiếm thông tin</label>
                                            <input type="text" autocomplete="off" name="tim" placeholder="" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-2 px-2 ml-auto" style="padding-top: 21px;">
                                        <button type="button" class="btn btn-primary btn-sm wd-48p" title="Tìm kiếm" id="btnTimKiemCT">
                                            <i class="fa fa-search"></i>
                                        </button>
                                        <button type="button" class="btn btn-primary btn-sm wd-49p d-none" title="Thêm mới" id="btnNhapCT">
                                            <i class="fa fa-plus"></i>
                                        </button>
                                    </div>
                                    <div class="col-12 px-2 mt-2">
                                        <div class="table-responsive">
                                            <div id="gridViewPhanTrangCT" class="table-app"></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer py-1 px-2 justify-content-start">
                <button type="button" class="btn btn-outline-primary btn-sm wd-80  d-none" id="btnXoaThongTin">
                    <i class="fas fa-trash-alt mr-2"></i>Xóa
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80 ml-auto" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalUploadCauHinh" tabindex="-1">
    <div class="modal-dialog" style="max-width:calc( 100vw - 20px );">
        <div class="modal-content">
            <div class="modal-header p-2">
                <h5 class="modal-title">Upload danh sách gara</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-2 bg-light">
                <form name="frmUploadCauHinh" method="post" class="row">
                    <div class="col-2">
                        <div class="form-group">
                            <label for="ngay_ad" class="_required">Ngày áp dụng</label>
                            <div class="input-group">
                                <input required type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                <div class="input-group-append">
                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label for="ngay_kt" class="_required">Ngày kết thúc</label>
                            <div class="input-group">
                                <input required type="text" class="form-control datepicker" autocomplete="off" name="ngay_kt" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                <div class="input-group-append">
                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label class="_required">Loại chi phí</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="loai" style="width: 100%; height:36px;" required>
                                <option value="">Chọn chi phí</option>
                                <option value="VAT_TU">Tiền vật tư</option>
                                <option value="NHAN_CONG">Tiền Nhân công</option>
                                <option value="SON">Tiền sơn</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label class="_required">Trạng thái</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="trang_thai" style="width: 100%; height:36px;" required>
                                <option value="">Chọn trạng thái</option>
                                <option value="D">Đang áp dụng</option>
                                <option value="K">Không áp dụng</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="form-group">
                            <label class="_required">Chọn file upload</label>
                            <input type="file" name="file" class="form-control" accept=".xls,.xlsx" required>
                        </div>
                    </div>
                    <div class="col-2 ml-auto" style="padding-top: 21px;">
                        <button type="button" class="btn btn-primary btn-sm wd-70p" id="btnDownloadTemplate">
                            <i class="fas fa-download mr-2"></i>Tải template
                        </button>
                    </div>
                    <div class="col-2 mt-3">
                        <div class="form-group">
                            <label>Tỉnh thành</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" style="width: 100%; height:36px;" id="filterCheckListGara_TinhThanh">
                                <option value="">Chọn tỉnh thành</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2 mt-3">
                        <div class="form-group">
                            <label>Quận huyện</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" style="width: 100%; height:36px;" id="filterCheckListGara_QuanHuyen">
                                <option value="">Chọn quận huyện</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2 mt-3">
                        <div class="form-group">
                            <label class="">Chính hãng</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="chinh_hang" style="width: 100%; height:36px;" id="filterCheckListGara_ChinhHang">
                                <option value="">Chọn chính hãng</option>
                                <option value="C">Chính hãng</option>
                                <option value="K">Không chính hãng</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-2 mt-3">
                        <div class="form-group">
                            <label class="">Hợp tác</label>
                            <select class="select2 form-control custom-select select2-hidden-accessible" name="hop_tac" style="width: 100%; height:36px;" id="filterCheckListGara_HopTac">
                                <option value="">Chọn hợp tác</option>
                                <option value="C">Có hợp tác</option>
                                <option value="K">Không hợp tác</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-4 mt-3">
                        <div class="form-group">
                            <label>Danh sách gara</label>
                            <input id="filterCheckListGara" type="text" class="form-control" autocomplete="off" placeholder="Tìm kiếm gara">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="card m-0 border" style="height:400px">
                            <div class="card-body p-2 overflow-auto scrollable">
                                <div class="row mx-n2" id="frmUploadCauHinh_container">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer py-1 px-2 justify-content-start">
                <button type="button" class="btn btn-primary btn-sm wd-80 ml-auto" onclick="uploadCauHinh?.();">
                    <i class="fas fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-80" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<partial name="~/Views\Shared\_FormUploadExcel.cshtml" />
@section Styles{
    <link href="~/libs/tree-js/vakata-jstree/dist/themes/default/style.min.css" rel="stylesheet" />
}
@section Scripts{
    <script src="~/libs/tree-js/vakata-jstree/dist/jstree.js" asp-append-version="true"></script>
    <script src="~/js/common/UploadExcelService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CarManufacturerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/HieuXeService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CategoryvehicleListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/GaraListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CooperationPriceService.js"></script>
    <script src="~/js/app/Admin/CooperationPrice.js"></script>
}