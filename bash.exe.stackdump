Stack trace:
Frame         Function      Args
0007FFFF3430  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF3430, 0007FFFF2330) msys-2.0.dll+0x1FE8E
0007FFFF3430  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF3708) msys-2.0.dll+0x67F9
0007FFFF3430  000210046832 (000210286019, 0007FFFF32E8, 0007FFFF3430, 000000000000) msys-2.0.dll+0x6832
0007FFFF3430  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF3430  000210068E24 (0007FFFF3440, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF3710  00021006A225 (0007FFFF3440, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD93EB0000 ntdll.dll
7FFD938D0000 KERNEL32.DLL
7FFD91940000 KERNELBASE.dll
7FFD92510000 USER32.dll
7FFD917B0000 win32u.dll
7FFD93DE0000 GDI32.dll
7FFD91D00000 gdi32full.dll
7FFD91710000 msvcp_win.dll
7FFD91540000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD92430000 advapi32.dll
7FFD920B0000 msvcrt.dll
7FFD92390000 sechost.dll
7FFD936F0000 RPCRT4.dll
7FFD91C40000 bcrypt.dll
7FFD90E00000 CRYPTBASE.DLL
7FFD91C70000 bcryptPrimitives.dll
7FFD93020000 IMM32.DLL
