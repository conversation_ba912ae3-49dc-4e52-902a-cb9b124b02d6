﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@*Modal thêm phương pháp khấu hao*@
<div id="modalNhapKhauHao" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:60%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Phương pháp tính khấu hao <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThemKhauHao" name="frmThemKhauHao" method="post" novalidate="novalidate">
                    <input type="hidden" name="ngay_ad" value="" />
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required="" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" name="nv" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Loại xe</label>
                                <select class="select2 form-control custom-select" name="loai_xe" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row">
                    <div class="col-4" style="margin-top: 10px;">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="rounded" style="max-height:325px;">
                                    <div class="justify-content-between align-items-center p-2 card-title-bg text-center">
                                        <h6 class="m-0">Ngày áp dụng</h6>
                                    </div>
                                    <table class="table table-hover" style="border-bottom: 1px solid #e8eef3;">
                                        <tbody id="tblDsKhauHao" class="text-center">
                                        </tbody>
                                    </table>
                                    <div class="pt-3 text-center" id="divThemMoiNgayKH">
                                        <button type="button" class="btn btn-primary btn-sm" style="width:100%" id="btnOpenInputThemNgayKH"><i class="fa fa-plus"></i> Thêm mới</button>
                                    </div>
                                    <div class="pt-3" id="divInputThemNgayKH">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <input type="text" id="ngayad_kh" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" placeholder="dd/mm/yyyy" style="width:100%">
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary btn-sm" id="btnThemNgayKH" style="margin-top:5px; width:49%">
                                                <i class="fa fa-save mr-2"></i>Lưu
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" id="btnDongNgayKH" style="margin-top: 5px; width: 49%">
                                                <i class="fas fa-window-close mr-2"></i>Đóng
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="tab-content" style="padding-top: 10px">
                            <div id="pp_khau_hao" class="tab-pane fade show active data-scroll" role="tabpanel" aria-labelledby="xe-tab">
                                <div class="table-responsive" style="max-height: 425px">
                                    <table id="tblKhauHao" class="table table-bordered fixed-header">
                                        <thead class="font-weight-bold">
                                            <tr class="text-center uppercase">
                                                <th>Tuổi xe từ</th>
                                                <th>Tuổi xe tới</th>
                                                <th width="20%">Tỷ lệ khấu hao</th>
                                                <th style="width:40px"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="modalThemKhauHao"></tbody>
                                        <tfoot>
                                            <tr class="card-title-bg">
                                                <td colspan="8">
                                                    <a href="javascript:void(0)" id="btnAddKhauHao">
                                                        <i class="fa fa-plus mr-2"></i>Thêm dòng dữ liệu
                                                    </a>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="tab-content">
                            <div id="pp_khau_hao_loai_xe" class="tab-pane fade show active data-scroll" role="tabpanel" aria-labelledby="xe-tab">
                                <div class="table-responsive" style="max-height: 340px">
                                    <table id="tblKhauHaoLoaiXe" class="table table-bordered fixed-header">
                                        <thead class="font-weight-bold">
                                            <tr class="text-center uppercase">
                                                <th width="20%">Tuổi xe từ</th>
                                                <th width="20%">Tuổi xe tới</th>
                                                <th width="20%">Hệ số tỷ lệ</th>
                                                <th style="width:6%"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="modalThemKhauHaoLoaiXe"></tbody>
                                        <tfoot>
                                            <tr class="card-title-bg">
                                                <td colspan="8">
                                                    <a href="javascript:void(0)" id="btnAddKhauHaoLoaiXe">
                                                        <i class="fa fa-plus mr-2"></i>Thêm dòng dữ liệu
                                                    </a>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuKhauHao">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaKhauHao">
                    <i class="fas fa-trash-alt"></i>&nbsp;&nbsp;Xóa
                </button>
            </div>
        </div>
    </div>
</div>

@*Modal thêm phương pháp giảm trừ*@
<div id="modalNhapGiamTru" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình phương pháp tính giảm trừ bảo hiểm <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <div class="row p-2">
                    <div class="col-2 pr-0">
                        <div class="row p-2">
                            <div class="col-12 pr-0">
                                <div class="card border mb-0">
                                    <div class="card-body justify-content-between align-items-center p-2 card-title-bg" style="height:65vh;overflow-y:auto">
                                        <h6 class="text-center">Ngày áp dụng</h6>
                                        <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="tblDsNgayGiamTru">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-10">
                        <div class="row p-2">
                            <div class="col-12">
                                <ul class="nav nav-pills font-weight-bold" role="tablist" style="background-color:#f8f9fa">
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link active" id="xe-tab" onclick="changeTabNvGiamTru('XE')" data-toggle="tab" href="#xe_oto" role="tab" aria-controls="home" aria-selected="true">
                                            Nghiệp vụ xe ô tô
                                        </a>
                                    </li>
                                    <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="xe-may-tab" onclick="changeTabNvGiamTru('XE_MAY')" data-toggle="tab" href="#xe_may" role="tab" aria-controls="profile" aria-selected="false">
                                            Nghiệp vụ xe máy
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content" style="padding-top: 10px">
                                    <div id="xe_oto" class="tab-pane fade data-scroll active show" role="tabpanel" aria-labelledby="xe-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0" style="height:59vh;overflow-y:auto">
                                                <div class="table-responsive" style="height:30vh;">
                                                    <table id="tblGiamTru" class="table table-bordered fixed-header">
                                                        <thead class="font-weight-bold">
                                                            <tr class="text-center uppercase">
                                                                <th width="16%">Từ ngày</th>
                                                                <th width="15%">Tới ngày</th>
                                                                <th width="15%">Tỷ lệ giảm trừ (%)</th>
                                                                <th style="width:3%"></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalThemGiamTru"></tbody>
                                                        <tfoot>
                                                            <tr class="card-title-bg">
                                                                <td colspan="8">
                                                                    <a href="javascript:void(0)" id="btnAddGiamTru">
                                                                        <i class="fa fa-plus mr-2"></i>Thêm dòng dữ liệu
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        </tfoot>
                                                    </table>
                                                </div>
                                                <div class="table-responsive" style="height:29vh;">
                                                    <table id="tblGiamTruDKBS" class="table table-bordered fixed-header">
                                                        <thead class="font-weight-bold">
                                                            <tr class="text-center uppercase">
                                                                <th width="16%">Điều khoản bổ sung</th>
                                                                <th width="15%">Tỷ lệ giảm trừ</th>
                                                                <th width="15%">Số tiền giảm trừ</th>
                                                                <th style="width:3%"></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalThemGiamTruDKBS"></tbody>
                                                        <tfoot>
                                                            <tr class="card-title-bg">
                                                                <td colspan="8">
                                                                    <a href="javascript:void(0)" id="btnAddGiamTruDKBS" onclick="chonDKBS(this)">
                                                                        <i class="fa fa-plus mr-2"></i>Thêm dòng dữ liệu
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        </tfoot>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll" id="xe_may" role="tabpanel" aria-labelledby="xe-may-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0" style="height:59vh;overflow-y:auto">
                                                <div class="table-responsive" style="height:30vh;">
                                                    <table id="tblGiamTruXM" class="table table-bordered fixed-header">
                                                        <thead class="font-weight-bold">
                                                            <tr class="text-center uppercase">
                                                                <th width="16%">Từ ngày</th>
                                                                <th width="15%">Tới ngày</th>
                                                                <th width="15%">Tỷ lệ giảm trừ (%)</th>
                                                                <th style="width:3%"></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalThemGiamTruXM"></tbody>
                                                        <tfoot>
                                                            <tr class="card-title-bg">
                                                                <td colspan="8">
                                                                    <a href="javascript:void(0)" id="btnAddGiamTruXM">
                                                                        <i class="fa fa-plus mr-2"></i>Thêm dòng dữ liệu
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        </tfoot>
                                                    </table>
                                                </div>
                                                <div class="table-responsive" style="height:29vh;">
                                                    <table id="tblGiamTruDKBSXM" class="table table-bordered fixed-header">
                                                        <thead class="font-weight-bold">
                                                            <tr class="text-center uppercase">
                                                                <th width="16%">Điều khoản bổ sung</th>
                                                                <th width="15%">Tỷ lệ giảm trừ</th>
                                                                <th width="15%">Số tiền giảm trừ</th>
                                                                <th style="width:3%"></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalThemGiamTruDKBSXM"></tbody>
                                                        <tfoot>
                                                            <tr class="card-title-bg">
                                                                <td colspan="8">
                                                                    <a href="javascript:void(0)" id="btnAddGiamTruDKBSXM" onclick="chonDKBS(this)">
                                                                        <i class="fa fa-plus mr-2"></i>Thêm dòng dữ liệu
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        </tfoot>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnThemNgayGT">
                    <i class="fas fa-plus mr-2"></i>Thêm ngày áp dụng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" id="btnLuuGiamTru">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm wd-85 float-right" id="btnXoaGiamTru">
                    <i class="fas fa-trash-alt"></i>&nbsp;&nbsp;Xóa
                </button>
            </div>
        </div>
    </div>
</div>

@*Modal cấu hình chung xe ô tô*@
<div id="modalCauHinhChungXeOto" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:60%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình chung xe ô tô <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form name="frmThemCauHinhXeOto" method="post" novalidate="novalidate">
                    <input type="hidden" name="ngay_ad" value="" />
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required="" name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row">
                    <div class="col-4" style="margin-top: 10px;">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="rounded">
                                    <div class="d-flex justify-content-between align-items-center p-2 card-title-bg">
                                        <h5 class="m-0">Các ngày khai báo</h5>
                                    </div>
                                    <table class="table table-hover" id="tblDsNgayCauHinhXe" style="border-bottom:1px solid #e8eef3;">
                                        <thead>
                                            <tr>
                                                <th class="text-center">Ngày khai báo</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-center"></tbody>
                                    </table>
                                    <div class="pt-3 text-center" id="divThemMoiNgayCH">
                                        <button type="button" class="btn btn-primary btn-sm" style="width:100%" id="btnOpenInputThemNgayCH"><i class="fa fa-plus"></i> Thêm mới</button>
                                    </div>
                                    <div class="pt-3" id="divInputThemNgayCH">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <input type="text" id="ngayad_ch" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" placeholder="dd/mm/yyyy" style="width:100%">
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary btn-sm" id="btnThemNgayCH" style="margin-top:5px; width:49%">
                                                <i class="fa fa-save mr-2"></i>Lưu
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" id="btnDongNgayCH" style="margin-top: 5px; width: 49%">
                                                <i class="fas fa-window-close mr-2"></i>Đóng
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="tab-content" style="padding-top: 10px">
                            <div class="tab-pane fade show active data-scroll" role="tabpanel" aria-labelledby="xe-tab">
                                <div class="table-responsive" style="max-height: 405px">
                                    <table id="tblCauHinhXeOto" class="table table-bordered fixed-header">
                                        <thead class="font-weight-bold">
                                            <tr class="text-center uppercase">
                                                <th width="75%">Cấu hình</th>
                                                <th style="width:25%">Trạng thái</th>
                                            </tr>
                                        </thead>
                                        <tbody id="modalCauHinhXeOto">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinhXeOto">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaCauHinhXeOto">
                    <i class="fas fa-trash-alt"></i>&nbsp;&nbsp;Xóa
                </button>
            </div>
        </div>
    </div>
</div>

@*Modal cấu hình KPI*@
<div id="modalThemCauHinhkpi" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width: 65%; margin-top:10px; margin-bottom: unset;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Tiêu chuẩn thời gian KPI <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmThemkpi" name="frmThemkpi" method="post" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required name="nv" style="width: 100%; height:36px;">
                                    <option value="">Chọn nghiệp vụ</option>
                                    <option value="XE">Xe cơ giới</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Loại hình nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required name="nv_ct" style="width: 100%; height:36px;">
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row mt-2">
                    <div class="col-2" style="padding-right: 5px;">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="border rounded">
                                    <div class="justify-content-between align-items-center p-2 card-title-bg text-center">
                                        <h6 class="m-0">Ngày áp dụng</h6>
                                    </div>
                                    <table class="table table-hover" style="border-bottom: 1px solid #e8eef3;">
                                        <tbody id="tblDsNgay" class="text-center">
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="text" id="themMoiNgayAD" class="form-control datepicker ngay_ad" required="" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm w-100" title="Thêm mới ngày áp dụng" id="btnThemNgayApDung">
                                                        <i class="fa fa-plus"></i> Thêm ngày
                                                    </button>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2" style="padding-left: 5px;">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="border rounded">
                                    <div class="justify-content-between align-items-center p-2 card-title-bg text-center">
                                        <h6 class="m-0">Số tiền BT</h6>
                                    </div>
                                    <table class="table table-hover" style="border-bottom: 1px solid #e8eef3;" id="tableSoTien">
                                        <tbody id="tblDsTien" class="text-center">
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td>
                                                    <input type="text" id="themMoiTien" class="floating-input number" autocomplete="off" maxlength="50" name="tien" style="height: 30px;" placeholder="Thêm số tiền bồi thường" />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm w-100" title="Thêm số tiền bồi thường" id="btnThemSoTien">
                                                        <i class="fa fa-plus"></i> Thêm số tiền
                                                    </button>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="table-responsive" style="max-height: 365px;">
                            <table id="tblKPI" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center uppercase">
                                        <th width="70%">Tiến trình giải quyết</th>
                                        <th width="30%">Thời gian thực hiện (phút)</th>
                                    </tr>
                                </thead>
                                <tbody id="tblTienTrinhKPI">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display: block; padding: 10px 0px 0px;">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuukpi">
                        <i class="fa fa-save mr-2"></i>Lưu
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoakpi">
                        <i class="fas fa-trash-alt"></i>&nbsp;&nbsp;Xóa
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@*Cấu hình bồi thường*@
@* <div id="modalThemCauHinhBoiThuong" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình bồi thường xe <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <div class="row p-2">
                    <div class="col-2 pr-0">
                        <div class="row p-2">
                            <div class="col-12 pr-0">
                                <div class="card border mb-0">
                                    <div class="card-body justify-content-between align-items-center p-2 card-title-bg" style="height:65vh;overflow-y:auto">
                                        <h6 class="text-center">Ngày áp dụng</h6>
                                        <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="tblDsNgayCHBT">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-10">
                        <div class="row p-2">
                            <div class="col-12">
                                <ul class="nav nav-pills font-weight-bold" role="tablist" style="background-color:#f8f9fa">
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link active" id="xe-bt-tab" onclick="changeTabCauHinhBoiThuong('XE')" data-toggle="tab" href="#xe_oto_bt" role="tab" aria-controls="home" aria-selected="true">
                                            Nghiệp vụ xe ô tô
                                        </a>
                                    </li>
                                    <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="xe-bt-may-tab" onclick="changeTabCauHinhBoiThuong('XE_MAY')" data-toggle="tab" href="#xe_may_bt" role="tab" aria-controls="profile" aria-selected="false">
                                            Nghiệp vụ xe máy
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content" style="padding-top: 10px">
                                    <div id="xe_oto_bt" class="tab-pane fade data-scroll active show" role="tabpanel" aria-labelledby="xe-bt-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tblCauHinhBoiThuong">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th style="width:50px">STT</th>
                                                                <th style="width:150px;">Màn hình</th>
                                                                <th>Tên cấu hình</th>
                                                                <th style="width:180px;">Giá trị</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalCauHinhBoiThuong">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll" id="xe_may_bt" role="tabpanel" aria-labelledby="xe-bt-may-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tblCauHinhBoiThuongXM">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th style="width:50px">STT</th>
                                                                <th style="width:150px;">Màn hình</th>
                                                                <th>Tên cấu hình</th>
                                                                <th style="width:180px;">Giá trị</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalCauHinhBoiThuongXM">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnThemNgayCHBT">
                    <i class="fas fa-plus mr-2"></i>Thêm ngày áp dụng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" id="btnLuuCauHinhBoiThuong">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div> *@

<div id="modalThemNgayApDung" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Nhập ngày áp dụng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <form name="frmThemNgayApDung" method="post">
                    <div class="row mg-t-6 p-2">
                        <div class="col col-12">
                            <div class="form-group">
                                <label for="ngay_d">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.8em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuThemNgayApDungCauHinhBT">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemNgayApDungGT" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Nhập ngày áp dụng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <form name="frmThemNgayApDungGT" method="post">
                    <div class="row mg-t-6 p-2">
                        <div class="col col-12">
                            <div class="form-group">
                                <label for="ngay_d">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.8em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuThemNgayApDungCauHinhGT">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

@*Modal cấu hình hồ sơ chứng từ*@
<div id="modalThemCauHinhHoSo" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình hồ sơ chứng từ <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <div class="row p-2">
                    <div class="col-2 pr-0">
                        <div class="row p-2">
                            <div class="col-12 pr-0">
                                <div class="card border mb-0">
                                    <div class="card-body justify-content-between align-items-center p-2 card-title-bg" style="height:65vh;overflow-y:auto">
                                        <h6 class="text-center">Ngày áp dụng</h6>
                                        <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="tblDsNgayHoSo">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2 pr-0">
                        <div class="row p-2">
                            <div class="col-12 pr-0">
                                <div class="card border mb-0">
                                    <div class="card-body justify-content-between align-items-center p-2 card-title-bg" style="height:65vh;overflow-y:auto">
                                        <h6 class="text-center">Bước</h6>
                                        <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="tblDsCacBuoc">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="row p-2">
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-4 pr-0">
                                        <div class="form-group">
                                            <select class="select2 form-control custom-select" id="cauHinhHSCT_chonNV" style="height:36px;"></select>
                                        </div>
                                    </div>
                                    <div class="col-8 d-none">
                                        <div class="form-group ">
                                            <select class="select2 form-control custom-select" id="cauHinhHSCT_chonLHNV" style="height:36px;"></select>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-content" style="padding-top: 10px">
                                    <div id="xe_oto" class="tab-pane fade data-scroll active show" role="tabpanel" aria-labelledby="xe-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tblHoSoChungTu">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th style="width:50px">STT</th>
                                                                <th>Tên giấy tờ</th>
                                                                <th style="width:100px;">Bắt buộc</th>
                                                                <th style="width:150px;">Loại</th>
                                                                @* <th style="width:150px;">Nhóm nguyên nhân</th> *@
                                                            </tr>
                                                        </thead>
                                                        <tbody id="tblHoSo">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @* <div class="tab-pane fade data-scroll" id="xe_may" role="tabpanel" aria-labelledby="xe-may-tab">
                                    <div class="card border mb-0">
                                    <div class="card-body p-0">
                                    <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                    <table class="table table-bordered" id="tblHoSoChungTu">
                                    <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                    <tr class="text-center uppercase">
                                    <th style="width:50px">STT</th>
                                    <th>Tên giấy tờ</th>
                                    <th style="width:100px;">Bắt buộc1</th>
                                    <th style="width:150px;">Loại</th>
                                    </tr>
                                    </thead>
                                    <tbody id="tblHoSo">
                                    </tbody>
                                    </table>
                                    </div>
                                    </div>
                                    </div>
                                    </div> *@
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnThemNgayApDungHS">
                    <i class="fas fa-plus mr-2"></i>Thêm ngày áp dụng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" id="btnLuuHoSo">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaHoSo">
                    <i class="fas fa-trash-alt"></i>&nbsp;&nbsp;Xóa
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemNgayApDungHSCT" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Nhập ngày áp dụng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <form name="frmThemNgayApDungHSCT" method="post">
                    <div class="row mg-t-6 p-2">
                        <div class="col col-12">
                            <div class="form-group">
                                <label for="ngay_d">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.8em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuThemNgayApDungCauHinhHSCT">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

@*Modal loại hồ sơ giấy tờ*@
<div id="modalLoaiHSGT" class="modal-drag" style="width: 250px; z-index: 9999999;">
    <div class="modal-drag-header border-bottom">
        <h5><span class="modal-drag-title">Chọn loại hồ sơ</span> <span data-dismiss="modal-drag"><i class="fa fa-times mr-2"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="hidden" id="modalLoaiHSGTElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalLoaiHSGTDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonLoaiHSGT">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
        <button type="button" class="btn-outline-primary btn-sm wd-85 float-right" id="btnBoChonLoaiHSGT">
            <i class="fas fa-times mr-2"></i> Bỏ chọn
        </button>
    </div>
</div>




@*Modal nhóm nguyên nhân*@
<div id="modalNhomNguyenNhan" class="modal-drag" style="width: 250px; z-index: 9999999;">
    <div class="modal-drag-header border-bottom">
        <h5><span class="modal-drag-title">Chọn nhóm nguyên nhân</span> <span data-dismiss="modal-drag"><i class="fa fa-times mr-2"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="hidden" id="modalNhomNguyenNhanElementSelect">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:250px;" id="modalNhomNguyenNhanDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-85" id="btnChonNhomNguyenNhan">
            <i class="fas fa-mouse-pointer mr-2"></i> Chọn
        </button>
        <button type="button" class="btn-outline-primary btn-sm wd-85 float-right" id="btnBoChonNhomNguyenNhan">
            <i class="fas fa-times mr-2"></i> Bỏ chọn
        </button>
    </div>
</div>

@**Modal cấu hình SLA*@
<div id="modalCauHinhSLA" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width: 75%; margin-bottom:unset; margin-top:10px;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình SLA <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form id="frmCauHinhSLA" name="frmCauHinhSLA" novalidate="novalidate" method="post">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ</label>
                                <select class="select2 form-control custom-select" required name="nv" style="width: 100%; height:36px;">
                                    <option value="">Chọn nghiệp vụ</option>
                                    <option value="XE">Xe ô tô</option>
                                    <option value="XE_MAY">Xe máy</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row mt-2">
                    <div class="col-2" style="padding-right: 0;">
                        <div class="card" style="margin-bottom:unset;">
                            <div class="card-body p-0">
                                <div class="border rounded">
                                    <div class="justify-content-between align-items-center p-2 card-title-bg">
                                        <h6 class="m-0 text-center">Ngày áp dụng</h6>
                                    </div>
                                    <table class="table table-hover" id="tblDsNgayCauHinhSLA" style="border-bottom:1px solid #e8eef3;">
                                        <tbody id="tblDsNgayCauHinhSLA">
                                        </tbody>
                                    </table>
                                    <div class="pt-3 text-center" id="divThemMoiNgayCHSLA">
                                        <button type="button" class="btn btn-primary btn-sm" style="width:100%" id="btnOpenInputThemNgayCHSLA"><i class="fa fa-plus"></i> Thêm mới</button>
                                    </div>
                                    <div class="pt-3" id="divInputThemNgayCHSLA">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <input type="text" id="ngayad_sla" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" placeholder="dd/mm/yyyy" style="width:100%">
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary btn-sm" id="btnThemNgayCHSLA" style="margin-top:5px; width:48%">
                                                <i class="fa fa-save mr-2"></i>Lưu
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" id="btnDongNgayCHSLA" style="margin-top: 5px; width: 49%">
                                                <i class="fas fa-window-close mr-2"></i>Đóng
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-10">
                        <div class="table-responsive" style="max-height: 410px;">
                            <table id="tblThongTinCauHinhSLA" class="table table-bordered fixed-header">
                                <thead class="font-weight-bold">
                                    <tr class="text-center uppercase">
                                        <th style="width: 45px">STT</th>
                                        <th style="width: 26%">Bước thực hiện</th>
                                        <th>Tiền từ (VNĐ)</th>
                                        <th>Tiền tới (VNĐ)</th>
                                        <th style="width: 14%">Thời gian SLA theo quy trình (phút)</th>
                                        <th style="width: 14%">Thời gian sắp đến hạn SLA (phút)</th>
                                        <th>Thời gian hành chính</th>
                                        <th style="width: 60px"></th>
                                    </tr>
                                </thead>
                                <tbody id="tblCauHinhSLA" class="tblCauHinhSLA">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display: block; padding: 10px 0px 0px;">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                        <i class="fas fa-window-close mr-2"></i>Đóng
                    </button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinhSLA">
                        <i class="fa fa-save mr-2"></i>Lưu
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@*Modal phân công theo địa bàn giám định*@
<div id="modalCauHinhPhanCong" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình phân công theo địa bàn giám định <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <div class="row p-2 tabCauHinhPhanCongTheoDiaBan d-none" style="height: 65vh;">
                    <div class="col-12 pb-2" id="navCauHinhPhanCongDiaBanGD">
                        <ul class="nav nav-pills font-weight-bold" role="tablist">
                            <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                <a class="nav-link active" id="tabCauHinhPhanCongDiaBanGDXeOTo" data-val="XE" data-toggle="tab" href="#" role="tab" aria-controls="tabCauHinhPhanCongDiaBanGDXeOTo" aria-selected="true" onclick="layCauHinhPhanCongDiaBanGD('XE')">
                                    Nghiệp vụ xe ô tô
                                </a>
                            </li>
                            <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                <a class="nav-link" id="tabCauHinhPhanCongDiaBanGDXeMay" data-val="XE_MAY" data-toggle="tab" href="#" role="tab" aria-controls="tabCauHinhPhanCongDiaBanGDXeMay" aria-selected="false" onclick="layCauHinhPhanCongDiaBanGD('XE_MAY')">
                                    Nghiệp vụ xe máy
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-12">
                        <div class="tab-content">
                            <div class="tab-pane fade show active data-scroll" role="tabpanel" aria-labelledby="tabCauHinhPhanCongDiaBanGDXeOTo">
                                <div class="row w-100 m-0 p-0">
                                    <div class="col-4 px-0">
                                        <div class="card border mb-0">
                                            <div class="justify-content-between align-items-center p-2 card-title-bg text-center font-weight-bold border-bottom">
                                                <div>
                                                    <input type="text" class="form-control" id="inputTimKiemTinhThanh" placeholder="Tìm kiếm tỉnh thành" />
                                                </div>
                                            </div>
                                            <div class="card-body p-2" style="height: 51vh; overflow-y: auto;">
                                                <div id="modalCHDanhSachTinhThanh">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 px-2">
                                        <div class="card border mb-0">
                                            <div class="justify-content-between align-items-center p-2 card-title-bg text-center font-weight-bold border-bottom">
                                                <div>
                                                    <input type="text" class="form-control" id="inputTimKiemQuanHuyen" placeholder="Tìm kiếm quận huyện" />
                                                </div>
                                            </div>
                                            <div class="card-body p-2" style="height: 51vh; overflow-y: auto;">
                                                <div id="modalCHDanhSachQuanHuyen">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 px-0">
                                        <div class="card border mb-0">
                                            <div class="justify-content-between align-items-center p-2 card-title-bg text-center font-weight-bold border-bottom">
                                                <div>
                                                    <input type="text" class="form-control" id="inputTimKiemDonViXuLy" placeholder="Tìm kiếm đơn vị xử lý" />
                                                </div>
                                            </div>
                                            <div class="card-body p-2" style="height: 51vh; overflow-y: auto;">
                                                <div id="modalCHDanhSachDonViXuLy">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row p-2 tabDanhSachCauHinh" style="height: 65vh;">
                    <div class="table-responsive px-2">
                        <table class="table table-striped table-bordered fixed-header">
                            <thead class="font-weight-bold">
                                <tr class="text-center uppercase">
                                    <th style="width: 50px;">STT</th>
                                    <th>Nghiệp vụ</th>
                                    <th>Tỉnh thành</th>
                                    <th>Quận huyện</th>
                                    <th>Đơn vị bồi thường</th>
                                    <th style="width: 50px;"></th>
                                    <th style="width: 50px;"></th>
                                </tr>
                            </thead>
                            <tbody id="tblDanhSachCauHinhPhanCong">
                            </tbody>
                        </table>
                    </div>
                    <div class="px-2 mt-2" id="tblDanhSachCauHinhPhanCong_pagination"></div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinh">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-100 float-right" id="btnThemMoiPhanCong">
                    <i class="fa fa-plus mr-2"></i>Thêm mới
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-140 float-right" id="btnXemDanhSach">
                    <i class="fa fa-eye mr-2"></i>Xem danh sách
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalChonMaChiNhanh" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header px-2">
        <h5><span class="modal-drag-title">Chọn chi nhánh</span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" placeholder="Tìm kiếm thông tin" class="form-control" id="modalChonMaChiNhanhElementSearch">
            </div>
            <div class="col-12 mt-2 scrollable" style="max-height:230px;" id="modalChonMaChiNhanhDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonMaChiNhanh">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

<div id="modalChonQuanHuyen" class="modal-drag" style="width:400px; z-index:9999999;">
    <div class="modal-drag-header px-2">
        <h5><span class="modal-drag-title">Chọn phường xã </span> <span data-dismiss="modal-drag"><i class="fa fa-times"></i></span></h5>
    </div>
    <div class="modal-drag-content" style="padding-top:5px;">
        <div class="row">
            <div class="col-12">
                <input type="text" placeholder="Tìm kiếm thông tin" class="form-control" id="modalChonQuanHuyenElementSearch">
            </div>
            <div class="col-12 d-flex justify-content-between align-items-center py-2">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="chon-tat-ca" onchange="onChonTatCaQuanHuyen(this)">
                    <label class="custom-control-label font-weight-bold" for="chon-tat-ca">Chọn tất cả quận huyện</label>
                </div>
            </div>
            <div class="col-12 scrollable" style="max-height:230px;" id="modalChonQuanHuyenDanhSach">
            </div>
        </div>
    </div>
    <div class="modal-drag-footer">
        <button type="button" class="btn btn-primary btn-sm wd-90" id="btnChonQuanHuyen">
            <i class="fas fa-save mr-2"></i> Chọn
        </button>
    </div>
</div>

@*Modal Cấu hình bên tham gia giám định mặc định*@
<div id="modalCHBenGDMD" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width: 50%;">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình bên tham gia giám định mặc định <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 pb-2" id="navCauHinhBenGDMD">
                        <ul class="nav nav-pills font-weight-bold" role="tablist">
                            <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                <a class="nav-link active" data-val="XE" data-toggle="tab" href="#tabCauHinhBenGDMDXeOTo" role="tab" aria-controls="tabCauHinhBenGDMDXeOTo" aria-selected="true" onclick="layCauHinhBenThamGiaGiamDinh('XE')">
                                    Nghiệp vụ xe ô tô
                                </a>
                            </li>
                            <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                <a class="nav-link" data-val="XE_MAY" data-toggle="tab" href="#tabCauHinhBenGDMDXeMay" role="tab" aria-controls="tabCauHinhBenGDMDXeMay" aria-selected="false" onclick="layCauHinhBenThamGiaGiamDinh('XE_MAY')">
                                    Nghiệp vụ xe máy
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="tab-content">
                            <div id="tabCauHinhBenGDMDXeOTo" class="tab-pane fade show active data-scroll" role="tabpanel" aria-labelledby="tabCauHinhBenGDMDXeOTo">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="table-responsive" style="max-height: 410px;">
                                            <table class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th style="width: 45px">STT</th>
                                                        <th style="width: 26%">Tên cấu hình</th>
                                                        <th>Mối quan hệ</th>
                                                        <th>Mặc định</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblCHBenGDMDXe" class="tblCHBenGDMD">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="tabCauHinhBenGDMDXeMay" class="tab-pane fade data-scroll" role="tabpanel" aria-labelledby="tabCauHinhBenGDMDXeMay">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="table-responsive" style="max-height: 410px;">
                                            <table class="table table-bordered fixed-header">
                                                <thead class="font-weight-bold">
                                                    <tr class="text-center uppercase">
                                                        <th style="width: 45px">STT</th>
                                                        <th style="width: 26%">Tên cấu hình</th>
                                                        <th>Mối quan hệ</th>
                                                        <th>Mặc định</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tblCHBenGDMDXeMay" class="tblCHBenGDMD">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display: block;">
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalCHBenGDMD_MQH" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width: 350px; margin-bottom:unset; margin-top:50px;">
        <form name="frmCHBenGDMD_MQH" method="post">
            <div class="modal-content">
                <div class="modal-header py-1">
                    <h4 class="modal-title">Chọn mối quan hệ</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>

                <div class="modal-body">
                    <div class="row my-2">
                        <div class="col-sm-12">
                            <input type="text" name="ma_doi_tac" hidden />
                            <input type="text" name="doi_tuong" hidden />
                            <input type="text" name="mac_dinh" hidden />
                            <input type="text" name="nv" hidden />
                            <div class="form-group mb-3">
                                <select class="select2 form-control custom-select select2-hidden-accessible" name="ma_moi_qhe" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="display: block; padding: 10px 0px 0px;">
                        <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                            <i class="fas fa-window-close mr-2"></i>Đóng
                        </button>
                        <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCHBenGDMD_MQH">
                            <i class="fa fa-save"></i> Lưu
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@*Cấu hình bồi thường*@
<div id="modalThemCauHinhXuLyBoiThuong" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình xử lý hồ sơ <span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="padding:0px">
                <div class="row p-2">
                    <div class="col-2 pr-0">
                        <div class="row p-2">
                            <div class="col-12 pr-0">
                                <div class="card border mb-0">
                                    <div class="card-body justify-content-between align-items-center p-2" style="height:68vh;overflow-y:auto">
                                        <h6 class="text-center">Ngày áp dụng</h6>
                                        <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="tblDsNgayCauHinhXLBT">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-10">
                        <div class="row p-2">
                            <div class="col-7">
                                <form name="frmCauHinhXuLyBoiThuong" novalidate="novalidate" method="post">
                                    <div class="form-group">
                                        <label class="_required">Đơn vị xử lý</label>
                                        <select class="select2 form-control custom-select" required name="ma_chi_nhanh" style="width: 100%; height:36px;">
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="col-5 pl-0">
                                <div class="form-group">
                                    <label class="">Tìm kiếm</label>
                                    <input type="text" class="form-control" autocomplete="off" name="ma_nsd" onkeyup="onChangeTimKiemCanBo(this)" placeholder="Tìm kiếm cán bộ">
                                </div>
                            </div>
                            <div class="col-12 py-2" id="navCauHinhXuLy">
                                <ul class="nav nav-pills font-weight-bold" role="tablist">
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link active" data-val="XE" data-toggle="tab" href="#tabCauHinhXeOTo" role="tab" aria-controls="tabCauHinhXeOTo" aria-selected="true" onclick="layCauHinhXuLyBoiThuong('XE')">
                                            Nghiệp vụ xe ô tô
                                        </a>
                                    </li>
                                    <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" data-val="XE_MAY" data-toggle="tab" href="#tabCauHinhXeMay" role="tab" aria-controls="tabCauHinhXeMay" aria-selected="false" onclick="layCauHinhXuLyBoiThuong('XE_MAY')">
                                            Nghiệp vụ xe máy
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-12">
                                <div class="tab-content">
                                    <div id="tabCauHinhXeOTo" class="tab-pane fade show active data-scroll" role="tabpanel" aria-labelledby="tabCauHinhXeOTo">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="card border mb-0">
                                                    <div class="card-body p-0">
                                                        <div class="table-responsive" style="height: 54vh; overflow-y: auto;">
                                                            <table class="table table-bordered">
                                                                <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                                    <tr class="text-center uppercase">
                                                                        <th style="width:50px" class="text-center">STT</th>
                                                                        <th>Cán bộ</th>
                                                                        <th style="width:180px;">
                                                                            <div class="custom-control custom-checkbox">
                                                                                <input type="checkbox" onchange="onChonTatCaGDVHTXe(this)" id="chon_tat_ca_gdvht_xe" class="custom-control-input">
                                                                                <label class="custom-control-label" for="chon_tat_ca_gdvht_xe">GĐVHT là GĐVTT</label>
                                                                            </div>
                                                                        </th>
                                                                        <th style="width:180px;">
                                                                            <div class="custom-control custom-checkbox">
                                                                                <input type="checkbox" onchange="onChonTatCaGDVTTXe(this)" id="chon_tat_ca_gdvtt_xe" class="custom-control-input">
                                                                                <label class="custom-control-label" for="chon_tat_ca_gdvtt_xe">GĐVTT là BTV</label>
                                                                            </div>
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="modalCauHinhXuLyBoiThuongXe">
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="tabCauHinhXeMay" class="tab-pane fade data-scroll" role="tabpanel" aria-labelledby="tabCauHinhXeMay">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="card border mb-0">
                                                    <div class="card-body p-0">
                                                        <div class="table-responsive" style="height: 54vh; overflow-y: auto;">
                                                            <table class="table table-bordered">
                                                                <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                                    <tr class="text-center uppercase">
                                                                        <th style="width:50px" class="text-center">STT</th>
                                                                        <th>Cán bộ</th>
                                                                        <th style="width:180px;">
                                                                            <div class="custom-control custom-checkbox">
                                                                                <input type="checkbox" onchange="onChonTatCaGDVHTXeMay(this)" id="chon_tat_ca_gdvht_xe_may" class="custom-control-input">
                                                                                <label class="custom-control-label" for="chon_tat_ca_gdvht_xe_may">GĐVHT là GĐVTT</label>
                                                                            </div>
                                                                        </th>
                                                                        <th style="width:180px;">
                                                                            <div class="custom-control custom-checkbox">
                                                                                <input type="checkbox" onchange="onChonTatCaGDVTTXeMay(this)" id="chon_tat_ca_gdvtt_xe_may" class="custom-control-input">
                                                                                <label class="custom-control-label" for="chon_tat_ca_gdvtt_xe_may">GĐVTT là BTV</label>
                                                                            </div>
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="modalCauHinhXuLyBoiThuongXeMay">
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnThemNgayXuLyBoiThuong">
                    <i class="fas fa-plus mr-2"></i>Thêm ngày áp dụng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" id="btnLuuCauHinhXuLyBoiThuong">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemNgayApDungXLBT" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h5 class="modal-title">Nhập ngày áp dụng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <form name="frmThemNgayApDungXLBT" method="post">
                    <div class="row mg-t-6 p-2">
                        <div class="col col-12">
                            <div class="form-group">
                                <label for="ngay_d">Ngày áp dụng</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" autocomplete="off" name="ngay_ad" display-format="date" value-format="number" placeholder="dd/mm/yyyy">
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="padding: 0.1em 0.8em !important;"><span class="ti-calendar"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-85 float-right" id="btnLuuThemNgayApDungCauHinhXLBT">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemCauHinhBoiThuong" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình bồi thường TSKT<span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <div class="row p-2">
                    <div class="col-2 pr-0">
                        <div class="row p-2">
                            <div class="col-12 pr-0">
                                <div class="card border mb-0">
                                    <div class="card-body justify-content-between align-items-center p-2 card-title-bg" style="height:65vh;overflow-y:auto">
                                        <h6 class="text-center">Ngày áp dụng</h6>
                                        <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="tblDsNgayCHBT">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-10">
                        <div class="row p-2">
                            <div class="col-12">
                                <ul class="nav nav-pills font-weight-bold" role="tablist" style="background-color:#f8f9fa">
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link active" id="tai_san-bt-tab" onclick="changeTabCauHinhBoiThuong('TAISAN')" data-toggle="tab" href="#tai_san_bt" role="tab" aria-controls="home" aria-selected="true">
                                            Nghiệp vụ tài sản
                                        </a>
                                    </li>
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="ky_thuat-tab" onclick="changeTabCauHinhBoiThuong('KYTHUAT')" data-toggle="tab" href="#ky_thuat_bt" role="tab" aria-controls="profile" aria-selected="false">
                                            Nghiệp vụ kỹ thuật
                                        </a>
                                    </li>
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="trach_nhiem-tab" onclick="changeTabCauHinhBoiThuong('TRACHNHIEM')" data-toggle="tab" href="#trach_nhiem_bt" role="tab" aria-controls="profile" aria-selected="false">
                                            Nghiệp vụ trách nhiệm
                                        </a>
                                    </li>
                                    <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="hon_hop-tab" onclick="changeTabCauHinhBoiThuong('HONHOP')" data-toggle="tab" href="#hon_hop_bt" role="tab" aria-controls="profile" aria-selected="false">
                                            Nghiệp vụ hỗn hợp
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content" style="padding-top: 10px">
                                    <div id="tai_san_bt" class="tab-pane fade data-scroll active show" role="tabpanel" aria-labelledby="taisan-bt-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tblCauHinhBoiThuongTaiSan">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th style="width:50px">STT</th>
                                                                <th style="width:150px;">Màn hình</th>
                                                                <th>Tên cấu hình</th>
                                                                <th style="width:180px;">Giá trị</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalCauHinhBoiThuongTaiSan">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll" id="ky_thuat_bt" role="tabpanel" aria-labelledby="kythuat-bt-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tblCauHinhBoiThuongKyThuat">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th style="width:50px">STT</th>
                                                                <th style="width:150px;">Màn hình</th>
                                                                <th>Tên cấu hình</th>
                                                                <th style="width:180px;">Giá trị</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalCauHinhBoiThuongKyThuat">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll" id="trach_nhiem_bt" role="tabpanel" aria-labelledby="trachnhiem-bt-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tblCauHinhBoiThuongTrachNhiem">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th style="width:50px">STT</th>
                                                                <th style="width:150px;">Màn hình</th>
                                                                <th>Tên cấu hình</th>
                                                                <th style="width:180px;">Giá trị</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalCauHinhBoiThuongTrachNhiem">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll" id="hon_hop_bt" role="tabpanel" aria-labelledby="honhop-bt-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tblCauHinhBoiThuongHonHop">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th style="width:50px">STT</th>
                                                                <th style="width:150px;">Màn hình</th>
                                                                <th>Tên cấu hình</th>
                                                                <th style="width:180px;">Giá trị</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="modalCauHinhBoiThuongHonHop">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnThemNgayCHBT">
                    <i class="fas fa-plus mr-2"></i>Thêm ngày áp dụng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary wd-85 btn-sm float-right" id="btnLuuCauHinhBoiThuong">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div id="modalThemCauHinhTSKTSLA" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" style="max-width:90%">
        <div class="modal-content">
            <div class="modal-header py-1">
                <h4 class="modal-title">Cấu hình SLA<span class="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body" style="background-color:#54667a0a; padding:0px">
                <div class="row p-2">
                    <div class="col-2 pr-0">
                        <div class="row p-2">
                            <div class="col-12 pr-0">
                                <div class="card border mb-0" style="height:65vh;overflow-y:auto">
                                    <div class="card-body justify-content-between align-items-center p-2 card-title-bg">
                                        <h6 class="text-center">Ngày áp dụng</h6>
                                        <div class="nav flex-column nav-pills" role="tablist" aria-orientation="vertical" id="tblDsNgayCauHinhTSKTSLA">
                                        </div>
                                    </div>
                                    <div class="ngayad_sla_tskt">
                                        <div class="input-group">
                                            <input type="text" id="ngayad_sla_tskt" class="form-control datepicker" autocomplete="off" display-format="date" value-format="number" placeholder="dd/mm/yyyy" style="width:100%">
                                            <div class="input-group-append">
                                                <span class="input-group-text" style="padding: 0.1em 0.3em !important;"><span class="ti-calendar"></span></span>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn btn-primary btn-sm" id="btnThemNgayCHSLATSKT" style="margin-top:5px; width:48%">
                                                <i class="fa fa-save mr-2"></i>Lưu
                                            </button>
                                            <button type="button" class="btn btn-primary btn-sm" id="btnDongNgayCHSLATSKT" style="margin-top: 5px; width: 49%">
                                                <i class="fas fa-window-close mr-2"></i>Đóng
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-10">
                        <div class="row p-2">
                            <div class="col-12" id="navTSKTSLA">
                                <ul class="nav nav-pills font-weight-bold"  role="tablist" style="background-color:#f8f9fa">
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="tai_san_sla_tab" onclick="changeTabCauHinhSLA('TAISAN')" data-tab="TAISANSLA" data-toggle="tab" data-nv="TAISAN" href="#TAISANSLA" role="tab" aria-controls="tai_san_sla" aria-selected="true">
                                            Nghiệp vụ tài sản
                                        </a>
                                    </li>
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="ky_thuat_sla_tab" onclick="changeTabCauHinhSLA('KYTHUAT')" data-toggle="tab" data-tab="KYTHUATSLA" data-nv="KYTHUAT" href="#KYTHUATSLA" role="tab" aria-controls="ky_thuat_sla" aria-selected="false">
                                            Nghiệp vụ kỹ thuật
                                        </a>
                                    </li>
                                    <li class="nav-item mr-2" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="trach_nhiem_sla_tab" onclick="changeTabCauHinhSLA('TRACHNHIEM')" data-toggle="tab" data-tab="TRACHNHIEMSLA" data-nv="TRACHNHIEM" href="#TRACHNHIEMSLA" role="tab" aria-controls="trach_nhiem_sla" aria-selected="false">
                                            Nghiệp vụ trách nhiệm
                                        </a>
                                    </li>
                                    <li class="nav-item" style="background-color: #edeff0; border-radius: 5px;">
                                        <a class="nav-link" id="hon_hop_sla_tab" onclick="changeTabCauHinhSLA('HONHOP')" data-toggle="tab" data-nv="HONHOP" data-tab="HONHOPSLA" href="#HONHOPSLA" role="tab" aria-controls="hon_hop_sla" aria-selected="false">
                                            Nghiệp vụ hỗn hợp
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content" style="padding-top: 10px">
                                    <div class="tab-pane fade data-scroll tskt" id="TAISANSLA" role="tabpanel" aria-labelledby="taisan-sla-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tableCauHinhSLATaiSan">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th class="align-middle" style="width: 45px;">STT</th>
                                                                <th class="align-middle">Bước thực hiện</th>
                                                                <th class="align-middle">Mã tính SLA</th>
                                                                <th class="align-middle">Tiền từ (VNĐ)</th>
                                                                <th class="align-middle">Tiền tới (VNĐ)</th>
                                                                <th class="align-middle" style="width: 130px;">Thời gian SLA theo quy trình (phút)</th>
                                                                <th class="align-middle" style="width: 125px;">Thời gian sắp đến hạn SLA (phút)</th>
                                                                <th class="align-middle" style="width: 85px;">Thời gian hành chính</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="tblCauHinhSLATAISAN">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll tskt" id="KYTHUATSLA" role="tabpanel" aria-labelledby="kythuat-sla-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tableCauHinhSLAKyThuat">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th class="align-middle" style="width: 45px;">STT</th>
                                                                <th class="align-middle">Bước thực hiện</th>
                                                                <th class="align-middle">Mã tính SLA</th>
                                                                <th class="align-middle">Tiền từ (VNĐ)</th>
                                                                <th class="align-middle">Tiền tới (VNĐ)</th>
                                                                <th class="align-middle" style="width: 130px;">Thời gian SLA theo quy trình (phút)</th>
                                                                <th class="align-middle" style="width: 125px;">Thời gian sắp đến hạn SLA (phút)</th>
                                                                <th class="align-middle" style="width: 85px;">Thời gian hành chính</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="tblCauHinhSLAKYTHUAT">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll tskt" id="TRACHNHIEMSLA" role="tabpanel" aria-labelledby="trachnhiem-sla-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tableCauHinhSLATrachNhiem">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th class="align-middle" style="width: 45px;">STT</th>
                                                                <th class="align-middle">Bước thực hiện</th>
                                                                <th class="align-middle">Mã tính SLA</th>
                                                                <th class="align-middle">Tiền từ (VNĐ)</th>
                                                                <th class="align-middle">Tiền tới (VNĐ)</th>
                                                                <th class="align-middle" style="width: 130px;">Thời gian SLA theo quy trình (phút)</th>
                                                                <th class="align-middle" style="width: 125px;">Thời gian sắp đến hạn SLA (phút)</th>
                                                                <th class="align-middle" style="width: 85px;">Thời gian hành chính</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="tblCauHinhSLATRACHNHIEM">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade data-scroll tskt" id="HONHOPSLA" role="tabpanel" aria-labelledby="honhop-sla-tab">
                                        <div class="card border mb-0">
                                            <div class="card-body p-0">
                                                <div class="table-responsive" style="height: 59vh;overflow-y: auto;">
                                                    <table class="table table-bordered" id="tableCauHinhSLAHonHop">
                                                        <thead class="font-weight-bold card-title-bg-primary" style="position: sticky; top: 0; z-index: 999999;">
                                                            <tr class="text-center uppercase">
                                                                <th class="align-middle" style="width: 45px;">STT</th>
                                                                <th class="align-middle">Bước thực hiện</th>
                                                                <th class="align-middle">Mã tính SLA</th>
                                                                <th class="align-middle">Tiền từ (VNĐ)</th>
                                                                <th class="align-middle">Tiền tới (VNĐ)</th>
                                                                <th class="align-middle" style="width: 130px;">Thời gian SLA theo quy trình (phút)</th>
                                                                <th class="align-middle" style="width: 125px;">Thời gian sắp đến hạn SLA (phút)</th>
                                                                <th class="align-middle" style="width: 85px;">Thời gian hành chính</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="tblCauHinhSLAHONHOP">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="display:block">
                <button type="button" class="btn btn-primary btn-sm float-left" id="btnOpenInputThemNgayCHSLATSKT">
                    <i class="fas fa-plus mr-2"></i>Thêm ngày áp dụng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal">
                    <i class="fas fa-window-close mr-2"></i>Đóng
                </button>
                <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuCauHinhSLATSKT">
                    <i class="fa fa-save mr-2"></i>Lưu
                </button>
            </div>
        </div>
    </div>
</div>
