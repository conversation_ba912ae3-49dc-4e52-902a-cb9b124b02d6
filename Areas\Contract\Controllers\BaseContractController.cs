﻿using Microsoft.AspNetCore.Mvc;

namespace ESCS.Areas.Contract.Controllers
{
    public class BaseContractController : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        //private static void TestExcel()
        //{
        //    ApplicationClass app = new ApplicationClass();
        //    Workbook book = null;
        //    Microsoft.Office.Interop.Excel.Range range = null;

        //    try
        //    {
        //        app.Visible = false;
        //        app.ScreenUpdating = false;
        //        app.DisplayAlerts = false;

        //        string execPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().CodeBase);

        //        book = app.Workbooks.Open(@"C:\data.xls", Missing.Value, Missing.Value, Missing.Value
        //                                          , Missing.Value, Missing.Value, Missing.Value, Missing.Value
        //                                         , Missing.Value, Missing.Value, Missing.Value, Missing.Value
        //                                        , Missing.Value, Missing.Value, Missing.Value);
        //        foreach (Worksheet sheet in book.Worksheets)
        //        {
        //            Console.WriteLine(@"Values for Sheet " + sheet.Index);

        //            // get a range to work with
        //            range = sheet.get_Range("A1", Missing.Value);
        //            // get the end of values to the right (will stop at the first empty cell)
        //            range = range.get_End(XlDirection.xlToRight);
        //            // get the end of values toward the bottom, looking in the last column (will stop at first empty cell)
        //            range = range.get_End(XlDirection.xlDown);

        //            // get the address of the bottom, right cell
        //            string downAddress = range.get_Address(
        //                false, false, XlReferenceStyle.xlA1,
        //                Type.Missing, Type.Missing);

        //            // Get the range, then values from a1
        //            range = sheet.get_Range("A1", downAddress);
        //            object[,] values = (object[,])range.Value2;

        //            // View the values
        //            Console.Write("\t");
        //            Console.WriteLine();
        //            for (int i = 1; i <= values.GetLength(0); i++)
        //            {
        //                for (int j = 1; j <= values.GetLength(1); j++)
        //                {
        //                    Console.Write("{0}\t", values[i, j]);
        //                }
        //                Console.WriteLine();
        //            }
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        Console.WriteLine(e);
        //    }
        //    finally
        //    {
        //        range = null;
        //        if (book != null)
        //            book.Close(false, Missing.Value, Missing.Value);
        //        book = null;
        //        if (app != null)
        //            app.Quit();
        //        app = null;
        //    }
        //}
    }
}