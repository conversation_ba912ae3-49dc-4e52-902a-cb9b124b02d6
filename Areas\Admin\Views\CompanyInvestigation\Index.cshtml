﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục công ty giám định";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}
<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Công ty giám định</h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Công ty giám định</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:5px;">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Nhập thông tin mã/tên/email/điện thọai" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3 d-none">
                            <div class="form-group">
                                <label class="">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                         <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Nghiệp vụ </label>
                                <select class="select2 form-control custom-select" name="nv" style="width: 100%; height:36px;">
                                    <option value="">Chọn nghiệp vụ</option>
                                    <option value="XE">Xe cơ giới</option>
                                    <option value="TAISAN">Tài sản</option>
                                    <option value="KYTHUAT">Kỹ thuật</option>
                                    <option value="HONHOP">Hỗn hợp</option>
                                    <option value="TRACHNHIEM">Trách nhiệm</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Trạng thái</label>
                                <select class="select2 form-control custom-select" name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="N">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3" style="padding-top: 21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Tìm kiếm" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-24p" title="Thêm mới" id="btnNhapCongTyGD">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:3px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewCongTyGD" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapMaCongTyGD" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuThongTinMaCongTyGD" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin công ty giám định <span id="modal-user-log" style="font-size: 14px; font-style: italic;"></span></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Nghiệp vụ </label>
                                <select class="select2 form-control custom-select" required name="nv" style="width: 100%; height:36px;">
                                    <option value="">Chọn nghiệp vụ</option>
                                    <option value="XE">Xe cơ giới</option>
                                    <option value="TAISAN">Tài sản</option>
                                    <option value="KYTHUAT">Kỹ thuật</option>
                                    <option value="HONHOP">Hỗn hợp</option>
                                    <option value="TRACHNHIEM">Trách nhiệm</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Trạng thái</label>
                                <select class="select2 form-control custom-select" required name="trang_thai" style="width: 100%; height:36px;">
                                    <option value="">Chọn trạng thái</option>
                                    <option value="D">Đang sử dụng</option>
                                    <option value="N">Ngừng sử dụng</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Mã</label>
                                <input type="text" maxlength="30" name="ma" autocomplete="off" required class="form-control" placeholder="Mã công ty giám định">
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-group">
                                <label class="_required">Tên</label>
                                <input type="text" maxlength="250" name="ten" autocomplete="off" required class="form-control" placeholder="Tên công ty giám định">
                            </div>
                        </div>                       
                    </div>
                    <div class="row" style="margin-top:3px">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Địa chỉ</label>
                                <input type="text" maxlength="250" name="dia_chi" autocomplete="off" required class="form-control" placeholder="Địa chỉ công ty giám định">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Điện thoại</label>
                                <div class="input-group">
                                    <input type="text" autocomplete="off" maxlength="20" name="dthoai" class="form-control" placeholder="Số điện thoại">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Email</label>
                                <div class="input-group">
                                    <input type="text" maxlength="50" fn-validate="validateEmailControl" autocomplete="off" name="email" required="" class="form-control email-inputmask" im-insert="true" placeholder="Email">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top:3px">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="">Ngân hàng</label>
                                <select class="select2 form-control custom-select" name="ma_nh" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Người thụ hưởng</label>
                                <input type="text" autocomplete="off" maxlength="100" name="nguoi_th" class="form-control" placeholder="Người thụ hưởng">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="">Số tài khoản</label>
                                <input type="text" autocomplete="off" maxlength="50" name="stk" class="form-control text" placeholder="Số tài khoản">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuMaCongTyGD"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaMaCongTyGD"><i class="fas fa-trash-alt"></i> Xóa</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts{
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/CompanyInvestigationService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/BankListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/CompanyInvestigation.js" asp-append-version="true"></script>
}