﻿@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "Danh mục Menu";
    Layout = "~/Views/Shared/_LayoutAdmin.cshtml";
}

<div class="row page-titles">
    <div class="col-md-5 col-12 align-self-center">
        <h3 class="text-themecolor mb-0">Quản lý Men<PERSON></h3>
    </div>
    <div class="col-md-7 col-12 align-self-center justify-content-end d-none d-md-flex">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="/">Trang chủ</a>
            </li>
            <li class="breadcrumb-item active">Menu</li>
        </ol>
    </div>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-12">
            <div class="card card-body" style="padding-top:4px">
                <form action="/" method="post" name="frmTimKiem" novalidate="novalidate" data-select2-id="20">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label>Tìm kiếm thông tin</label>
                                <input type="text" name="tim" id="tim" autocomplete="off" placeholder="Tên menu" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3 d-none">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Nhóm</label>
                                <select class="select2 form-control custom-select" name="nhom" style="width: 100%; height:36px;">
                                    <option value="">Chọn nhóm menu</option>
                                    <option value="ADMIN">Quản trị viên</option>
                                    <option value="CLIENT">Người dùng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="">Hiển thị</label>
                                <select class="select2 form-control custom-select" name="hien_thi" style="width:100%; height:36px;">
                                    <option value="">Chọn hiển thị</option>
                                    <option value="1">Hiển thị</option>
                                    <option value="2">Không hiển thị</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding-top:21px;">
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnTimKiem">
                                <i class="fa fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm wd-40p" id="btnNhapMenu">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="row" style="margin-top:2px">
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <div id="gridViewMenu" class="table-app" style="height: 64vh;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-example-modal-lg" id="modalNhapMenu" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="myLargeModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form name="frmLuuMenu" method="post">
                <div class="modal-header">
                    <h4 class="modal-title">Thông tin danh mục menu</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="pm" value="BT" />
                    <input type="hidden" name="so_id" value="" />
                    <input type="hidden" name="nhom_quyen" value="" />
                    <div class="row d-none">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Đối tác</label>
                                <select class="select2 form-control custom-select" required name="ma_doi_tac" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Nhóm</label>
                                <select class="select2 form-control custom-select" required name="nhom" style="width: 100%; height:36px;">
                                    <option value="">Chọn nhóm menu</option>
                                    <option value="ADMIN">Quản trị viên</option>
                                    <option value="CLIENT">Người dùng</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>Menu cha</label>
                                <select class="select2 form-control custom-select" name="so_id_cha" style="width: 100%; height:36px;"></select>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>URL</label>
                                <input type="text" maxlength="100" autocomplete="off" name="url" placeholder="Đường dẫn menu" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="_required">Tên</label>
                                <input type="text" maxlength="100" autocomplete="off" name="ten" placeholder="Tên menu" required class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Icon</label>
                                <input type="text" maxlength="100" autocomplete="off" name="icon" placeholder="Icon" required class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>Thứ tự hiển thị</label>
                                <input type="text" max="100" name="stt" autocomplete="off" placeholder="Thứ tự hiển thị" class="form-control number">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Hiển thị tab mới</label>
                                <select class="select2 form-control custom-select" name="target" style="width:100%; height:36px;">
                                    <option value="">Mặc định</option>
                                    <option value="_blank">Hiển thị tab mới</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="_required">Hiển thị</label>
                                <select class="select2 form-control custom-select" required name="hien_thi" style="width:100%; height:36px;">
                                    <option value="">Chọn hiển thị</option>
                                    <option value="1">Hiển thị</option>
                                    <option value="2">Không hiển thị</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col-12">
                            <label style="font-weight:bold">Hiển thị menu theo nhóm quyền</label>
                        </div>
                    </div>
                    <div class="row scrollable" id="dsNhomQuyen" style="height:280px; max-height:280px">
                    </div>
                </div>
                <div class="modal-footer" style="display:block">
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" data-dismiss="modal"><i class="fas fa-window-close"></i> Đóng</button>
                    <button type="button" class="btn btn-primary btn-sm wd-90 float-right" id="btnLuuThongBao"><i class="fa fa-save"></i> Lưu</button>
                    <button type="button" class="btn btn-outline-primary btn-sm wd-80" id="btnXoaThongBao"><i class="fas fa-trash-alt"></i> Xóa</button>
                </div>
            </form>
        </div>
    </div>
</div>
<partial name="_Template.cshtml" />
@section Scripts{
    <script src="~/js/app/Admin/services/FunctionService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/MenuService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/services/PartnerListService.js" asp-append-version="true"></script>
    <script src="~/js/app/Admin/Menu.js" asp-append-version="true"></script>
}

